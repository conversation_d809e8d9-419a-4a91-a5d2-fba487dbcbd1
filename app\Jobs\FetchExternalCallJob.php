<?php

namespace App\Jobs;


use App\Mail\ScriptsMail;
use App\Models\AccountPrefix;
use App\Models\CallRequest;
use App\Models\Interaction;
use App\Models\Organization;
use App\Models\User;
use App\Models\UserGroup;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FetchExternalCallJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $callId;

    /**
     * Max time (in seconds) the job should run.
     */
    public $timeout = 1440000; // 10 minutes

    /**
     * Max attempts before failing.
     */
    public $tries = 3;

    public function __construct($callId)
    {
        $this->callId = $callId;
    }

    public function handle()
    {
        $call = CallRequest::where('call_id', $this->callId)->first();

        if (!$call) return;

        $call->update(['status' => 'processing']);

        $response = Http::withoutVerifying()->timeout(3600)->get('https://oms.extwebonline.com/I-log/fetching/v1/index.php?call_id=' . $this->callId);
        // the response like bellow
        /*
         {
              "call_id":"00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********",
              "Genesys_CallUUID":"00SVAC2AO8BST2KL840SK2LAES0366NK",
              "arrival_time":"2025-07-13 01:37:04.783",
              "start_time":"2025-07-13 01:37:21.737",
              "end_time":"2025-07-13 01:39:54.943",
              "insert_date_time":"2025-07-13 01:39:54.977",
              "Source":"***********",
              "agent_extension":"********",
              "agent_ip":"***********",
              "routing_point":"81917",
              "customer_number":"**********",
              "hold_time":null,
              "hold_count":null,
              "duration":null,
              "ring_time":null,
              "language":null,
              "call_type":null,
              "agent_login_id":null,
              "final_file":{
                "***********":"/i_log/calls/2025-07-13/call_00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********_towxhbp0/segment_1/segment_1_final_arrivel.wav",
                "***********":"/i_log/calls/2025-07-13/call_00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********_j_hi1ctk/segment_1/segment_1_final_arrivel.wav",
                "***********":"/i_log/calls/2025-07-13/call_00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********_z_bqgzxn/segment_1/segment_1_final.wav"
              },
              "server_source":"***********"
            }

         */

        if ($response->successful()) {

            $data = $response->json();

            if (!$data['status']){
                $call->update([
                    'status' => 'failed',
                    'result' => $data['message'],
                ]);
                return;
            }



            try {
                $call_ender = null;

                if (isset($data['payload']['genesys_data']['PARTY_DISCONNECTED'])) {
                    $partyDisconnected = (int) $data['payload']['genesys_data']['PARTY_DISCONNECTED'];
                    $call_ender = $partyDisconnected === 0 ? 'Agent' : 'Customer';
                }

                try {
                    //code...
                    $arrivalTime = isset($data['payload']['arrival_time'])
                    ? substr($data['payload']['arrival_time'], 0, 19)
                    : null;
                    $interaction = Interaction::updateOrCreate(
                        ['call_id' => $data['payload']['call_id']],
                        [
                            'Genesys_CallUUID' => $data['payload']['Genesys_CallUUID'] ?? null,

                            // 'caller_id' => $data['payload']['genesys_data']['ORIGINATOR'] ?? null,
                            'caller_id' => $data['payload']['customer_number'] ?? null,

                            // 'called_id' => $data['payload']['genesys_data']['TARGET'] ?? null,
                            'called_id' => $data['payload']['routing_point'] ?? null,

                            // 'call_type' => isset($data['payload']['genesys_data']['INTERACTION_TYPE']) ? Str::title($data['payload']['genesys_data']['INTERACTION_TYPE']) : null,
                             'call_type' => isset($data['payload']['call_type']) ? $data['payload']['call_type'] === 2 ? "Inbound" : "Outbound" : null,


                            // 'call_type' => "Inbound",

                            // 'hold_duration' => isset($data['payload']['genesys_data']['HOLD_DURATION']) ? gmdate('H:i:s', $data['payload']['genesys_data']['HOLD_DURATION']) : null,
                            'hold_duration' => isset($data['payload']['hold_time']) ? gmdate('H:i:s', $data['payload']['hold_time']) : null,

                            // 'hold_count' => $data['payload']['genesys_data']['HOLD_COUNT'] ?? null,
                            'hold_count' => $data['payload']['hold_count']?? null,

                            // 'call_duration' => isset($data['payload']['genesys_data']['ENGAGE_DURATION']) ? gmdate('H:i:s', $data['payload']['genesys_data']['ENGAGE_DURATION']) : null,
                            'call_duration' => isset($data['payload']['duration']) ? gmdate('H:i:s', $data['payload']['duration']) : null,

                            // 'ring' => $data['payload']['genesys_data']['RING_DURATION'] ?? null,
                            'ring' => $data['payload']['ring_time']?? null,


                            // 'language' => $data['payload']['genesys_data']['CALL_LANGUAGE'] ?? null,
                            'language' => $data['payload']['language'] ?? null,

                            'call_ender' => $call_ender,

                            // 'arrival_time' => isset($data['payload']['genesys_data']['START_TS']) ? Carbon::createFromTimestamp($data['payload']['genesys_data']['START_TS'])->toDateTimeString() : null,
                            'arrival_time' => $arrivalTime,
                        ]
                    );
                    if (!$interaction) {
                        $call->update([
                            'status' => 'failed',
                            'result' => 'Interaction could not be created or updated for call_id: ' . $data['payload']['call_id'],
                        ]);

                    }
                } catch (\Throwable $th) {
                    $call->update([
                        'status' => 'failed',
                        'result' => $th,
                    ]);

                }

                //$callsIdInserted[] = $data['payload']['call_id'];

                // Process EMPLOYEE_ID and assign organization/user group
                if (isset($data['payload']['agent_login_id'])) {
                    $ops_with_prefix = $data['payload']['agent_login_id'] ?? null;


                    $prefixes = DB::table('account_prefixes')->pluck('prefix');
                    $prefixMatched = false;
                    $agent_prefix = null;
                    $agent_id = null;

                    foreach ($prefixes as $prefix) {
                        if (substr($ops_with_prefix, 0, strlen($prefix)) === $prefix) {
                            $prefixMatched = true;
                            $agent_prefix = $prefix;
                            break;
                        }
                    }

                    if ($prefixMatched) {
                        $agent_id = substr($ops_with_prefix, strlen($agent_prefix));
                    } else {
                        $agent_id = substr($ops_with_prefix, 2);
                        $agent_prefix = substr($ops_with_prefix, 0, 2);
                    }

                    $orgainzation_and_user_group = $this->getOrganizationAndUserGroupFromPrefix($agent_prefix);
                    $org_name = $orgainzation_and_user_group['organization'];
                    $user_group_name = $orgainzation_and_user_group['user_group'];

                    $orgAndUserGroupIds = $this->extractOrganizationIdAndUserGroupId($org_name, $user_group_name);
                    $org_id = $orgAndUserGroupIds['org_id'];
                    $user_group_id = $orgAndUserGroupIds['user_group_id'];

                    $interaction->organization_id = $org_id;
                    $interaction->user_group_id = $user_group_id;

                    // Find or create user
                    $user = User::firstWhere('agent_id', $agent_id);
                    if (!$user) {
                        $agent_data_from_headcount = $this->getAgentsDatafromHeadcount($agent_id);
                        if ($agent_data_from_headcount) {
                            $newUser = User::create([
                                'full_name' => $agent_data_from_headcount['full_name'],
                                'username' => $agent_data_from_headcount['username'],
                                'email' => $agent_data_from_headcount['email'],
                                'agent_id' => $agent_id,
                                'organization_id' => $org_id,
                                'password' => Hash::make('extensya@12345678'),
                                'role' => 4,
                                'terminated' => 0,
                                'enabled' => 0,
                                'web_access' => 0,
                            ]);

                            $newUser->parameters()->sync([2, 4, 6, 7, 19, 22, 25, 27]);
                            $newUser->permissions()->sync([1, 2, 4, 5, 8, 10]);
                            $newUser->save();

                            $interaction->user_id = $newUser?->id ?? 0;
                        }
                    } else {
                        $interaction->user_id = $user?->id ?? 0;
                    }

                    $interaction->save();
                }
            } catch (\Exception $e) {
                $call->update([
                    'status' => 'failed',
                    'result' => $e->getMessage(),
                ]);
            }


            if (isset($data['payload']['final_file'])) {
                //the response like bellow

                /*
                  "final_file":{
                        "***********":"/i_log/calls/2025-07-13/call_00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********_towxhbp0/segment_1/segment_1_final_arrivel.wav",
                        "***********":"/i_log/calls/2025-07-13/call_00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********_j_hi1ctk/segment_1/segment_1_final_arrivel.wav",
                        "***********":"/i_log/calls/2025-07-13/call_00310E70-4AC2-17CE-8A95-4101CA0AAA77-*********@***********_z_bqgzxn/segment_1/segment_1_final.wav"
                      },

                 */

                DownloadCallJob::dispatchSync($data['payload']['filename'])->onQueue('fetchCallWavDetails');
//                FetchExternalCallJob::dispatch($callId)->onQueue('fetchCallDetails');
            }



//            Mail::to('<EMAIL>')->send(
//                new ScriptsMail("Fetch Done for Call ID: {$this->callId}", $this->callId)
//            );

            $call->update([
                'status' => 'completed',
                'result' => $data['payload'],
            ]);
        }


        else {

            $call->update([
                'status' => 'failed',
                'result' => "Failed to fetch call ID: {$this->callId}",
            ]);

//            Mail::to('<EMAIL>')->send(
//                new ScriptsMail("Failed to fetch call ID: {$this->callId}", $this->callId)
//            );

            throw new \Exception("Failed to fetch call ID: {$this->callId}");

        }
    }

    public function getAgentsDatafromHeadcount($agent_id)
    {
        $headcountApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/agentsFromHeadcount.php';

        // Send a GET request to the API and get the result
        $userData = Http::withoutVerifying()->timeout(60 * 6000)->get($headcountApiUrl, [
            'ops_id' => $agent_id,
        ])->json();

        // Check if any of the name fields are missing
        if (
            empty($userData['first_name']) ||
            empty($userData['second_name']) ||
            empty($userData['third_name']) ||
            empty($userData['last_name'])
        ) {
            return [
                'full_name' => 'Unknown User',
                'username' => 'Unknown User',
                'email' => $userData['email'] ?? '-', // Use "-" if email is not present
            ];
        }

        // Return the data if all fields are available
        return [
            'full_name' => $userData['first_name'] . ' ' . $userData['second_name'] . ' ' . $userData['third_name'] . ' ' . $userData['last_name'],
            'username' => $userData['first_name'] . ' ' . $userData['last_name'],
            'email' => $userData['email'] ?? '-', // Use "-" if email is not present
        ];
    }

    protected function getOrganizationAndUserGroupFromPrefix($prefix)
    {
        $prefix_data = AccountPrefix::where('prefix', $prefix)->first();
        $org = $prefix_data->account;
        $user_group = $prefix_data->user_group;

        return [
            'organization' => $org,
            'user_group' => "$org - $user_group",
        ];
    }

    protected function extractOrganizationIdAndUserGroupId($org_name, $user_group_name)
    {
        $org_id = Organization::where('name', 'like', "%$org_name%")->first()->id;
        $user_group_id = UserGroup::where('name', 'like', "%$user_group_name%")?->first()?->id;

        return [
            'org_id' => $org_id,
            'user_group_id' => $user_group_id,
        ];
    }

    public function failed(\Throwable $exception)
    {
        $call = CallRequest::where('call_id', $this->callId)->first();

        if ($call){
            $call->update([
                'status' => 'failed',
                'result' => $exception->getMessage(),
            ]);
        }

        // Notify via email
//        Mail::to('<EMAIL>')->send(
//            new ScriptsMail("Fetch failed for Call ID: {$this->callId}", $exception->getMessage())
//        );
    }
}
