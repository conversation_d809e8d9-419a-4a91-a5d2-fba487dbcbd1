<?php

namespace App\Console\Commands;

use App\Jobs\DownloadCallsJob;
use App\Models\AccountPrefix;
use App\Models\Interaction;
use App\Models\Organization;
use App\Models\User;
use App\Models\UserGroup;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class FetchFinalData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetchFinalData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data from server and update or create interactions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fetching data from the API...');

        $callsIdInserted = [];

        set_time_limit(0);

        ini_set('memory_limit', '3800M');

        $response = Http::withoutVerifying()->timeout(60 * 4000)->get('https://oms.extwebonline.com/I-log/index.php');

        if ($response->successful()) {
            $data = $response->json();

            $this->info('Data fetched successfully. Processing records...');
            Log::info('Data fetched successfully. Processing records...');

            if (isset($data['message']) && $data['message'] === 'No records found') {
                $this->info("No records found in the response.");
                Log::info("No records found in the response.");
                return;
            }

            $filesToDownload = [];
            $missingRecords  = [];

            foreach ($data as $key => $record) {
                try {
                    // Dynamically detect and handle 'missing_records'
                    if ($record === 'missing_records' && isset($data[$key + 1]) && is_array($data[$key + 1])) {
                        $missingRecords = $data[$key + 1];
                        continue;
                    }

                    // Check if filename exists and is valid
                    if (isset($record['filename']) && $record['filename'] !== 'Remote file not found') {
                        $filesToDownload[] = $record['filename'];
                    }

                    // Ensure the record has a call_id
                    if (!isset($record['call_id'])) {
                        Log::info("Skipping record without call_id: " . json_encode($record));
                        $this->info("Skipping record without call_id: " . json_encode($record));
                        continue;
                    }

                    Log::info("Processing call_id: {$record['call_id']}");
                    $this->info("Processing call_id: {$record['call_id']}");

                    $call_ender = null;
                    if (isset($record['Genysis_data']['PARTY_DISCONNECTED'])) {
                        $partyDisconnected = (int) $record['Genysis_data']['PARTY_DISCONNECTED'];
                        $call_ender = $partyDisconnected === 0 ? 'Agent' : 'Customer';
                    }
                    // Update or create the interaction record
                    try {
                        //code...
                        $interaction = Interaction::updateOrCreate(
                            ['call_id' => $record['call_id']],
                            [
                                'Genesys_CallUUID' => $record['Genesys_CallUUID'] ?? null,
                                'caller_id' => $record['Genysis_data']['ORIGINATOR'] ?? null,
                                'called_id' => $record['Genysis_data']['TARGET'] ?? null,
                                'call_type' => isset($record['Genysis_data']['INTERACTION_TYPE']) ? Str::title($record['Genysis_data']['INTERACTION_TYPE']) : null,
                                'hold_duration' => isset($record['Genysis_data']['HOLD_DURATION']) ? gmdate('H:i:s', $record['Genysis_data']['HOLD_DURATION']) : null,
                                'hold_count' => $record['Genysis_data']['HOLD_COUNT'] ?? null,
                                'call_duration' => isset($record['Genysis_data']['ENGAGE_DURATION']) ? gmdate('H:i:s', $record['Genysis_data']['ENGAGE_DURATION']) : null,
                                'ring' => $record['Genysis_data']['RING_DURATION'] ?? null,
                                'language' => $record['Genysis_data']['CALL_LANGUAGE'] ?? null,
                                'call_ender' => $call_ender,
                                'arrival_time' => isset($record['Genysis_data']['START_TS']) ? Carbon::createFromTimestamp($record['Genysis_data']['START_TS'])->toDateTimeString() : null,
                            ]
                        );
                        if (!$interaction) {
                            Log::error('Interaction could not be created or updated for call_id: ' . $record['call_id']);
                            continue;
                        }
                    } catch (\Throwable $th) {
                        //throw $th;
                        Log::info('Erorr HERE : ' . $th);
                        continue;

                    }

                    //$callsIdInserted[] = $record['call_id'];

                    // Process EMPLOYEE_ID and assign organization/user group
                    if (isset($record['Genysis_data']['EMPLOYEE_ID'])) {
                        $ops_with_prefix = $record['Genysis_data']['EMPLOYEE_ID'];
                        Log::info('EMPLOYEE_ID: ' . $ops_with_prefix);
                        $this->info('EMPLOYEE_ID: ' . $ops_with_prefix);

                        $prefixes = DB::table('account_prefixes')->pluck('prefix');
                        $prefixMatched = false;
                        $agent_prefix = null;
                        $agent_id = null;

                        foreach ($prefixes as $prefix) {
                            if (substr($ops_with_prefix, 0, strlen($prefix)) === $prefix) {
                                $prefixMatched = true;
                                $agent_prefix = $prefix;
                                break;
                            }
                        }

                        if ($prefixMatched) {
                            $agent_id = substr($ops_with_prefix, strlen($agent_prefix));
                        } else {
                            $agent_id = substr($ops_with_prefix, 2);
                            $agent_prefix = substr($ops_with_prefix, 0, 2);
                        }

                        $orgainzation_and_user_group = $this->getOrganizationAndUserGroupFromPrefix($agent_prefix);
                        $org_name = $orgainzation_and_user_group['organization'];
                        $user_group_name = $orgainzation_and_user_group['user_group'];

                        $orgAndUserGroupIds = $this->extractOrganizationIdAndUserGroupId($org_name, $user_group_name);
                        $org_id = $orgAndUserGroupIds['org_id'];
                        $user_group_id = $orgAndUserGroupIds['user_group_id'];

                        $interaction->organization_id = $org_id;
                        $interaction->user_group_id = $user_group_id;

                        // Find or create user
                        $user = User::firstWhere('agent_id', $agent_id);
                        if (!$user) {
                            $agent_data_from_headcount = $this->getAgentsDatafromHeadcount($agent_id);
                            if ($agent_data_from_headcount) {
                                $newUser = User::create([
                                    'full_name' => $agent_data_from_headcount['full_name'],
                                    'username' => $agent_data_from_headcount['username'],
                                    'email' => $agent_data_from_headcount['email'],
                                    'agent_id' => $agent_id,
                                    'organization_id' => $org_id,
                                    'password' => Hash::make('extensya@12345678'),
                                    'role' => 4,
                                    'terminated' => 0,
                                    'enabled' => 0,
                                    'web_access' => 0,
                                ]);

                                $newUser->parameters()->sync([2, 4, 6, 7, 19, 22, 25, 27]);
                                $newUser->permissions()->sync([1, 2, 4, 5, 8, 10]);
                                $newUser->save();

                                $interaction->user_id = $newUser?->id ?? 0;
                            }
                        } else {
                            $interaction->user_id = $user?->id ?? 0;
                        }

                        $interaction->save();
                    }
                } catch (\Exception $e) {
                    $this->error('Error occurred: ' . $e->getMessage());
                    Log::error('Error occurred: ' . $e->getMessage());
                    continue;
                }
            }


            $this->info('All records have been processed successfully.');

            Log::info('All records have been processed successfully.');

            DownloadCallsJob::dispatchSync($filesToDownload);

            if (!empty($missingRecords)) {
                // Transform missingRecords into URLs
                $missingRecords = array_map(function ($record) {
                    return 'https://oms.extwebonline.com/I-Log/interactions/' . $record['call_id'] . '_final.wav';
                }, $missingRecords);

                var_dump($missingRecords);
                DownloadCallsJob::dispatchSync($missingRecords);
            }
        } else {
            $this->error('Failed to fetch data from the API. Status: ' . $response->status());
            Log::error('Failed to fetch data from the API. Status: ' . $response->status());
        }

    }

    public function getAgentsDatafromHeadcount($agent_id)
    {
        $headcountApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/agentsFromHeadcount.php';

        // Send a GET request to the API and get the result
        $userData = Http::withoutVerifying()->timeout(60 * 6000)->get($headcountApiUrl, [
            'ops_id' => $agent_id,
        ])->json();

        // Check if any of the name fields are missing
        if (
            empty($userData['first_name']) ||
            empty($userData['second_name']) ||
            empty($userData['third_name']) ||
            empty($userData['last_name'])
        ) {
            return [
                'full_name' => 'Unknown User',
                'username' => 'Unknown User',
                'email' => $userData['email'] ?? '-', // Use "-" if email is not present
            ];
        }

        // Return the data if all fields are available
        return [
            'full_name' => $userData['first_name'] . ' ' . $userData['second_name'] . ' ' . $userData['third_name'] . ' ' . $userData['last_name'],
            'username' => $userData['first_name'] . ' ' . $userData['last_name'],
            'email' => $userData['email'] ?? '-', // Use "-" if email is not present
        ];
    }

    protected function getOrganizationAndUserGroupFromPrefix($prefix)
    {
        $prefix_data = AccountPrefix::where('prefix', $prefix)->first();
        $org = $prefix_data->account;
        $user_group = $prefix_data->user_group;

        return [
            'organization' => $org,
            'user_group' => "$org - $user_group",
        ];
    }

    protected function extractOrganizationIdAndUserGroupId($org_name, $user_group_name)
    {
        $org_id = Organization::where('name', 'like', "%$org_name%")->first()->id;
        $user_group_id = UserGroup::where('name', 'like', "%$user_group_name%")?->first()?->id;

        return [
            'org_id' => $org_id,
            'user_group_id' => $user_group_id,
        ];
    }
}




