<?php

namespace App\Http\Controllers\Reports;

use App\Exports\UserExport;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

class UsersReportController extends Controller
{
    public function index()
    {
        return view('reports.administration.users');
    }

    public function export()
    {
        // return Excel::download(new UserExport, 'Users-Report.pdf', \Maatwebsite\Excel\Excel::MPDF);
        return Excel::download(new UserExport, 'Users-Report.xlsx');
    }
}
