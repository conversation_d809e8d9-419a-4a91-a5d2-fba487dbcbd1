// Fonts
@import url("https://fonts.bunny.net/css?family=Nunito");

// Variables
@import "variables";

// Bootstrap
@import "bootstrap/scss/bootstrap";

// FontAwesome
@import "node_modules/@fortawesome/fontawesome-free/css/all.css";

body {
    font-family: "Poppins", sans-serif;
    background-color: #ffff;
}

$firstColor: #03a34d;
$secondColor: #c6d64e;
* {
    // font-family: 'Poppins', sans-serif;
    letter-spacing: 1px;
    box-sizing: border-box;
}

/* WebKit Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-thumb {
    background-color: #38798b4f;
    border-radius: 4px;
}

::-webkit-scrollbar-track {
    background-color: transparent;
}

label {
    font-size: 13px;
}

#clear:hover {
    background-color: transparent;
    color: $firstColor;
}

#apply:hover {
    // background-color: $secondColor !important;
    background-color: #029647 !important;
}

.page-link {
    background-color: $firstColor;
    // color: var(--bs-secondary-color) !important;
    color: white;
    font-size: smaller;
    box-shadow: none;
}

.page-link:hover {
    background-color: $secondColor !important;
    color: white;
}

.page-link.active,
.active > .page-link {
    background-color: $secondColor !important;
    color: white;
    border-color: $firstColor !important;
    box-shadow: none;
}

.page-link:active {
    box-shadow: none;
}

.previous,
.next {
    box-shadow: none;
}

div.table-responsive > div.dataTables_wrapper > div.row {
    margin-top: 3px;
    // border-top: solid 1px gray;
}

// hide the empty row by dataTables under the table header !
// .dataTables_scroll > .dataTables_scrollBody > table > thead > tr {
//     display: none;
// }

.dataTables_wrapper > .row:nth-of-type(3) {
    border-top: solid 1px rgb(175, 170, 170) !important;
    // padding-top: 0.7rem;
    text-wrap: nowrap;
}

div.dataTables_wrapper > div.row > div[class^="col-"]:nth-child(2) {
    display: flex;
    justify-content: flex-end;
    padding-bottom: 2px;
    padding-top: 5px;
}

#table_wrapper {
    border: solid 1px rgb(231, 221, 221);
    border-radius: 0.5rem;
    padding: 14px;
    padding-bottom: 0.5rem;
    background-color: white;
}

.table-hover > tbody > tr > * {
    background-color: white;
}

table.dataTable thead th {
    background-color: white !important;
}

div.dataTables_scrollBody > table > tbody tr > td {
    // padding: 0.1rem !important;
    padding: 0 1.5rem !important; //left and right
    padding-top: 0.5rem !important; //the bottom padding is from the #table_wrapper
    vertical-align: middle;
}

// user management
.dropdown-toggle::after {
    float: right;
    margin-top: 0.5rem;
    width: 0.6rem;
    height: 0.9rem;
    color: black;
}

#dropdownMenuButton1 {
    border-color: #adb5bd;
}

.btn-outline-secondary:hover {
    background-color: transparent;
    color: gray;
}

.sidenav {
    height: 100%;
    width: 4rem;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: white;
    overflow-x: hidden;
    padding-top: 16px;
}

.logo {
    width: 3rem;
    margin-bottom: 4.5rem;
}
.py-4{
    max-width: 99% !important;
 }
audio {
    width: 300px; /* Set a specific width */
    border: 1px solid #ccc; /* Add a border */
    padding: 10px; /* Add some padding */
    background-color: #f5f5f5; /* Set a background color */
    border-radius: 5px; /* Add rounded corners */
}

.custom-switch-input {
    transform: scale(1.1);
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: $firstColor !important;
    border-color: white !important;
}

// analytics page cards
.card-pop-out-effect {
    transition: transform 0.2s ease-in-out;
}

.card-pop-out-effect:hover {
    transform: scale(1.01);
    z-index: 99999999;
}

#dropDownList {
    border: 1px solid rgb(172, 172, 172);
    padding: 10px;
    border-radius: 10px;
    width: 100%;
    text-align: left;
    // background-color:#f8fafc;
    background-color: white;
}

#dropDownList:active,
#dropDownList:focus {
    border: solid 1px $firstColor;
}

#dropdownMenu {
    margin-top: 2%;
    transform: translate3d(0px, 0, 0px);
    width: 100%;
    border: 1px solid rgb(172, 172, 172);
    border-radius: 10px;
    padding: 0.2rem 0 0.2rem 0;
    // padding: 2% 2% 4% 0%;
}

#dropdownMenu li {
    cursor: pointer;
}

#dropdownMenu li :hover {
    background-color: #00a34e;
    color: white;
}

// the pagination text on the old composer
div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between
    > div:first-child {
    padding-right: 1rem;
}

// input {
//     border: solid 1px #b6b6b6 !important;
//     border-radius: 0.6rem !important;
//     // background-color: white !important;
//     padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
// }

thead {
    height: 3rem !important;
}

thead tr th {
    padding: 1rem !important;
}

.table-responsive {
    border-collapse: collapse !important;
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
    border: solid 1px #e7eaed;
}

.page-link[aria-label="« Previous"],
.page-link[aria-label="Next »"] {
    font-size: 1.2rem;
    padding: 0.1rem 0.9rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.page-item {
    margin: 0 0.25rem;
}

.page-link:hover {
    background-color: #f1f1f1;
    color: #0056b3;
}


.btn:not(.dropdown-toggle):not(.dropdown-toggle-action):hover {
    /* background: #f8f8f8 !important; */
    transform: scale(1.10) !important; /* Make the card slightly larger */
    box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3); /* Add a larger shadow */
}
 .sidenav > a:hover
 {
    transform: scale(1.25) !important; /* Make the card slightly larger */
    color:green;
 }

 .dropdown-item.active, .dropdown-item:active {
    background: #40798c !important;
    border-color: #40798c !important;
 }
