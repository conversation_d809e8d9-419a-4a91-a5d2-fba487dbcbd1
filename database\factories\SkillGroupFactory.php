<?php

namespace Database\Factories;

use App\Models\SkillGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SkillGroup>
 */
class SkillGroupFactory extends Factory
{

    protected $model = SkillGroup::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'acdid' => $this->faker->numberBetween([1000,1999]),
            'language' => $this->faker->randomElement(['Arabic', 'English']),
            'type' => $this->faker->randomElement(['Inbound', 'Outbound']),
            'description' => $this->faker->paragraph(1),
        ];
    }
}
