<div class="container-fluid mt-3 px-4">


    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto">
                <button
                    wire:target="export"
                    wire:click="export"
                    title="Export"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                    wire:loading.attr="disabled">

                    <i
                        wire:loading.remove
                        wire:target="export"
                        class="fas fa-file-excel text-white me-2"
                        style="font-size: 20px;"></i>

                    <span
                        wire:loading.class="spinner-border spinner-border-sm"
                        wire:target="export"
                        style="width: 1rem; height: 1rem;"
                        role="status"
                        aria-hidden="true"></span>

                    <span
                        wire:loading.remove
                        wire:target="export"
                        style="font-size: 17px;">Extract Excel</span>
                </button>
            </div>
            <i  id="exportButton"  style="display: none !important;" title="Export to Excel"></i>

            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button
                    data-bs-toggle="modal"
                    data-bs-target="#filterModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="filter">
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>

    <div class="parent-sections mx-3 ps-5">

        <div class="section-one">
            <div class="div-table rounded-2 shadow-sm mb-3">
                @if($reportType == "evaluation_report")
                    <table class="table table-hover table-striped" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                        <thead class="thead" style="font-size: .8rem">
                        <tr>
                            <th scope="col" style="padding: 11px !important;">Reference ID</th>
                            <th scope="col" style="padding: 11px !important;">Name</th>
                            <th scope="col" style="padding: 11px !important;">User Id</th>
                            <th scope="col" style="padding: 11px !important;">Date & Time</th>
                            <th scope="col" style="padding: 11px !important;">Source</th>
                            <th scope="col" style="padding: 11px !important;">Quality Percentage</th>
                            <th scope="col" style="padding: 11px !important;">Evaluation Duration</th>
                            <th scope="col" style="padding: 11px !important;">View</th>
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($report ?? [] as $data)
                        <tr>
                            <td class='text-muted py-3'>{{$data->referenceID }}</td>
                            <td class='text-muted py-3'>{{$data->name}}</td>
                            <td class='text-muted py-3'>{{$data->user->agent_id}}</td>
                            <td class='text-muted py-3'>{{$data->created_at}}</td>
                            <td class='text-muted py-3'>{{$data->Interaction?->call_type ?? '' }}</td>
                            <td class='text-muted py-3'>{{ intval($data->quality_percentage) }}%</td>
                            <td class='text-muted py-3'>{{$data->evaluation_duration ?? '00:00:00'}}</td>
                            <td class='text-muted py-3'>
                                <a href="{{ route('evaluation.reportAnswers', ['submit_id' => $data->id]) }}">
                                    <i class="fas fa-eye" style="font-size: 20px;color: #00a34e;" ></i>
                                </a>
                            </td>
                        </tr>
                        @empty
                            <tr>
                                <td colspan="11" class="text-muted text-center"> There is no data found</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>


                @elseif($reportType == "evaluation_avg_score_report")
                    <table class="table table-hover" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                        <thead class="thead" style="font-size: .8rem">
                        <tr>
                            <th scope="col">User ID</th>
                            <th scope="col">Name</th>
                            <th scope="col">Call Date & Time </th>
                            <th scope="col">Evaluation Date & Time</th>
                            <th scope="col">Interaction ID</th>
                            <th scope="col">Unique ID</th>
                            <th scope="col">Tenure</th>
                            <th scope="col">Evaluator Name</th>
                            <th scope="col">Quality Percentage</th>
                            <th scope="col">QA Comment </th>
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($report ?? [] as $data)
                        <tr>
                            <td class='text-muted py-3'>{{$data->user->agent_id}}</td>
                            <td class='text-muted py-3'>{{$data->name}}</td>
                            <td class='text-muted py-3'>{{$data->Interaction?->arrival_time}}</td>
                            <td class='text-muted py-3'>{{$data->created_at}}</td>
                            <td class="text-muted">
                                    @if ($data->Interaction?->call_type === 'Outbound')
                                        {{$data->Interaction?->called_id}}
                                    @elseif ($data->Interaction?->call_type === 'Inbound')
                                        {{$data->Interaction?->caller_id}}
                                    @else
                                        ''
                                    @endif
                            </td>
                            <td class='text-muted py-3'>{{$data->Interaction?->call_id }}</td>
                            <td class="text-muted"></td>
                            <td class='text-muted py-3'>{{$data->evaluator->full_name ?? '' }}</td>
                            <td class='text-muted py-3'>{{ intval($data->quality_percentage) }}%</td>
                            <td class='text-muted py-3'>{{ $data->commentEvaluation }}</td>

                        </tr>
                        @empty
                            <tr>
                                <td colspan="11" class="text-muted text-center"> There is no data found</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>

                @elseif($reportType == "evaluation_parameteres_report")

                        <table class="table table-hover" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                            <thead class="thead" style="font-size: .8rem">
                            <tr>
                                <th scope="col">Reference ID</th>
                                <th scope="col">Call ID</th>
                                <th scope="col">Name</th>
                                <th scope="col">User Id</th>
                                <th scope="col">Date & Time</th>
                                <th scope="col">Source</th>
                                <th scope="col">Quality Percentage</th>
                                <th scope="col">Evaluation Duration</th>
                                <th scope="col">Ai Score</th>
                                <th scope="col">Language</th>
                                <th scope="col">Account</th>
                                <th scope="col">Original call duration</th>
                                @if($evaluation_id)
                                    @foreach ($report as $data)
                                        @php
                                            $index = 0;
                                        @endphp

                                        @foreach ($data->submissionAnswers as $quastions)
                                            <th scope="col">
                                                <!-- Button trigger modal -->
                                                <button type="button" class="btn btn-light question-modal-trigger" data-bs-toggle="modal" data-bs-target="#questionModal{{ $index }}">
                                                    Q{{ $index + 1 }}
                                                </button>

                                                <!-- Modal -->
                                                <div class="modal fade" id="questionModal{{ $index }}" tabindex="-1" aria-labelledby="questionModalLabel{{ $index }}" aria-hidden="true">
                                                    <div class="modal-dialog modal-dialog-centered">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="questionModalLabel{{ $index }}" style="color:black">Question Details</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body" style="text-wrap: pretty;color:black">
                                                                {!! $quastions->question->question_name !!}
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </th>
                                            @php
                                                $index++;
                                            @endphp
                                        @endforeach

                                        @break
                                    @endforeach
                                @endif
                            </tr>
                            </thead>
                            <tbody>
                            @if($evaluation_id)
                                @forelse($report ?? [] as $data)

                                        @php
                                            $index = 0;
                                        @endphp
                                        <tr>
                                            <td class='text-muted py-3'>{{$data->referenceID }}</td>
                                            <td class='text-muted py-3'>{{$data->Interaction?->call_id ?? '' }}</td>
                                            <td class='text-muted py-3'>{{$data->name}}</td>
                                            <td class='text-muted py-3'>{{$data->user->agent_id}}</td>
                                            <td class='text-muted py-3'>{{$data->created_at}}</td>
                                            <td class='text-muted py-3'>{{$data->Interaction?->call_type ?? '' }}</td>
                                            <td class='text-muted py-3'>{{ intval($data->quality_percentage) }}%</td>
                                            <td class='text-muted py-3'>{{$data->evaluation_duration ?? '00:00:00'}}</td>
                                            <td class='text-muted py-3'>{{$data->Interaction?->ai_score ?? '-' }}</td>
                                            <td class='text-muted py-3'>{{$data->Interaction?->language ?? '-' }}</td>
                                            <td class='text-muted py-3'>{{$data->Interaction->organization->name ?? '-'}}</td>
                                            <td class='text-muted py-3'>{{$data->Interaction->call_duration ?? '-' }}</td>
                                            @foreach ($data->submissionAnswers as $quastions)
                                                    <td class='text-muted py-3'>

                                                        <div class="question-popup">
                                                            {{ $quastions->mark }}
                                                        </div>
                                                    </td>
                                                    @php
                                                        $index++;
                                                    @endphp

                                            @endforeach
                                        </tr>



                                @empty
                                    <tr>
                                        <td colspan="11" class="text-muted text-center"> There is no data found</td>
                                    </tr>
                                @endforelse
                            @else
                                <tr>
                                    <td colspan="11" class="text-muted text-center"> Please choose the filter</td>
                                </tr>
                            @endif
                            </tbody>
                        </table>

                @elseif($reportType == "evaluation_agent_avg_score_report")
                    <table class="table table-hover" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                        <thead class="thead" style="font-size: .8rem">
                        <tr>
                            <th scope="col">User ID</th>
                            <th scope="col">Name</th>
                            <th scope="col">Tenure</th>
                            <th scope="col">Number OF Evaluated Records</th>
                            <th scope="col">Quality Score</th>
                            <th scope="col">From The Date Range Selected</th>
                        </tr>
                        </thead>
                        <tbody>
                            @if($organizationSelected)
                                @php
                                    $grouped = $report->groupBy('user_id');
                                @endphp

                                @if($grouped->count() > 0)
                                    @foreach($grouped as $userId => $userReports)
                                        @php
                                            $totalRecords = $userReports->count();
                                            $averageQuality = $totalRecords > 0 ? intval($userReports->avg('quality_percentage')) : 'N/A';
                                            $firstRecord = $userReports->first();
                                        @endphp
                                        <tr>
                                            <td class="text-muted">
                                                {{ $firstRecord->user->agent_id }}
                                            </td>
                                            <td class="text-muted">
                                                {{ $firstRecord->name }}
                                            </td>
                                            <td class="text-muted"></td>
                                            <td class="text-muted">{{ $totalRecords }}</td>
                                            <td class="text-muted">{{ $averageQuality }}</td>
                                            <td class="text-muted"></td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="11" class="text-muted text-center">There is no data found</td>
                                    </tr>
                                @endif
                            @else
                                <tr>
                                    <td colspan="11" class="text-muted text-center">Please choose an account from the filter</td>
                                </tr>
                            @endif


                        </tbody>
                    </table>
                @elseif($reportType == "agent_wise_defect_report")
                    <table class="table table-hover" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                        <thead class="thead" style="font-size: .8rem">
                        <tr>
                            <th scope="col">User ID</th>
                            <th scope="col">Name</th>
                            <th scope="col">Tenure</th>
                            <th scope="col">Caller ID</th>
                            <th scope="col">Unique ID</th>
                            <th scope="col">Language</th>
                            <th scope="col">Number of Hold</th>
                            <th scope="col">Hold Duration</th>
                            <th scope="col">Call Duration</th>
                            <th scope="col">Date Of Call</th>
                            <th scope="col">QA Name</th>
                            <th scope="col">Date of Evalauation</th>
                            <th scope="col">Number of Critical Errors</th>
                            <th scope="col">Number of Non-critical Errors</th>
                            <th scope="col">Evalauation Score</th>
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($report ?? [] as $data)
                            <tr>
                                <td class='text-muted py-3'>{{$data->user->agent_id}}</td>
                                <td class='text-muted py-3'>{{$data->name}}</td>
                                <td class="text-muted"></td>
                                <td class='text-muted py-3'>{{$data->Interaction?->caller_id ?? '-'}}</td>
                                <td class='text-muted py-3'>{{$data->Interaction?->call_id }}</td>
                                <td class='text-muted py-3'>{{$data->Interaction?->language ?? '-'}}</td>
                                <td class='text-muted py-3'>{{$data->Interaction?->hold_count ?? '-'}}</td>
                                <td class='text-muted py-3'>{{$data->Interaction?->hold_duration ?? '-'}}</td>
                                <td class='text-muted py-3'>{{$data->Interaction?->call_duration ?? '-'}}</td>
                                <td class='text-muted py-3'>{{$data->Interaction?->arrival_time ?? '-'}}</td>
                                <td class='text-muted py-3'>{{$data->evaluator->full_name ?? '' }}</td>
                                <td class='text-muted py-3'>{{$data->created_at}}</td>
                                <td class="text-muted">
                                    {{ $data->submissionAnswers->filter(function ($answer) {
                                        return $answer->mark === 'Fatal Critical' || $answer->mark === 'Fatal Per Group';
                                    })->count() }}
                                </td>
                                <td class="text-muted">
                                    @php
                                        $countNoneCriticle = 0;
                                        foreach ($data->submissionAnswers as $submissionAnswer) {
                                            if($submissionAnswer->mark != 'Fatal Critical' && $submissionAnswer->mark != 'Fatal Per Group'){
                                                $data2 = $submissionAnswer->question->answers[0]->mark_and_weight;
                                                $data2 = json_decode($data2, true);
                                                $maxMarkItem = array_reduce($data2, function ($carry, $item) {
                                                    return ($carry['weight'] ?? 0) > $item['weight'] ? $carry : $item;
                                                });
                                                // echo $maxMarkItem['weight'].'-';
                                                if($maxMarkItem['weight'] != $submissionAnswer->mark){
                                                    $countNoneCriticle++;
                                                }
                                            }
                                        }
                                        echo $countNoneCriticle;
                                    @endphp
                                </td>
                                <td class='text-muted py-3'>{{ intval($data->quality_percentage) }}%</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="11" class="text-muted text-center"> There is no data found</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                @elseif($reportType == "account_wise_defect_report")

                        <table class="table table-hover" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                            <thead class="thead" style="font-size: .8rem">
                            <tr>
                                <th scope="col">Account</th>
                                <th scope="col">Evaluator </th>
                                <th scope="col">Source</th>
                                <th scope="col">Parameter Name</th>
                                <th scope="col">Count Of Deduction</th>
                                <th scope="col">AVG %</th>
                                <th scope="col">From The Date Range Selected</th>
                            </tr>
                            </thead>
                            <tbody>
                                @if($evaluation_id)
                                    @forelse($report ?? [] as $data)

                                        <tr>
                                            <td class='text-muted py-3'>{{ $evaluationSubmission->evaluation->evaluation_name }}</td>
                                            <td class='text-muted py-3'>{{ $evaluationSubmission->evaluator->full_name ?? '' }}</td>
                                            <td class='text-muted py-3'>{{ $evaluationSubmission->Interaction->call_type ?? '' }}</td>
                                            <td class='text-muted py-3'>{{ $data->question->question_name }}</td>
                                            <td class='text-muted py-3'>
                                                @php
                                                    $maxWeight = collect($data->question->answers)
                                                        ->flatMap(fn($answer) => array_column(json_decode($answer->mark_and_weight, true), 'weight'))
                                                        ->max();

                                                    $baseQuery = \App\Models\EvaluationSubmissionAnswer::where('evaluation_question_id', $data->question->id);

                                                    $applyFilters = function ($query) use ($dateFrom, $dateTo, $yearSelected, $monthSelected, $weekSelected) {
                                                        if ($dateFrom && $dateTo) {
                                                            $query->whereBetween('created_at', ["{$dateFrom} 00:00:00", "{$dateTo} 23:59:59"]);
                                                        }

                                                        if (isset($yearSelected)) {
                                                            $query->whereHas('submission', fn($q) => $q->where('year', $yearSelected));
                                                        }

                                                        if (isset($monthSelected)) {
                                                            $query->whereHas('submission', fn($q) => $q->where('month', $monthSelected));
                                                        }

                                                        if (isset($weekSelected)) {
                                                            $query->whereHas('submission', fn($q) => $q->where('week', $weekSelected));
                                                        }
                                                    };

                                                    $count = (clone $baseQuery)->where('mark', '<', $maxWeight);
                                                    $applyFilters($count);
                                                    $count = $count->count();

                                                    $count2 = clone $baseQuery;
                                                    $applyFilters($count2);
                                                    $count2 = $count2->count();
                                                @endphp

                                                {{ $count }}
                                            </td>
                                            <td class='text-muted py-3'>
                                                @php
                                                    $totalSubmissions = $count2;
                                                    $percentage = $totalSubmissions > 0 ? ($count / $totalSubmissions) * 100 : 0;
                                                @endphp
                                                {{ round($percentage) }}%
                                            </td>
                                            <td class='text-muted py-3'></td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-muted text-center">There is no data found</td>
                                        </tr>
                                    @endforelse
                                @else
                                    <tr>
                                        <td colspan="11" class="text-muted text-center"> Please choose the filter</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>

                @elseif($reportType == "qa_occupancy_report")
                    <table class="table table-hover" id='dataTable' style="margin-bottom: 0px;border-radius: 0px;">
                        <thead class="thead" style="font-size: .8rem">
                        <tr>
                            <th scope="col">Account</th>
                            <th scope="col">User ID</th>
                            <th scope="col">User Name</th>
                            <th scope="col">Evaluator Name</th>
                            <th scope="col">Source</th>
                            <th scope="col">Language</th>
                            <th scope="col">Caller ID</th>
                            <th scope="col">Call Id</th>
                            <th scope="col">Evaluation Time</th>
                            <th scope="col">Evaluation Date</th>
                            <th scope="col">Evaluation Duration</th>
                            <th scope="col">Interaction Score</th>
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($report ?? [] as $data)
                        <tr>
                            <td class='text-muted py-3'>{{$data->user->organization->name}}</td>
                            <td class='text-muted py-3'>{{$data->user->agent_id}}</td>
                            <td class='text-muted py-3'>{{$data->name}}</td>
                            <td class='text-muted py-3'>{{$data->evaluator->full_name ?? '' }}</td>
                            <td class='text-muted py-3'>{{$data->Interaction?->call_type ?? '-'}}</td>
                            <td class='text-muted py-3'>{{$data->Interaction?->language ?? '-'}}</td>
                            <td class='text-muted py-3'>{{$data->Interaction?->caller_id ?? '-'}}</td>
                            <td class='text-muted py-3'>{{$data->Interaction?->call_id ?? '-'}}</td>
                            <td class='text-muted py-3'>{{$data->created_at->format('h:i:s')}}</td>
                            <td class='text-muted py-3'>{{$data->created_at->format('yy/m/d')}}</td>
                            <td class='text-muted py-3'>{{$data->evaluation_duration ?? '00:00:00'}}</td>
                            <td class='text-muted py-3'>{{ intval($data->quality_percentage) }}%</td>
                        </tr>
                        @empty
                            <tr>
                                <td colspan="11" class="text-muted text-center"> There is no data found</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>

                @endif
            </div>
            @if($reportType && $reportType != "evaluation_parameteres_report" && $reportType != "account_wise_defect_report" && $reportType != "evaluation_agent_avg_score_report")
                {{-- {{ $report->links() }} --}}
                <div class="d-flex justify-content-between">
                    <!-- Dropdown for Number of Items per Page -->
                    <div>
                        <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                            <option value="10">10</option>
                            <option value="15" selected>15</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>Results Per Page</span>
                    </div>

                    <!-- Pagination Links -->
                    <div>
                        {{ $report->links(data: ['scrollTo' => false]) }}
                    </div>
                </div>
            @elseif(($reportType == "evaluation_parameteres_report") && $evaluation_id)
                {{-- {{ $report->links() }} --}}
                <div class="d-flex justify-content-between align-items-center">
                    <!-- Dropdown for Number of Items per Page -->
                    <div style="align-self: baseline !important">
                        <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                            <option value="10">10</option>
                            <option value="15" selected>15</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>Results Per Page</span>
                    </div>

                    <!-- Pagination Links -->
                    <div>
                        {{ $report->links(data: ['scrollTo' => false]) }}
                    </div>
                </div>
            @else
            @endif
        </div>

    </div>

    {{-- <div class="mx-3 ps-5">
        <div class="col-6">
            <a href="{{ route('evaluation.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div> --}}

    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">

                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">
                        <div class="main-filter row m-auto">
                            @if($reportType == "evaluation_parameteres_report" || $reportType == "account_wise_defect_report")
                                <div class="mb-4" style="margin-left: -10px;">
                                    <label>Evaluations Name:</label>
                                    <select class="form-select"  wire:model.live="evaluation_id" wire:click='getQuestions()'>
                                        <option value=""></option>
                                        @foreach ($evaluations as $eval)
                                            <option value="{{ $eval->id }}">{{ $eval->evaluation_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif
                            @if($reportType)
                                <button class="category" wire:click="getFilterType('date')">
                                    <input class="form-check-input" type="radio" name="flexRadio5" id="flexRadio5" {{ $date != null ? 'checked' : '' }}>
                                    <span class="spanText">Date</span>
                                </button>

                                <button class="category" wire:click="getFilterType('year')">
                                    <input class="form-check-input" type="radio" name="flexRadio1" id="flexRadio1" {{ $year != null ? 'checked' : '' }}>
                                    <div class="spanText">Year</div>
                                </button>

                                <button class="category" wire:click="getFilterType('month')">
                                    <input class="form-check-input" type="radio" name="flexRadio2" id="flexRadio2" {{ $month != null ? 'checked' : '' }}>
                                    <span class="spanText">Month</span>
                                </button>

                                <button class="category" wire:click="getFilterType('week')">
                                    <input class="form-check-input" type="radio" name="flexRadio5" id="flexRadio5" {{ $week != null ? 'checked' : '' }}>
                                    <span class="spanText">Week</span>
                                </button>

                                <button class="category category-exc2" wire:click="getFilterType('referenceID')">
                                    <input class="form-check-input" type="radio" name="flexRadio4" id="flexRadio5" {{ $referenceID != null ? 'checked' : '' }}>
                                    <span class="spanText">Reference ID</span>
                                </button>
                                <button class="category" wire:click="getFilterType('source')">
                                    <input class="form-check-input" type="radio" name="flexRadio6" id="flexRadio6" {{ $source != null ? 'checked' : '' }}>
                                    <span class="spanText">Source</span>
                                </button>
                                @if($role == 2 || $role == 5)
                                    <button class="category" wire:click="getFilterType('organization')">
                                        <input class="form-check-input" type="radio" name="flexRadio7" id="flexRadio7" {{ $organization != null ? 'checked' : '' }}>
                                        <span class="spanText">Account</span>
                                    </button>
                                    <button class="category" style="" wire:click="getFilterType('agentNameOpsId')">
                                        <input class="form-check-input" type="radio" name="flexRadio8" id="flexRadio8" {{ $agentNameOpsId != null ? 'checked' : '' }}>
                                        <span class="spanText">User Name Or User ID</span>
                                    </button>
                                @endif
                            @endif

                                @if($year)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>Year:</label>
                                        <select class="form-select"  wire:model.defer="yearSelected" >
                                            <option value="">Please Select</option>
                                            @forelse($yearOptions as $value)
                                                <option value="{{$value}}">{{$value}}</option>
                                            @empty
                                            @endforelse
                                        </select>
                                    </div>
                                @endif
                                @if($month)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>Month:</label>
                                        <select class="form-select"  wire:model.defer="monthSelected" >
                                            <option value="">Please Select</option>
                                            @forelse($monthOptions as $value)
                                                <option value="{{$value}}">{{$value}}</option>
                                            @empty
                                            @endforelse
                                        </select>
                                    </div>
                                @endif
                                @if($week)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>Week:</label>
                                        <select class="form-select"  wire:model.defer="weekSelected" >
                                            <option value="">Please Select</option>
                                            @forelse($weekOptions as $value)
                                                <option value="{{$value}}">{{$value}}</option>
                                            @empty
                                            @endforelse
                                        </select>
                                    </div>
                                @endif
                                @if($date)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>from:</label>
                                        <input type="date" class="form-control" wire:model.defer="dateFrom"/>
                                        <label>to:</label>
                                        <input type="date" class="form-control" wire:model.defer="dateTo"/>
                                    </div>
                                @endif
                                @if($referenceID)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>Reference ID:</label>
                                        <select class="form-select"  wire:model.defer="referenceIDSelected" >
                                            <option value="">Please Select</option>
                                            @forelse($referenceIDOptions as $value)
                                                <option value="{{$value}}">{{$value}}</option>
                                            @empty
                                            @endforelse
                                        </select>
                                    </div>
                                @endif
                                @if($source)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>Source:</label>
                                        <select class="form-select"  wire:model.defer="sourceSelected" >
                                            <option value="">Please Select</option>
                                            @forelse($sourceOptions as $value)
                                                <option value="{{$value}}">{{$value}}</option>
                                            @empty
                                            @endforelse
                                        </select>
                                    </div>
                                @endif
                                @if($organization)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>Account:</label>
                                        <select class="form-select"  wire:model.defer="organizationSelected" >
                                            <option value="">Please Select</option>
                                            @forelse($Organizations as $organization1)
                                                <option value="{{$organization1->id}}"> {{ $organization1->name == 'Ewa' ? 'Kease' : $organization1->name }}</option>
                                            @empty
                                            @endforelse
                                        </select>
                                    </div>
                                @endif
                                @if($agentNameOpsId)
                                    <div class="mb-3" style="margin-left: -10px;">
                                        <label>User Name Or User ID:</label>
                                        <input type="text" class="form-control"  wire:model.defer="agentNameOpsIdSelected" />
                                    </div>
                                @endif

                    </div>
                </div>
                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        wire:click="reloadComponent"
                        wire:loading.attr="disabled"
                        wire:target="reloadComponent">
                        <span wire:loading.remove wire:target="reloadComponent" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="reloadComponent"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="getData"
                        wire:loading.attr="disabled"
                        wire:target="getData">
                        <span wire:loading.remove wire:target="getData" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                    </button>
                </div>
        </div>
    </div>







</div>
