<?php

namespace App\Console\Commands;

use App\Jobs\DownloadChannelsJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class CallsAnalysis extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calls-analysis';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        $filesToDownload = [];
        $response = Http::withoutVerifying()->timeout(14400)->get('https://oms.extwebonline.com/I-log/fetch_calls_with_channels.php');
        if ($response->successful()) {
            $data = $response->json();
            foreach ($data as $key => $record) {
                $filesToDownload[$record['callid']]['file1'] = $record['filename'];
                $filesToDownload[$record['callid']]['file2'] = $record['filename2'];
            }
            DownloadChannelsJob::dispatch($filesToDownload);

        }

    }
}
