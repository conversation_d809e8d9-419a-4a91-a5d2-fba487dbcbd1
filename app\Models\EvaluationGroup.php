<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationGroup extends Model
{
    use HasFactory;

    public $guarded = [];

    public function evaluation()
    {
        return $this->belongsTo(Evaluation::class);
    }

    public function questions()
    {
        return $this->hasMany(EvaluationQuestion::class,'evaluation_group_id');
    }
}
