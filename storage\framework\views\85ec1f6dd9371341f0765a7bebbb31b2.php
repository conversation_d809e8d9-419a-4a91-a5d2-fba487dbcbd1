<div class="container-fluid mt-1 px-4">
     
    
    
    
    <div class="parent-sections mx-3 ps-lg-5">

        <div class="section-one col-12 p-3 pb-2 bg-white">
            <div class="filter row col-12">
                <!-- Left Section -->
                <div class="col-lg-6 col-12 d-flex flex-wrap">
                    <!-- Dropdown -->
                    <div class="col-12 col-md-6 col-lg-3 mb-3 mb-md-0">
                        <div class="dropdown">
                            <button
                                class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-style color rounded-2 d-flex flex-row justify-content-between"
                                type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="mt-1"><?php echo e($accountNameFilter ?? 'All'); ?></span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton" id="dropdownMenu"
                                style="max-height: 200px; overflow-y: auto; z-index:*********">
                                <li>
                                    <a class="dropdown-item" wire:click="getAllAccountsData()">
                                        All
                                    </a>
                                </li>
                                <hr class="m-0">
                                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <li>
                                        <a class="dropdown-item"
                                            wire:click="setAccountFilter('<?php echo e($account->id); ?>','<?php echo e($account->name); ?>')">
                                            <?php echo e($account->name); ?>

                                        </a>
                                    </li>
                                    <!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>
                                        <hr class="m-0">
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <li><a class="dropdown-item text-muted">No account found</a></li>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </ul>
                        </div>
                    </div>
                    <!-- Label -->
                    
                </div>
                <!-- Right Section -->
                <div class="col-lg-2 col-12"></div>
                <div
                    class="col-lg-4 col-12 d-flex flex-column flex-lg-row justify-content-end align-items-lg-center text-lg-end">
                    <!-- Date Dropdown -->
                    <div class="dropdown me-0 me-lg-3 mb-3 mb-lg-0 w-100 w-lg-auto">
                        <button
                            class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-style color rounded-2 w-100 d-flex flex-row justify-content-between"
                            type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                            
                            <span class="mt-1"><?php echo e($dateType ?? 'Custom'); ?></span>
                        </button>
                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu"
                            style="z-index: *********9999999">
                            <li><a class="dropdown-item" wire:click="getDate('Last 24 Hours')">Last 24 Hours</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" wire:click="getDate('Last 7 Days')">Last 7 Days</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" wire:click="getDate('Last 30 Days')">Last 30 Days</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" wire:click="getDate('Last 60 Days')">Last 60 Days</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" data-bs-toggle="modal"
                                    data-bs-target="#custom_time_modal">Custom</a></li>
                        </ul>
                    </div>
                    <!-- Search Box -->
                    <div class="d-flex align-items-center ps-2 rounded-2 bg-color w-100 w-lg-auto">
                        <i class="fas fa-search me-2 color"></i>
                        <input type="text"
                            wire:input="setAccountFilter('<?php echo e($accountIDFilter); ?>', '<?php echo e($accountNameFilter); ?>')"
                            class="rounded-2 form-control border-0 color shadow-none text-secondary"
                            wire:model='searchGroup' placeholder="Search Group">
                    </div>
                </div>
            </div>


            <div class="mt-3 row col-12">
                
                <div class="col-lg-12 table-responsive col-12 group-card" wire:ignore.self
                    style="min-height: 110px !important; height:400px !important; overflow-y: auto; scroll-behavior: smooth;
">
                    <table class="table table-borderless align-middle">
                        <thead class="sticky-top bg-white">
                            <tr class="text-center border-0">
                                <th class="ps-4 py-2" style="color: #40798c; font-size:0.9rem;width:20%">
                                    
                                    Group
                                </th>
                                <th class="py-2" style="color: #40798c; font-size:0.9rem;width:30%">Avg. Interactions
                                    Duration</th>
                                <th class="py-2" style="color: #40798c; font-size:0.9rem;width:20%">Total Interactions
                                </th>
                                <th class="py-2" style="color: #40798c; font-size:0.9rem;width:20%">Total Duration
                                </th>
                            </tr>
                        </thead>
                        <tbody class="text-center" style="">

                            <!--[if BLOCK]><![endif]--><?php if($accountIDFilter && !$searchGroup && $role != 4): ?>
                                <tr class="group text-center border-0" wire:click="getData('All')"
                                    <?php if($groupSelected == 'All'): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                    <td class="rounded-start ps-4 py-2"
                                        <?php if($groupSelected == 'All'): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div class="d-flex align-items-center justify-content-start"
                                            style="margin-left: 27%">
                                            <div class="avatar bg-purple text-white rounded-circle d-flex justify-content-center align-items-center text-center"
                                                style="width: 40px; height: 40px;">
                                                <strong>All</strong>
                                            </div>
                                            <div class="ms-3 text-center" style="white-space: nowrap;">
                                                <div><strong>All <?php echo e($accountNameFilter); ?> Groups</strong></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-2"
                                        <?php if($groupSelected == 'All'): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div>
                                            <strong><?php echo e($dataPage2['avg_interactions_duration'] ?? '00:00:00'); ?></strong>
                                        </div>
                                    </td>
                                    <td class="py-2"
                                        <?php if($groupSelected == 'All'): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div><strong><?php echo e($dataPage2['totalHandledCalls'] ?? '0'); ?></strong></div>
                                    </td>
                                    <td class="py-2"
                                        <?php if($groupSelected == 'All'): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div><strong><?php echo e($dataPage2['total_call_duration'] ?? '00:00:00'); ?></strong>
                                        </div>
                                    </td>
                                </tr>
                            <?php elseif(!$accountIDFilter): ?>
                                <tr class="group text-center border-0" wire:click="getData('All')"
                                    <?php if($groupSelected == 'All'): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                    <td class="rounded-start ps-4 py-2"
                                        style="background-color: rgb(243, 248, 255) !important;">
                                        <div class="d-flex align-items-center justify-content-start"
                                            style="margin-left: 27%">
                                            <div class="avatar bg-purple text-white rounded-circle d-flex justify-content-center align-items-center text-center"
                                                style="width: 40px; height: 40px;">
                                                <strong>All</strong>
                                            </div>
                                            <div class="ms-3 text-center" id="all_accounts">
                                                
                                                <div><strong>All Groups</strong></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-2" style="background-color: rgb(243, 248, 255) !important;">
                                        <div>
                                            <strong><?php echo e($dataPage['avg_interactions_duration'] ?? '00:00:00'); ?></strong>
                                        </div>
                                    </td>
                                    <td class="py-2" style="background-color: rgb(243, 248, 255) !important;">
                                        <div><strong><?php echo e($dataPage['total_interactions'] ?? '0'); ?></strong></div>
                                    </td>
                                    <td class="py-2" style="background-color: rgb(243, 248, 255) !important;">
                                        <div><strong><?php echo e($dataPage['total_call_duration'] ?? '00:00:00'); ?></strong>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $groupsAccount ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="main-row group border-0" id='row<?php echo e($group['group_id']); ?>' wire:ignore.self
                                    wire:click="getData(<?php echo e($group['group_id']); ?>)"
                                    <?php if($groupSelected == $group['group_id']): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                    <td class="rounded-start ps-4 py-2 text-center"
                                        <?php if($groupSelected == $group['group_id']): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div class="d-flex align-items-center justify-content-start"
                                            style="margin-left:27%">
                                            <div class="avatar bg-purple text-white rounded-circle d-flex justify-content-center align-items-center"
                                                style="width: 40px; height: 40px;">
                                                <strong><?php echo e(substr($group['group_name'], 0, 3)); ?></strong>
                                            </div>
                                            <div class="ms-3">
                                                <div><strong><?php echo e($group['group_name']); ?></strong></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-2"
                                        <?php if($groupSelected == $group['group_id']): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div><strong><?php echo e($group['avg_interactions_duration']); ?></strong></div>
                                    </td>
                                    <td class="py-2"
                                        <?php if($groupSelected == $group['group_id']): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div><strong><?php echo e($group['total_interactions']); ?></strong></div>
                                    </td>
                                    <td class="py-2"
                                        <?php if($groupSelected == $group['group_id']): ?> style="background-color: rgb(243, 248, 255) !important;" <?php endif; ?>>
                                        <div><strong><?php echo e($group['total_call_duration'] ?? '00:00:00'); ?></strong></div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tbody>
                    </table>
                </div>

            </div>

            <div class="paginationTable toggle-details me-4"
                style="text-align: right; cursor: pointer;
                    <?php if(empty($groupsAccount) || count($groupsAccount) <= 1): ?> display:none <?php endif; ?>">
                Expand <i class="ms-2 fa-solid fa-caret-down"></i>
            </div>





        </div>

        
        <div class="row align-items-stretch">
            
            <div class=" col-7 p-4 mt-0 pb-2 pt-0">
                <div id="cards" class="row">
                    <?php if (isset($component)) { $__componentOriginal67eedaf072b8a8ff29ff53fae1e8c41f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal67eedaf072b8a8ff29ff53fae1e8c41f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.swipe','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('swipe'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <div class="card rounded-3 motion-cards bg-white custom-card-col swiper-slide"
                            wire:click="getChartData('Avg Call Duration Card')" id='firstCard'
                            style="<?php echo e(in_array('Avg Call Duration Card', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                        <?php echo e($editFlag && in_array('Avg Call Duration Card', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : ''); ?> background-color: #faf3ff !important;'">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Avg Call Duration Card', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Avg Call Duration Card')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Avg Call Duration Card')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->


                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Average
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Avg Call Duration Card' ? 'color: #40798c ;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Avg. Call Duration:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['avg_interactions_duration'] ?? '00:00:00'); ?></strong>
                                        </h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart1"
                                        style="width: 180px !important;"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="card rounded-3 motion-cards bg-white shadow-sm custom-card-col swiper-slide"
                            wire:click="getChartData('Avg Hold Duration Card')"
                            style="<?php echo e(in_array('Avg Hold Duration Card', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                              <?php echo e($editFlag && in_array('Avg Hold Duration Card', $userSettings) ? 'opacity: .5; background-color: #fffbf3 !important;' : ''); ?> background-color: #fffbf3 !important;'">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Avg Hold Duration Card', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Avg Hold Duration Card')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Avg Hold Duration Card')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Average
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Avg Hold Duration Card' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Avg. Hold Time:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['avg_hold_time'] ?? '00:00:00'); ?></strong></h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart2"
                                        style="width: 180px !important;"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="card rounded-3 motion-cards bg-color1 shadow-sm custom-card-col swiper-slide"
                            wire:click="getChartData('Duration < 2 Minutes')"
                            style="<?php echo e(in_array('Duration < 2 Minutes', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                             <?php echo e($editFlag && in_array('Duration < 2 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : ''); ?> background:#f3f8ff !important;">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Duration < 2 Minutes', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Duration < 2 Minutes')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Duration < 2 Minutes')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Duration
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Duration < 2 Minutes' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Duration < 2 Minutes:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['short_call_duration_count'] ?? '00:00:00'); ?></strong>
                                        </h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart3"
                                        style="width: 180px !important;"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="card rounded-3 motion-cards bg-color2 shadow-sm custom-card-col swiper-slide "
                            wire:click="getChartData('Duration > 8 Minutes')"
                            style="<?php echo e(in_array('Duration > 8 Minutes', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                             <?php echo e($editFlag && in_array('Duration > 8 Minutes', $userSettings) ? 'opacity: .5; background-color: #faf3ff !important;' : ''); ?> background-color: #faf3ff !important">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Duration > 8 Minutes', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Duration > 8 Minutes')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Duration > 8 Minutes')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Duration
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Duration > 8 Minutes' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Duration > 8 Minutes:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['long_call_duration_count'] ?? '00:00:00'); ?></strong>
                                        </h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart4"
                                        style="width: 180px !important;"></canvas>

                                </div>
                            </div>
                        </div>

                        <div class="card rounded-3 motion-cards bg-color3 shadow-sm custom-card-col swiper-slide"
                            wire:click="getChartData('Hold Duration > 2 Minutes')"
                            style="<?php echo e(in_array('Hold Duration > 2 Minutes', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                             <?php echo e($editFlag && in_array('Hold Duration > 2 Minutes', $userSettings) ? 'opacity: .5; background-color: #fffbf3 !important;' : ''); ?> background-color: #fffbf3 !important;">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Hold Duration > 2 Minutes', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Hold Duration > 2 Minutes')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Hold Duration > 2 Minutes')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Count
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Hold Duration > 2 Minutes' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Hold > 2 Minutes:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['long_hold_duration_count'] ?? '00:00:00'); ?></strong>
                                        </h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart5"
                                        style="width: 180px !important;"></canvas>

                                </div>
                            </div>
                        </div>


                        
                        <div class="card rounded-3 motion-cards bg-color3 shadow-sm custom-card-col swiper-slide"
                            wire:click="getChartData('Total Hold Time')"
                            style="<?php echo e(in_array('Total Hold Time', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                              <?php echo e($editFlag && in_array('Total Hold Time', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : ''); ?>">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Total Hold Time', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Total Hold Time')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Total Hold Time')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Duration
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Total Hold Time' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Total Hold Time:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['total_hold_time'] ?? '00:00:00'); ?></strong></h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart12"
                                        style="width: 180px !important;"></canvas>
                                </div>
                            </div>
                        </div>




                        
                        <div class="card rounded-3 motion-cards bg-white shadow-sm custom-card-col swiper-slide"
                            wire:click="getChartData('Total Flaged Calls')"
                            style="<?php echo e(in_array('Total Flaged Calls', $userSettings) && !$editFlag ? 'display: none;' : ''); ?> <?php echo e($role == 4 ? 'display: none;' : ''); ?>

                              <?php echo e($editFlag && in_array('Total Flaged Calls', $userSettings) ? 'opacity: .5;background-color: #f3f3f3 !important;' : ''); ?> background-color: #f3f3f3 !important;">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Total Flaged Calls', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Total Flaged Calls')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Total Flaged Calls')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->


                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Count
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Total Flaged Calls' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Total Flagged Calls:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['qaFlagsCount'] ?? '0'); ?></strong></h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart7"
                                        style="width: 180px !important;"></canvas>
                                </div>
                            </div>
                        </div>


                        <div class="card rounded-3 motion-cards bg-white custom-card-col swiper-slide"
                            wire:click="getChartData('Avg Ring')"
                            style="<?php echo e(in_array('Avg Ring', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                                <?php echo e($editFlag && in_array('Avg Ring', $userSettings) ? 'opacity: .5; background-color: #fffbf3 !important;' : ''); ?> background-color: #fffbf3 !important;">
                            <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                                <!--[if BLOCK]><![endif]--><?php if(in_array('Avg Ring', $userSettings)): ?>
                                    <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                        wire:click="add('Avg Ring')">
                                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                        wire:click="remove('Avg Ring')">
                                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="d-flex flex-row justify-content-between">
                                <div class="text-muted">
                                    Average
                                </div>
                                <div class="text-muted">
                                    <i class="fas fa-circle"
                                        style="font-size:14px; <?php echo e($cardSelected == 'Avg Ring' ? 'color:  #40798c;' : 'color: #d9d9d9;'); ?>"></i>
                                </div>
                            </div>
                            <div class="color">
                                <strong>Avg. Ring:</strong>
                            </div>
                            <div class="d-flex flex-row justify-content-between mt-3">
                                <div class="mt-3">
                                    <div>
                                        <h3><strong><?php echo e($dataPage['averageRing'] ?? '0'); ?></strong></h3>
                                    </div>
                                    
                                </div>
                                <div wire:ignore>
                                    
                                    <canvas class="myChart4" id="chart8"
                                        style="width: 180px !important;"></canvas>
                                </div>
                            </div>
                        </div>

                        

                        
                        

                        

                        

                        


                        
                        


                        



                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal67eedaf072b8a8ff29ff53fae1e8c41f)): ?>
<?php $attributes = $__attributesOriginal67eedaf072b8a8ff29ff53fae1e8c41f; ?>
<?php unset($__attributesOriginal67eedaf072b8a8ff29ff53fae1e8c41f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal67eedaf072b8a8ff29ff53fae1e8c41f)): ?>
<?php $component = $__componentOriginal67eedaf072b8a8ff29ff53fae1e8c41f; ?>
<?php unset($__componentOriginal67eedaf072b8a8ff29ff53fae1e8c41f); ?>
<?php endif; ?>
                </div>
                <div id="statistics" class="row" style="max-height: 25.3rem; min-height:25.3rem">
                    <div class="card-2 rounded-3 bg-white pb-0   my-2  position-relative"
                        style="<?php echo e(in_array('Chart Line', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                      <?php echo e($editFlag && in_array('Chart Line', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : ''); ?>">
                        <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                            <!--[if BLOCK]><![endif]--><?php if(in_array('Chart Line', $userSettings)): ?>
                                <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                    wire:click="add('Chart Line')">
                                    <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                </div>
                            <?php else: ?>
                                <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                    wire:click="remove('Chart Line')">
                                    <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <div class="col-12  d-flex flex-row justify-content-between border-buttom">
                            <div class="col-8">
                                <div>
                                    <div class="text-muted">
                                        <h5>Statistics</h5>
                                    </div>
                                    <div class="color">
                                        <h4 class="mb-0"><strong><?php echo e($cardSelected ?? 'No Cards Selected'); ?></strong>
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div>
                                    <div class="color">
                                        <strong></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            
                            <canvas id="chartCanvas" class="w-100" style="height: 20.7rem"></canvas>
                        </div>
                    </div>
                </div>


            </div>
            
            <div class="col-5 p-4 mt-0 pb-2">
                <div class=" mt-0 pt-0  d-flex" style="height: auto">
                    
                    <div id="circles" class="col-6 me-3">
                        <div class="card rounded-3 bg-white " style="height: 95%">
                            <div class="row" style="height: 10%; margin-bottom:5%;">
                                <h6 class="color">Call Disconnection</h6>
                                <hr style="border: 0.113rem solid rgb(161, 160, 160);">
                            </div>
                            
                            
                            
                            
                            <!--[if BLOCK]><![endif]--><?php if(
                                (int) !empty($disconnected_by_agent) +
                                    (int) !empty($disconnected_by_customer) +
                                    (int) !empty($disconnected_by_system) ===
                                    1): ?>
                                <div class="row d-flex justify-content-center align-items-center"
                                    style="height: 70%; position: relative;">
                                <?php else: ?>
                                    <div class="row d-flex justify-content-start align-items-center"
                                        style="height: 70%; position: relative; margin-left:20%;">
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php
                                // Prepare the data
                                $disconnections = [
                                    ['percentage' => $disconnected_by_agent, 'label' => 'Agent', 'color' => '#FFC463'],
                                    [
                                        'percentage' => $disconnected_by_customer,
                                        'label' => 'Customer',
                                        'color' => '#83AEFF',
                                    ],
                                    [
                                        'percentage' => $disconnected_by_system,
                                        'label' => 'System',
                                        'color' => '#C577FF',
                                    ],
                                ];

                                // Sort the data by percentage descending
                                usort($disconnections, function ($a, $b) {
                                    return $b['percentage'] <=> $a['percentage'];
                                });

                                // Filter out zero values
                                $disconnections = array_filter($disconnections, fn($item) => $item['percentage'] > 0);
                            ?>


                            
                            <!--[if BLOCK]><![endif]--><?php if(count($disconnections) >= 1): ?>
                                
                                <div
                                    style="position: absolute;
                                               width: 150px;
                                               height: 150px;
                                               background-color: <?php echo e($disconnections[0]['color']); ?>;
                                               border-radius: 50%;
                                               z-index: 1;
                                               text-align: center;
                                               line-height: 150px;
                                               font-size: 1.2rem;
                                               color: white;">
                                    <span style="font-size: 1.2rem; font-weight: bolder;">
                                        <?php echo e($disconnections[0]['percentage']); ?>%
                                    </span>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <?php if(count($disconnections) == 0): ?>
                                
                                <div
                                    style="position: absolute;
                                               width: 150px;
                                               border: 1px solid white;
                                               height: 150px;
                                               background-color: rgb(179, 172, 172);
                                               border-radius: 50%;
                                               z-index: 1;
                                               text-align: center;
                                               line-height: 150px;
                                               font-size: 1.2rem;
                                               color: white;">
                                    <span style="font-size: 1.2rem; font-weight: bolder;">
                                        <?php echo e(0); ?>%
                                    </span>
                                </div>
                                <div
                                    style="position: absolute;
                                            border: 1px solid white;
                                           width: 100px;
                                           height: 100px;
                                           background-color: rgb(179, 172, 172);
                                           border-radius: 50%;
                                           z-index: 2;
                                           top: 10px;
                                           left: 100px;
                                           text-align: center;
                                           line-height: 100px;
                                           font-size: 1rem;
                                           color: white;">
                                    <span style="font-size: 1rem; font-weight: bolder;">
                                        <?php echo e(0); ?>%
                                    </span>
                                </div>
                            <?php endif; ?>

                            <?php if(count($disconnections) >= 2): ?>
                                
                                <div
                                    style="position: absolute;
                                               width: 100px;
                                               height: 100px;
                                               background-color: <?php echo e($disconnections[1]['color']); ?>;
                                               border-radius: 50%;
                                               z-index: 2;
                                               top: 10px;
                                               left: 100px;
                                               text-align: center;
                                               line-height: 100px;
                                               font-size: 1rem;
                                               color: white;">
                                    <span style="font-size: 1rem; font-weight: bolder;">
                                        <?php echo e($disconnections[1]['percentage']); ?>%
                                    </span>
                                </div>
                            <?php endif; ?>

                            <?php if(count($disconnections) >= 3): ?>
                                
                                <div
                                    style="position: absolute;
                                               width: 50px;
                                               height: 50px;
                                               background-color: <?php echo e($disconnections[2]['color']); ?>;
                                               border-radius: 50%;
                                               z-index: 3;
                                               top: 95px;
                                               left: 110px;
                                               text-align: center;
                                               line-height: 50px;
                                               font-size: 0.8rem;
                                               color: white;">
                                    <span style="font-size: 0.6rem; font-weight: bolder;">
                                        <?php echo e($disconnections[2]['percentage']); ?>%
                                    </span>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        
                        <!--[if BLOCK]><![endif]--><?php if(($disconnected_by_agent ?? 0) == 0 && ($disconnected_by_customer ?? 0) == 0 && ($disconnected_by_system ?? 0) == 0): ?>
                            <div class="row mb-1 m-auto d-flex" style="height: 20%;">
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <li style="display: flex; align-items: center; font-size: 0.7rem; color: gray;">
                                        <span
                                            style="width: 8px; height: 8px; background-color: grey; border-radius: 50%; margin-right: 8px;"></span>
                                        Disconnected By Agent
                                    </li>
                                    <li style="display: flex; align-items: center; font-size: 0.7rem; color: gray;">
                                        <span
                                            style="width: 8px; height: 8px; background-color: grey; border-radius: 50%; margin-right: 8px;"></span>
                                        Disconnected By Customer
                                    </li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <div class="row mb-1 m-auto d-flex" style="height: 20%">
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $disconnections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $disconnection): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li
                                            style="display: flex; align-items: center; font-size: 0.7rem; color: gray;">
                                            <span
                                                style="width: 8px; height: 8px; background-color: <?php echo e($disconnection['color']); ?>; border-radius: 50%; margin-right: 8px;"></span>
                                            Disconnected By <?php echo e($disconnection['label']); ?>

                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </ul>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>




                
                <div id="inbound-outbound" class="col-6">
                    <div class="card rounded-3 bg-white"
                        style="height:95%; <?php echo e(in_array('Chart Circle', $userSettings) && !$editFlag ? 'display: none;' : ''); ?>

                            <?php echo e($editFlag && in_array('Chart Circle', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : ''); ?>">
                        <!--[if BLOCK]><![endif]--><?php if($editFlag): ?>
                            <!--[if BLOCK]><![endif]--><?php if(in_array('Chart Circle', $userSettings)): ?>
                                <div class="p-1 circle-x-y" style="background: #a6ffd1;"
                                    wire:click="add('Chart Circle')">
                                    <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                </div>
                            <?php else: ?>
                                <div class="p-1 circle-x-y" style="background: #ffd7d8;"
                                    wire:click="remove('Chart Circle')">
                                    <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <div class="col-12  d-flex flex-row justify-content-between border-buttom"
                            style="padding-bottom: 0 !important;">
                            <div class="col-6">
                                <div>
                                    <div class="color">
                                        Inbound
                                    </div>
                                    <div class="color">
                                        Calls:
                                    </div>
                                    <div class="t">
                                        <h3 class="mb-0"><strong><?php echo e($dataPage['total_inbound'] ?? '0'); ?></strong>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 text-end">
                                <div>
                                    <div class="color">
                                        Outbound
                                    </div>
                                    <div class="color">
                                        Calls:
                                    </div>
                                    <div class="t">
                                        <h3 class="mb-0"><strong><?php echo e($dataPage['total_outbound'] ?? '0'); ?></strong>
                                        </h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="" style="position: relative;height: 243px;">
                            <canvas id="doughnutChart"
                                style="position: absolute;width: 117%;height: 88%;top: -45px;left: 14px;"></canvas>
                        </div>

                    </div>
                </div>
            </div>


            


            <div class=" " style="margin-top: 1.0% !important;">
                <div class="rounded-3 bg-white card-2 g-0 p-4"
                    style="padding-bottom: 2.2rem; padding-top: 2.2rem; width: 102%;">
                    <div class="row align-items-center">
                        <!-- Left Section -->
                        <div class="col-lg-6 col-12 d-flex align-items-start">
                            <img src="<?php echo e(asset('assets/SVG/assets-v2/80.svg')); ?>"
                                style="width: 3.5rem; background-color: #faf3ff; border-radius: 5rem;" alt="eval"
                                class="me-3">
                            <div>
                                <div class="text-muted mb-2 text-start">Total</div>
                                <div class="color">
                                    <strong>Evaluations</strong>
                                </div>
                            </div>
                            <h3 style="font-size: 2.5rem" class="ms-4 text-center w-100">
                                <strong><?php echo e($dataPage['countEvaluation'] ?? '0'); ?></strong>
                            </h3>
                        </div>

                        <!-- Right Section -->
                        <div
                            class="col-lg-6 col-12 mt-3 mt-lg-0 d-flex flex-column flex-lg-row align-items-center gap-3">
                            <div>
                                <div class="text-muted mb-2 text-start">Average</div>
                                <div class="color">
                                    <strong>Evaluations</strong>
                                </div>
                            </div>
                            <div class="progress-bar w-100">
                                
                                <div class="progress-fill text-center"
                                    style="width: <?php echo e(isset($dataPage['avgEvaluationScore']) && $dataPage['avgEvaluationScore'] >= 0.5 ? min(number_format($dataPage['avgEvaluationScore'], 2), 100) : '0'); ?>%;">
                                    <span
                                        class="
                                            <?php echo e(isset($dataPage['avgEvaluationScore']) && $dataPage['avgEvaluationScore'] > 15 ? 'text-white' : 'text-black'); ?>

                                            <?php echo e(!isset($dataPage['avgEvaluationScore']) || $dataPage['avgEvaluationScore'] < 0.5 ? 'ml-6vw' : (isset($dataPage['avgEvaluationScore']) && $dataPage['avgEvaluationScore'] < 15 ? 'ml-1-5rem' : '')); ?>">
                                        <?php echo e(isset($dataPage['avgEvaluationScore']) && $dataPage['avgEvaluationScore'] >= 0.5 ? round($dataPage['avgEvaluationScore']) : '0'); ?>%
                                    </span>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>



            

            <div class="mb-lg-2 " style="margin-top: 2.5% !important;">
                <div class="rounded-3 bg-white card-2 p-4"
                    style="padding-bottom: 2.2rem; padding-top: 2.2rem; width: 102%;">
                    <div class="row align-items-center">
                        <!-- Left Section -->
                        <div class="col-lg-6 col-12 d-flex align-items-start">
                            <div class="d-flex">
                                <img src="<?php echo e(asset('assets/SVG/assets-v2/81.svg')); ?>" style="width: 3rem;"
                                    alt="eval" class="d-block me-3">
                                <div>
                                    <div class="text-muted mb-2 text-start">Total</div>
                                    <div class="color text-nowrap">
                                        <strong>Supervisor Flags</strong>
                                    </div>
                                </div>
                            </div>
                            
                            <h3 style="font-size: 2.7rem" class="ms-0 text-center w-100">
                                <strong>
                                    <div id="counter2" data-target="<?php echo e($dataPage['qaFlagsCount'] ?? '0'); ?>"
                                        wire:ignore>0</div>
                                </strong>
                            </h3>
                        </div>

                        <!-- Right Section -->
                        <div
                            class="col-lg-6 col-12 mt-3 mt-lg-0 d-flex flex-column flex-lg-row align-items-center gap-3">
                            <div class="d-flex align-items-center">
                                
                                <img src="<?php echo e(asset('assets/SVG/assets-v2/chip ai.svg')); ?>"
                                    style="width: 3rem; background-color: #ddc9eb; border-radius: 5rem; padding: 0rem; align-self:baseline !important;"
                                    alt="eval" class="me-3">
                                <div>
                                    <div class="text-muted mb-2 text-start">Total</div>
                                    <div class="color text-nowrap">
                                        <strong>AI Flags</strong>
                                    </div>
                                </div>
                            </div>
                            <h3 style="font-size: 2.7rem" class="ms-3">
                                <strong>
                                    <div id="counter" data-target="0" wire:ignore>0</div>
                                </strong>
                            </h3>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    
    <div class="modal fade" id="custom_time_modal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="  :50vw !important">

                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3"
                            style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel"
                            style="font-size: 30px;">Custom Period</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 fw-bold">Date From <i
                                    class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="from" id="from" class="form-control"
                                style="border: solid 1px #b6b6b6" placeholder="Date From"
                                wire:model="dateFromFilter">
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['dateFromFilter'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger fs-6"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 fw-bold">Date To <i
                                    class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="to" id="to" class="form-control"
                                style="border: solid 1px #b6b6b6" placeholder="Date To" wire:model="dateToFilter">
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['dateToFilter'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger fs-6"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border: none;">
                    
                    <button id="closeCustomDate" type="button" class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal">
                        <span style="font-size: 17px;">Close</span>
                    </button>
                    <button class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="apply_custom_date" wire:loading.attr="disabled" wire:target="apply_custom_date">
                        <span wire:loading.remove wire:target="apply_custom_date" id="apply-custom-date"
                            style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="apply_custom_date"
                            role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>


</div>

<div wire:loading class="loading-overlay">
    <div class="spinner"></div>
</div>
<?php /**PATH C:\xampp\htdocs\i-sentiment\resources\views/livewire/analytics-new.blade.php ENDPATH**/ ?>