<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flag_filter_qa_flag', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flag_filter_id')->constrained()->onDelete('cascade');
            $table->foreignId('qa_flag_id')->constrained()->onDelete('cascade');
            $table->string('first_data');
            $table->string('condition');
            $table->string('second_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flag_filter_qa_flag');
    }
};
