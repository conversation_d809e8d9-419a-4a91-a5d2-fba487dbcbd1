<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transcription_classifications', function (Blueprint $table) {
            $table->id();
            $table->string('score');
            $table->string('language');
            $table->string('classification');
            $table->string('call_id');
            $table->longText('text')->nullable();
            $table->integer('calls_transcription_id');
            $table->foreign('calls_transcription_id')
                ->references('id')->on('calls_transcription')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transcription_classifications');
    }
};
