{"__meta": {"id": "X2e073c1e1d5b8a81333752f1233c38ae", "datetime": "2025-07-22 15:51:02", "utime": **********.759688, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.067795, "end": **********.759738, "duration": 0.6919429302215576, "duration_str": "692ms", "measures": [{"label": "Booting", "start": **********.067795, "relative_start": 0, "end": **********.541427, "relative_end": **********.541427, "duration": 0.4736318588256836, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.541456, "relative_start": 0.473660945892334, "end": **********.759741, "relative_end": 3.0994415283203125e-06, "duration": 0.21828508377075195, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29038224, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.analytics-new", "param_count": null, "params": [], "start": **********.739333, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/analytics-new.blade.phplivewire.analytics-new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Fanalytics-new.blade.php&line=1", "ajax": false, "filename": "analytics-new.blade.php", "line": "?"}}, {"name": "components.swipe", "param_count": null, "params": [], "start": **********.748137, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/components/swipe.blade.phpcomponents.swipe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Fcomponents%2Fswipe.blade.php&line=1", "ajax": false, "filename": "swipe.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.01385, "accumulated_duration_str": "13.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.610719, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "ilog", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 36 limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.6152809, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ilog", "start_percent": 0, "width_percent": 24.26}, {"sql": "select * from `organizations` where `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2212}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.662096, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2212", "source": "app/Livewire/AnalyticsNew.php:2212", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2212", "ajax": false, "filename": "AnalyticsNew.php", "line": "2212"}, "connection": "ilog", "start_percent": 24.26, "width_percent": 3.538}, {"sql": "select * from `user_groups` where `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2213}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.66717, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2213", "source": "app/Livewire/AnalyticsNew.php:2213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2213", "ajax": false, "filename": "AnalyticsNew.php", "line": "2213"}, "connection": "ilog", "start_percent": 27.798, "width_percent": 4.116}, {"sql": "select * from `organizations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2214}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.673712, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2214", "source": "app/Livewire/AnalyticsNew.php:2214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2214", "ajax": false, "filename": "AnalyticsNew.php", "line": "2214"}, "connection": "ilog", "start_percent": 31.913, "width_percent": 7.87}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2220}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.684847, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2220", "source": "app/Livewire/AnalyticsNew.php:2220", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2220", "ajax": false, "filename": "AnalyticsNew.php", "line": "2220"}, "connection": "ilog", "start_percent": 39.783, "width_percent": 9.964}, {"sql": "SELECT DATE_FORMAT(arrival_time, \"%H:00\") AS receiving_hour,\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,\nCOUNT(call_id) AS total_calls\nFROM interactions\nWHERE arrival_time BETWEEN '2025-07-21 15:50:59' AND '2025-07-22 15:50:59'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)\nGROUP BY receiving_hour\nORDER BY receiving_hour", "type": "query", "params": [], "bindings": ["2025-07-21 15:50:59", "2025-07-22 15:50:59", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2236}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.697321, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2236", "source": "app/Livewire/AnalyticsNew.php:2236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2236", "ajax": false, "filename": "AnalyticsNew.php", "line": "2236"}, "connection": "ilog", "start_percent": 49.747, "width_percent": 24.332}, {"sql": "select\nCOUNT(*) as total_calls,\nSUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,\nSUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,\nSUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections\nfrom `interactions` where `arrival_time` between '2025-07-21 15:50:59' and '2025-07-22 15:50:59' limit 1", "type": "query", "params": [], "bindings": ["2025-07-21 15:50:59", "2025-07-22 15:50:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3372}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.709973, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:3372", "source": "app/Livewire/AnalyticsNew.php:3372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=3372", "ajax": false, "filename": "AnalyticsNew.php", "line": "3372"}, "connection": "ilog", "start_percent": 74.079, "width_percent": 18.773}, {"sql": "select * from `organizations` where `id` in (4, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50)", "type": "query", "params": [], "bindings": ["4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 124}, {"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3397}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.717716, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:124", "source": "app/Livewire/AnalyticsNew.php:124", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=124", "ajax": false, "filename": "AnalyticsNew.php", "line": "124"}, "connection": "ilog", "start_percent": 92.852, "width_percent": 7.148}]}, "models": {"data": {"App\\Models\\Organization": {"value": 151, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Interaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FInteraction.php&line=1", "ajax": false, "filename": "Interaction.php", "line": "?"}}}, "count": 153, "is_counter": true}, "livewire": {"data": {"analytics-new #Sy3JOG7efAGsEoHTB7GH": "array:4 [\n  \"data\" => array:23 [\n    \"totalCallDurationAllGroups\" => null\n    \"selected_group\" => null\n    \"disconnected_by_customer\" => 0\n    \"disconnected_by_agent\" => 0\n    \"disconnected_by_system\" => 0\n    \"accountIDFilter\" => null\n    \"accountNameFilter\" => null\n    \"dateFrom\" => Carbon\\Carbon @********** {#758\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000002f60000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-21 15:50:59.0 +03:00\n    }\n    \"dateTo\" => Carbon\\Carbon @********** {#762\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000002fa0000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-22 15:50:59.0 +03:00\n    }\n    \"dateFromFilter\" => null\n    \"dateToFilter\" => null\n    \"dateType\" => \"Last 24 Hours\"\n    \"groupsAccount\" => null\n    \"dataPage\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => null\n      \"avg_hold_time\" => null\n      \"total_hold_time\" => null\n      \"total_interactions\" => 0\n      \"short_call_duration_count\" => 0\n      \"long_call_duration_count\" => 0\n      \"long_hold_duration_count\" => 0\n      \"total_call_duration\" => null\n      \"total_outbound\" => 0\n      \"total_inbound\" => 0\n      \"countEvaluation\" => 0\n      \"avgEvaluationScore\" => null\n      \"totalRing\" => null\n      \"averageRing\" => 0\n      \"totalHandledCalls\" => 0\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 0\n    ]\n    \"dataPage2\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => null\n      \"avg_hold_time\" => null\n      \"total_hold_time\" => null\n      \"total_interactions\" => 0\n      \"short_call_duration_count\" => 0\n      \"long_call_duration_count\" => 0\n      \"long_hold_duration_count\" => 0\n      \"total_call_duration\" => null\n      \"total_outbound\" => 0\n      \"total_inbound\" => 0\n      \"countEvaluation\" => 0\n      \"avgEvaluationScore\" => null\n      \"totalRing\" => null\n      \"averageRing\" => 0\n      \"totalHandledCalls\" => 0\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 0\n    ]\n    \"searchGroup\" => null\n    \"groupSelected\" => null\n    \"editFlag\" => false\n    \"userSettings\" => []\n    \"cardSelected\" => \"Avg Call Duration Card\"\n    \"role\" => 2\n    \"page\" => \"pageOne\"\n    \"queryGrouFormat\" => \"%H:00\"\n  ]\n  \"name\" => \"analytics-new\"\n  \"component\" => \"App\\Livewire\\AnalyticsNew\"\n  \"id\" => \"Sy3JOG7efAGsEoHTB7GH\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/analytics-new\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "36", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753187629\n]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f739abf-360f-4c35-b728-17ba1b63e427\" target=\"_blank\">View in Telescope</a>", "path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-164380305 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-164380305\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1267280650 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1267280650\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-22022208 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1725 characters\">{&quot;data&quot;:{&quot;totalCallDurationAllGroups&quot;:null,&quot;selected_group&quot;:null,&quot;disconnected_by_customer&quot;:0,&quot;disconnected_by_agent&quot;:0,&quot;disconnected_by_system&quot;:0,&quot;accountIDFilter&quot;:null,&quot;accountNameFilter&quot;:null,&quot;dateFrom&quot;:[&quot;2025-07-21T15:50:59+03:00&quot;,{&quot;type&quot;:&quot;carbon&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;dateTo&quot;:[&quot;2025-07-22T15:50:59+03:00&quot;,{&quot;type&quot;:&quot;carbon&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;dateFromFilter&quot;:null,&quot;dateToFilter&quot;:null,&quot;dateType&quot;:&quot;Last 24 Hours&quot;,&quot;groupsAccount&quot;:null,&quot;dataPage&quot;:[{&quot;group_id&quot;:&quot;All&quot;,&quot;group_name&quot;:&quot;All&quot;,&quot;avg_interactions_duration&quot;:null,&quot;avg_hold_time&quot;:null,&quot;total_hold_time&quot;:null,&quot;total_interactions&quot;:0,&quot;short_call_duration_count&quot;:0,&quot;long_call_duration_count&quot;:0,&quot;long_hold_duration_count&quot;:0,&quot;total_call_duration&quot;:null,&quot;total_outbound&quot;:0,&quot;total_inbound&quot;:0,&quot;countEvaluation&quot;:0,&quot;avgEvaluationScore&quot;:null,&quot;totalRing&quot;:null,&quot;averageRing&quot;:0,&quot;totalHandledCalls&quot;:0,&quot;qaFlagsCount&quot;:0,&quot;aiFlagsCount&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],&quot;dataPage2&quot;:[{&quot;group_id&quot;:&quot;All&quot;,&quot;group_name&quot;:&quot;All&quot;,&quot;avg_interactions_duration&quot;:null,&quot;avg_hold_time&quot;:null,&quot;total_hold_time&quot;:null,&quot;total_interactions&quot;:0,&quot;short_call_duration_count&quot;:0,&quot;long_call_duration_count&quot;:0,&quot;long_hold_duration_count&quot;:0,&quot;total_call_duration&quot;:null,&quot;total_outbound&quot;:0,&quot;total_inbound&quot;:0,&quot;countEvaluation&quot;:0,&quot;avgEvaluationScore&quot;:null,&quot;totalRing&quot;:null,&quot;averageRing&quot;:0,&quot;totalHandledCalls&quot;:0,&quot;qaFlagsCount&quot;:0,&quot;aiFlagsCount&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchGroup&quot;:null,&quot;groupSelected&quot;:null,&quot;editFlag&quot;:false,&quot;userSettings&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;cardSelected&quot;:null,&quot;role&quot;:2,&quot;page&quot;:&quot;pageOne&quot;,&quot;queryGrouFormat&quot;:&quot;%H:00&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;Sy3JOG7efAGsEoHTB7GH&quot;,&quot;name&quot;:&quot;analytics-new&quot;,&quot;path&quot;:&quot;analytics-new&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;66a6904e5bb3c93d0891333edf72b673b06d4edfd47003d5a2f239dbc69e6c3e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">callBackendMethod</span>\"\n            <span class=sf-dump-index>1</span> => []\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">getChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Avg Call Duration Card</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22022208\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-691364108 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2176</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1253 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndORVZmZm1CQWx1OVVtcUxhcW9Wbnc9PSIsInZhbHVlIjoiNEJpVnZuTkZPUmgxdk1ndVNLeXltN3hiL3lRMkpmckV4Y3ZFczI4Qm0xaXM0eldYak1IU1NYQitGUlhyVUFsODNvTWVWN3NxV2p2bEpkbEc0R1NVWDF2Q0d5dTgrQTgyQWZTNFBMQTFqY25YU1F2UldyNDhxaUNFZ1FiSSthUWpaakExbUJPM0h2cDlyMHI3YU1BaC82d09tYlRzS29EbFpwRTNYVU5GbGY0clN1bTFBTlQzekk0Wi96RUR5aUdwZWtIRFdGaENSYjBDQjlxNnlNOFdKeFg5dXZWQ0twbGNSMkRGcVFGREJ6QT0iLCJtYWMiOiJjYWYxMjVmN2NiZGM3YzJlMDVmYWQ1NjI3YzdjZGQxZmE0MmYyMTVmZDQ5MGY2NTA2NDU3ODc1NThiNTAwYWFmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9rRmZGS01kbW9JUSttU2tqRTU2N2c9PSIsInZhbHVlIjoidDRpTm9tT09uT1BlNW9XWURObzRJbzdjOHFjWnNlYkFlcXRrMjhtcExnWm0wM2NUbjhYMFpxZ2RxaUdmbjRQZ1F5cVhIVndhbS9BdVdoVzdVZHZ6OC9MNVE2UkEyR0YrL2JjZnE0TTYrYjUxREIyRld0MDBEZUQwbzF5RG5UWjIiLCJtYWMiOiI2MDdlOTdjMTI2MTgzMTJiMTk5YWEwODI2NzliMzY2ZTM4NWEzZjI4MGYxMzRjZGY0ZTYyZWM4MzhjOWZjOWM1IiwidGFnIjoiIn0%3D; i_log_session=eyJpdiI6IjFFSERROERMZEVmcmZlYmpqQjRLVVE9PSIsInZhbHVlIjoiS1padHRHTVNzT1puZmsvLzF5b3h3cURKVGlVK0xsVjZHK3NEMW9uY1V3eVM0STNaNUpYeE9PcnNPWUdyS09nRjRvMjhQRlh1bXhSM3JvM04yaDJUSVF3Nm1NWUtFcWlIRldreEMySWVoeFpXRlcvY0FkcnU1S2gyTVNuVUhrNW0iLCJtYWMiOiI4Yzc3MWNlYmMzYWNlNzVkNTcxMjM0N2ZkNDRlMmI3MmU4NTdhMDcwMTZiOTBiYmQzNTYyODFkZGI1OTkwMWYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691364108\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-673836511 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">36|5ib4AQhtLHzATEGHqeW0FyHXea5XYBhkXcIWMbiaY723twUGMepP4FnyoU3L|$2y$12$sKmCpsW.aT14AEFPyOgHeO2lhV7BRWjK7d1ywUnOncv84uKprV4di</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>i_log_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3Bna7bWbMH5plZLa1koljiLCJBixIU0hg7L8yoS8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673836511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1375505274 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 12:51:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6Ijd4OHBsekZXSy8wTEs0N2ZVbzlvSHc9PSIsInZhbHVlIjoiWlpRZFFxUDNFcFVJcmsxazVDQ0pyd2pQclNOci9jYVNDV1U1MXdtNGhnU2kreURyZXBNN25XT2JGbkI3aG1wcVZiNkV0OTBkVmJ5bWdHMmZaNzdZYTJHOXd3eWxHejFPZUlZT0Qwajc4aXFTc3hTL01HZFRxbVVjMkhVN3hCU28iLCJtYWMiOiJlNTc2YjNkZGEzOTk0YmM0Yjk0ZTdhMDliYTBlMGE2MzNmMjVlYTVjNGM3NzQwZTk1NmIxYjk4YzYxMTg1NjM4IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:51:02 GMT; Max-Age=21600; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">i_log_session=eyJpdiI6IjZ5WGJOMlZ4aFdRSHplbWk0Rlk3WWc9PSIsInZhbHVlIjoidnhxcFI5WDgzblN0NUNGVFpFbTMrOW9EVTFTcDNsYUo0Wi9zNlFUNGVjaW1WT1VkWENzV0ZTY0xsdUlXWm9oRXJIU0VFdkVFcHIxeDhvUm5sVU1xeXNER3ZQVmVRV2ZxVm14cldEYlBMcWZkUFRLV2NuR3V4SU9BY01uQTA5VlciLCJtYWMiOiI5MDkyMDUxZTAzMmY5YWE2N2EyMTBkOGJhNTk4YTc5ZGE4ZmFmNTkyNmZiNWFlMmI0M2Y3YWY2NGIzOTJhY2I5IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:51:02 GMT; Max-Age=21600; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijd4OHBsekZXSy8wTEs0N2ZVbzlvSHc9PSIsInZhbHVlIjoiWlpRZFFxUDNFcFVJcmsxazVDQ0pyd2pQclNOci9jYVNDV1U1MXdtNGhnU2kreURyZXBNN25XT2JGbkI3aG1wcVZiNkV0OTBkVmJ5bWdHMmZaNzdZYTJHOXd3eWxHejFPZUlZT0Qwajc4aXFTc3hTL01HZFRxbVVjMkhVN3hCU28iLCJtYWMiOiJlNTc2YjNkZGEzOTk0YmM0Yjk0ZTdhMDliYTBlMGE2MzNmMjVlYTVjNGM3NzQwZTk1NmIxYjk4YzYxMTg1NjM4IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:51:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">i_log_session=eyJpdiI6IjZ5WGJOMlZ4aFdRSHplbWk0Rlk3WWc9PSIsInZhbHVlIjoidnhxcFI5WDgzblN0NUNGVFpFbTMrOW9EVTFTcDNsYUo0Wi9zNlFUNGVjaW1WT1VkWENzV0ZTY0xsdUlXWm9oRXJIU0VFdkVFcHIxeDhvUm5sVU1xeXNER3ZQVmVRV2ZxVm14cldEYlBMcWZkUFRLV2NuR3V4SU9BY01uQTA5VlciLCJtYWMiOiI5MDkyMDUxZTAzMmY5YWE2N2EyMTBkOGJhNTk4YTc5ZGE4ZmFmNTkyNmZiNWFlMmI0M2Y3YWY2NGIzOTJhY2I5IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:51:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375505274\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-521841877 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>36</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753187629</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521841877\", {\"maxDepth\":0})</script>\n"}}