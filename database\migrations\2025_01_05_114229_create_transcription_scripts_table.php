<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transcription_scripts', function (Blueprint $table) {
            $table->id();
            $table->longText('input_text');
            $table->longText('best_script');
            $table->string('best_ratio');
            $table->json('scripts');
            $table->longText('call_id');
            $table->integer('calls_transcription_id');
            $table->foreign('calls_transcription_id')
                ->references('id')->on('calls_transcription')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transcription_scripts');
    }
};
