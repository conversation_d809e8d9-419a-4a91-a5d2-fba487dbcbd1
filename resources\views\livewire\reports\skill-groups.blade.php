<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto">
                <button
                    wire:target="export"
                    wire:click="export"
                    title="Export"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                    wire:loading.attr="disabled">

                    <i
                        wire:loading.remove
                        wire:target="export"
                        class="fas fa-file-excel text-white me-2"
                        style="font-size: 20px;"></i>

                    <span
                        wire:loading.class="spinner-border spinner-border-sm"
                        wire:target="export"
                        style="width: 1rem; height: 1rem;"
                        role="status"
                        aria-hidden="true"></span>

                    <span
                        wire:loading.remove
                        wire:target="export"
                        style="font-size: 17px;">Extract Excel</span>
                </button>
            </div>
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button
                    data-bs-toggle="modal"
                    data-bs-target="#filterModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    {{-- <i
                        class="fas fa-filter text-white me-2"
                        style="font-size: 20px;"></i> --}}
                        <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon" >
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>

        <div class="parent-sections mx-3 ps-5">

            <div class="section-one">
                    <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">
                    <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                        <thead id="thead" class="text-muted thead" style="font-size: 0.7rem">
                            <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                <th scope="col" class="text-center align-middle">ID</th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('name')">
                                    Name
                                    @if ($sortBy !== 'name')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>

                                <th scope="col" class="text-center align-middle">ACDID</th>

                                <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('type')">
                                    Type
                                    @if ($sortBy !== 'acdid')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('language')">
                                    Language
                                    @if ($sortBy !== 'language')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                                    Created At
                                    @if ($sortBy !== 'created_at')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                            </tr>
                        </thead>
                        <tbody class="" style="font-size:0.8rem" id="tbody">

                            @forelse($skillGroups as $group)
                                <tr class="align-middle">
                                    <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                    <td class="text-muted text-center py-3 align-middle"> {{ $group->name }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $group->acdid }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">{{ Str::title($group->type) }}</td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Str::title($group->language) }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Carbon::parse($group->created_at)->format('Y-m-d h:i A') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="18" class="text-muted text-center bg-white"> No groups found</td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>

                {{ $skillGroups->links(data: ['scrollTo' => false]) }}
            </div>
        </div>


    {{-- <div class="mx-3 ps-5">
        <div class="col-6">
            <a href="{{ route('admin.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div> --}}

    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                {{-- <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i> --}}
                                <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">

                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">
                        <div class="col-md-12">
                            <form class="row g-2 mb-3">
                                <div class="col-md-6 col-12">
                                    <label for="call id" class="mb-2 ">Name <i class="fa-solid fa-people-roof fa-lg" style="color: white"></i></label>
                                    <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="text" class="form-control py-2" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Skill Group Name" wire:model='filter_name'>
                                </div>
                                <div class="col-md-6 col-12">
                                    <label for="call id" class="mb-2 ">ACDID <i class="fa-regular fa-address-card fa-lg" style="color: white"></i></label>
                                    <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="text" class="form-control py-2" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="ACDID #" wire:model='filter_acdid'>
                                </div>
                                <div class="col-md-6 col-12">
                                    <label for="call id" class="mb-2 ">Type <i class="fa-solid fa-user-group fa-lg" style="color: white"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_type == null ? 'All' : Str::title($filter_type) }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterType('All')">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterType('inbound')">Inbound</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterType('outbound')">Outbound</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-12">
                                    <label for="call id" class="mb-2 ">Language <i class="fa-solid fa-user-group fa-lg" style="color: white"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_language == null ? 'All' : Str::title($filter_language) }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterLang('All')">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterLang('arabic')">Arabic</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterLang('english')">English</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        wire:click="clear"
                        wire:loading.attr="disabled"
                        wire:target="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="getData"
                        wire:loading.attr="disabled"
                        wire:target="getData">
                        <span wire:loading.remove wire:target="getData" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                    </button>
                </div>
        </div>
    </div>
</div>
