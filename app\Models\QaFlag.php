<?php

namespace App\Models;

use App\Models\Interaction;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class QaFlag extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table = 'qa_flags';

    // each flag belongs to an org 
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    // each flag has many forms and vice versa 
    public function evaluationForms()
    {
        return $this->belongsToMany(Evaluation::class, 'evaluation_qa_flag', 'qa_flag_id', 'evaluation_id');
    }

    // each flag has many filters and vice versa 
    // public function filters()
    // {
    //     return $this->belongsToMany(FlagFilter::class)->withPivot('first_data', 'condition', 'second_data');
    // }
    public function filters()
    {
        return $this->hasMany(FlagFilter::class, 'qa_flag_id');
    }

    public function interactions()
    {
        return $this->belongsToMany(Interaction::class);
    }
}
