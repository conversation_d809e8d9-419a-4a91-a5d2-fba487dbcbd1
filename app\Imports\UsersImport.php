<?php

namespace App\Imports;

use App\Models\Parameter;
use App\Models\User;
use App\Models\Permission;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UsersImport implements ToModel, WithHeadingRow
{
    protected $users = []; // Array to store user objects
    protected $additionalData;
    protected $permissions = [];
    protected $params = [];
    protected $orgs = [];
    protected $userGroups = [];

    public function __construct($additionalData, $permissions, $params, $orgs = null, $userGroups = null)
    {
        $this->additionalData = $additionalData;
        $this->permissions = $permissions;
        $this->params = $params;
        $this->orgs = $orgs;
        $this->userGroups = $userGroups;
    }

    public function model(array $row)
    {
        // Create a new User object
        // if agent 
        if ($this->additionalData['role'] == 4 || $this->additionalData['role'] == 6) {
            $user = new User([
                'full_name' => $row['full_name'],
                'username' => $row['username'],
                'email' => $row['email'],
                'password' => Hash::make($row['password']),
                'agent_id' => $row['id'],
                'organization_id' => $this->additionalData['organization_id'],
                'user_group_id' => $this->additionalData['user_group_id'],
                'role' => $this->additionalData['role'],
                'password_policy' => $this->additionalData['password_policy'],
                'reset_pass_next_login' => $this->additionalData['reset_pass_next_login'],
            ]);
        } else {
            $user = new User([
                'full_name' => $row['full_name'],
                'username' => $row['username'],
                'email' => $row['email'],
                'password' => Hash::make($row['password']),
                'agent_id' => $row['id'],
                'role' => $this->additionalData['role'],
                'password_policy' => $this->additionalData['password_policy'],
                'reset_pass_next_login' => $this->additionalData['reset_pass_next_login'],
            ]);
        }

        // save the user object in DB 
        $user->save();

        // Sync permissions for the user
        $this->syncPermissions($user);

        // Sync parmaeters for the user
        $this->syncParams($user);

        // save orgs for non agents 
        $this->saveOrgs($user);

        // save user groups for non agents 
        $this->saveUserGroups($user);

        // Add the user object to the users array
        $this->users[] = $user;

        return $user; // Return the user object
    }


    protected function syncPermissions($user)
    {
        // Query permission IDs based on permission names
        $permissionIds = Permission::whereIn('name', $this->permissions)->pluck('id');
        Log::info(Permission::whereIn('name', $this->permissions)->toSql());

        // Sync permissions for the user
        $user->permissions()->sync($permissionIds);
    }

    protected function syncParams($user)
    {
        // Query permission IDs based on permission names
        $parameterIds = Parameter::whereIn('name', $this->params)->pluck('id');

        // Sync permissions for the user
        $user->parameters()->sync($parameterIds);
    }

    // non agents 
    protected function saveOrgs($user)
    {
        $user->supervisorOrganizations()->sync($this->orgs);
    }

    // non agents
    protected function saveUserGroups($user)
    {
        $user->supervisorGroups()->sync($this->userGroups);
    }
    // public function saveUsers()
    // {
    //     // Use Laravel's Eloquent ORM to insert users in bulk
    //     User::insert($this->users);
    // }
}
