<div>
    {{-- <div class="card  ms-5"> --}}
    {{-- <div class="card-body"> --}}
    {{-- <div class="card-title mb-4">
                <h3>File Encryption</h3>
            </div> --}}
    <div class="table-responsive mt-4 ms-5 rounded-3">
        <table class="table table-striped table-hover card-text rounded-1">
            <thead style="height: 1rem !important;">
                <tr>
                    {{-- <th scope="col" class="checked text-center">
                        <div class="form-check">#</div>
                    </th> --}}

                    <th scope="col">File Type</th>
                    <th scope="col">Status</th>
{{--                    <th scope="col" class="text-center" style="width: 10%"></th>--}}
                </tr>
            </thead>
            <tbody>
                @forelse($encryptions as $encryption)
                    <tr>
                        {{-- <th scope="row" class="checked text-center">
                            <div class="form-check">
                                {{ $loop->index + 1 }}
                            </div>
                        </th> --}}
                        <td>{{ Str::title($encryption->type) }}</td>
{{--                        <td>{{ $encryption->status() }}</td>--}}

{{--                        <td class="text-center">--}}
{{--                            <div class="btn-group btn-group-sm" role="group" aria-label="Basic mixed styles example">--}}
{{--                                <img wire:click="showUpdateModal({{ $encryption->id }})" src="{{ asset('assets/SVG/assets-v2/union-1.svg') }}" alt="Edit" style="width: 1.5rem; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#departmentModal" />--}}
{{--                            </div>--}}
{{--                        </td>--}}

                        <td class="text-center text-muted"> <!-- Centering the content within the table cell -->
                            <div class="d-flex align-items-center gap-2 justify-content-center align-items-center ">
                                <div class="position-relative">
                                    <input type="checkbox" class="d-none" id="customSwitch{{ $encryption->id }}" wire:click="statusUpdateModal({{$encryption->id }})" />
                                    <label for="customSwitch{{ $encryption->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $encryption->status ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                        <div class="switch-handle position-absolute"
                                             style="
                                                 width: 18px;
                                                 height: 18px;
                                                 background-color: {{ $encryption->status ? '#ffffff' : '#FF5E60' }};
                                                 border-radius: 50%;
                                                 top: 3px;
                                                 left: {{ $encryption->status ? '22px' : '3px' }};
                                                 transition: left 0.3s, background-color 0.3s;">
                                            @if ($encryption->status)
                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                    <path
                                                        d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                        fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                </svg>
                                            @else
                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                </svg>
                                            @endif
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </td>

                    </tr>
                @empty
                @endforelse
            </tbody>
        </table>
    </div>

    {{-- </div> --}}
    {{-- </div> --}}
    {{-- <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" wire:ignore.self wire:key="department" id="departmentModal" tabindex="-1" aria-labelledby="departmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="departmentModalLabel">
                        @if ($modalIdShow != 'on')
                            {{ $modalId ? 'Update encryption' : 'New encryption' }}
                        @else
                            {{ 'View encryption' }}
                        @endif
                    </h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Type:</label>
                            @if ($modalIdShow != 'on')
                                <select id="type" class="form-control" wire:model="type">


                                    <option value="call">call</option>
                                    <option value="video">video</option>
                                </select>
                                @error('type')
                                    <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select id="status" class="form-control" wire:model="type" disabled>

                                    <option value="call">Call</option>
                                    <option value="video">Video</option>
                                </select>
                            @endif
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Status:</label>
                            @if ($modalIdShow != 'on')
                                <select id="status" class="form-control" wire:model="status">
                                    <option value="">---</option>

                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                                @error('status')
                                    <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select id="status" class="form-control" wire:model="status" disabled>
                                    <option value="">---</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                            @endif
                        </div>


                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" wire:click="closeModal" id="closeModal">Close</button>
                    @if ($modalIdShow != 'on')
                        <button class="btn btn-success" wire:click="{{ $modalId ? 'update' : 'store' }}">{{ $modalId ? 'Update' : 'Save' }}</button>
                    @endif
                </div>
            </div>
        </div>
    </div> --}}

    <div wire:ignore.self class="modal fade" id="departmentModal" tabindex="-1" role="dialog" aria-labelledby="departmentModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white; border-radius: 12px;">
                <!-- Modal Header -->
                <div class="modal-header" style="border: none; padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color: #eff3f4 !important;">
                            <i class="fa-solid fa-lock" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h5 class="modal-title fw-bold" id="departmentModalLabel" style="font-size: 24px; color: #40798c;">
                            @if ($modalIdShow != 'on')
                                {{ $modalId ? 'Update Encryption' : 'New Encryption' }}
                            @else
                                {{ 'View Encryption' }}
                            @endif
                        </h5>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id="closeModal" data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="modal-body" style="padding: 20px; border: none;">
                    <form>
                        <div class="mb-3">
                            <label for="type" style="color: #40798c;">Type:</label>
                            @if ($modalIdShow != 'on')
                                <select id="type" class="form-control" wire:model="type"
                                    style="color:#6c757d; background-color:#eff3f4 !important; border: 1px solid transparent; height:3rem;">
                                    <option value="call">Call</option>
                                    <option value="video">Video</option>
                                </select>
                                @error('type')
                                    <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select id="type" class="form-control" wire:model="type" disabled
                                    style="color:#6c757d; background-color:#eff3f4 !important; border: 1px solid transparent !important; height:3rem;">
                                    <option value="call">Call</option>
                                    <option value="video">Video</option>
                                </select>
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="status" style="color: #40798c;">Status:</label>
                            @if ($modalIdShow != 'on')
                                <select id="status" class="form-control" wire:model="status"
                                    style="color:#6c757d; background-color:#eff3f4 !important; border: 1px solid transparent; height:3rem;">
                                    <option value="">---</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                                @error('status')
                                    <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select id="status" class="form-control" wire:model="status" disabled
                                    style="color:#6c757d; background-color:#eff3f4 !important; border: 1px solid transparent; height:3rem;">
                                    <option value="">---</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                            @endif
                        </div>
                    </form>


                </div>

                <!-- Modal Footer -->
                <div class="modal-footer" style="border: none; padding: 20px;">
                    <button type="button" class="btn btn-outline-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" data-bs-dismiss="modal" wire:click="closeModal">
                        Close
                    </button>
                    @if ($modalIdShow != 'on')
                        <button class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="{{ $modalId ? 'update' : 'store' }}">
                            {{ $modalId ? 'Update' : 'Save' }}
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

</div>
