<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto">
                <button
                    wire:target="export"
                    wire:click="export"
                    title="Export"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                    wire:loading.attr="disabled">

                    <i
                        wire:loading.remove
                        wire:target="export"
                        class="fas fa-file-excel text-white me-2"
                        style="font-size: 20px;"></i>

                    <span
                        wire:loading.class="spinner-border spinner-border-sm"
                        wire:target="export"
                        style="width: 1rem; height: 1rem;"
                        role="status"
                        aria-hidden="true"></span>

                    <span
                        wire:loading.remove
                        wire:target="export"
                        style="font-size: 17px;">Extract Excel</span>
                </button>
            </div>
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button
                    data-bs-toggle="modal"
                    data-bs-target="#filterModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">

                        <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon" >
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>

    {{-- bottom row  --}}
    <div class="parent-sections mx-3 ps-5">

        <div class="section-one">
                <div class="div-table rounded-2 shadow-sm mb-3">

                    <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">

                        <thead id="thead" class="text-muted thead" style="font-size: 0.8rem">
                            <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                <th scope="col" class="text-center align-middle">ID</th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('full_name')">
                                    Full Name
                                    @if ($sortBy !== 'full_name')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('role')">
                                    Role
                                    @if ($sortBy !== 'role')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>

                                <th scope="col" class="text-center align-middle">User ID</th>
                                <th scope="col" class="text-center align-middle" style="cursor: pointer" wire:click="setSortBy('enabled')">
                                    Access
                                    @if ($sortBy !== 'enabled')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('username')">
                                    Username
                                    @if ($sortBy !== 'username')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('organization_id')">
                                    Organization
                                    @if ($sortBy !== 'organization_id')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('user_group_id')">
                                    Group
                                    @if ($sortBy !== 'user_group_id')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('terminated')">
                                    Terminated
                                    @if ($sortBy !== 'terminated')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                                    Created At
                                    @if ($sortBy !== 'created_at')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                            </tr>
                        </thead>
                        <tbody class="" style="font-size:0.8rem" id="tbody">

                            @forelse($users as $user)
                                <tr class="align-middle">
                                    <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                    <td class="text-muted text-center py-3 align-middle"> {{ $user->full_name }} </td>
                                    {{-- <td class="text-muted text-center py-3 align-middle"> {{ $user->email }} </td> --}}
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        @if ($user->role == 1)
                                            Admin
                                        @elseif ($user->role == 2)
                                            Supervisor
                                        @elseif ($user->role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $user->agent_id }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        @if ($user->enabled)
                                            <i class="fa-solid fa-user-check fa-lg" aria-hidden="true" title="Active" style="color: #00a34e"></i>
                                        @else
                                            <i class="fa-solid fa-user-xmark fa-lg " aria-hidden="true" title="Disabled" style="color: tomato"></i>
                                        @endif
                                    </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">{{ $user->username }}</td>
                                    {{-- <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $user->organization->name ?? '-' }} </td> --}}
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        {{ $user->organization && $user->organization->name === 'Ewa' ? 'Kease' : ($user->organization->name ?? '-') }}
                                    </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">{{ $user->userGroup->name ?? '-' }} </td>
                                    <td @class([
                                        'text-danger fw-bold' => $user->terminated,
                                        'text-muted' => !$user->terminated,
                                        'text-center py-3 text-nowrap align-middle',
                                    ])> {{ $user->terminated == 1 ? 'Yes' : 'No' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Carbon::parse($user->created_at)->format('Y-m-d h:i A') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="18" class="text-muted text-center bg-white"> No users found</td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between mt-3">
                    <!-- Dropdown for Number of Items per Page -->
                    <div>
                        <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                            <option value="10">10</option>
                            <option value="15" selected>15</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>Results Per Page</span>
                    </div>

                    <!-- Pagination Links -->
                    <div>
                        {{ $users->links(data: ['scrollTo' => false]) }}
                    </div>
                </div>
        </div>
    </div>

    {{-- <div class="mx-3 ps-5">
        <div class="col-6">
            <a href="{{ route('admin.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div> --}}

    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                {{-- <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i> --}}
                                <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">
                        <div class="col-md-12">
                            <form class="row g-2 mb-3">
                                <div class="col-md-6">
                                    <label for="call id" class="mb-2 ">Name <i class="fa-solid fa-user fa-lg" style="color: #00a34e"></i></label>
                                    <input style="border: solid 1px #b6b6b6;font-size:0.85rem" type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Name" wire:model='filter_name'>
                                </div>
                                <div class="col-md-6">
                                    <label for="id" class="mb-2 ">User ID <i class="fa-solid fa-hashtag fa-lg" style="color: #00a34e"></i></label>
                                    <input style="border: solid 1px #b6b6b6;font-size:0.85rem" type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" wire:model='filter_agent_id' placeholder="User ID">
                                </div>
                                <div class="col-md-6">
                                    <label for="Agent name" class="mb-2 ">User Group <i class="fa-solid fa-people-group fa-lg" style="color: #00a34e"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_user_group_name ?? '--' }}</span>
                                        </button>
                                        <ul id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton" style="height: 16rem; overflow:auto">
                                            @forelse ($groups as $item)
                                                <li>
                                                    <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterUserGroup('{{ $item->id }}')">{{ $item->name }}</span>
                                                </li>
                                                @if (!$loop->last)
                                                    <hr class="m-0">
                                                @endif
                                            @empty
                                                <li>
                                                    <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item text-muted">No Groups Found</span>
                                                </li>
                                            @endforelse
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="call id" class="mb-2 ">Role <i class="fa-solid fa-user-group fa-lg" style="color: #00a34e"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_role_name ?? 'All' }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole('All')">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(4)">Agents</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(1)">Admins</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(2)">Supervisors</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(6)">Clients</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(5)">Quality</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Agent name" class="mb-2 ">Terminated <i class="fa-solid fa-person-walking-arrow-right fa-lg" style="color: #00a34e"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_terminated_name ?? 'All' }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTerminated('All')">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTerminated('yes')">Terminated</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTerminated('no')">Not Terminated</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="Duration" class="mb-2 ">Access <i class="fa-solid fa-globe fa-lg" style="color: #00a34e"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_enabled_name ?? 'All' }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterEnabled('All')">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterEnabled('one')">Enabled</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterEnabled('zero')">Disabled</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        wire:click="clear"
                        wire:loading.attr="disabled"
                        wire:target="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="getData"
                        wire:loading.attr="disabled"
                        wire:target="getData">
                        <span wire:loading.remove wire:target="getData" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                    </button>
                </div>
        </div>
    </div>
</div>
