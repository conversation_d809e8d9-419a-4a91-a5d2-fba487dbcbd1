@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Analytics')

@section('style')
    <style>
        /* hide the scrollbar for lists  */
        .dropdown-menu::-webkit-scrollbar {
            display: none
        }

        .no-data-message {
            font-size: 18px;
            font-weight: bold;
            color: #c6d64e;
            text-align: center;
            margin-top: 20px;
        }

        .header-button {
            transition: box-shadow 0.3s !important;
        }

        .header-button:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        }

        .zoom-out {
            /* -webkit-transform: scale(0.9);
            -moz-transform: scale(0.9);
            -ms-transform: scale(0.9);
            -o-transform: scale(0.9);
            transform: scale(0.9);
            transform-origin: top left; */
            zoom: 0.76;
        }
    </style>
@endsection

@section('content')
    <livewire:analytics>

        <script>
            window.addEventListener('closeModal', () => {
                document.querySelector('#close').click();
                document.querySelector('#closeEdit').click();
            });
        </script>

        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        {{-- bar chart  --}}
        <script>
            const ctx = document.getElementById('barChart');

            const DATA_COUNT = 24; // we have 24 hours
            const NUMBER_CFG = {
                count: DATA_COUNT,
                min: 0,
                max: 100
            };
            // Create an array of counts from 0 to 23
            const labels = Array.from({
                length: DATA_COUNT
            }, (_, index) => index);

            const barChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Average Interactions Per Hour',
                        // data: [12, 19, 3, 5, 2, 3, 12, 19, 3, 5, 2, 3, 12, 19, 3, 5, 2, 3, 12, 19, 3, 5, 2, 3],
                        data: {{ $barChartData }},
                        backgroundColor: '#00a34e', // Set the background color for the bars
                        borderWidth: 1,
                        borderRadius: 8
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    maintainAspectRatio: false, // Set to false to allow the canvas to dynamically resize
                    responsive: true, // Enable responsiveness
                    interaction: {
                        mode: 'nearest', // Use nearest mode for better hover accuracy
                        axis: 'x', // Only consider the x-axis for interactions
                        intersect: true // Ensure interaction only when hovering directly over the bar
                    },
                },
                plugins: {
                    tooltip: {
                        intersect: true // Ensure tooltip only shows when hovering directly over the bar
                    }
                }
            });
        </script>

        {{-- Doghnout chart  --}}
        <script>
            const ctx2 = document.getElementById('pieChart');

            // if ({{ $inboundCount }} == 0 && {{ $outboundCount }} == 0) {
            //     document.querySelector('.pieChartContainer').innerHTML = '<div class="no-data-message">No Data Available</div>';
            // }

            const data = {
                labels: ({{ $inboundCount }} == 0 && {{ $outboundCount }} == 0) ? ['No Data Available'] : ['Inbound', 'Outbound'],
                datasets: [{
                    data: ({{ $inboundCount }} == 0 && {{ $outboundCount }} == 0) ? ['100'] : [{{ $inboundCount }}, {{ $outboundCount }}], // Example data, adjust as needed
                    backgroundColor: ({{ $inboundCount }} == 0 && {{ $outboundCount }} == 0) ? ['gray'] : ['#00a34e', '#13653f'], // Set the background colors for the pie slices
                    borderWidth: 1
                }]
            };

            const doughnutChart = new Chart(ctx2, {
                type: 'doughnut',
                data: data,
                options: {
                    cutout: 45,
                    maintainAspectRatio: false, // Set to false to allow the canvas to dynamically resize
                    responsive: true, // Enable responsiveness
                    legend: {
                        position: 'bottom', // Adjust the legend position as needed
                    }
                }
            });
        </script>

        <script>
            window.addEventListener('updateBarChart', (event) => {
                const barChartData = JSON.parse(event.detail[0].barChartData); // Parse JSON string into JavaScript object
                barChart.data.datasets[0].data = barChartData;
                barChart.update();
            });


            // updating doughnut chart data
            window.addEventListener('updatePieChart', (event) => {
                // doughnutChart.data.datasets[0].data = [event.detail[0].inboundCount, event.detail[0].outboundCount];

                // handle the zero values
                if (event.detail[0].inboundCount == 0 && event.detail[0].outboundCount == 0) {
                    data.labels = ['No Data Available'];
                    doughnutChart.data.datasets[0].data = ['100'];
                    doughnutChart.data.datasets[0].backgroundColor = ['gray'];
                } else {
                    data.labels = ['Inbound', 'Outbound'];
                    doughnutChart.data.datasets[0].data = [event.detail[0].inboundCount, event.detail[0].outboundCount];;
                    doughnutChart.data.datasets[0].backgroundColor = ['#00a34e', '#13653f'];
                }

                doughnutChart.update();
            });
        </script>

        <script>
            window.addEventListener('closeCustomDateModal', () => {
                document.querySelector('#closeCustomDate').click();
            });
            window.addEventListener('closeFiltersModal', () => {
                document.querySelector('#closeFiltersModal').click();
            });
        </script>
    @endsection
