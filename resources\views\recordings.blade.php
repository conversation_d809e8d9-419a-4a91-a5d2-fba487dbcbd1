@extends('layouts.app')

@section('title', 'Recordings')
@section('style')
    <style>
        .dropdown-menu::-webkit-scrollbar {
            /* display: none */
        }
        .div-table::-webkit-scrollbar {
            width: 15px !important;
            height: 15px !important;
        }
        #dropDownList {
            border-radius: 7px !important;
        }
        .dropdown-toggle::after {
            vertical-align: top !important;
        }

        input::placeholder {
            font-size: 0.85rem;
        }

        .textarea-with-padding {
            padding-left: 5px;
        }

        .textarea-with-padding::placeholder {
            padding-left: 5px;
        }

        .dropdown-menu.w-100.show {
            transform: translate3d(0px, 39.2px, 0px) !important;
            z-index: 999999999 !important;
        }


        /* seeing a feature for the first time  */
        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            5%,
            15%,
            25%,
            35%,
            45%,
            55%,
            65%,
            75%,
            85%,
            95% {
                transform: translateX(-5px);
            }

            10%,
            20%,
            30%,
            40%,
            50%,
            60%,
            70%,
            80%,
            90% {
                transform: translateX(5px);
            }
        }

        @keyframes pulse-border {
            0% {
                border-color: #ff0000;
            }

            50% {
                border-color: #ffae00;
            }

            75% {
                border-color: #66ff00;
            }

            100% {
                border-color: #fbff00;
            }
        }

        .shake {
            animation: shake 2s, pulse-border 3s infinite;
            /* Faster shake over 2 seconds */
            border-width: 3px;
            border-style: solid;
        }

        /*
                        new styles
                        */
        .thead {
            height: 50px;
            vertical-align: middle;
        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
        }

        table td {
            border-bottom: none !important;
        }

        table {
            position: relative;
        }

        #view-comments-table--head tr th{
            background-color: #40798c !important;
            color: white;
            font-weight: 600;
            font-size: small !important;
        }

        #view-comments-table--body td {
            font-weight: 600;
            font-size: small !important;
        }

        .table-responsive {
            position: relative;
        }

        /* .fixed-col {
            position: sticky;
            left: 0;
            z-index: 2;
            background-color: white;
        }

        .fixed-col:nth-child(2) {
            left: 50px;
        }

        .fixed-col:nth-child(3) {
            left: 100px;
        }

        .table th.fixed-col,
        .table td.fixed-col {
            z-index: 3;
        } */

        @if(Auth::user()->parameters()->where('parameter_id', 33)->exists())

        .fixed-col0{
            position: sticky; left: 0px; z-index: 3; background-color: white;
        }
        .fixed-col1{
            position: sticky; left: 95px; z-index: 3; background-color: white;
        }
        .fixed-col2{
            position: sticky; left: 424px; z-index: 3; background-color: white;
        }
        .fixed-col{
            position: sticky; left: 198px; z-index: 3; background-color: white;
        }
        .first-th0{
            position: sticky; top: 0; left: 0; z-index: 5;
        }
        .first-th1{
            position: sticky; top: 0; left: 95px; z-index: 5;
        }

        .first-th{
            position: sticky; top: 0; left: 198px; z-index: 5;
        }
        .first-th2{
            position: sticky; top: 0; left: 454px; z-index: 5;
        }
        @else

        .fixed-col0{
            position: sticky; left: 0px; z-index: 3; background-color: white;
        }
        .fixed-col1{
            position: sticky; left: 0; z-index: 3; background-color: white;
        }
        .fixed-col2{
            position: sticky; left: 358px; z-index: 3; background-color: white;
        }
        .fixed-col{
            position: sticky; left: 102px; z-index: 3; background-color: white;
        }
        .first-th0{
            position: sticky; top: 0; left: 0; z-index: 5;
        }
        .first-th1{
            position: sticky; top: 0; left: 0; z-index: 5;
        }
        .first-th{
            position: sticky; top: 0; left: 104px; z-index: 5;
        }
        .first-th2{
            position: sticky; top: 0; left: 358px; z-index: 5;
        }

        @endif

.dropdown-menu{
    z-index: 999999 !important;
}
        .parent-sections {
            height: 70vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: 1.5%;
            margin-bottom: 3%;
        }

        .section-one {
            width: 100%;
            height: 90vh;
        }

        .div-table {
            /* border: 1px solid #d0caca; */
            border-radius: 0px;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        .form-control,
        .form-select,
        .dropdown-toggle-style {
            background-color: #eff3f4 !important;
            border: none !important;
            height: 40px;
        }

        label,
        .textColor {
            color: #40798c;
            font-size: 17px;
        }

        .previous {
            margin-bottom: 5px;
        }

        /*
                        end new styles
                        */
        /*
                        pagination styles
                        */
        #searchInput {
            height: 2.5rem !important;
            /*width: 100% !important;*/
            /* Increase the height for a larger input */
            padding-left: 2.5rem !important;
            /* Increase padding for better spacing */
            border: none !important;
            /* Slightly darker border */
            border-radius: 0.5rem;
            /* Rounded corners */
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow */
            transition: box-shadow 0.3s ease, border-color 0.3s ease;
            /* Smooth transition */
            font-size: 1rem;
            /* Slightly larger text size */
            background-position: left 0.5rem center;
            /* Icon positioning */
            background-color: white !important;
        }

        /* Focus styles */
        #searchInput:focus {
            outline: none;
            /* Remove default outline */
            box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
            /* Larger shadow on focus */
            border-color: rgba(0, 0, 0, 0.3);
            /* Slightly darker border on focus */
        }

        /* Placeholder styling */
        #searchInput::placeholder {
            font-family: inherit;
            /* Use inherited font style */
            color: #01A44F;
            /* Green placeholder text */
            font-size: 1rem;
            /* Match placeholder size with input text */
        }

        .main-buttons-container button {
            height: 2.9rem;
            font-size: 15px;
        }

        .main-buttons-container button:hover {
            background-color: #018F3E !important;
        }

        /* pagination  */
        ul.pagination {
            gap: 0;
        }

        ul.pagination li button,
        ul.pagination li span {
            padding: 0.7rem;
            padding-top: 0.4rem;
            padding-bottom: 0.4rem;
        }

        ul.pagination li button:hover {
            background-color: rgb(196, 183, 183) !important;
        }

        ul.pagination>li>button,
        ul.pagination>li>span {
            color: black !important;
            font-weight: 600 !important;
            background-color: white;
        }

        .page-item span,
        .page-item button {
            border-radius: 0.7rem !important;
        }

        .page-link[aria-label="« Previous"], .page-link[aria-label="Next »"] {
            padding: 0.8rem !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        .page-item.active span,
        .page-item.active button {
            border-radius: 0.5rem !important;
        }

        .page-item.active>span {
            background-color: #00a34e !important;
            color: white !important;
        }

        div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
            font-size: 0.9rem;
        }

        div.tab-pane label {
            font-weight: 600 !important;
        }

        div.tab-pane hr {
            display: none;
        }

        /*
                        end pagination styles
                        */
        .dropdown-toggle-action::after {
            display: none !important;
        }
        .color {
            color: #40798c !important;
        }
        .bg-color {
            background-color: #eff3f4 !important;
        }
        thead {
            height: 0rem !important;
        }
        thead {
            height: 1rem !important;
        }
        tbody {
            height: 1rem !important;
        }

        tr th {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        tr td {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 7px !important;
        }
    </style>

@endsection


@section('content')


    <div class="">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">

        @livewire('records')

    </div>

@endsection

<script type="module">
    window.addEventListener('close-modal', event => {
        document.getElementById('closeModal').click()
    });
    window.addEventListener('close-modall', event => {
        document.getElementById('closeModall').click()
    });
    window.addEventListener('refresh-model', event => {
        document.getElementById('evaluationSelect').value = 0;
    });
    document.addEventListener('DOMContentLoaded', function() {
        // Call the function when the page loads

        document.getElementById('evaluationSelect').value;

        // Add event listener to the select element
    });
    document.addEventListener('DOMContentLoaded', function () {
    const evaluationSelect = document.getElementById('evaluationSelect');
    const evaluationLink = document.getElementById('evaluationLink');
    const btnDisabled = document.getElementById('btnDisabled');


    function updateEvaluationLink() {
        const selectedValue = evaluationSelect.value;
        const isValid = selectedValue !== "0";

        evaluationLink.classList.toggle('d-none', !isValid);
        btnDisabled.style.display = isValid ? 'none' : '';
    }

    evaluationSelect.addEventListener('change', updateEvaluationLink);

    evaluationLink.addEventListener('click', function () {
        btnDisabled.style.display = 'none';
    });

    // Initialize on page load
    updateEvaluationLink();
});

</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (!localStorage.getItem('search-shake-seen')) {
            const searchInput = document.getElementById('searchInput');
            // Apply the shake class
            searchInput.classList.add('shake');

            // Remove the shake class after the animation completes
            setTimeout(function() {
                searchInput.classList.remove('shake');
            }, 3000); // Duration is extended to match both the shake and pulse effect

            // Set a flag in local storage so this only happens once
            localStorage.setItem('search-shake-seen', 'true');
        }
    });
</script>

<script>
    function toggleArrow(button) {
        const arrow = button.querySelector('i');
        const expanded = button.getAttribute('aria-expanded') === 'true';
        arrow.classList.toggle('fa-caret-right', !expanded); // Right arrow when closed
        arrow.classList.toggle('fa-caret-up', expanded); // Up arrow when open
    }


    window.addEventListener('selectAllLangs', () => {
        document.querySelectorAll('#dropdownMenu2323sd input[type="checkbox"]').forEach((checkbox) => {
            checkbox.checked = true;
        });
    });

    window.addEventListener('checkOrUncheckLangs', (event) => {
        // Get the state from the event
        const shouldCheck = event.detail.shouldCheck;

        // Select all checkbox elements within the dropdown menu
        document.querySelectorAll('#dropdownMenu2323sd input[type="checkbox"]').forEach((checkbox) => {

            checkbox.checked = event.detail[0].shouldCheck;
        });
    });



    window.addEventListener('closeModal', () => {
        document.querySelector('#closeAddComment').click();
        document.querySelector('#closeViewComments').click();
        document.querySelector('#closeViewListeners').click();
    });

    window.addEventListener('closeCustomDateModal', () => {
        document.querySelector('#closeCustomDate').click();
    });

</script>
