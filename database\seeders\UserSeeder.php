<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // make 200 fake users 
        User::factory(15)->create();
        
        DB::table('users')->insert([
            'full_name' => '<PERSON><PERSON>',
            'username' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'agent_id' => '101',
            'password' => Hash::make('Aa@123456789'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => '<PERSON>',
            'username' => '<PERSON>',
            'email' => '<EMAIL>',
            'agent_id' => '15678',
            'password' => Hash::make('Aa@123456789'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => 'Asem Musleh',
            'username' => 'Asem Musleh',
            'email' => '<EMAIL>',
            'agent_id' => '11111113',
            'password' => Hash::make('Aa@123456789'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => 'Emad Abu Ghazaleh',
            'username' => 'Emad Abu Ghazaleh',
            'email' => '<EMAIL>',
            'agent_id' => '11111114',
            'password' => Hash::make('Aa@123456789'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => 'Abdullah Al-Kurdi',
            'username' => 'Abdullah Al-Kurdi',
            'email' => '<EMAIL>',
            'agent_id' => '11111115',
            'password' => Hash::make('Aa@123456789'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => 'Osaid Zaatreh',
            'username' => 'Osaid Zaatreh',
            'email' => '<EMAIL>',
            'agent_id' => '11111116',
            'password' => Hash::make('Aa@123456789'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => 'Aseel',
            'username' => 'Aseel',
            'email' => '<EMAIL>',
            'agent_id' => '11111118',
            'password' => Hash::make('aseel12345678'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
        DB::table('users')->insert([
            'full_name' => 'Layan',
            'username' => 'Layan',
            'email' => '<EMAIL>',
            'agent_id' => '11111119',
            'password' => Hash::make('layan12345678'),
            'role' => 1,
            'terminated' => 0,
            'enabled' => 1,
            'created_at' => now()
        ]);
    }
}
