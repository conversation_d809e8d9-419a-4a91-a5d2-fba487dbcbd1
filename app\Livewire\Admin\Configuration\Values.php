<?php

namespace App\Livewire\Admin\Configuration;

use App\Models\ScriptVariable;
use App\Models\ScriptVariableValue;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Values extends Component
{
    use WithPagination,LivewireAlert;

    public $value;
    public $script_variable_id;


    public $modalId;
    public $modalIdShow;

    public function getListeners()
    {
        return [
            'confirmed','modelFormReset',
            'getEncryption',
        ];
    }


    public function BadWord()
    {
        try
        {
            return ScriptVariableValue::query()->get();
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function modelData()
    {
        return [
            'value'          => $this->value,
            'script_variable_id'          => $this->script_variable_id,


        ];
    }

    public function rules()
    {
        return [
            'value' =>       ['required'],
            'script_variable_id' =>       ['required'],



        ];
    }

    public function update(){


        $this->validate();

        try {
            ScriptVariableValue::find($this->modalId)->update($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }
    public function store(){


        $this->validate();

        try {
            ScriptVariableValue::create($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }

    public function closeModal()
    {
        $this->modelFormReset();
    }

    public function showModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
        $this->modalIdShow = "on";
    }

    public function showDeleteAlert($id)
    {
        $this->modalId = $id;
        $this->alert('warning', 'Are you sure you want to delete this group?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirmed()
    {
        if (ScriptVariableValue::find($this->modalId)->delete()) {
            $this->alert("success", "Group Deleted Successfully");
        }
    }

    public function showUpdateModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
    }

    public function modelLoadData()
    {
        $encryption = ScriptVariableValue::find($this->modalId);

        $this->value = $encryption->value;
        $this->script_variable_id = $encryption->script_variable_id;


    }

    public function modelFormReset()
    {
        $this->value = "";
        $this->script_variable_id = "";


        $this->modalId=null;
        $this->modalIdShow=null;

        $this->resetValidation();
    }

    public function variables()
    {
        return ScriptVariable::all();
    }

    public function changePage($data)
    {
        $this->dispatch('changePage', $data);

    }

    public function render()
    {
        return view('livewire.admin.configuration.values',['variables'=>$this->variables(), 'transcriptionBadWords' => $this->BadWord()]);
    }
}
