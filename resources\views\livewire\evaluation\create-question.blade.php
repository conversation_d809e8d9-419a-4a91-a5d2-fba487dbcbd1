<div class="container-fluid mt-3 px-4">

        {{-- header row  --}}
        <div class="header">
            <div class="row justify-content-end mx-3 ps-lg-5">
                <div class="filters-block">

                    <div data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip" data-bs-title="Search" class="center mt-10  modal-button-search " id='search_button' style="cursor: pointer">
                        <img id='col_img' class="p-10 b-white b-radius-10 shadow-sm" src="{{ asset('assets/images/evaluation/Vector9.png') }}" alt="mdo" width="45" height="44">
                    </div>

                    <div data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip" data-bs-title="Refresh" class="center mt-10"  style="cursor: pointer">
                        <img id='col_img' class="p-10 b-white b-radius-10 shadow-sm" wire:click="modelFormReset" src="{{ asset('assets/images/evaluation/Vector12.png') }}" alt="mdo" width="45" height="44">
                    </div>

                    <div id="search" class="card mb-3 mb-2 modal-search modal-content-search rounded shadow-sm" wire:ignore.self>
                        <div class="row">
                            <div class="col-1">
                                <div class="search-icon"><i class="fa-solid fa-magnifying-glass" style="color: #939f99;"></i></div>
                            </div>
                            <div class="col-8 div-search">
                                <input type="text" class="input-search" wire:model.defer="search_question_name" placeholder="Write your answer here">
                            </div>
                            <div class="col-3">
                                <button class="btn btn-success align-content-center style-btn-search"  wire:click="getEvaluationQuestion">Search</button>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-auto mt-3 mt-sm-0 pe-0">
                    <button id="open_modal"
                        data-bs-toggle="modal"
                        data-bs-target="#evaluationModal"
                        class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                        style="min-width: 150px; height: 45px; border-color: #01a44f; background: #01a44f;">
                        <i
                            class="fa fa-plus fa-style text-white ms-3"
                            style="font-size: 20px;    margin-inline-end: auto;"></i>
                        <span style="font-size: 17px;">Add New Question</span>
                    </button>
                </div>

            </div>
        </div>


        <div class="parent-sections mx-3 ps-5">
            <div class="section-one">
                    <div class="div-table rounded-2 shadow-sm mb-3">
                        <table class="table table-bordered table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                            <thead style="background-color: #f9f9f9;font-size: .8rem">
                            <tr>
                                <th scope="col" class="checked text-center" style="width: 10%;">
                                    <div class="form-check">
                                        #
                                    </div>
                                </th>
                                <th scope="col" style="width: 60%;">Question</th>
                                <th scope="col" style="width: 15%;">Edit</th>
                                <th scope="col" style="width: 15%;">Status</th>
                            </tr>
                            </thead>
                            <tbody>
                            @forelse($Evaluation_question ?? [] as $question)
                                <tr>
                                    <th class="text-muted" scope="row text-center">
                                        <div class="form-check">
                                            <button class="but">{{$loop->index +1}}</button>
                                        </div>
                                    </th>
                                    <td class="text-muted">
                                        {!! $question->question_name !!}
                                    </td>
                                    <td class="text-muted">
                                        <a href="{{ route('evaluation.editQuestion', ['evaluation_id' => $evaluation_id,'group_id'=>$group_id,'question_id'=>$question->id]) }}">
                                            <i class="fa-solid fa-pen" id="pencel_icon{{$question->id}}" style="font-size: 20px;color: #00a34e;"></i>
                                        </a>
                                    </td>
                                    <td class="text-center text-muted"> <!-- Centering the content within the table cell -->
                                        <div class="d-flex align-items-center gap-2 justify-content-center align-items-center">
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="customSwitch{{ $question->id }}" wire:click="statusUpdateModal({{$question->id }})" />
                                                <label for="customSwitch{{ $question->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $question->status ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="
                                                            width: 18px;
                                                            height: 18px;
                                                            background-color: {{ $question->status ? '#ffffff' : '#FF5E60' }};
                                                            border-radius: 50%;
                                                            top: 3px;
                                                            left: {{ $question->status ? '22px' : '3px' }};
                                                            transition: left 0.3s, background-color 0.3s;">
                                                        @if ($question->status)
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path
                                                                    d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                    fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </td>

                                </tr>

                            @empty
                                <tr>
                                    <td colspan="11" class="text-muted text-center"> There is no Question found</td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                    {{-- {!! $CannedResponse_apps->links() !!}--}}
            </div>
        </div>


        <div class="mx-3 ps-5">
            <div class="col-6">
                <a href="{{ route('evaluation.createGroup', ['id' => $evaluation_id]) }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                    <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
                </a>
            </div>
        </div>


        <div wire:ignore.self class="modal fade" id="evaluationModal" tabindex="-1" role="dialog" aria-labelledby="evaluationModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content" style="background-color: white;">
                        <div class="modal-header" style="border: none;">
                            <div class="d-flex">
                                <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                    {{-- <i class="fa-solid fa-filter" ></i> --}}
                                    <i class="fa fa-question" style="font-size: 35px; color: #01a44f !important;"></i>
                                </div>
                                <h6 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 20px;">
                                    New Question
                                </h6>
                            </div>
                            <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal">
                                <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                            </button>
                        </div>
                        <div class="modal-body" style="border: none;">

                            <div class="col-12">



                                    <div class="row">
                                        <div class="">
                                            <div class="mb-3">
                                                <label for="title" class="col-form-label ">Header Name:</label>
                                                <div class="custom-select" style="position: relative">
                                                    <select class="form-control" wire:model.defer="header_name" id="header_name" style="font-weight: 300;">
                                                        <option value="">Select Header...</option>

                                                        @forelse($headers ?? [] as $header)

                                                        <option value="{{$header->id}}">{{$header->header_name}}</option>
                                                        @empty
                                                        @endforelse
                                                    </select>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                        <path d="M7 10l5 5 5-5z" />
                                                    </svg>
                                                </div>
                                                <div class="new-header-style" wire:click="createNewHeader">Create new header</div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($create_new_header)
                                    <div class="row">
                                        <div class="">
                                            <div class="mb-3">
                                                <label for="title" class="col-form-label ">New Header:</label>

                                                <input type="text" class="form-control" wire:model.defer="new_header" id="new_header" style="resize: none;" />

                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    <div class="row">
                                        <div class="">
                                            <div class="mb-3">
                                                <label for="title" class="col-form-label ">Question Name:</label>

                                                <textarea class="form-control" wire:model.defer="question_name" id="question_name" style="resize: none;" placeholder="write question name here..."></textarea>
                                                @error('question_name')<small class="text-danger"> {{ $message }} </small> @enderror

                                            </div>
                                        </div>
                                    </div>


                                    <div class="row">
                                        <div class="">
                                            <div class="mb-3">
                                                <label for="title" class="col-form-label ">Add Default value:</label>
                                                <div class="checkbox-parent">
                                                    <div class="form-group" style="margin-right: 3%;">
                                                        <input type="checkbox" id="Fatal" wire:click="getFatalType">
                                                        <label for="Fatal" class="text-muted">Fatal</label>
                                                    </div>
                                                    <div class="form-group">
                                                        <input type="checkbox" id="NA" wire:click="getFatalNA">
                                                        <label for="NA" class="text-muted">NA</label>
                                                    </div>
                                                </div>

                                                @if($fatal)
                                                <hr style="width: 50%;color: #00a34e;">
                                                <div class="radio-parent">
                                                    <div class="form-group-raido" style="margin-right: 3%;width: 37%;border-radius: 20px">
                                                        <input type="radio" class="form-check-input" name="fatal_type" value="Fatal Per Group" wire:model="fatal_type">
                                                        <label for="Fatal Per Group" class="text-muted">Fatal Per Group</label>
                                                    </div>
                                                    <div class="form-group-raido" style="margin-right: 5%;width: 34%;border-radius: 20px">
                                                        <input type="radio" class="form-check-input" name="fatal_type" value="Fatal Critical" wire:model="fatal_type">
                                                        <label for="Fatal Critical" class="text-muted">Fatal Critical</label>
                                                    </div>
                                                </div>
                                                @error('fatal_type')<small class="text-danger"> {{ $message }} </small> @enderror
                                                @endif
                                            </div>
                                        </div>
                                    </div>


                                    <div class="row">
                                        <div class="">
                                            <div class="mb-3">
                                                <label for="title" class="col-form-label ">Mark Type:</label>
                                                <div class="custom-select" style="position: relative">
                                                    <button class="form-control" wire:click="showDropdown" style="font-weight: 300;cursor: pointer;text-align: left;">
                                                        @if($selected_mark_type=='Emojy')
                                                        <span style="margin-left: 5px;">&#128513;</span>
                                                        @elseif($selected_mark_type=='Text')
                                                        <img src="{{ asset('assets/images/evaluation/textIcon.PNG') }}" alt="Text Icon" width="30" height="30">
                                                        @elseif($selected_mark_type=='Number')
                                                        <span style="margin-left: 4px;"><img src="{{ asset('assets/images/evaluation/numberIcon.PNG') }}" alt="Text Icon" width="23" height="30"></span>
                                                        @endif
                                                        {{$selected_mark_type}}
                                                    </button>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                        <path d="M7 10l5 5 5-5z" />
                                                    </svg>
                                                </div>
                                                @if($dropdownIsOpen)
                                                <div class="shadow dropdown-style">
                                                    <div class="dropdown-item" wire:click="getMarkType('Emojy')"><span style="margin-left: 5px;">&#128513;</span> <span style="margin-left: 5px;">Emoji</span></div>
                                                    <div class="dropdown-item" wire:click="getMarkType('Text')">
                                                        <img src="{{ asset('assets/images/evaluation/textIcon.PNG') }}" alt="Text Icon" width="30" height="30">
                                                        Text
                                                    </div>
                                                    <div class="dropdown-item" wire:click="getMarkType('Number')">
                                                        <span style="margin-left: 4px;"><img src="{{ asset('assets/images/evaluation/numberIcon.PNG') }}" alt="Text Icon" width="23" height="30"></span>
                                                        <span style="margin-left: 5px;">Number</span>
                                                    </div>
                                                </div>
                                                @endif
                                                @error('mark_type')<small class="text-danger"> {{ $message }} </small> @enderror
                                            </div>
                                        </div>
                                    </div>


                                    @if($mark_type)
                                    <div class="row js-copy">
                                        @if($selected_mark_type=='Emojy')
                                        <div class="mb-3 col-5 mrk">
                                            <label for="title" class="col-form-label ">Mark:</label>
                                            <div class="custom-select" style="position: relative">
                                                <select class="form-control" wire:model="arrayMarkAndWeight.0.mark" style="font-weight: 200;">
                                                    <option value="">Select Mark...</option>
                                                    <option value="&#129321; Amazed">&#129321; Amazed</option>
                                                    <option value="&#128526; Great">&#128526; Great</option>
                                                    <option value="&#128513; Happy">&#128513; Happy</option>
                                                    <option value="&#128533; Confused">&#128533; Confused</option>
                                                    <option value="&#128577; Disappointed">&#128577; Disappointed</option>
                                                    <option value="&#128532; Sad">&#128532; Sad</option>
                                                    <option value="&#x1F44D; Good">&#x1F44D; Good</option>
                                                    <option value="&#x1F44E; Bad">&#x1F44E; Bad</option>
                                                </select>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                    <path d="M7 10l5 5 5-5z" />
                                                </svg>
                                            </div>
                                            @error('arrayMarkAndWeight.0.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        @elseif($selected_mark_type=='Text')
                                        <div class="mb-3 col-5 mrk">
                                            <label for="title" class="col-form-label  mark-lable">Mark:</label>
                                            <div class="custom-select" style="position: relative">
                                                <select class="form-control" wire:model="arrayMarkAndWeight.0.mark" style="font-weight: 200;">
                                                    <option value="">Select Mark...</option>
                                                    <option value="Yes">Yes</option>
                                                    <option value="No">No</option>
                                                    <option value="Pass">Pass</option>
                                                    <option value="Fail">Fail</option>
                                                </select>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                    <path d="M7 10l5 5 5-5z" />
                                                </svg>
                                            </div>
                                            @error('arrayMarkAndWeight.0.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        @elseif($selected_mark_type=='Number')

                                        <div class="mb-3 col-5 mrk">
                                            <label for="title" class="col-form-label  mark-lable">Mark:</label>
                                            <div class="custom-select" style="position: relative">
                                                <select class="form-control" wire:model="arrayMarkAndWeight.0.mark" style="font-weight: 200;">
                                                    <option value="">Select Mark...</option>
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="15">15</option>
                                                    <option value="20">20</option>
                                                    <option value="25">25</option>
                                                    <option value="30">30</option>
                                                    <option value="35">35</option>
                                                    <option value="40">40</option>
                                                    <option value="45">45</option>
                                                    <option value="50">50</option>
                                                    <option value="55">55</option>
                                                    <option value="60">60</option>
                                                    <option value="65">65</option>
                                                    <option value="70">70</option>
                                                    <option value="75">75</option>
                                                    <option value="80">80</option>
                                                    <option value="85">85</option>
                                                    <option value="90">90</option>
                                                    <option value="95">95</option>
                                                    <option value="100">100</option>

                                                </select>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                    <path d="M7 10l5 5 5-5z" />
                                                </svg>
                                            </div>
                                            @error('arrayMarkAndWeight.0.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        @endif
                                        <div class="mb-3 col-5 weight">
                                            <label for="title" class="col-form-label  weight-lable">Weight:</label>
                                            <input type="number" class="form-control" wire:model="arrayMarkAndWeight.0.weight" style="font-weight: 300;" placeholder="Write Number..." />
                                            @error('arrayMarkAndWeight.0.weight')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                    </div>



                                    @foreach($inputs as $key => $value)
                                    <div class="row js-copy">
                                        @if($selected_mark_type=='Emojy')
                                        <div class="mb-3 col-5">
                                            <div class="custom-select" style="position: relative">
                                                <select class="form-control" wire:model="arrayMarkAndWeight.{{$value}}.mark" style="font-weight: 200;">
                                                    <option value="">Select Mark...</option>
                                                    <option value="&#129321; Amazed">&#129321; Amazed</option>
                                                    <option value="&#128526; Great">&#128526; Great</option>
                                                    <option value="&#128513; Happy">&#128513; Happy</option>
                                                    <option value="&#128533; Confused">&#128533; Confused</option>
                                                    <option value="&#128577; Disappointed">&#128577; Disappointed</option>
                                                    <option value="&#128532; Sad">&#128532; Sad</option>
                                                    <option value="&#x1F44D; Good">&#x1F44D; Good</option>
                                                    <option value="&#x1F44E; Bad">&#x1F44E; Bad</option>
                                                </select>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                    <path d="M7 10l5 5 5-5z" />
                                                </svg>
                                            </div>
                                            @error('arrayMarkAndWeight.' .$value. '.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        @elseif($selected_mark_type=='Text')
                                        <div class="mb-3 col-5 mrk">
                                            <div class="custom-select" style="position: relative">
                                                <select class="form-control" wire:model="arrayMarkAndWeight.{{$value}}.mark" style="font-weight: 200;">
                                                    <option value="">Select Mark...</option>
                                                    <option value="Yes">Yes</option>
                                                    <option value="No">No</option>
                                                    <option value="Pass">Pass</option>
                                                    <option value="Fail">Fail</option>
                                                </select>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                    <path d="M7 10l5 5 5-5z" />
                                                </svg>
                                            </div>
                                            @error('arrayMarkAndWeight.' .$value. '.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        @elseif($selected_mark_type=='Number')

                                        <div class="mb-3 col-5">
                                            <div class="custom-select" style="position: relative">
                                                <select class="form-control" wire:model="arrayMarkAndWeight.{{$value}}.mark" style="font-weight: 200;">
                                                    <option value="">Select Mark...</option>
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="15">15</option>
                                                    <option value="20">20</option>
                                                    <option value="25">25</option>
                                                    <option value="30">30</option>
                                                    <option value="35">35</option>
                                                    <option value="40">40</option>
                                                    <option value="45">45</option>
                                                    <option value="50">50</option>
                                                    <option value="55">55</option>
                                                    <option value="60">60</option>
                                                    <option value="65">65</option>
                                                    <option value="70">70</option>
                                                    <option value="75">75</option>
                                                    <option value="80">80</option>
                                                    <option value="85">85</option>
                                                    <option value="90">90</option>
                                                    <option value="95">95</option>
                                                    <option value="100">100</option>

                                                </select>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                    <path d="M7 10l5 5 5-5z" />
                                                </svg>
                                            </div>
                                            @error('arrayMarkAndWeight.' .$value. '.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        @endif
                                        <div class="mb-3 col-5 weight">
                                            <input type="number" class="form-control" name="weight[]" wire:model="arrayMarkAndWeight.{{$value}}.weight" style="font-weight: 300;" placeholder="Write Number..." />
                                            @error('arrayMarkAndWeight.' .$value. '.weight')<small class="text-danger"> {{ $message }} </small> @enderror
                                        </div>
                                        <div class="mb-2 col-2 delete-btn" style="padding-top: 1.8%;">
                                            <i class="fa fa-trash-alt" style="font-size:20px;cursor: pointer" wire:click="removeElement({{$key}})"></i>
                                        </div>
                                    </div>

                                    @endforeach
                                    <div class="">
                                        <div class="circle">
                                            <i class="fa fa-plus fa-style" style="font-size: 19px;color: white" wire:click="addNewElement({{$counter}})"></i>
                                        </div>
                                    </div>
                                    @endif



                            </div>
                        </div>

                    <div class="modal-footer" style="border: none;">
                        <button
                            type="button"
                            class="btn btn-secondary rounded-3 px-4"
                            style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                            data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal">
                            Close
                        </button>
                        @if(!$modalIdShow)
                            <button
                                class="btn btn-success rounded-3 px-4"
                                style="height: 40px; border-color: #01a44f; background: #01a44f;"
                                wire:click="{{ $modalId ? 'update' : 'store' }}" >
                                {{ $modalId ? 'Update' : 'Submit' }}
                            </button>
                        @endif
                    </div>
            </div>
        </div>
</div>

