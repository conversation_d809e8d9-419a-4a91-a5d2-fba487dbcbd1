<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\SkillGroup;
use App\Models\Organization;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;

class Organizations extends Component
{
    use LivewireAlert, WithPagination;

    public $perPage = 15;

    // add modal 
    public $openMenu = 0;
    public $openSupervisorsMenu = 0;
    public $correspondingSupers;

    public $selectedAdmins = [];
    public $add_description;
    public $add_orgName;
    public $selectedUserGroups_add = [];

    // search supervisor (modal edit user group)
    public $searchSupervisors = '';
    public $possibleSupervisors = [];
    public $possibleSupervisors_ids = [];

    public $selectedGroupId;
    public $selectedGroupName;
    public $selectedGroupOrg;
    public $selectedGroupOrg_id;
    public $selectedGroupSupervisors = [];
    public $selectedGroupAgents = [];

    // edit modal
    public $editOrgModal = false;
    public $selectedOrgId;
    public $selectedOrgName;
    public $selectedOrgParentID;
    public $selectedOrgParentName;
    public $selectedOrgUserGroups = [];
    public $selectedOrgUserGroupsIds = [];
    public $selectedOrgAdmins = [];
    public $selectedOrgAdminsIds = [];
    public $selectedOrgDescription;
    public $searchUserGroupsEditModal = '';
    public $searchAdminsEditModal = '';

    // skill modal 
    public $added_type;
    public $added_lang;
    public $added_name;
    public $added_acdid;
    public $added_desc;

    // user group modal 
    public $added_org;
    public $added_group_name = null;
    public $added_supervisor;
    public $added_org_id;
    public $selected_supervisors = [];
    public $selected_agents = [];

    // public $possibleUserGroups = [];
    // public $possibleAdmins = [];


    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }

    public function toggleOpenMenu($orgId)
    {
        if ($this->openMenu == $orgId) {
            $this->openMenu = 0;
        } else {
            $this->openMenu = $orgId;
        }
        if ($this->openMenu) $this->openSupervisorsMenu = 0;

        $this->dispatch('changeTableStyle', ['rowId' => $orgId]);
    }

    public function toggleSupervisorMenu($orgId)
    {
        // get the corresponding supervisors 
        $this->correspondingSupers = User::where('role', 2)
            ->whereHas('supervisorOrganizations', function ($query) use ($orgId) {
                $query->where('organization_id', $orgId);
            })
            ->get();

        if ($this->openSupervisorsMenu == $orgId) {
            $this->openSupervisorsMenu = 0;
        } else {
            $this->openSupervisorsMenu = $orgId;
        }

        if ($this->openSupervisorsMenu) $this->openMenu = 0;
    }

    public function addSupervisor($supervisorId, $organizationId)
    {
        $user = User::findOrFail($supervisorId);
        $user->supervisorOrganizations()->attach($organizationId);
        // $this->emit('supervisorUpdated', $organizationId);
    }

    public function removeSupervisor($supervisorId, $organizationId)
    {
        $user = User::findOrFail($supervisorId);
        $user->supervisorOrganizations()->detach($organizationId);
        // $this->emit('supervisorUpdated', $organizationId);
    }

    // search agents (modal edit user group)
    public $searchAgents = '';
    public $possibleAgents = [];

    // live search 
    public $searchOrg = '';
    public $selectedUser = [];
    public $sortBy = 'name';
    public $sortDir = 'ASC';


    // Users modal
    public $activeTab = 'general';

    // public $users;
    public $selectedUserId;
    public $selectedUserName;
    public $selectedUserRole;

    public $theIdToDelete;

    public $email;
    public $name;
    public $password;
    public $role;

    protected $paginationTheme = 'bootstrap';

    public function toggleOrg($orgId)
    {
        $organization = Organization::findOrFail($orgId);
        $organization->is_enabled = !$organization->is_enabled;
        $organization->save();
    }

    public function toggleUserGroup($groupId)
    {
        $group = UserGroup::findOrFail($groupId);
        $group->is_enabled = !$group->is_enabled;
        $group->save();
    }

    public function selectGroup($id)
    {
        $this->selectedGroupId = $id;
        $group = UserGroup::find($id);
        $this->selectedGroupName = $group->name;
        $this->selectedGroupOrg = $group->organization->name;
        $this->selectedGroupOrg_id = $group->organization_id;
        $this->selectedGroupSupervisors = $group->supervisors->pluck('full_name');
        $this->selectedGroupAgents = $group->users->pluck('full_name');
    }

    // for user group modal ================================
    public function add_org($id)
    {
        $this->added_org_id = $id;
        $this->added_org = Organization::find($id)->name;

        // clear the already selected supervisors and agents 
        $this->selected_supervisors = [];
        $this->selected_agents = [];

        // get possible supervisors (that belong to the selected org)
        $this->possibleSupervisors = User::where('role', 2)->whereHas('supervisorOrganizations', function ($query) {
            $query->where('organization_id', $this->added_org_id);
        })->pluck('full_name', 'id');


        // get possible agents (that belong to the selected org)
        $this->possibleAgents = User::where('organization_id', $this->added_org_id)->pluck('full_name', 'id');
    }

    public function select_agent_addModal($index)
    {
        $this->selected_agents[$index] = User::find($index)->full_name;
    }

    public function select_supervisor_addModal($index)
    {
        $this->selected_supervisors[$index] = User::find($index)->full_name;
    }

    public function remove_agent($index)
    {
        unset($this->selected_agents[$index]);
    }

    public function remove_supervisor($index)
    {
        unset($this->selected_supervisors[$index]);
    }

    public function submitAddGroup()
    {

        $rules = [
            'added_group_name' => ['required', 'string', 'max:255', 'unique:user_groups,name'],
            'added_org' => ['required'],
        ];

        $this->validate($rules);


        // save the group 
        $group = UserGroup::create([
            'name' => $this->added_group_name,
            'organization_id' => $this->added_org_id,
        ]);

        // save the group supervisors 
        $group->supervisors()->sync(array_keys($this->selected_supervisors));

        // save the group agents 
        foreach ($this->selected_agents as $agent_id => $agent) {
            User::find($agent_id)->update([
                'user_group_id' => $group->id
            ]);
        }


        $this->alert("success", "Group Added Successfully");

        $this->closeModal();
    }

    // end of user group modal =================================================


    // for skill modal ==============================


    public function addType($type)
    {
        $this->added_type = $type;
    }

    public function addlang($lang)
    {
        $this->added_lang = $lang;
    }

    public function addGroup()
    {
        $skill_rules = [
            'added_name' => ['required', 'string', 'max:255', 'unique:skill_groups,name'],
            'added_acdid' => ['required', 'numeric', 'not_in:0'],
            'added_lang' => 'required|in:Arabic,English',
            'added_type' => 'required|string|in:Inbound,Outbound',
        ];

        $this->validate($skill_rules);

        SkillGroup::create([
            'name' => $this->added_name,
            'type' => $this->added_type,
            'language' => $this->added_lang,
            'acdid' => $this->added_acdid,
            'description' => $this->added_desc,
        ]);

        $this->alert("success", "Skill Added Successfully");

        $this->closeModal();
    }
    // end skill modal =========================

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function selectOrg($id)
    {
        $this->selectedOrgId = $id;
        $org = Organization::find($id);
        $this->selectedOrgName = $org->name;

        // $this->selectedOrgParentID = $id;
        // $this->selectedOrgParentName = $this->selectedOrgName;

        // user groups 
        $this->selectedOrgUserGroups = $org->userGroups->pluck('name', 'id')->toArray();

        // admins 
        $this->selectedOrgAdmins = $org->admins->pluck('full_name', 'id')->toArray();

        // desc 
        $this->selectedOrgDescription = $org->description;
    }

    // public function editParentOrg($id)
    // {
    //     $this->selectedOrgParentID = $id;
    //     $this->selectedOrgParentName = Organization::find($id)->name;
    // }

    public function selectAdmin($id)
    {
        $admin = User::find($id);

        if ($admin && !in_array($id, $this->selectedOrgAdmins)) {
            $this->selectedOrgAdmins[$id] = $admin->full_name;
        }

        $this->searchAdminsEditModal = '';
    }

    // edit modal 
    public function selectUserGroup($id)
    {
        $userGroup = UserGroup::find($id);

        if ($userGroup && !in_array($id, $this->selectedOrgUserGroups)) {
            $this->selectedOrgUserGroups[$id] = $userGroup->name;
        }

        $this->searchUserGroupsEditModal = '';
    }

    // edit modal 
    public function removeUserGroup_edit($index)
    {
        unset($this->selectedOrgUserGroups[$index]);
    }

    // edit modal 
    public function removeAdmin($id)
    {
        unset($this->selectedOrgAdmins[$id]);
    }

    public function getListeners()
    {
        return [
            'confirmed'
        ];
    }

    public function confirmed()
    {
        if (Organization::find($this->theIdToDelete)->delete()) {
            $this->alert("success", "Organization Deleted Successfully");
        };
    }

    public function clear()
    {
        $this->selectedAdmins = [];
        $this->add_description = '';
        $this->add_orgName = '';
        $this->selectedOrgId = '';
        $this->selectedOrgName = '';
        $this->selectedOrgParentID = '';
        $this->selectedOrgParentName = '';
        $this->selectedOrgUserGroups = [];
        $this->selectedOrgUserGroupsIds = [];
        $this->selectedOrgAdmins = [];
        $this->selectedOrgAdminsIds = [];
        $this->selectedOrgDescription = '';
        $this->searchUserGroupsEditModal = '';
        $this->searchAdminsEditModal = '';
        $this->selectedUserGroups_add = [];
        $this->resetValidation();

        // skill
        $this->added_type = null;
        $this->added_lang = null;
        $this->added_name = null;
        $this->added_acdid = null;
        $this->added_desc = null;

        // group 
        $this->added_org = null;
        $this->added_name = null;
        $this->added_supervisor = null;
        $this->added_org_id = null;
        $this->selected_supervisors =  [];
        $this->selected_agents = [];
        $this->selectedGroupId = null;
        $this->selectedGroupName  = null;
        $this->selectedGroupOrg = null;
        $this->selectedGroupOrg_id = null;
        $this->selectedGroupSupervisors = [];
        $this->selectedGroupAgents = [];
        $this->theIdToDelete = null;
    }

    public function editOrg()
    {

        $this->editOrgModal = true;

        // validate 
        $this->validate();

        // Save the org
        $org = Organization::find($this->selectedOrgId);
        $org->update([
            'name' => $this->selectedOrgName,
            'description' => $this->selectedOrgDescription,
        ]);

        // Sync the admins 
        $adminUserIds = array_keys($this->selectedOrgAdmins);
        $org->admins()->sync($adminUserIds);

        // sync the user group 
        $userGroups = UserGroup::all();

        foreach ($userGroups as $userGroup) {
            if (in_array($userGroup->id, array_keys($this->selectedOrgUserGroups))) {
                $userGroup->organization_id = $org->id;
            } else {
                $userGroup->organization_id = null;
            }

            $userGroup->save();
        }


        // alert
        $this->alert('success', "Organization Edited Successfully", [
            'timerProgressBar' => true,
            'timer' => '4000',
        ]);

        $this->closeModal();
    }

    public function showDeleteAlert($id)
    {
        $this->theIdToDelete = $id;
        $this->alert('warning', 'Are you sure you want to delete this Organization?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    // add modal 
    public function select_admin($id)
    {
        $this->selectedAdmins[$id] = User::find($id)->full_name;
    }

    // add modal
    public function selectUserGroup_add($id)
    {
        $userGroup = UserGroup::find($id);

        if ($userGroup && !in_array($id, $this->selectedUserGroups_add)) {
            $this->selectedUserGroups_add[$id] = $userGroup->name;
        }

        $this->searchUserGroupsEditModal = '';
    }

    // add modal 
    public function removeUserGroup_add($index)
    {
        unset($this->selectedUserGroups_add[$index]);
    }

    // add modal
    public function remove_admin($id)
    {
        unset($this->selectedAdmins[$id]);
    }

    public function rules()
    {
        $rules = [];

        if ($this->editOrgModal) {
            $rules = [
                'selectedOrgName' => ['required', 'string', 'max:100', Rule::unique('organizations', 'name')->ignore($this->selectedOrgId)],
                'selectedOrgDescription' => ['nullable', 'string', 'max:255', 'required_if:add_description,text'],
            ];
        } else {
            $rules = [
                'add_orgName' => ['required', 'string', 'max:100', 'unique:organizations,name'],
                'add_description' => ['nullable', 'string', 'max:255', 'required_if:add_description,text'],
            ];
        }
        return $rules;
    }

    // add modal 
    public function addOrg()
    {
        // dd($this->selectedAdmins);
        // validate the name 
        $this->validate();

        // dd($this->selectedAdmins);
        // save the org into Org table 
        $org = Organization::create([
            'name' => $this->add_orgName,
            'description' => $this->add_description
        ]);

        // add the org to the selected admins 
        // foreach ($this->selectedAdmins as $userId => $fullName) {
        //     $user = User::find($userId);
        //     if ($user) {
        //         $org->admins()->sync($user);
        //     }
        // }

        // save the admins 
        $org->users()->sync(array_keys($this->selectedAdmins));

        // save the user groups 
        foreach (array_keys($this->selectedUserGroups_add) as $userGroupId) {
            $userGroup = UserGroup::findOrFail($userGroupId);
            $userGroup->organization_id = $org->id;
            $userGroup->save();
        }

        // alert
        $this->alert('success', "Organization Added Successfully", [
            'timerProgressBar' => true,
            'timer' => '4000',
        ]);

        $this->closeModal();


        // dd($this->add_description, $this->add_orgName, $this->selectedAdmins);
    }

    public function closeModal()
    {
        $this->dispatch('closeModal');
    }

    public function render()
    {

        return view('livewire.organizations', [
            'organizations' => Organization::search($this->searchOrg)->orderBy($this->sortBy, $this->sortDir)->paginate($this->perPage),
            'userGroups' => UserGroup::all(),
            'allAdmins' => User::where('role', 1)->get(),

            // for edit modal, possible groups should be those with no org already
            'possibleUserGroups' => UserGroup::where('name', 'like', "%$this->searchUserGroupsEditModal%")->whereNull('organization_id')->get(),
            'possibleUserGroups_add' => UserGroup::where('name', 'like', "%$this->searchUserGroupsEditModal%")->get(),
            'possibleAdmins' => User::where('role', 1)->get(),
        ]);
    }
}
