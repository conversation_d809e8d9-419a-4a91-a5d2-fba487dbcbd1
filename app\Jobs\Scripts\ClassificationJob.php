<?php

namespace App\Jobs\Scripts;

use App\Models\Interaction;
use App\Models\TranscriptionClassifications;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class ClassificationJob implements ShouldQueue
{
    use Batchable, InteractsWithQueue, Queueable, SerializesModels;

    public $callConversation;
    public $call_id;
    public $timeout = 14400;


    public function __construct($callConversation,$call_id)
    {
        $this->callConversation = $callConversation;
        $this->call_id = $call_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $texts =[];

            $exePath = storage_path('app/scripts/run_classification.sh');
//            Interaction::where('call_id','like','%'.$call_id.'%')->update(['ai_flag'=>1]);
            $newCallId = Interaction::where('call_id','like','%'.$this->callConversation[0]->call_id.'%')->first()->call_id;

            foreach ($this->callConversation as $textData){

                $inputData = [
                    'text' => mb_convert_encoding($textData->content, 'UTF-8', 'auto'),
                    'calls_transcription_id' => $textData->id,
                ];
                $texts[] = json_encode($inputData); // JSON encode text and ID
                Log::channel('classification')->info($textData->content);
            }


            $process = new Process(array_merge([$exePath], $texts));
            $process->setEnv([
                'LANG' => 'en_US.UTF-8',
                'PYTHONIOENCODING' => 'utf-8',
            ]);
            $process->setTimeout(360);
            $process->run();

            // Check for errors
            if (!$process->isSuccessful()) {
                Log::channel('classification')->info($process->getErrorOutput());
            }

            $data = json_decode($process->getOutput(), true);

            Log::channel('classification')->info($process->getOutput());
            foreach ($data as $value) {


                if(isset($value['score'])){
                    TranscriptionClassifications::create([
                        'score' => $value['score'],
                        'text' => $value['text'],
                        'language' => $value['language'],
                        'classification' => strtolower($value['classification']),
                        'calls_transcription_id' => $value['calls_transcription_id'], // Save ID from response
                        'call_id' => $newCallId,
                    ]);
                }
            }

            $defaultClassifications = [
                'positive' => 0,
                'negative' => 0,
                'neutral' => 0,
            ];

            // Fetch classifications from the database
            $classifications = TranscriptionClassifications::selectRaw('
                    LOWER(classification) as classification,
                    ROUND((COUNT(*) * 100.0) / (SELECT COUNT(*) FROM transcription_classifications WHERE call_id = ?), 1) as average_percentage
                ', [$newCallId])
                ->where('call_id', $newCallId)
                ->groupByRaw('LOWER(classification)')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->classification => $item->average_percentage];
                })
                ->toArray();

            $data = array_merge($defaultClassifications, $classifications);

            if (100 - $data['negative'] < 90){
                Interaction::where('call_id', $newCallId)->update(['ai_flag' => 1]);
            }
        } catch (\Exception $e) {
            Log::channel('classification')->error('Error in DetectJob: ' . $e->getMessage(), [
                'callConversation' => $this->callConversation,
                'exception' => $e,
            ]);
        }
    }
}
