<?php
namespace App\Jobs\Scripts;

use App\Models\Interaction;
use App\Models\TranscriptionTopics;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class TopicsJob implements ShouldQueue
{
    use Batchable, InteractsWithQueue, Queueable, SerializesModels;

    public $callConversation;
    public $call_id;
    public $lang;
    public $timeout = 14400;

    public function __construct($callConversation, $call_id, $lang)
    {
        $this->callConversation = $callConversation;
        $this->call_id          = $call_id;
        $this->lang             = $lang;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->lang == 'English' ? $this->lang = 'en' : $this->lang = 'ar';
        try {

            $newCallId = Interaction::where('call_id', 'like', '%' . $this->callConversation[0]->call_id . '%')->first()->call_id;
            $text      = "";
            foreach ($this->callConversation as $textData) {

                $text .= $textData->content . ",";
            }

            $exePath = storage_path('app/scripts/run_keyword_and_topics.sh');

            $text = mb_convert_encoding($text, 'UTF-8', 'auto');

            // Execute the .exe file
            $process = new Process([$exePath, $text, '--lang=' . $this->lang]);

            $process->setEnv([
                'LANG'             => 'en_US.UTF-8', // Ensure UTF-8 locale
                'PYTHONIOENCODING' => 'utf-8',       // Force Python to use UTF-8
            ]);
            $process->setTimeout(360);
            $process->run();

            Log::channel('topics')->info("Standard Output: " . $process->getOutput() . PHP_EOL);
            // Check for errors
            if (! $process->isSuccessful()) {
                Log::channel('topics')->info($process->getErrorOutput());

            }

            Log::channel('topics')->info('done');
            Log::channel('topics')->info($process->getOutput());

            $data = json_decode($process->getOutput(), true);

            $topics = json_encode($data['Detected Topics'], JSON_UNESCAPED_UNICODE);

            TranscriptionTopics::create([
                'detected_topics' => $topics,
                'call_id'         => $newCallId,
            ]);

        } catch (\Exception $e) {
            Log::channel('topics')->error('Error in DetectJob: ' . $e->getMessage(), [
                'callConversation' => $this->callConversation,
                'exception'        => $e,
            ]);

            throw $e;
        }

    }
}
