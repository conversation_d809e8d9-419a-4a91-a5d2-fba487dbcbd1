<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qa_flags', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('organization_id');
            $table->foreign('organization_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');

            $table->string('name');
            $table->date('start_date');
            $table->date('end_date');

            $table->integer('interactions_number');
            $table->integer('per');
            $table->string('time_interval')->comment('day, month, or week');
            
            $table->string('distribution_level');
            $table->string('screen_capture')->nullable();
            $table->string('call_type');

            $table->json('interaction_days')->comment('saturday 1 to friday 7');

            $table->time('interaction_time_from')->comment('null means all time')->nullable();
            $table->time('interaction_time_to')->comment('null means all time')->nullable();

            $table->time('duration_start')->comment('in seconds')->nullable();
            $table->time('duration_end')->comment('in seconds, null except when between is used')->nullable();
            $table->string('duration_condition')->comment('=, >, <, between')->nullable();

            $table->boolean('enabled')->default(1);
            $table->boolean('active')->default(1);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qa_flags');
    }
};
