<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TestFileWriteCommand extends Command
{
    protected $signature = 'test:file-write';

    protected $description = 'Test file writing operation';

    public function handle()
    {
        $localFilePath = storage_path('app/ftp-files/test-file.txt');
        $contents = 'Testing file write operation';

        // Attempt to write to the file
        $result = file_put_contents($localFilePath, $contents);

        if ($result !== false) {
            $this->info("File successfully written.");
        } else {
            $this->error("Failed to write the file.");
        }
    }
}
