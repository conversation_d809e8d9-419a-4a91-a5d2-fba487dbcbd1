<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        //$schedule->command('app:FetchFinalData')->everyTenMinutes();
        // $schedule->command('inspire')->hourly();
        // $schedule->command('app:fetchDatabase')->hourly();
        // $schedule->command('app:delayedOracleData')->cron('20 * * * *');
        // $schedule->command('qa_flags:updateActiveState')->dailyAt('23:58');
        // $schedule->command('flags:set')->dailyAt('00:05');
        // $schedule->command('zara:sendCalls')->dailyAt('03:25');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

}
