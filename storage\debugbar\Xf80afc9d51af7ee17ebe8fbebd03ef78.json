{"__meta": {"id": "Xf80afc9d51af7ee17ebe8fbebd03ef78", "datetime": "2025-07-22 15:52:48", "utime": **********.767935, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.17603, "end": **********.767987, "duration": 0.5919570922851562, "duration_str": "592ms", "measures": [{"label": "Booting", "start": **********.17603, "relative_start": 0, "end": **********.524124, "relative_end": **********.524124, "duration": 0.34809398651123047, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.524144, "relative_start": 0.348114013671875, "end": **********.76799, "relative_end": 3.0994415283203125e-06, "duration": 0.24384617805480957, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29211360, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.analytics-new", "param_count": null, "params": [], "start": **********.754448, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/analytics-new.blade.phplivewire.analytics-new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Fanalytics-new.blade.php&line=1", "ajax": false, "filename": "analytics-new.blade.php", "line": "?"}}, {"name": "components.swipe", "param_count": null, "params": [], "start": **********.760401, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/components/swipe.blade.phpcomponents.swipe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Fcomponents%2Fswipe.blade.php&line=1", "ajax": false, "filename": "swipe.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Livewire\\AnalyticsNew@getDate", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=3284\" onclick=\"\">app/Livewire/AnalyticsNew.php:3284-3323</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.04718, "accumulated_duration_str": "47.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.590249, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "ilog", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 36 limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.5950658, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ilog", "start_percent": 0, "width_percent": 6.867}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 397}, {"index": 21, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.63748, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:397", "source": "app/Livewire/AnalyticsNew.php:397", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=397", "ajax": false, "filename": "AnalyticsNew.php", "line": "397"}, "connection": "ilog", "start_percent": 6.867, "width_percent": 2.141}, {"sql": "SELECT AVG(quality_percentage) AS avg_quality, COUNT(a.id) AS total_qa_submissions\nFROM evaluation_submissions a\nINNER JOIN evaluations b ON a.evaluation_id = b.id\nWHERE\na.created_at BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND\nb.organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 650}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.644808, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:650", "source": "app/Livewire/AnalyticsNew.php:650", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=650", "ajax": false, "filename": "AnalyticsNew.php", "line": "650"}, "connection": "ilog", "start_percent": 9.008, "width_percent": 5.532}, {"sql": "SELECT COUNT(call_id) AS totalCalls,call_type FROM interactions   WHERE arrival_time BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50) GROUP BY call_type", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 660}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.6537821, "duration": 0.00666, "duration_str": "6.66ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:660", "source": "app/Livewire/AnalyticsNew.php:660", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=660", "ajax": false, "filename": "AnalyticsNew.php", "line": "660"}, "connection": "ilog", "start_percent": 14.54, "width_percent": 14.116}, {"sql": "SELECT\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,\nAVG(ring) AS avg_ring,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,\nSUM(call_duration) AS total_ring,\nCOUNT(call_id) AS total_calls\nFROM interactions\nWHERE arrival_time BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 667}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.664637, "duration": 0.00751, "duration_str": "7.51ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:667", "source": "app/Livewire/AnalyticsNew.php:667", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=667", "ajax": false, "filename": "AnalyticsNew.php", "line": "667"}, "connection": "ilog", "start_percent": 28.656, "width_percent": 15.918}, {"sql": "SELECT\nCOUNT(CASE WHEN call_duration < '00:02:00' THEN 1 END) AS less_than_2_minutes,\nCOUNT(CASE WHEN call_duration >= '00:08:00' THEN 1 END) AS greater_or_equal_8_minutes,\nCOUNT(CASE WHEN hold_duration >= '00:02:00' THEN 1 END) AS greater_or_equal_2_minutes_hold\nFROM interactions\nWHERE arrival_time BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 685}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.676625, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:685", "source": "app/Livewire/AnalyticsNew.php:685", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=685", "ajax": false, "filename": "AnalyticsNew.php", "line": "685"}, "connection": "ilog", "start_percent": 44.574, "width_percent": 8.669}, {"sql": "SELECT\nCOUNT(a.interaction_id) AS totalFlags\nFROM interaction_qa_flag a INNER JOIN interactions b\nON a.interaction_id = b.id AND\narrival_time BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 700}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.685864, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:700", "source": "app/Livewire/AnalyticsNew.php:700", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=700", "ajax": false, "filename": "AnalyticsNew.php", "line": "700"}, "connection": "ilog", "start_percent": 53.243, "width_percent": 2.098}, {"sql": "SELECT\nCOUNT(*) AS totalaiFlags\nFROM interactions WHERE\narrival_time BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND ai_flag = '1'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 711}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3321}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.691036, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:711", "source": "app/Livewire/AnalyticsNew.php:711", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=711", "ajax": false, "filename": "AnalyticsNew.php", "line": "711"}, "connection": "ilog", "start_percent": 55.341, "width_percent": 6.867}, {"sql": "select * from `organizations` where `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2232}, {"index": 17, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3322}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.6991, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2232", "source": "app/Livewire/AnalyticsNew.php:2232", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2232", "ajax": false, "filename": "AnalyticsNew.php", "line": "2232"}, "connection": "ilog", "start_percent": 62.209, "width_percent": 1.272}, {"sql": "select * from `user_groups` where `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2233}, {"index": 17, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3322}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.704077, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2233", "source": "app/Livewire/AnalyticsNew.php:2233", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2233", "ajax": false, "filename": "AnalyticsNew.php", "line": "2233"}, "connection": "ilog", "start_percent": 63.48, "width_percent": 0.975}, {"sql": "select * from `organizations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2234}, {"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3322}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.708761, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2234", "source": "app/Livewire/AnalyticsNew.php:2234", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2234", "ajax": false, "filename": "AnalyticsNew.php", "line": "2234"}, "connection": "ilog", "start_percent": 64.455, "width_percent": 1.526}, {"sql": "SELECT DATE_FORMAT(arrival_time, \"%Y-%m-%d\") AS receiving_hour,\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,\nCOUNT(call_id) AS total_calls\nFROM interactions\nWHERE arrival_time BETWEEN '2025-05-24 00:00:00' AND '2025-07-22 15:52:48'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)\nGROUP BY receiving_hour\nORDER BY receiving_hour", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2256}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3322}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.71419, "duration": 0.0094, "duration_str": "9.4ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2256", "source": "app/Livewire/AnalyticsNew.php:2256", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2256", "ajax": false, "filename": "AnalyticsNew.php", "line": "2256"}, "connection": "ilog", "start_percent": 65.981, "width_percent": 19.924}, {"sql": "select\nCOUNT(*) as total_calls,\nSUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,\nSUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,\nSUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections\nfrom `interactions` where `arrival_time` between '2025-05-24 00:00:00' and '2025-07-22 15:52:48' limit 1", "type": "query", "params": [], "bindings": ["2025-05-24 00:00:00", "2025-07-22 15:52:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3392}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.729449, "duration": 0.00571, "duration_str": "5.71ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:3392", "source": "app/Livewire/AnalyticsNew.php:3392", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=3392", "ajax": false, "filename": "AnalyticsNew.php", "line": "3392"}, "connection": "ilog", "start_percent": 85.905, "width_percent": 12.103}, {"sql": "select * from `organizations` where `id` in (4, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50)", "type": "query", "params": [], "bindings": ["4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 124}, {"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3417}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.739678, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:124", "source": "app/Livewire/AnalyticsNew.php:124", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=124", "ajax": false, "filename": "AnalyticsNew.php", "line": "124"}, "connection": "ilog", "start_percent": 98.008, "width_percent": 1.992}]}, "models": {"data": {"App\\Models\\Organization": {"value": 151, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Interaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FInteraction.php&line=1", "ajax": false, "filename": "Interaction.php", "line": "?"}}}, "count": 153, "is_counter": true}, "livewire": {"data": {"analytics-new #8H2k68ZyXbGMiMRMj0z7": "array:4 [\n  \"data\" => array:23 [\n    \"totalCallDurationAllGroups\" => null\n    \"selected_group\" => null\n    \"disconnected_by_customer\" => 62.0\n    \"disconnected_by_agent\" => 24.0\n    \"disconnected_by_system\" => 0.0\n    \"accountIDFilter\" => null\n    \"accountNameFilter\" => null\n    \"dateFrom\" => Carbon\\Carbon @********** {#762\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000002fa0000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-05-24 00:00:00.0 Asia/Amman (+03:00)\n    }\n    \"dateTo\" => Carbon\\Carbon @********** {#850\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000003520000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-22 15:52:48.626828 Asia/Amman (+03:00)\n    }\n    \"dateFromFilter\" => null\n    \"dateToFilter\" => null\n    \"dateType\" => \"Last 60 Days\"\n    \"groupsAccount\" => null\n    \"dataPage\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => \"00:07:13\"\n      \"avg_hold_time\" => \"00:00:06\"\n      \"total_hold_time\" => \"07:15:18\"\n      \"total_interactions\" => 3971\n      \"short_call_duration_count\" => 10\n      \"long_call_duration_count\" => 931\n      \"long_hold_duration_count\" => 73\n      \"total_call_duration\" => \"478:20:12\"\n      \"total_outbound\" => 340\n      \"total_inbound\" => 3631\n      \"countEvaluation\" => 231\n      \"avgEvaluationScore\" => 86.************\n      \"totalRing\" => \"2832892\"\n      \"averageRing\" => 1.8\n      \"totalHandledCalls\" => 3971\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 192\n    ]\n    \"dataPage2\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => \"00:07:13\"\n      \"avg_hold_time\" => \"00:00:06\"\n      \"total_hold_time\" => \"07:15:18\"\n      \"total_interactions\" => 3971\n      \"short_call_duration_count\" => 10\n      \"long_call_duration_count\" => 931\n      \"long_hold_duration_count\" => 73\n      \"total_call_duration\" => \"478:20:12\"\n      \"total_outbound\" => 340\n      \"total_inbound\" => 3631\n      \"countEvaluation\" => 231\n      \"avgEvaluationScore\" => 86.************\n      \"totalRing\" => \"2832892\"\n      \"averageRing\" => 1.8\n      \"totalHandledCalls\" => 3971\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 192\n    ]\n    \"searchGroup\" => null\n    \"groupSelected\" => null\n    \"editFlag\" => false\n    \"userSettings\" => []\n    \"cardSelected\" => \"Avg Call Duration Card\"\n    \"role\" => 2\n    \"page\" => \"pageOne\"\n    \"queryGrouFormat\" => \"%Y-%m-%d\"\n  ]\n  \"name\" => \"analytics-new\"\n  \"component\" => \"App\\Livewire\\AnalyticsNew\"\n  \"id\" => \"8H2k68ZyXbGMiMRMj0z7\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/analytics-new\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "36", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753187629\n]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f739b60-f943-4d84-8e18-b90fb4fb1a2d\" target=\"_blank\">View in Telescope</a>", "path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-1314581681 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1314581681\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1488330170 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1488330170\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-724545052 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1745 characters\">{&quot;data&quot;:{&quot;totalCallDurationAllGroups&quot;:null,&quot;selected_group&quot;:null,&quot;disconnected_by_customer&quot;:0,&quot;disconnected_by_agent&quot;:0,&quot;disconnected_by_system&quot;:0,&quot;accountIDFilter&quot;:null,&quot;accountNameFilter&quot;:null,&quot;dateFrom&quot;:[&quot;2025-07-21T15:52:33+03:00&quot;,{&quot;type&quot;:&quot;carbon&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;dateTo&quot;:[&quot;2025-07-22T15:52:33+03:00&quot;,{&quot;type&quot;:&quot;carbon&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;dateFromFilter&quot;:null,&quot;dateToFilter&quot;:null,&quot;dateType&quot;:&quot;Last 24 Hours&quot;,&quot;groupsAccount&quot;:null,&quot;dataPage&quot;:[{&quot;group_id&quot;:&quot;All&quot;,&quot;group_name&quot;:&quot;All&quot;,&quot;avg_interactions_duration&quot;:null,&quot;avg_hold_time&quot;:null,&quot;total_hold_time&quot;:null,&quot;total_interactions&quot;:0,&quot;short_call_duration_count&quot;:0,&quot;long_call_duration_count&quot;:0,&quot;long_hold_duration_count&quot;:0,&quot;total_call_duration&quot;:null,&quot;total_outbound&quot;:0,&quot;total_inbound&quot;:0,&quot;countEvaluation&quot;:0,&quot;avgEvaluationScore&quot;:null,&quot;totalRing&quot;:null,&quot;averageRing&quot;:0,&quot;totalHandledCalls&quot;:0,&quot;qaFlagsCount&quot;:0,&quot;aiFlagsCount&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],&quot;dataPage2&quot;:[{&quot;group_id&quot;:&quot;All&quot;,&quot;group_name&quot;:&quot;All&quot;,&quot;avg_interactions_duration&quot;:null,&quot;avg_hold_time&quot;:null,&quot;total_hold_time&quot;:null,&quot;total_interactions&quot;:0,&quot;short_call_duration_count&quot;:0,&quot;long_call_duration_count&quot;:0,&quot;long_hold_duration_count&quot;:0,&quot;total_call_duration&quot;:null,&quot;total_outbound&quot;:0,&quot;total_inbound&quot;:0,&quot;countEvaluation&quot;:0,&quot;avgEvaluationScore&quot;:null,&quot;totalRing&quot;:null,&quot;averageRing&quot;:0,&quot;totalHandledCalls&quot;:0,&quot;qaFlagsCount&quot;:0,&quot;aiFlagsCount&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchGroup&quot;:null,&quot;groupSelected&quot;:null,&quot;editFlag&quot;:false,&quot;userSettings&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;cardSelected&quot;:&quot;Avg Call Duration Card&quot;,&quot;role&quot;:2,&quot;page&quot;:&quot;pageOne&quot;,&quot;queryGrouFormat&quot;:&quot;%H:00&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;8H2k68ZyXbGMiMRMj0z7&quot;,&quot;name&quot;:&quot;analytics-new&quot;,&quot;path&quot;:&quot;analytics-new&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;00c37c7909764cbb64d69f0ad5b6a1f317c6b19ce950aa429c5140741922c1c2&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">getDate</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Last 60 Days</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724545052\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1802947365 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2115</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1253 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndORVZmZm1CQWx1OVVtcUxhcW9Wbnc9PSIsInZhbHVlIjoiNEJpVnZuTkZPUmgxdk1ndVNLeXltN3hiL3lRMkpmckV4Y3ZFczI4Qm0xaXM0eldYak1IU1NYQitGUlhyVUFsODNvTWVWN3NxV2p2bEpkbEc0R1NVWDF2Q0d5dTgrQTgyQWZTNFBMQTFqY25YU1F2UldyNDhxaUNFZ1FiSSthUWpaakExbUJPM0h2cDlyMHI3YU1BaC82d09tYlRzS29EbFpwRTNYVU5GbGY0clN1bTFBTlQzekk0Wi96RUR5aUdwZWtIRFdGaENSYjBDQjlxNnlNOFdKeFg5dXZWQ0twbGNSMkRGcVFGREJ6QT0iLCJtYWMiOiJjYWYxMjVmN2NiZGM3YzJlMDVmYWQ1NjI3YzdjZGQxZmE0MmYyMTVmZDQ5MGY2NTA2NDU3ODc1NThiNTAwYWFmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inl2UHJ0aEoyTGp0YzJ2VmlZU0NCVFE9PSIsInZhbHVlIjoiRnRuQmFnOUl5SW1kaGlVaEFmTDJaTitOZ054L0lzL0hwTUs0VmxRUk9TZTRhUnF3WDl0VFA4clo5WjFrU0pUTnpjUnBqbmh5VmpndUx3MDE3bmlZcEsxZkIwNGxmSDJiajY2SldGelFweU1tTDdacHNnMS96RElXTHNYNms1aXgiLCJtYWMiOiIxOGFiMDAwZTUyMDBhZGQwNmE3MDE3YjUwNzNmNmJiMmU5NjNhZTI1YmJiM2Y4MGFiODBmN2JjNjQ1ZGQ3Zjk4IiwidGFnIjoiIn0%3D; i_log_session=eyJpdiI6InczOGhwaVBTUmwyeEQ1b0R5RnpsUFE9PSIsInZhbHVlIjoiNCtrVDYyVDVFN0RJZTlUbUxhK1A2aVNZdWt2aG1DRStsSllYd0NTRFBQd1BYKzFrT0oxM0VHRWR1ZFpmOUlqQjJENnlmenNJZHZPQjg5SDhCbXdtNnZYYzYwOHZGakE5azRaa0laZUJwV2lMSmp0OWtTaDkrcVgrL3pEU2FXR3kiLCJtYWMiOiI3ZjkyZjhkZWU3ZmRlMGZjNjFiMGJmODE1ZjAzYTM0ZDZiOTdhZjM5Y2QzZmU0ZDE0NzA0NjQ1NWRjYTZhMDk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802947365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-117861653 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">36|5ib4AQhtLHzATEGHqeW0FyHXea5XYBhkXcIWMbiaY723twUGMepP4FnyoU3L|$2y$12$sKmCpsW.aT14AEFPyOgHeO2lhV7BRWjK7d1ywUnOncv84uKprV4di</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>i_log_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3Bna7bWbMH5plZLa1koljiLCJBixIU0hg7L8yoS8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117861653\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1659749951 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 12:52:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6IjFIUW5aOUpwM3ZJeTl6VzA4WlpWd0E9PSIsInZhbHVlIjoiVXJrQ3RKb2dqbzBiaGFYTUtZbnR5dUdOK2hMVDl1bzhwNmUvT3JLYldncVpQZ1Nrd2ptSGRQVzNKT3ltU1V6Qkl3aFpSM1BQYnFvM3o2eHlTbC9pRjY2TWowcEQzR2pDdUhUd2NwS2dQNEwxR09SYlhqMWNrRjQxQVkyY3ZzTUIiLCJtYWMiOiJmZmI5ZGY3NTc4OTUzZDFlYjE3OGIwYTBlNDdjMjRmNjU2Mjk5M2NhODVhNjQxNWQzMjMyNDZjNTYzMTA1OTQzIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:52:48 GMT; Max-Age=21600; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">i_log_session=eyJpdiI6IndUem42RUdHMzIxcEx2bVdZNVIxR0E9PSIsInZhbHVlIjoiQktuc1FvMDBhODZONkdRNFU4eW5qMEhVcWE3S2R3a0dSbFZRekhBbnkxZmxJUHFudFpkY3IvQ2loRE1qRnZwVkh1alNQc1o2cnhyT0MwVjQvNTZoYjhGTTljZkhlQ3dOUSszYWRHRFFhQnB2SWdrV0M5eXE3TnpydysrL3I0c3IiLCJtYWMiOiIwMGI3YTFlMDZkYmQ5N2E0NGU4NmU3MzhiNzVlMWVkMjA5OGNlNjg0MDNjYzMyZDI4M2IzZjQ3OTdhMzFiNjVlIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:52:48 GMT; Max-Age=21600; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFIUW5aOUpwM3ZJeTl6VzA4WlpWd0E9PSIsInZhbHVlIjoiVXJrQ3RKb2dqbzBiaGFYTUtZbnR5dUdOK2hMVDl1bzhwNmUvT3JLYldncVpQZ1Nrd2ptSGRQVzNKT3ltU1V6Qkl3aFpSM1BQYnFvM3o2eHlTbC9pRjY2TWowcEQzR2pDdUhUd2NwS2dQNEwxR09SYlhqMWNrRjQxQVkyY3ZzTUIiLCJtYWMiOiJmZmI5ZGY3NTc4OTUzZDFlYjE3OGIwYTBlNDdjMjRmNjU2Mjk5M2NhODVhNjQxNWQzMjMyNDZjNTYzMTA1OTQzIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:52:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">i_log_session=eyJpdiI6IndUem42RUdHMzIxcEx2bVdZNVIxR0E9PSIsInZhbHVlIjoiQktuc1FvMDBhODZONkdRNFU4eW5qMEhVcWE3S2R3a0dSbFZRekhBbnkxZmxJUHFudFpkY3IvQ2loRE1qRnZwVkh1alNQc1o2cnhyT0MwVjQvNTZoYjhGTTljZkhlQ3dOUSszYWRHRFFhQnB2SWdrV0M5eXE3TnpydysrL3I0c3IiLCJtYWMiOiIwMGI3YTFlMDZkYmQ5N2E0NGU4NmU3MzhiNzVlMWVkMjA5OGNlNjg0MDNjYzMyZDI4M2IzZjQ3OTdhMzFiNjVlIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:52:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1659749951\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-819961293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>36</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753187629</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819961293\", {\"maxDepth\":0})</script>\n"}}