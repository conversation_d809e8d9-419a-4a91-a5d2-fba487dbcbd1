<?php
namespace App\Exports;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TransactionReasonExport implements FromCollection, WithHeadings, ShouldAutoSize, WithChunkReading, ShouldQueue
{
    use Exportable;

    // Filter properties
    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_account;

    /**
     * CallReasonExport constructor.
     */
    public function __construct(
        $filter_time_name,
        $filter_time_days,
        $custom_date_from,
        $custom_date_to,
        $filter_callId,
        $filter_account
    ) {
        $this->filter_time_name = $filter_time_name;
        $this->filter_time_days = $filter_time_days;
        $this->custom_date_from = $custom_date_from;
        $this->custom_date_to   = $custom_date_to;
        $this->filter_callId    = $filter_callId;
        $this->filter_account   = $filter_account;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // Set memory and execution limits
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 300);

        // Base query joining interactions with calls_summary table
        /*      $query = DB::table('interactions')
            ->leftJoin('calls_summary', function($join) {
                $join->on(DB::raw("SUBSTRING_INDEX(interactions.call_id, '@', 1)"), '=', 'calls_summary.call_id');
            })
            ->leftJoin('organizations', 'interactions.organization_id', '=', 'organizations.id')
            ->select([
                'interactions.call_id as transaction_id',
                'organizations.name as account',
                DB::raw("'Call' as type_call"),
                'calls_summary.call_type as call_reason',
                'calls_summary.sub_type as sub_call_reason',
                'interactions.arrival_time'
            ])
            ->orderByDesc('interactions.arrival_time'); */

        $latestSummarySubquery = DB::table('calls_summary as cs1')
            ->select('cs1.id', 'cs1.call_id', 'cs1.call_type', 'cs1.sub_type')
            ->whereRaw('cs1.id = (
        SELECT MAX(cs2.id)
        FROM calls_summary as cs2
        WHERE cs2.call_id = cs1.call_id
    )');

        $query = DB::table('interactions')
            ->joinSub($latestSummarySubquery, 'latest_summary', function ($join) {
                $join->on(DB::raw("SUBSTRING_INDEX(interactions.call_id, '@', 1)"), '=', 'latest_summary.call_id');
            })
            ->join('organizations', 'interactions.organization_id', '=', 'organizations.id')
            ->select([
                'interactions.call_id as transaction_id',
                'organizations.name as account',
                DB::raw("'Call' as type_call"),
                'latest_summary.call_type as call_reason',
                'latest_summary.sub_type as sub_call_reason',
                'interactions.arrival_time',
                'interactions.id as interaction_id',
            ])
            ->orderByDesc('interactions.arrival_time');

        // Apply role-based filtering
        if (Auth::user()->role == 4) {
            $query->where('interactions.user_id', Auth::id());
        }

        // Apply filters
        if ($this->filter_callId) {
            $query->where('interactions.call_id', 'like', "%{$this->filter_callId}%");
        }

        if ($this->filter_account && $this->filter_account !== 'All') {
            $query->where('organizations.name', 'like', "%{$this->filter_account}%");
        }

        // Time filter
        if ($this->filter_time_name == 'Custom' && $this->custom_date_from && $this->custom_date_to) {
            $custom_date_from = $this->custom_date_from . ' 00:00:00';
            $custom_date_to   = $this->custom_date_to . ' 23:59:59';
            $query->whereBetween('interactions.arrival_time', [$custom_date_from, $custom_date_to]);
        } elseif ($this->filter_time_days && $this->filter_time_name != 'Custom') {
            $startDate = now()->subDays($this->filter_time_days)->toDateString();
            $query->whereDate('interactions.arrival_time', '>=', $startDate);
        }

        return $query->get()->map(function ($record) {
            return [
                'Transaction ID'  => $record->transaction_id ?? '-',
                'Account'         => $record->account ?? '-',
                'Type Call'       => $record->type_call ?? '-',
                'Call Reason'     => $record->call_reason ?? '-',
                'Sub Call Reason' => $record->sub_call_reason ?? '-',
                'Date Time'       => $record->arrival_time ? \Carbon\Carbon::parse($record->arrival_time)->format('Y-m-d h:i A') : '-',
            ];
        });
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Transaction ID',
            'Account',
            'Type Call',
            'Call Reason',
            'Sub Call Reason',
            'Date Time',
        ];
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 1000;
    }
}
