<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('evaluation_submissions', function (Blueprint $table) {

            $table->id();
            $table->timestamps();
            $table->foreignId('evaluation_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->unsignedBigInteger('created_by'); // Use unsignedBigInteger instead of bigInteger
            $table->foreign('created_by')->references('id')->on('users'); // Ensure the id column in the users table is indexed
        
            $table->string('name');
            $table->foreignId('user_id')->constrained(); //foreignId with users table
            $table->string('source');
            $table->string('year');
            $table->string('month');
            $table->unsignedBigInteger('referenceID');
            $table->foreign('referenceID')->references('id')->on('interactions');

            //foreignId with users table
            $table->string('week');
            $table->longText('commentEvaluation')->nullable();
            $table->string('total_weight_points');
            $table->string('total_possible_weighted_points');
            $table->string('total_score');
            $table->string('total_possible_score');
            $table->string('quality_percentage');
            $table->date('date')->nullable();
            $table->json('extra_field_one')->nullable();
            $table->json('extra_field_tow')->nullable();
            $table->json('extra_field_three')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('evaluation_submissions');
    }
};
