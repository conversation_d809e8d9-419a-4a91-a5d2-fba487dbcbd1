<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class PrefixesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            ['prefix' => '50', 'account' => 'Hertz', 'user_group' => 'Inbound'],
            ['prefix' => '51', 'account' => 'Hertz', 'user_group' => 'Outbound'],
            ['prefix' => '52', 'account' => 'Modanisa', 'user_group' => 'Blending'],
            ['prefix' => '53', 'account' => 'Modanisa', 'user_group' => 'Inbound'],
            ['prefix' => '54', 'account' => 'Modanisa', 'user_group' => 'Outbound'],
            ['prefix' => '55', 'account' => 'Modanisa', 'user_group' => 'Chat'],
            ['prefix' => '56', 'account' => 'Jollychic', 'user_group' => 'Inbound'],
            ['prefix' => '57', 'account' => 'Jollychic', 'user_group' => 'Outbound'],
            ['prefix' => '58', 'account' => 'Jollychic', 'user_group' => 'Manual'],
            ['prefix' => '59', 'account' => 'Jollychic', 'user_group' => 'Chat'],
            ['prefix' => '60', 'account' => 'SaudiAirline', 'user_group' => 'GCC'],
            ['prefix' => '61', 'account' => 'SaudiAirline', 'user_group' => 'Europe'],
            ['prefix' => '62', 'account' => 'SaudiAirline', 'user_group' => 'US'],
            ['prefix' => '63', 'account' => 'SaudiAirline', 'user_group' => 'Outbound'],
            ['prefix' => '64', 'account' => 'SaudiAirline', 'user_group' => 'Students'],
            ['prefix' => '65', 'account' => 'SaudiAirline', 'user_group' => 'Reserved'],
            ['prefix' => '66', 'account' => 'Hunger Station', 'user_group' => 'Reserved'],
            ['prefix' => '67', 'account' => 'Swyft', 'user_group' => 'Inbound'],
            ['prefix' => '68', 'account' => 'Swyft', 'user_group' => 'Manual'],
            ['prefix' => '69', 'account' => 'Swyft', 'user_group' => 'Reserved'],
            ['prefix' => '70', 'account' => 'Careem', 'user_group' => 'Reserved'],
            ['prefix' => '71', 'account' => 'Jollychic', 'user_group' => 'Dialer'],
            ['prefix' => '72', 'account' => 'Gobx', 'user_group' => 'Tracking'],
            ['prefix' => '73', 'account' => 'Gobx', 'user_group' => 'Inbound'],
            ['prefix' => '74', 'account' => 'Gobx', 'user_group' => 'Manual'],
            ['prefix' => '75', 'account' => 'Gobx', 'user_group' => 'Dialer'],
            ['prefix' => '76', 'account' => 'Shahid', 'user_group' => 'Inbound'],
            ['prefix' => '77', 'account' => 'Shahid', 'user_group' => 'Manual'],
            ['prefix' => '78', 'account' => 'Shahid', 'user_group' => 'Tracking'],
            ['prefix' => '79', 'account' => 'Shahid', 'user_group' => 'Inbound-Cancellation'],
            ['prefix' => '80', 'account' => 'AliBaba', 'user_group' => 'Dialer'],
            ['prefix' => '81', 'account' => 'AliBaba', 'user_group' => 'Manual'],
            ['prefix' => '82', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '83', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '84', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '85', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '86', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '87', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '88', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '89', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '90', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '91', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '92', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '93', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '94', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '95', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '96', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '97', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '98', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '99', 'account' => 'Irbid', 'user_group' => 'Reserved'],
            ['prefix' => '100', 'account' => 'Pace', 'user_group' => 'Inbound'],
            ['prefix' => '101', 'account' => 'Pace', 'user_group' => 'Manual'],
            ['prefix' => '102', 'account' => 'Pace /Tayseer', 'user_group' => 'Tracking /Manual'],
            ['prefix' => '103', 'account' => 'Monshaat', 'user_group' => 'Inbound'],
            ['prefix' => '104', 'account' => 'Monshaat', 'user_group' => 'Manual Outbound'],
            ['prefix' => '105', 'account' => 'Yallow', 'user_group' => 'Inbound'],
            ['prefix' => '106', 'account' => 'Dome', 'user_group' => 'Tracking'],
            ['prefix' => '107', 'account' => 'Pace', 'user_group' => 'Client Tracking'],
            ['prefix' => '108', 'account' => 'Zad', 'user_group' => 'Fresh Tracking'],
            ['prefix' => '109', 'account' => 'Monshaat', 'user_group' => 'Auto-Dialer'],
            ['prefix' => '110', 'account' => 'Saudi Electricity', 'user_group' => 'Manual Outbound'],
            ['prefix' => '111', 'account' => 'Saudi Electricity', 'user_group' => 'Dialer'],
            ['prefix' => '112', 'account' => 'Monshaat', 'user_group' => 'Tracking'],
            ['prefix' => '113', 'account' => 'SHEIN', 'user_group' => 'Inbound'],
            ['prefix' => '114', 'account' => 'SHEIN', 'user_group' => 'Manual Outbound'],
            ['prefix' => '115', 'account' => 'SHEIN', 'user_group' => 'Tracking'],
            ['prefix' => '116', 'account' => 'Noon', 'user_group' => 'Tracking'],
            ['prefix' => '117', 'account' => 'Carrefour', 'user_group' => 'Tracking'],
            ['prefix' => '118', 'account' => 'Carrefour', 'user_group' => 'Dialer'],
            ['prefix' => '119', 'account' => 'Carrefour', 'user_group' => 'Manual'],
            ['prefix' => '120', 'account' => 'Carrefour', 'user_group' => 'Inbound'],
            ['prefix' => '121', 'account' => 'Geidea', 'user_group' => 'Inbound'],
            ['prefix' => '122', 'account' => 'Geidea', 'user_group' => 'Dialer'],
            ['prefix' => '123', 'account' => 'Geidea', 'user_group' => 'Manual Outbound'],
            ['prefix' => '124', 'account' => 'Geidea', 'user_group' => 'Tracking'],
            ['prefix' => '125', 'account' => 'Saudi Specialties', 'user_group' => 'Inbound'],
            ['prefix' => '126', 'account' => 'Saudi Specialties', 'user_group' => 'Manual'],
            ['prefix' => '127', 'account' => 'Saudi Specialties', 'user_group' => 'Tracking'],
            ['prefix' => '128', 'account' => 'Saudi Specialties', 'user_group' => 'Dialer'],
            ['prefix' => '129', 'account' => 'Naqel', 'user_group' => 'Tracking'],
            ['prefix' => '130', 'account' => 'Gediea', 'user_group' => 'Survey Campaign'],
            ['prefix' => '131', 'account' => 'Zajil', 'user_group' => 'Inbound'],
            ['prefix' => '132', 'account' => 'Zajil', 'user_group' => 'Tracking'],
            ['prefix' => '133', 'account' => 'Zajil', 'user_group' => 'Manual Outbound'],
            ['prefix' => '134', 'account' => 'Zajil', 'user_group' => 'Dialer Outbound'],
            ['prefix' => '135', 'account' => 'Zajil', 'user_group' => 'Client Inbound'],
            ['prefix' => '136', 'account' => 'Mumzworld', 'user_group' => 'Inbound'],
            ['prefix' => '137', 'account' => 'Mumzworld', 'user_group' => 'Outbound'],
            ['prefix' => '138', 'account' => 'Mumzworld', 'user_group' => 'Tracking'],
            ['prefix' => '139', 'account' => 'Fordeal', 'user_group' => 'Inbound'],
            ['prefix' => '140', 'account' => 'Fordeal', 'user_group' => 'Outbound'],
            ['prefix' => '141', 'account' => 'Fordeal', 'user_group' => 'Tracking'],
            ['prefix' => '142', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '143', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '144', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '145', 'account' => 'Zajil', 'user_group' => 'Client Outbound'],
            ['prefix' => '146', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '147', 'account' => 'AlRaedah', 'user_group' => 'Inbound'],
            ['prefix' => '148', 'account' => 'AlRaedah', 'user_group' => 'Manual Outbound'],
            ['prefix' => '149', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '150', 'account' => 'Hertz', 'user_group' => 'Tracking'],
            ['prefix' => '151', 'account' => 'Hertz', 'user_group' => 'International'],
            ['prefix' => '152', 'account' => 'Stalar', 'user_group' => 'Inbound'],
            ['prefix' => '153', 'account' => 'Stalar', 'user_group' => 'Manual'],
            ['prefix' => '154', 'account' => 'Stalar', 'user_group' => 'Tracking'],
            ['prefix' => '155', 'account' => 'Stalar', 'user_group' => 'Dialer'],
            ['prefix' => '156', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '157', 'account' => '-', 'user_group' => 'Reserved'],
            ['prefix' => '158', 'account' => 'Level Shoes', 'user_group' => 'Manual'],
            ['prefix' => '159', 'account' => 'Talabat', 'user_group' => 'Tracking'],
            ['prefix' => '160', 'account' => 'SaudiAirlines', 'user_group' => 'Blending'],
            ['prefix' => '161', 'account' => 'SaudiAirlines', 'user_group' => 'Tracking'],
            ['prefix' => '162', 'account' => 'SaudiAirlines', 'user_group' => 'Reserved'],
            ['prefix' => '163', 'account' => 'SaudiAirlines', 'user_group' => 'Reserved'],
            ['prefix' => '164', 'account' => 'SaudiAirlines', 'user_group' => 'Reserved'],
            ['prefix' => '165', 'account' => 'Chicpoint', 'user_group' => 'Dialer'],
            ['prefix' => '166', 'account' => 'Chicpoint', 'user_group' => 'Manual'],
            ['prefix' => '167', 'account' => 'Chicpoint', 'user_group' => 'Tracking'],
            ['prefix' => '168', 'account' => 'Chicpoint', 'user_group' => 'Special'],
            ['prefix' => '169', 'account' => 'KITOPI', 'user_group' => 'Tracking'],
            ['prefix' => '170', 'account' => 'KITOPI', 'user_group' => 'Reserved'],
            ['prefix' => '171', 'account' => 'KITOPI', 'user_group' => 'Reserved'],
            ['prefix' => '172', 'account' => 'KITOPI', 'user_group' => 'Reserved'],
            ['prefix' => '173', 'account' => 'KITOPI', 'user_group' => 'Reserved'],
            ['prefix' => '174', 'account' => 'SurePay', 'user_group' => 'Inbound'],
            ['prefix' => '175', 'account' => 'SurePay', 'user_group' => 'Tracking'],
            ['prefix' => '176', 'account' => 'SurePay', 'user_group' => 'Outbound'],
            ['prefix' => '177', 'account' => 'SurePay', 'user_group' => 'Reserved'],
            ['prefix' => '178', 'account' => 'Meddy', 'user_group' => 'Blended'],
            ['prefix' => '179', 'account' => 'Meddy', 'user_group' => 'Reserved'],
            ['prefix' => '180', 'account' => 'Meddy', 'user_group' => 'Reserved'],
            ['prefix' => '181', 'account' => 'Meddy', 'user_group' => 'Reserved'],
            ['prefix' => '182', 'account' => 'Rock', 'user_group' => 'Manual'],
            ['prefix' => '183', 'account' => 'Rock', 'user_group' => 'Dialer'],
            ['prefix' => '184', 'account' => 'Rock', 'user_group' => 'Manual Outstanding'],
            ['prefix' => '185', 'account' => 'Rock', 'user_group' => 'Manual Survey'],
            ['prefix' => '186', 'account' => 'Rantion', 'user_group' => 'Tracking'],
            ['prefix' => '187', 'account' => 'Rantion', 'user_group' => 'Inbound'],
            ['prefix' => '188', 'account' => 'Rantion', 'user_group' => 'Manual'],
            ['prefix' => '189', 'account' => 'Rantion', 'user_group' => 'Reserved'],
            ['prefix' => '190', 'account' => 'Rantion', 'user_group' => 'Reserved'],
            ['prefix' => '191', 'account' => 'Tawal', 'user_group' => 'Inbound'],
            ['prefix' => '192', 'account' => 'Tawal', 'user_group' => 'Manual'],
            ['prefix' => '194', 'account' => 'Tawal', 'user_group' => 'Reserved'],
            ['prefix' => '195', 'account' => 'Tawal', 'user_group' => 'Reserved'],
            ['prefix' => '196', 'account' => 'Ewa', 'user_group' => 'Inbound'],
            ['prefix' => '197', 'account' => 'Ewa', 'user_group' => 'Manual'],
            ['prefix' => '198', 'account' => 'Ewa', 'user_group' => 'Tracking'],
            ['prefix' => '199', 'account' => 'Ewa', 'user_group' => 'Reserved'],
            ['prefix' => '200', 'account' => 'Cars24', 'user_group' => 'Dialer'],
            ['prefix' => '201', 'account' => 'Cars24', 'user_group' => 'Manual'],
            ['prefix' => '202', 'account' => 'Cars24', 'user_group' => 'Blending'],
            ['prefix' => '203', 'account' => 'Cars24', 'user_group' => 'Reserved'],
            ['prefix' => '204', 'account' => 'JAF', 'user_group' => 'Inbound'],
            ['prefix' => '205', 'account' => 'JAF', 'user_group' => 'Manual'],
            ['prefix' => '206', 'account' => 'JAF', 'user_group' => 'Tracking'],
            ['prefix' => '207', 'account' => 'JAF', 'user_group' => 'Reserved'],
            ['prefix' => '208', 'account' => 'Bigo', 'user_group' => 'Tracking'],
            ['prefix' => '209', 'account' => 'Bigo', 'user_group' => 'Reserved'],
            ['prefix' => '210', 'account' => 'Go_Wagon', 'user_group' => 'Tracking'],
            ['prefix' => '211', 'account' => 'Go_Wagon', 'user_group' => 'Reserved'],
            ['prefix' => '212', 'account' => 'PureGym', 'user_group' => 'Tracking'],
            ['prefix' => '213', 'account' => 'PureGym', 'user_group' => 'Reserved'],
            ['prefix' => '214', 'account' => 'Tiqmo', 'user_group' => 'Inbound'],
            ['prefix' => '215', 'account' => 'Tiqmo', 'user_group' => 'Blended Anti-Fraud'],
            ['prefix' => '216', 'account' => 'Tiqmo', 'user_group' => 'Tracking'],
            ['prefix' => '217', 'account' => 'Balsam', 'user_group' => 'Inbound'],
            ['prefix' => '218', 'account' => 'Balsam', 'user_group' => 'Manual'],
            ['prefix' => '219', 'account' => 'Balsam', 'user_group' => 'Reserved'],
            ['prefix' => '220', 'account' => 'QatarCreates', 'user_group' => 'Inbound'],
            ['prefix' => '221', 'account' => 'QatarCreates', 'user_group' => 'Reserved'],
            ['prefix' => '222', 'account' => 'QatarCreates', 'user_group' => 'Reserved'],
            ['prefix' => '223', 'account' => 'Xnara', 'user_group' => 'Inbound'],
            ['prefix' => '224', 'account' => 'Xnara', 'user_group' => 'Manual'],
            ['prefix' => '225', 'account' => 'Xnara', 'user_group' => 'Reserved'],
            ['prefix' => '226', 'account' => 'BCI', 'user_group' => 'Inbound'],
            ['prefix' => '227', 'account' => 'BCI', 'user_group' => 'Outbound'],
            ['prefix' => '228', 'account' => 'BCI', 'user_group' => 'Reserved'],
            ['prefix' => '229', 'account' => 'BCI', 'user_group' => 'Reserved'],
            ['prefix' => '230', 'account' => 'LC Waikiki', 'user_group' => 'Inbound'],
            ['prefix' => '231', 'account' => 'LC Waikiki', 'user_group' => 'Reserved'],
            ['prefix' => '232', 'account' => 'LC Waikiki', 'user_group' => 'Reserved'],
            ['prefix' => '233', 'account' => 'PBIC', 'user_group' => 'Inbound'],
            ['prefix' => '234', 'account' => 'PBIC', 'user_group' => 'Dialer'],
            ['prefix' => '235', 'account' => 'PBIC', 'user_group' => 'Reserved'],
            ['prefix' => '236', 'account' => 'PBIC', 'user_group' => 'Reserved'],
            ['prefix' => '237', 'account' => 'Fanchen', 'user_group' => 'Tracking'],
            ['prefix' => '238', 'account' => 'Fanchen', 'user_group' => 'Reserved'],
            ['prefix' => '239', 'account' => 'Fanchen', 'user_group' => 'Reserved'],
            ['prefix' => '240', 'account' => 'Entertainer', 'user_group' => 'Tracking'],
            ['prefix' => '241', 'account' => 'Entertainer', 'user_group' => 'Reserved'],
            ['prefix' => '242', 'account' => 'Entertainer', 'user_group' => 'Reserved'],
            ['prefix' => '243', 'account' => 'Brandatt', 'user_group' => 'Tracking'],
            ['prefix' => '244', 'account' => 'Brandatt', 'user_group' => 'Inbound'],
            ['prefix' => '245', 'account' => 'Brandatt', 'user_group' => 'Outbound'],
            ['prefix' => '246', 'account' => 'Sauce Capital', 'user_group' => 'Inbound'],
            ['prefix' => '247', 'account' => 'Sauce Capital', 'user_group' => 'Outbound'],
            ['prefix' => '248', 'account' => 'Sauce Capital', 'user_group' => 'Reserved'],
            ['prefix' => '249', 'account' => 'SVTS', 'user_group' => 'Tracking'],
            ['prefix' => '250', 'account' => 'SVTS', 'user_group' => 'Reserved'],
            ['prefix' => '251', 'account' => 'Samsung', 'user_group' => 'Inbound'],
            ['prefix' => '252', 'account' => 'Samsung', 'user_group' => 'Manual OB'],
            ['prefix' => '253', 'account' => 'Samsung', 'user_group' => 'Dialer'],
            ['prefix' => '254', 'account' => 'Munjz', 'user_group' => 'Tracking'],
            ['prefix' => '255', 'account' => 'Munjz', 'user_group' => 'Reserved'],
            ['prefix' => '256', 'account' => 'Munjz', 'user_group' => 'Reserved'],
            ['prefix' => '257', 'account' => 'Patchi', 'user_group' => 'Blended'],
            ['prefix' => '258', 'account' => 'Patchi', 'user_group' => 'Reserved'],
            ['prefix' => '259', 'account' => 'Patchi', 'user_group' => 'Tracking'],
            ['prefix' => '260', 'account' => 'Temu', 'user_group' => 'Tracking'],
            ['prefix' => '261', 'account' => 'Temu', 'user_group' => 'Reserved'],
            ['prefix' => '262', 'account' => 'Temu', 'user_group' => 'Reserved'],
            ['prefix' => '265', 'account' => 'Albarq', 'user_group' => 'Inbound'],
            ['prefix' => '266', 'account' => 'Albarq', 'user_group' => 'Reserved'],
            ['prefix' => '267', 'account' => 'Albarq', 'user_group' => 'Reserved'],
            ['prefix' => '268', 'account' => 'Zara', 'user_group' => 'Blended'],
            ['prefix' => '269', 'account' => 'Zara', 'user_group' => 'Reserved'],
            ['prefix' => '270', 'account' => 'Zara', 'user_group' => 'Reserved'],
            ['prefix' => '271', 'account' => 'Madar', 'user_group' => 'Blended'],
            ['prefix' => '272', 'account' => 'Madar', 'user_group' => 'Reserved'],
        ];

        DB::table('account_prefixes')->insert($data);
    }
}
