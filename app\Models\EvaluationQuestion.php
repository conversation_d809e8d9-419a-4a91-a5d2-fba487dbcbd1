<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationQuestion extends Model
{
    use HasFactory;

    public $guarded = [];

    public function group()
    {
        return $this->belongsTo(EvaluationGroup::class,'evaluation_group_id');
    }
    public function header()
    {
        return $this->belongsTo(EvaluationHeader::class,'evaluation_header_id');
    }
    public function answers()
    {
        return $this->hasMany(EvaluationAnswer::class,'evaluation_question_id');
    }
    public function submissionAnswers()
    {
        return $this->hasMany(EvaluationSubmissionAnswer::class,'evaluation_question_id');
    }
}
