@props(['time', 'time_to', 'source', 'content', 'classificationRow'])
@php
    if (!function_exists('formatSecondsToTime')) {
        function formatSecondsToTime($seconds)
        {
            $minutes = floor($seconds / 60); // Extract minutes
            $remainingSeconds = $seconds % 60; // Extract remaining seconds

            return sprintf('%02d:%02d', $minutes, $remainingSeconds); // Format as MM:SS.sss
        }
    }
@endphp
@php
$score = number_format(($classificationRow->score  * 100) ?? 0, 2, '.', ',');
switch ($classificationRow->classification) {
    case 'negative':
        # code...
        $timeClass = 'text-danger fw-bold';
        $iconClass = 'fa-thumbs-down text-danger px-3';
        $textClass = 'fw-bold';

        break;
    case 'positive':
        # code...
        $timeClass = 'text-success fw-bold';
        $iconClass = 'fa-thumbs-up text-success px-3';
        $textClass = 'fw-bold';
        break;
    default:
        # code...
        $timeClass = '';
        $iconClass = 'fa-circle custom-circle-blue px-3';
        $textClass = 'fw-bold custom-circle-blue';
        break;
}
@endphp
<style>
  .tooltip.custom-tooltip {
    background-color: #40798c !important; /* Change background color */
    border: 2px solid #40798c !important; /* Add a border */
    border-radius: 8px; /* Rounded corners */
}

/* Customize the text inside the tooltip */
.tooltip.custom-tooltip .tooltip-inner {
    color: #fff !important; /* Text color */
    font-size: 14px !important; /* Font size */
    font-weight: bold !important; /* Font weight */
    background-color: #40798c !important; /* Ensure background matches */
    padding: 10px !important; /* Add padding */
}

/* Customize the arrow */
.tooltip.custom-tooltip .tooltip-arrow::before {
    background-color: #40798c !important; /* Arrow background color */
    border-color: #40798c !important; /* Optional: Match border color */
}

.score-span{
    font-size: smaller;
    padding: 0.4rem;

}
.custom-circle-blue
{
    color:#40798c;
}
    </style>
<div class="row px-2 conv-row {{-- py-2 --}} mx-1" style="display: flex
;
    flex-direction: row;
    justify-content: space-between;">
    @if ($source == 'right')
    <div class="col-1" style="max-width: 5%;" style="    justify-content: flex-end;display: flex;">
        <div class="badge badge-danger conv-controllers {{ $timeClass }}" style=" color:black">
            {{ formatSecondsToTime($time) }}</div>
    </div>
        <div class="col-11 conv-text-container {{ $source == 'right' ? 'conv-text-container-agent' : 'conv-text-container-customer' }}"
            style="justify-content:">

            <p class='conv-text px-2 rounded-4{{-- py-1 --}}   custom-tooltip {{$textClass}}' data-from-time="{{(int)$time}}" data-from-to="{{(int)$time_to}}"    data-source="{{ $source }}"
            data-content="{{ e($content) }}">{{ $content }}</p>
            <i class="fa {{$iconClass}}" aria-hidden="true"></i>
            <span class="score-span {{$textClass}} {{$timeClass}}">{{(int)$score}}%</span>
        </div>

    @else
        <div class="col-1" style="max-width: 5%;">
            <div class="badge badge-danger conv-controllers {{ $timeClass }}" style=" color:black">
                {{ formatSecondsToTime($time) }}</div>
        </div>
        <div class="col-11 conv-text-container {{ $source == 'right' ? 'conv-text-container-agent' : 'conv-text-container-customer' }}"
            style="justify-content:">
            <p class='conv-text px-2 rounded-4{{--  py-1 --}}  custom-tooltip {{$textClass}}' data-from-time="{{(int)$time}}" data-from-to="{{(int)$time_to}}"    data-source="{{ $source }}"
            data-content="{{ e($content) }}">{{ $content }}</p>
            <span class="score-span {{$textClass}} {{$timeClass}}">{{(int)$score}}%</span>
            <i class="fa {{$iconClass}}" aria-hidden="true"></i>
        </div>
    @endif
</div>
{{--
<script>
    $(function () {
    $('[data-toggle="tooltip"]').tooltip({
        template: '<div class="tooltip custom-tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
    });
});
</script> --}}


