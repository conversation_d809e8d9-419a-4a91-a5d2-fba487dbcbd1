<?php

namespace App\Console\Commands;

use App\Models\AccountPrefix;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Interaction;
use Illuminate\Support\Str;
use App\Models\Organization;
use App\Models\UserGroup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class FetchDataBase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetchDatabase';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'fetch the DB Data';


    public function handle()
    {
        try {
            // Retrieve the last run time from the cache
            // $lastRun = Cache::get('last_fetch_database_run');

            // Check if 10 hours have passed since the last run
            // if (!$lastRun || now()->diffInHours($lastRun) >= 10) {
            Log::info("Started fetching data " . now());
            $this->info("Started fetching data " . now());

            // Extend maximum execution time
            set_time_limit(0);

            // Extend the memory
            ini_set('memory_limit', '3800M');

            $thereAreNewCalls = false;
            $modanisaCalls = false;

            // Phase 1: Fetch Call Data and store initial interaction data
            // $callsUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/fetchDB.php';

            $callsUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/linux_fetch_db.php';
            $callsResponse = Http::withoutVerifying()->timeout(60 * 4000)->get($callsUrl);
            $callsData = $callsResponse->json();


            // Check if the response contains no call data
            // if (isset($callsData['message']) && $callsData['message'] === "No records found") {
            //     $this->info('No new call data received from the remote database');
            //     // return;
            // } else {
            //     $thereAreNewCalls = true;
            // }

            if (isset($callsData['message']) && $callsData['message'] === "No records found") {
                $this->info('No new call data received from the remote database');
                return;
            }


            // Phase 2: Fetch Data from fetchDB-modanisa-BCI.php
            // $modanisaUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/fetchDB-modanisa-BCI.php';
            // $modanisaResponse = Http::withoutVerifying()->timeout(60 * 4000)->get($modanisaUrl);
            // $modanisaData = $modanisaResponse->json();

            // Check if the second response contains no data
            // if (isset($modanisaData['message']) && $modanisaData['message'] === "No records found") {
            //     $this->info('No new Modanisa data received from the remote database');
            //     $modanisaCalls = false;
            //     // return;
            // } else {
            //     $this->info('there are modanisa and bci calls');
            //     $modanisaCalls = true;
            //     $thereAreNewCalls = true;
            // }


            // if (!$thereAreNewCalls) {
            //     $this->info('No new data received from all remote DBs.');
            //     return;
            // }


            // Proceed with other tasks using $callsData and $modanisaData as needed
            // Log::info("Fetched data successfully from x84 and x72 sources " . now());
            // $this->info("Fetched data successfully from x84 and x72 sources " . now());
            $this->info("Fetched data successfully from the new DB " . now());
            Log::info("Fetched data successfully from the new DB " . now());


            // Store the initial call data in the local interactions table
            foreach ($callsData as $call) {
                Interaction::updateOrCreate(
                    ['call_id' => $call['call_id']],
                    ['Genesys_CallUUID' => $call['Genesys_CallUUID']]
                );
            }

            // if ($modanisaCalls) {

            //     foreach ($modanisaData as $modanisaCall) {
            //         Interaction::updateOrCreate(
            //             ['call_id' => $modanisaCall['call_id']],
            //             ['Genesys_CallUUID' => $modanisaCall['Genesys_CallUUID']]
            //         );
            //     }
            // }

            $this->info('Interactions stored successfully! ' . now());
            Log::info('Interactions stored successfully! ' . now());

            // Fetch interactions that need to be updated
            $interactions = Interaction::whereNull('user_id')->whereNotNull('Genesys_CallUUID')
                // ->where('created_at', '>=', '2024-06-24 19:00:00')
                ->where('id', '>', 359821)
                ->get();


            $this->info('done getting interactions with null user id and not null genesys UUID');
            Log::info('done getting interactions with null user id and not null genesys UUID');

            $this->info('Started Getting other calls data ' . now());
            Log::info('Started Getting other calls data ' . now());

            // Initialize array to collect Genesys_CallUUID values to send them all at once to the oracle view
            $genesysCallUUIDs = [];

            foreach ($interactions as $interaction) {
                $this->info('Interaction ' . $interaction->id . ' loop - ' . now());
                Log::info('Interaction ' . $interaction->id . ' loop - ' . now());

                // Collect Genesys_CallUUID values
                $genesysCallUUIDs[] = $interaction->Genesys_CallUUID;

                // Make an HTTP GET request to the PHP API
                $apiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/fetchInteractionsData.php';
                $response = Http::withoutVerifying()->timeout(60 * 5000)->get($apiUrl, [
                    'callId' => $interaction->Genesys_CallUUID,
                ]);

                $remoteRecords = $response->json();
                $this->info(print_r($remoteRecords));

                Log::info($interaction->Genesys_CallUUID);

                if (!is_null($remoteRecords)) {
                    $this->info('entered remote records loop');
                    foreach ($remoteRecords as $remoteRecord) {
                        if ($remoteRecord && !empty($remoteRecord['RTargetAgentSelected'])) {
                            // User data
                            $agent_id = $remoteRecord['RTargetAgentSelected'];
                            Log::info($agent_id);
                            $this->info('agent ID: ' . $agent_id);

                            // Check the prefix
                            $prefixes = DB::table('account_prefixes')->pluck('prefix');
                            $prefixMatched = false;
                            $agent_prefix = null;

                            foreach ($prefixes as $prefix) {
                                $this->info('checking prefix: ' . $prefix);
                                if (substr($agent_id, 0, strlen($prefix)) === $prefix) {
                                    $prefixMatched = true;
                                    $agent_prefix = $prefix;
                                    break;
                                }
                            }

                            // if ($prefixMatched) {
                            //     // Delete the first 3 characters from $agent_id if prefix matched
                            //     $agent_id = substr($agent_id, 3);
                            // } else {
                            //     // Delete the first two characters from $agent_id
                            //     $agent_id = substr($agent_id, 2);
                            // }

                            if ($prefixMatched) {
                                // Delete the matched prefix length from $agent_id
                                $agent_id = substr($agent_id, strlen($agent_prefix));
                            } else {
                                // Delete the first two characters from $agent_id
                                $agent_id = substr($agent_id, 2);
                            }

                            // Find the corresponding agent (if exists), otherwise create it
                            $user = User::where('agent_id', $agent_id)->where('role', 4)->first();

                            // found user email
                            $emailThatExists = $user?->email;

                            if ($user) {
                                $this->info('entered user existing statement');
                                Log::info('entered user existing statement if($user)');

                                // If the user exists, get the user's id
                                $userId = $user->id;
                                $newUser = $user; // Assign the existing user to $newUser for later use
                            } else {
                                $this->info('entered headcount statement');
                                Log::info('entered headcount statement, (else the if user)');
                                // If the user does not exist, get its data from headcount
                                $headcountApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/agentsFromHeadcount.php';

                                // Send a GET request to the API and get the result
                                $userData = Http::withoutVerifying()->timeout(60 * 6000)->get($headcountApiUrl, [
                                    'ops_id' => $agent_id,
                                ])->json();

                                // Construct full name
                                $full_name = $userData['first_name'] . ' ' . $userData['second_name'] . ' ' . $userData['third_name'] . ' ' . $userData['last_name'];

                                // Construct username
                                $username = $userData['first_name'] . ' ' . $userData['last_name'];

                                // Get email
                                $email = $userData['email'];

                                // if the email of the new user equal to some email already exist then update the current user
                                // (maybe it is a quality user that became an agent)

                                if ($email == $emailThatExists) {
                                    Log::info('Email Exists but role is not agent, the email is: ' . $email);
                                    $this->info('Email Exists but role is not agent, the email is: ' . $email);

                                    $newUser = User::where('email', $email)->first();
                                    $newUser->update([
                                        'role' => 4,
                                        'enabled' => 0
                                    ]);
                                } else {
                                    // Create the new user
                                    $newUser = User::create([
                                        'full_name' => $full_name,
                                        'username' => $username,
                                        'email' => $email,
                                        'agent_id' => $agent_id,
                                        'password' => Hash::make('extensya@12345678'),
                                        'role' => 4,
                                        'terminated' => 0,
                                        'enabled' => 0,
                                        'web_access' => 0,
                                    ]);
                                }

                                // Get the new user's id
                                $userId = $newUser->id;
                            }

                            // Add default params and permissions to the new agent
                            $newUser->parameters()->sync([2, 4, 6, 7, 19, 22, 25, 27]);
                            $newUser->permissions()->sync([1, 2, 4, 5, 8, 10]);

                            $org = null;

                            // Organization
                            if ($remoteRecord['ClientName'] && $remoteRecord['ClientName'] != "") {
                                Log::info('Client Name exists in SDK and it is: ' . $remoteRecord['ClientName']);
                                $this->info('Client Name exists in SDK and it is: ' . $remoteRecord['ClientName']);
                                $org = Organization::where('name', 'like', "%{$remoteRecord['ClientName']}%")->first();
                                $org_id = $org->id;
                            } else {
                                $org_id = null;
                            }

                            // ***NEW FEATURE*** Save the org id for the call
                            $interaction->organization_id = $org_id;
                            /////////////////////////////////////////////////

                            // $org = Organization::where('name', 'like', "%{$remoteRecord['ClientName']}%")->first();
                            // $org_id = $org ? $org->id : null;

                            Log::info('***client name from sdk ' . $remoteRecord['ClientName']);
                            Log::info('***org id from sdk ' . $org_id);

                            // Add the org id to the user
                            Log::info('****TEST NEWUSER VARIABLE->organization_id: ' . $newUser->organization_id);


                            if (!$newUser->organization_id) {
                                Log::info('****entered the if statement if !newUser->organization_id');
                                $newUser->organization_id = $org_id;
                            }

                            $newUser->save(); // Ensure the user is saved after setting the organization_id

                            // Save the user id for the call
                            $interaction->user_id = $userId;

                            // Arrival time
                            $interaction->arrival_time = Carbon::createFromTimestamp($remoteRecord['AttributeTimeinSecs'])->toDateTimeString();

                            // Language
                            $interaction->language = $remoteRecord['Language'];

                            // Extension
                            if (isset($remoteRecord['RTargetPlaceSelected'])) {
                                $this->info('entered RTargetPlaceSelected isset statement');
                                $interaction->agent_extension = str_replace('Place_3', '4', $remoteRecord['RTargetPlaceSelected']);
                            } else {
                                $interaction->agent_extension = null;
                            }

                            $interaction->save();
                            break;

                            $this->info('Interactions updated successfully! ' . now());
                            Log::info('Interactions updated successfully! ' . now());
                        }
                    }
                } else {
                    Log::error('Remote records are null or not an array/object.');
                }
            }


            // Make an HTTP POST request to the Oracle API
            $this->info('Sending post request to the Oracle View with all Genesys UUIDs to get the rest of the data ' . now());
            Log::info('Sending post request to the Oracle View with all Genesys UUIDs to get the rest of the data ' . now());
            Log::info('the array: ');
            $this->info('the array: ');
            Log::info(print_r($genesysCallUUIDs, true));

            try {
                Log::info('Entered the try-catch');
                $oracleApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/fetchDataFromOracleDb.php';
                $oracleResponse = Http::withHeaders([
                    'Content-Type' => 'application/json'
                ])
                    ->withoutVerifying()
                    ->timeout(60 * 6000)
                    ->post($oracleApiUrl, [
                        'Genesys_CallUUIDs' => $genesysCallUUIDs,
                    ]);

                $oracleData = $oracleResponse->json();
                Log::info('Oracle data fetched');

                foreach ($interactions as $interaction) {

                    Log::info('entered oracle interaction ' .  $interaction->id . ' loop, and Genesys UUID: ' . $interaction->Genesys_CallUUID . ' - ' . now());

                    $genesysCallUUID = $interaction->Genesys_CallUUID;

                    // Log::info('Oracle response is: ');
                    // Log::info($oracleData);

                    if (isset($oracleData[$genesysCallUUID]) && !empty($oracleData[$genesysCallUUID])) {


                        Log::info('Oracle Data is set and entered the if statement');
                        // Select the record with the maximum ENGAGE_DURATION
                        $oracleRecord = collect($oracleData[$genesysCallUUID])->sortByDesc('ENGAGE_DURATION')->first();
                        Log::info('Arranged by desc engage duration in case of many results, the result: ');
                        Log::info($oracleRecord);

                        // Save Oracle data into the interaction
                        $interaction->caller_id = $oracleRecord['ORIGINATOR'];
                        $interaction->called_id = $oracleRecord['TARGET'];
                        $interaction->call_duration =  gmdate('H:i:s', $oracleRecord['ENGAGE_DURATION']);
                        $interaction->call_type = Str::title($oracleRecord['INTERACTION_TYPE']);
                        $interaction->hold_duration =  gmdate('H:i:s', $oracleRecord['HOLD_DURATION']);
                        $interaction->hold_count =  $oracleRecord['HOLD_COUNT'];

                        // ring
                        $interaction->ring = $oracleRecord['RING_DURATION'];

                        // call ender
                        if (isset($oracleRecord['PARTY_DISCONNECTED'])) {
                            // Convert the value to an integer
                            $partyDisconnected = (int) $oracleRecord['PARTY_DISCONNECTED'];

                            // Check the value of partyDisconnected
                            if ($partyDisconnected === 0) {
                                $interaction->call_ender = 'Agent';
                            } else {
                                $interaction->call_ender = 'Customer';
                            }
                        }


                        // if the interaction has no user_id, get the agent data from Oracle and create it.
                        if ($interaction->user_id == null) {

                            Log::info('entered the of user id is null and we should get it from Oracle');
                            // $full_name = $oracleRecord['AGENT_FIRST_NAME'] . ' ' . $oracleRecord['AGENT_LAST_NAME'];
                            // $username = $oracleRecord['AGENT_FIRST_NAME'] . ' ' . $oracleRecord['AGENT_LAST_NAME'];

                            $agent_id = $oracleRecord['EMPLOYEE_ID'];

                            // if theres an ID, get data from headcount
                            if ($agent_id) {
                                // Check the prefix
                                $prefixes = DB::table('account_prefixes')->pluck('prefix');
                                $prefixMatched = false;
                                $agent_prefix = null;

                                foreach ($prefixes as $prefix) {
                                    $this->info('Oracle loop - checking prefix' . $prefix);
                                    if (substr($agent_id, 0, strlen($prefix)) === $prefix) {
                                        $prefixMatched = true;
                                        $agent_prefix = $prefix;
                                        break;
                                    }
                                }

                                // if ($prefixMatched) {
                                //     // Delete the first 3 characters from $agent_id if prefix matched
                                //     $agent_id = substr($agent_id, 3);
                                // } else {
                                //     // Delete the first two characters from $agent_id
                                //     $agent_id = substr($agent_id, 2);

                                //     // get the prefix for later use (in case of a new user that have no org from sdk table)
                                //     $agent_prefix = substr($agent_id, 0, 2);
                                // }

                                if ($prefixMatched) {
                                    // Delete the matched prefix length from $agent_id
                                    $agent_id = substr($agent_id, strlen($agent_prefix));
                                } else {
                                    // Delete the first two characters from $agent_id
                                    $agent_id = substr($agent_id, 2);

                                    // Get the prefix for later use (in case of a new user with no org from the SDK table)
                                    $agent_prefix = substr($agent_id, 0, 2);
                                }

                                Log::info('agent ops id - oracle: ' . $agent_id);
                                Log::info('agent prefix - oracle: ' . $agent_prefix);

                                // Find the corresponding agent (if exists), otherwise create it
                                $user = User::where('agent_id', $agent_id)->where('role', 4)->first();

                                $emailThatExists = $user?->email;



                                // ***NEW FEATURE*** save the org for the interaction regardless the existing user org, because he maybe changed account
                                $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                $user_group_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->user_group;

                                $org_id_for_interaction = null;
                                $user_group_id_for_interaction = null;

                                if ($account_name_in_prefix_table) {
                                    Log::info('***NEW FEATURE: Account name is in prefix table: ' . $account_name_in_prefix_table);
                                    $this->info('***NEW FEATURE: Account name is in prefix table: ' . $account_name_in_prefix_table);
                                    $org_id_for_interaction = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;

                                    // user group
                                    $user_group_id_for_interaction = UserGroup::where('name', 'like', "%$account_name_in_prefix_table - $user_group_in_prefix_table%")?->first()?->id;
                                }


                                // organization id
                                $interaction->organization_id = $org_id_for_interaction;
                                
                                // user group
                                $interaction->user_group_id = $user_group_id_for_interaction;

                                Log::info('***NEW FEATURE: Org ID: ' . $org_id_for_interaction);
                                $this->info('***NEW FEATURE: Org ID: ' . $org_id_for_interaction);

                                Log::info('***NEW FEATURE: user group ID: ' . $user_group_id_for_interaction);
                                $this->info('***NEW FEATURE: user group ID: ' . $user_group_id_for_interaction);
                                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////

                                if ($user) {
                                    $this->info('Oracle loop - entered user existing statement with id: ' . $user->id);
                                    Log::info('Oracle loop - entered user existing statement with id: ' . $user->id);

                                    // If the user exists, get the user's id
                                    $userId = $user->id;

                                    $newUser = $user; // Assign the existing user to $newUser for later use

                                    // already existing org of the user (if any)
                                    $org_id = $user->organization_id;

                                    $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                    if ($account_name_in_prefix_table) {
                                        $org_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                    }


                                    // if he has no org
                                    if (!$user->organization_id) {
                                        Log::info('the already existing user doesnt have org');
                                        $this->info('the already existing user doesnt have org');

                                        Log::info('The new org id for this already existing user that has no org: ' . $org_id . ' and user ID ' . $user->id);
                                        $this->info('The new org id for this already existing user that has no org: ' . $org_id . ' and user ID ' . $user->id);

                                        $newUser->organization_id = $org_id;
                                    }
                                } else {
                                    $this->info('Oracle loop - entered headcount statement because user doesnt already exist');
                                    Log::info('Oracle loop - entered headcount statement because user doesnt already exist');
                                    // If the user does not exist, get its data from headcount
                                    $headcountApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/agentsFromHeadcount.php';

                                    // Send a GET request to the API and get the result
                                    $userData = Http::withoutVerifying()->timeout(60 * 6000)->get($headcountApiUrl, [
                                        'ops_id' => $agent_id,
                                    ])->json();

                                    // if ($userData['error'] == "No user found with the provided ops_id") {
                                    //     $this->info('no user in headcount');
                                    //     Log::info('no user in headcount');

                                    //     continue;
                                    // }

                                    // Construct full name
                                    $full_name = $userData['first_name'] . ' ' . $userData['second_name'] . ' ' . $userData['third_name'] . ' ' . $userData['last_name'];

                                    // Construct username
                                    $username = $userData['first_name'] . ' ' . $userData['last_name'];

                                    // Get email
                                    $email = $userData['email'];


                                    if ($email == $emailThatExists) {
                                        Log::info('Email Exists but role is not agent, the email is: ' . $email);
                                        $this->info('Email Exists but role is not agent, the email is: ' . $email);

                                        $newUser = User::where('email', $email)->first();
                                        $newUser->update([
                                            'role' => 4,
                                            'enabled' => 0
                                        ]);
                                    }

                                    // Create the new user
                                    else {
                                        $newUser = User::create([
                                            'full_name' => $full_name,
                                            'username' => $username,
                                            'email' => $email,
                                            'agent_id' => $agent_id,
                                            'password' => Hash::make('extensya@12345678'),
                                            'role' => 4,
                                            'terminated' => 0,
                                            'enabled' => 0,
                                            'web_access' => 0,
                                        ]);
                                    }


                                    // Get the new user's id
                                    $userId = $newUser->id;

                                    $this->info('Oracle loop - new user id: ' . $userId);
                                    Log::info('Oracle loop - new user id: ' . $userId);


                                    Log::info('getting the org of the new agent');

                                    // save the organization for the new agent
                                    // $org = AccountPrefix::where('prefix', $agent_prefix)->first()->id;
                                    // $org_id = $org ? $org : null;

                                    $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                    $org_id = null;
                                    if ($account_name_in_prefix_table) {
                                        $org_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                    }

                                    $this->info('new org id: ' . $org_id);
                                    Log::info('***agent prefix to query prefix with: ' . $agent_prefix);
                                    Log::info('***new org id: ' . $org_id);

                                    if ($org_id) {
                                        $newUser->organization_id = $org_id;
                                    }
                                }

                                // Add default params and permissions to the new agent
                                $newUser->parameters()->sync([2, 4, 6, 7, 19, 22, 25, 27]);
                                $newUser->permissions()->sync([1, 2, 4, 5, 8, 10]);

                                $newUser->save();

                                // Save the user id for the call
                                $interaction->user_id = $userId;
                            }
                        }


                        // ****************************** if for some reason the call has user id but the user has no org ****************

                        else if ($interaction->user_id) {
                            $this->info('NEW LOOP - entered the scope where the call has user id already');
                            Log::info('NEW LOOP - entered the scope where the call has user id already');

                            $agent_id = $oracleRecord['EMPLOYEE_ID'];

                            // if theres an ID, get data from headcount
                            if ($agent_id) {
                                // Check the prefix
                                $prefixes = DB::table('account_prefixes')->pluck('prefix');
                                $prefixMatched = false;
                                $agent_prefix = null;

                                foreach ($prefixes as $prefix) {
                                    $this->info('Oracle loop - checking prefix' . $prefix);
                                    if (substr($agent_id, 0, strlen($prefix)) === $prefix) {
                                        $prefixMatched = true;
                                        $agent_prefix = $prefix;
                                        break;
                                    }
                                }

                                // if ($prefixMatched) {
                                //     // Delete the first 3 characters from $agent_id if prefix matched
                                //     $agent_id = substr($agent_id, 3);
                                // } else {
                                //     // Delete the first two characters from $agent_id
                                //     $agent_id = substr($agent_id, 2);

                                //     // get the prefix for later use (in case of a new user that have no org from sdk table)
                                //     $agent_prefix = substr($agent_id, 0, 2);
                                // }

                                if ($prefixMatched) {
                                    // Delete the matched prefix length from $agent_id
                                    $agent_id = substr($agent_id, strlen($agent_prefix));
                                } else {
                                    // Delete the first two characters from $agent_id
                                    $agent_id = substr($agent_id, 2);

                                    // Get the prefix for later use (in case of a new user with no org from the SDK table)
                                    $agent_prefix = substr($agent_id, 0, 2);
                                }

                                // ***NEW FEATURE*** save the org for the interaction regardless the existing user org, because he maybe changed account
                                $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                $user_group_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->user_group;

                                $org_id_for_interaction = null;
                                $user_group_id_for_interaction = null;

                                if ($account_name_in_prefix_table) {
                                    $org_id_for_interaction = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;

                                    $user_group_id_for_interaction = UserGroup::where('name', 'like', "%$account_name_in_prefix_table - $user_group_in_prefix_table%")->first()->id;
                                }
                                
                                $interaction->organization_id = $org_id_for_interaction;
                                $interaction->user_group_id = $user_group_id_for_interaction;
                                ///////////////////////////////////////////////////////////////////////////////////////////////////////////////
                            }

                            // Find the corresponding agent (if exists), otherwise create it
                            $user = User::where('agent_id', $agent_id)->where('role', 4)->first();

                            if ($user) {
                                $this->info('NEW LOOP - the user exists with ID ' . $user->id);
                                Log::info('NEW LOOP - the user exists with ID ' . $user->id);

                                if (!$user->organization_id) {
                                    $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                    $this->info('NEW LOOP - the user has no org already ... org name: ' . $account_name_in_prefix_table);
                                    Log::info('NEW LOOP - the user has no org already ... org name: ' . $account_name_in_prefix_table);

                                    if ($account_name_in_prefix_table) {
                                        $user->organization_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                        $user->save();
                                        $this->info('NEW LOOP - user saved with org: ' . $user->organization_id);
                                        Log::info('NEW LOOP - user saved with org: ' . $user->organization_id);
                                    }
                                }
                            }
                        }


                        //  **************************************************************************

                        // if theres no arrival time already, get it from oracle
                        if ($interaction->arrival_time == null && $oracleRecord['START_TS']) {
                            $interaction->arrival_time = Carbon::createFromTimestamp($oracleRecord['START_TS'])->toDateTimeString();
                        }

                        $interaction->save();
                    }
                }

                $this->info('Interactions updated from Oracle DB successfully! ' . now());
                Log::info('Interactions updated from Oracle DB successfully! ' . now());
            } catch (\Exception $e) {
                Log::error('Error fetching data from Oracle API: ' . $e->getMessage());
                $this->info('Error fetching data from Oracle API: ' . $e->getMessage());
            }

            // Saving the last fetch DB run to cache
            $this->info('Saving the last fetch DB run to cache ' . now());
            Log::info('Saving the last fetch DB run to cache ' . now());
            // Cache::put('last_fetch_database_run', now());
            // }
        } catch (\Exception $e) {
            Log::error('Error in handle method: ' . $e->getMessage());
            $this->info('Error in handle method: ' . $e->getMessage());
        }
    }
}
