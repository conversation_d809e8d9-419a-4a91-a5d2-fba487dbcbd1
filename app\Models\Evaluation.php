<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
 
class Evaluation extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function groups()
    {
        return $this->hasMany(EvaluationGroup::class);
    }
    public function fields()
    {
        return $this->hasMany(EvaluationFields::class);
    }
    public function evaluationSubmissions()
    {
        return $this->hasMany(EvaluationSubmission::class,'evaluation_id');
    }
    public function supervisors()
    {
        return $this->belongsToMany(User::class, 'evaluation_supervisor', 'evaluation_id', 'user_id');
    }
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }
}