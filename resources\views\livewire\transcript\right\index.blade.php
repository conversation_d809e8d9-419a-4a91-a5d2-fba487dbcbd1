<div class="col-12 events-container">
    <div class="events-div pt-3 px-2 " >
        <div >
            <h5 class="text-muted fw-bold " style="text-decoration-line: none;">Events:</h5>
        </div>
        <div>
            <h5 class="text-info " style="cursor:pointer;text-decoration-line: @if($page == "Classification")  line  @else  none  @endif;" wire:click="setPage('Classification')">Classification</h5>
        </div>

        <div >
            <h5 class="text-warning  " style="cursor:pointer;text-decoration-line: @if($page == "Topics")  line  @else  none  @endif;" wire:click="setPage('Topics')">Topics</h5>

        </div >
        <div>
            <h5 class="text-success " style="cursor:pointer;text-decoration-line: @if($page == "Bad")  line  @else  none  @endif;" wire:click="setPage('Bad')">Bad Words</h5>
        </div >
{{--        <div >--}}
{{--            <h5 class="text-danger fw-bold" style="cursor:pointer;text-decoration-line: @if($page == "Negative")  line  @else  none  @endif;" wire:click="setPage('Negative')">Negative</h5>--}}
{{--        </div>--}}

    </div>
    <div class="row mt-1" style="display: flex; justify-content: center;">
        @if($page == "Classification")
            <div class="col-12">
                <style>
                    /* Positive Circle */
                    .positive {
                        background: conic-gradient(#40788b 0% {{$result['positive']}}%, #e5e7eb {{$result['positive']}}% 100%);
                    }

                    /* Neutral Circle */
                    .neutral {
                        background: conic-gradient(#03a24c 0% {{$result['neutral']}}%, #e5e7eb {{$result['neutral']}}% 100%);
                    }

                    /* Negative Circle */
                    .negative {
                        background: conic-gradient(#ef4444 0% {{$result['negative']}}%, #e5e7eb {{$result['negative']}}% 100%);
                    }
                </style>

                <div class="row justify-content-center" style="margin: 20px 20px 15px 20px;">
                    <div class="col-4 justify-content-center" style="text-align: -webkit-center;">
                        <div class="progress-circle positive">
                            <span class="percentage">{{$result['positive']}}</span>
                        </div>
                        <div class="label">Positive</div>
                    </div>
                    <div class="col-4 justify-content-center" style="text-align: -webkit-center;">
                        <div class="progress-circle neutral">
                            <span class="percentage">{{$result['neutral']}}</span>
                        </div>
                        <div class="label">Neutral</div>
                    </div>
                    <div class="col-4 justify-content-center" style="text-align: -webkit-center;">
                        <div class="progress-circle negative">
                            <span class="percentage">{{$result['negative']}}</span>
                        </div>
                        <div class="label">Negative</div>
                    </div>
                </div>
            </div>

        </div>
        <div class="col-12" style="height: 24vh;overflow: scroll;">
            <table class="table table-striped table-bordered" style="text-align: -webkit-center;">

                <thead class="sticky-top">
                <tr style="text-align: -webkit-center;">
                    <th style="text-align: -webkit-center;background: #e1e5e6;margin: 0 !important;font-size: 16px">Classification</th>
                    <th style="text-align: -webkit-center; background: #e1e5e6;margin: 0 !important;font-size: 16px">Score</th>
                    <th style="text-align: -webkit-center;background: #e1e5e6;margin: 0 !important;font-size: 16px">Text</th>


                </tr>
                </thead>
                <tbody>
                <tr></tr>

                @forelse($classifications as $classification)
                    <tr style="text-align: -webkit-center;">


                        <td style="text-align: -webkit-center;">{{\Illuminate\Support\Str::upper($classification->classification)}}</td>
                        <td style="text-align: -webkit-center;">{{$classification->score}}</td>
                        <td style="text-align: -webkit-center;">{{$classification->text}}</td>

                    </tr>
                @empty
                    <tr>
                        <td colspan="3">No classification available</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
        </div>

    @elseif($page == "Topics")
        <div class="col-12" style="height: 47vh;overflow: scroll;">
        <table class="table table-striped mt-2 table-bordered">
            <thead class="sticky-top">
            <tr style="text-align: -webkit-center;">
                <th style="text-align: -webkit-center;background: #e1e5e6;margin: 0 !important;font-size: 16px">Category</th>
                <th style="text-align: -webkit-center; background: #e1e5e6;margin: 0 !important;font-size: 16px">Detected Topics</th>



            </tr>
            </thead>
            <tbody>
           <tr></tr>
            @forelse($topics as $topic)
                @php
                    // Decode the JSON string
                    $decodedTopics = json_decode($topic->detected_topics, true);
                @endphp
                @if ($decodedTopics)
                    @foreach ($decodedTopics as $key => $value)
                        <tr style="text-align: -webkit-center;">
                            <td>{{ ucfirst($key) }}</td> <!-- Display the key -->
                            <td>{{ $value }}</td>       <!-- Display the value -->
                        </tr>
                    @endforeach
                @endif
            @empty
                <tr>
                    <td colspan="2">No topics available</td>
                </tr>
            @endforelse
            </tbody>
        </table>
        </div>
    @elseif($page == "Bad")
        <div class="col-12" style="height: 47vh;overflow: scroll;">
            <table class="table table-striped mt-2 table-bordered">
                <thead class="sticky-top">
                <tr style="text-align: -webkit-center;">
                    <th style="text-align: -webkit-center;background: #e1e5e6;margin: 0 !important;font-size: 16px">Key</th>
                    <th style="text-align: -webkit-center; background: #e1e5e6;margin: 0 !important;font-size: 16px">Values</th>



                </tr>
                </thead>
            <tbody>
            {{--<tr style="text-align: -webkit-center;">--}}
            {{--<td style="text-align: -webkit-center;">Key</td>--}}
            {{--<td style="text-align: -webkit-center;">Values</td>--}}
            {{--</tr>--}}
            <tr></tr>
            @forelse($bads as $bad)
                @php
                    // Decode the JSON string
                    $decodedTopics = json_decode($bad->words_detected, true);
                @endphp
                @if ($decodedTopics)
                    @foreach ($decodedTopics as $key => $values)
                        <tr style="text-align: -webkit-center;">
                            <td>{{ $key }}</td>
                            <td>
                            @if (is_array($values))
                                {{ implode(', ', $values) }}
                                @else
                                    {{ $values }}
                                @endif
                            </td>
                        </tr>
                    @endforeach
                @endif
            @empty
                <tr>
                    <td colspan="3">No bad words detected</td>
                </tr>
            @endforelse

            </tbody>
        </table>
        </div>
    @elseif($page == "Negative")
        <table class="table table-striped mt-4">
            <tbody>
            <tr>
                <th scope="row">1</th>
                <td>Mark</td>
                <td>Otto</td>
                <td>@mdo</td>
            </tr>
            <tr>
                <th scope="row">2</th>
                <td>Jacob</td>
                <td>Thornton</td>
                <td>@fat</td>
            </tr>
            <tr>
                <th scope="row">3</th>
                <td colspan="2">Larry the Bird</td>
                <td>@twitter</td>
            </tr>
            </tbody>
        </table>
    @endif
</div>
