<?php

namespace App\Livewire\Evaluation;



use App\Models\Evaluation;
use App\Models\EvaluationFields;
use App\Models\EvaluationGroup;
use App\Models\EvaluationSubmission;
use App\Models\EvaluationSubmissionAnswer;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use App\Models\Interaction;
use App\Models\VoiceRecord;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class ReportAnswers extends Component
{
    public $order_by = null;
    public $sort_by = null;
    public $limit = null;

    public $evaluation_id;
    public $form_name;
    public $question_id;
    public $submit_id;
    public $qualityPercentage = 100;
    public $chartColor = '#08a54f';
    public $array = [];
    public $arraySubmittion = [];
    public $arrayFields = [];
    public $name;
    public $month;
    public $year;
    public $source;
    public $week;
    public $referenceID;
    public $voiceFileName = NULL;
    public $todayDate;
    public $totalWeightPoints;
    public $totalPossibleWeightPoints;
    public $totalScore;
    public $totalPossibleScore;
    public $commentEvaluation;
    public $pathVoice;
    public $elapsedTime;


    use WithPagination,LivewireAlert;
    protected $paginationTheme = 'bootstrap';
    protected $listeners = ['callFunctionFromJS' => 'renderChartAfterLodded'];

    public function mount($submit_id)
    {
        $this->submit_id = $submit_id;
        $this->getEvaluationID();
        $this->getEvaluationName();
        $this->getQuestionAndAnswers();
        $this->getEvaluationForm();
        $this->getChartColor();
        if (null !== request()->query('call_id')) {
            $this->referenceID = request()->query('call_id');
            $this->getVoiceRecord();
        }

    }
    public function getVoiceRecord(){
        
        // $voiceRecord = VoiceRecord::query()
        //     ->where('callID',$this->referenceID)->first();
            // $this->voiceFileName = $voiceRecord->file_name ?? '';

    }
    public function getEvaluationID(){

        $evaluation_id = EvaluationSubmission::query()->where('id',$this->submit_id)->first();
        $this->evaluation_id = $evaluation_id['evaluation_id'];
        $this->name = $evaluation_id['name'];
        $this->elapsedTime = $evaluation_id['evaluation_duration'] ?? '00:00:00';
        $this->month = $evaluation_id['month'];
        $this->year = $evaluation_id['year'];
        $this->week = $evaluation_id['week'];
        $this->source = $evaluation_id['source'];
        $this->referenceID = $evaluation_id['referenceID'];
        $voiceRecord = Interaction::query()->where('id',$this->referenceID)->first();
        $this->pathVoice = $voiceRecord->call_id;
        $this->todayDate = $evaluation_id['date'];
        $this->totalWeightPoints = $evaluation_id['total_weight_points'];
        $this->totalPossibleWeightPoints = $evaluation_id['total_possible_weighted_points'];
        $this->totalScore = $evaluation_id['total_score'];
        $this->totalPossibleScore = $evaluation_id['total_possible_score'];
        $this->qualityPercentage = $evaluation_id['quality_percentage'];
        $this->commentEvaluation = $evaluation_id['commentEvaluation'];

        $this->arrayFields[] = json_decode($evaluation_id['extra_field_one']);
        $this->arrayFields[] = json_decode($evaluation_id['extra_field_tow']);
        $this->arrayFields[] = json_decode($evaluation_id['extra_field_three']);

    }
    public function getEvaluationName(){

        $evaluation_name = Evaluation::query()->where('id',$this->evaluation_id)->first();
        $this->form_name = $evaluation_name['evaluation_name'];

    }
    public function getEvaluationForm(){

        // $evaluationData = EvaluationGroup::with(['questions.answers', 'questions.header'])
        //     ->whereHas('questions.header')
        //     ->whereHas('questions.answers')
        //     ->where('evaluation_id', '=', $this->evaluation_id)
        //     ->get();
            $evaluationData = EvaluationGroup::with(['questions.answers', 'questions.header'])
            ->whereHas('questions.header')
            ->whereHas('questions.answers')
            ->where('evaluation_id', $this->evaluation_id)
            ->where('status', 1)
            ->whereHas('questions', function ($query) {
                $query->where('status', 1); // Filter based on the 'status' column in the 'questions' table
            })
            ->get();

        //Sort Array To BE => (Group -> Header -> Questions -> Answer)
        foreach ($evaluationData as $index => $group){



            foreach ($group['questions'] as $index1 => $dataQuestion) {
                if ($dataQuestion['status'] == 0) {
                    continue;
                }
                if (!isset($this->array[$group['group_name']][$group['group_weight']][$dataQuestion['header']['header_name']])) {
                    $this->array[$group['group_name']][$group['group_weight']][$dataQuestion['header']['header_name']] = [];
                }

                //Sort Array To BE => (Group -> Header -> Questions -> Answer)
                $this->array[$group['group_name']][$group['group_weight']][$dataQuestion['header']['header_name']][] = $dataQuestion;

            }

        }

    }
    public function getQuestionAndAnswers(){


        $evaluationAnswer = EvaluationSubmissionAnswer::query()
            ->where('evaluation_submission_id', $this->submit_id)
            ->get();
        $this->arraySubmittion = $evaluationAnswer;
//dd($this->arraySubmittion);

//        $data = EvaluationSubmission::with(['submissionAnswers'=>function ($query){
//
//            $query->with('question.header','question.group')
//                ->whereHas('question.header')
//                ->whereHas('question.group')
//                ->get();
//
//          }])
//            ->where('id',$this->submit_id)
//            ->get();



//        dd($data);
    }
    public function getChartColor(){
        if($this->qualityPercentage >= 80){
            $this->chartColor = '#08a54f';
        }elseif($this->qualityPercentage >= 60){
            $this->chartColor = '#bbcf34';
        }elseif($this->qualityPercentage >= 40){
            $this->chartColor = '#e8cf4d';
        }elseif($this->qualityPercentage >= 20){
            $this->chartColor = '#cf810b';
        }elseif($this->qualityPercentage >= 0){
            $this->chartColor = '#a82020';
        }

        $this->dispatch('chart', ['qualityPercentage' => $this->qualityPercentage, 'chartColor' => $this->chartColor]);

    }
    public function renderChartAfterLodded(){

        $this->dispatch('chart', ['qualityPercentage' => $this->qualityPercentage, 'chartColor' => $this->chartColor]);
    }


    public function render()
    {
//        dd($this->arraySubmittion);
//        dd('dd');
        return view('livewire.evaluation.report-answers');
    }
}
