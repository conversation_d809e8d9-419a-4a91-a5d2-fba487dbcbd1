<?php
namespace App\Http\Controllers;

use App\Helpers\Classification;
use App\Models\Encryption;
use App\Models\Interaction;
use App\Models\Transcript;
use App\Models\TranscriptionClassifications;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use ZipArchive;

class AudioController extends Controller
{

    public function playAudio($call_id, $agent_name = null, $agent_id = null)
    {

        ini_set('memory_limit', '-1');

        // Get The encryption status of call type
        $encryptionStatus = Encryption::where('type', 'call')->first();

        $callId = $call_id;
        // $callId = '0018FB6E-FDDF-15FE-8579-4301CA0AAA77-********@***********';
        // $callId = '0018FB6E-FDDF-15FE-8579-4301CA0AAA77-********@***********';
        $agentName       = $agent_name;
        $agentId         = $agent_id;
        $interactionData = Interaction::firstWhere('call_id', $call_id);
        $callDate        = $interactionData->arrival_time;
        $callDuration    = $interactionData->call_duration;
        $calllanguage    = $interactionData->language;
        $callAccount     = $interactionData?->organization?->name;

        if ($callAccount == 'Ewa') {
            $callAccount = 'Kease';
        }

        $callType           = $interactionData?->call_type;
        $playedBy           = $interactionData->listeners()->get();
        $callEvaluation     = $interactionData?->callEvaluation?->quality_percentage ?? 0;
        $aiEvaluation       = 98;
        $interactionDetails = $interactionData->interactionDetails;
        $detailsPercentage  = $this->calculateDetailsPercentage($callDuration, $interactionDetails);
        $userGroup          = $interactionData?->userGroup?->name;

        //$callEvaluation = 80;

        $timeData = [
            'calculatedEndTime' => $this->calculateEndTime($callDate, $callDuration),
            'duration'          => $callDuration,
        ];
        $callDetails = [
            'caller_id'  => $interactionData?->caller_id,
            'called_id'  => $interactionData?->called_id,
            'call_ender' => $interactionData?->call_ender,
        ];

        // Check if the call_id already has the suffix @***********....
        if (! str_ends_with($callId, '@***********')) {
            $callId .= '@***********';
        }

        $fileName = "{$callId}.wav";

        $filePath = Storage::disk('audio')->path('calls/' . $fileName);

        $classifications = new Classification($callId);
        if (file_exists($filePath)) {
            // If the file exists, return it as a response
            return view('play-new')->with([
                'callId'            => $callId,
                'agent_name'        => $agentName,
                'agent_id'          => $agentId,
                'callPath'          => $filePath,
                'callName'          => $fileName,
                'callDate'          => $callDate,
                'callAccount'       => $callAccount,
                'calllanguage'      => $calllanguage,
                'callType'          => $callType,
                'transcript'        => $this->getTranscription($callId),
                'badWords'          => $this->getBadWords($callId),
                'aiEve'             => $this->aiEve($callId),
                'classifications'   => $classifications->getResults(),
                'getCallQuality'    => $this->getCallQuality($callId)[0]->overall_result ?? 'UNKNOWN',
                'getCallIssues'    => $this->getCallIssues($callId),
                'detailsPercentage' => $detailsPercentage,
                'playedBy'          => $playedBy,
                'timeData'          => $timeData,
                'callDetails'       => $callDetails,
                'callEvaluation'    => $callEvaluation,
                'aiEvaluation'      => $aiEvaluation,
                'userGroup'         => $userGroup,
            ]);

        }

        $response      = null;
        $linuxResponse = null;
/*
        $server86Response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//server86.php', [
            'call_id'  => $callId,
            'call_day' => $callDate->format('Y-m-d'),
        ]);

        $server76Response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//server76.php', [
            'call_id'  => $callId,
            'call_day' => $callDate->format('Y-m-d'),
        ]);

        if ($server86Response) {
            if ($server86Response->successful()) {Storage::disk('audio')->put($filePath, $server86Response->body());}
        } elseif ($server76Response) {
            if ($server76Response->successful()) {Storage::disk('audio')->put($filePath, $server76Response->body());}
        } */

        return view('play-new')->with([
            'callId'            => $callId,
            'agent_name'        => $agentName,
            'agent_id'          => $agentId,
            'callPath'          => $filePath,
            'callName'          => $fileName,
            'callDate'          => $callDate,
            'calllanguage'      => $calllanguage,
            'callAccount'       => $callAccount,
            'callType'          => $callType,
            'transcript'        => $this->getTranscription($callId),
            'summary'           => $this->getSummary($callId),
            'reason'            => $this->getReason($callId)['type'] ?? null,
            'sub_reason'        => $this->getReason($callId)['subtype'] ?? null,
            'badWords'          => $this->getBadWords($callId),
            'aiEve'             => $this->aiEve($callId),
            'classifications'   => $classifications->getResults(),
            'getCallQuality'    => $this->getCallQuality($callId)[0]->overall_result ?? 'UNKNOWN',
            'getCallIssues'    => $this->getCallIssues($callId),
            'detailsPercentage' => $detailsPercentage,
            'playedBy'          => $playedBy,
            'timeData'          => $timeData,
            'callDetails'       => $callDetails,
            'callEvaluation'    => $callEvaluation,
            'aiEvaluation'      => $aiEvaluation,
            'userGroup'         => $userGroup,

        ]);

        // Check if the file exists
        // if (Storage::exists($filePath)) {
        if (file_exists($filePath)) {
            // If the file exists, return it as a response
            return view('play-new')->with([
                'callId'            => $callId,
                'agent_name'        => $agentName,
                'agent_id'          => $agentId,
                'callPath'          => $filePath,
                'callName'          => $fileName,
                'callDate'          => $callDate,
                'calllanguage'      => $calllanguage,
                'callAccount'       => $callAccount,
                'aiEve'             => $this->aiEve($callId),
                'callType'          => $callType,
                'transcript'        => $this->getTranscription($callId),
                'badWords'          => $this->getBadWords($callId),
                'classifications'   => $classifications->getResults(),
                'getCallQuality'    => $this->getCallQuality($callId)[0]->overall_result ?? 'UNKNOWN',
                'getCallIssues'    => $this->getCallIssues($callId),
                'detailsPercentage' => $detailsPercentage,
                'playedBy'          => $playedBy,
                'timeData'          => $timeData,
                'callDetails'       => $callDetails,
                'userGroup'         => $userGroup,

                //--+9

            ]);

            // return response()->file(storage_path("app/{$filePath}"), ['Content-Type' => 'audio/wav']);
            // return response()->file(public_path("/{$filePath}"), ['Content-Type' => 'audio/wav']);
        }

        // If the file does not exist, download it

        $response      = null;
        $linuxResponse = null;

        // if modanisa 43 or bci 14, get it from 73 ftp server
        if ($callDate < Carbon::parse('06-11-2024')) {
            if (in_array($callAccount, [43, 14])) {
                $response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadRecordingModanisaBci.php', [
                    'call_id' => $callId,
                ]);
            }
            // else if not modanisa or bci
            else {
                if ($callDate <= Carbon::parse('24-10-2024')) {
                    $response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadCallSub.php', [
                        'call_id'  => $callId,
                        'call_day' => $callDate->format('Y_m_'),
                    ]);
                } else {
                    $response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadRecording.php', [
                        'call_id' => $callId,
                    ]);
                }
            }
            // since 5/11/2024, get the call from linux
        } else {
            $linuxResponse = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//linux-downloadCalls.php', [
                'call_id'  => $callId,
                'call_day' => $callDate->format('Y-m-d'),
            ]);
        }

        // Check if the request was successful
        if ($response) {
            if ($response->successful()) {
                if ($encryptionStatus->status) {

                    $interaction = Interaction::where('call_id', $callId)->first();
                    if ($interaction) {

                        $interaction->update(['is_encrypted', true]);
                        $encryptedContent = encrypt($response->body(), config('app.key'));

                        Storage::disk('public')->put($filePath, $encryptedContent);
                    } else {
                        Storage::disk('public')->put($filePath, $response->body());
                    }
                } else {

                    Storage::disk('public')->put($filePath, $response->body());
                }
                return view('play.new')->with([
                    'callId'     => $callId,
                    'agent_name' => $agentName,
                    'agent_id'   => $agentId,
                    'callPath'   => $filePath,
                    'callName'   => $fileName,
                    'transcript' => Transcript::where('call_id', '=', $callId)
                        ->orderBy('duration_from')
                        ->get(),

                ]);
            } else {
                session()->flash('alert_no_call', 'This interaction is still not fetched from the system. Please try again later.');

                // Redirect back to the original page
                return redirect()->back();
            }
        }

        // Linux
        if ($linuxResponse) {
            if ($linuxResponse->successful()) {
                if ($encryptionStatus->status) {

                    $interaction = Interaction::where('call_id', $callId)->first();
                    if ($interaction) {

                        $interaction->update(['is_encrypted', true]);
                        $encryptedContent = encrypt($linuxResponse->body(), config('app.key'));

                        Storage::disk('public')->put($filePath, $encryptedContent);
                    } else {
                        Storage::disk('public')->put($filePath, $linuxResponse->body());
                    }
                } else {

                    Storage::disk('public')->put($filePath, $linuxResponse->body());
                }
                return view('play.new')->with([
                    'callId'     => $callId,
                    'agent_name' => $agentName,
                    'agent_id'   => $agentId,
                    'callPath'   => $filePath,
                    'callName'   => $fileName,
                    'transcript' => Transcript::where('call_id', '=', $callId)
                        ->orderBy('duration_from')
                        ->get(),
                ]);
            } else {
                session()->flash('alert_no_call', 'This interaction is still not fetched from the system. Please try again later.');

                // Redirect back to the original page
                return redirect()->back();
            }
        }
    }

    public function decrypt($fileName, $call_id)
    {

        $is_encrypted = Interaction::where('is_encrypted', true)->where('call_id', $call_id)->first();

        $encryptionKey = config('app.key'); // Retrieve your encryption key

        if ($is_encrypted) {
            $filePath = "audio/{$fileName}";
        } else {
            $filePath = "audio/{$fileName}";
        }

        $encryptedContent = Storage::disk('public')->get("{$filePath}");

        if ($is_encrypted) {
            $file = decrypt($encryptedContent, $encryptionKey);
        } else {
            $file = $encryptedContent;
        }

        // Decrypt the content

        // Return the decrypted content as a response with appropriate headers
        return response($file)->header('Content-Type', 'audio/wav');
    }

    public function userPlayedInteraction(Request $req)
    {
        $user        = Auth::user();
        $interaction = Interaction::firstWhere('call_id', $req->interaction_id);

        $interaction->listeners()->attach([$user->id => ['created_at' => now()]]);
    }

    public function downloadCall($call_id)
    {
        $callId   = $call_id;
        $callDate = Interaction::firstWhere('call_id', $call_id)->arrival_time;

        // Append suffix if missing
        if (! str_ends_with($callId, '@***********')) {
            $callId .= '@***********';
        }

        $fileName = "{$callId}.wav";
        $filePath = "audio/{$fileName}";

        // Check if the file already exists locally
        if (! file_exists(public_path($filePath))) {
            // Determine API endpoint based on date
            $apiUrl = $callDate <= Carbon::parse('2024-10-24')
            ? 'https://oms.extwebonline.com/Extensya_APIs/recording/downloadCallTest.php'
            : 'https://oms.extwebonline.com/Extensya_APIs/recording/downloadRecording.php';

            // Send request to fetch the audio file
            $response = Http::withoutVerifying()->timeout(1200)->get($apiUrl, [
                'call_id'  => $callId,
                'call_day' => $callDate->format('Y_m_'),
            ]);

            // Check if the request was successful and save the file
            if ($response->successful()) {
                Storage::disk('public')->put($filePath, $response->body());
            } else {
                return response()->json(['error' => 'Failed to download the file from the API.'], 500);
            }
        }

        // Serve the file as a download response
        return response()->download(public_path($filePath), $fileName, ['Content-Type' => 'audio/wav']);
    }

    private function calculateEndTime($start_time, $duration)
    {
        $time = $duration; // Format: HH:MM:SS

        // Split the time into hours, minutes, and seconds
        list($hours, $minutes, $seconds) = explode(':', $time);

        // Convert to seconds
        $total_seconds = ($hours * 3600) + ($minutes * 60) + $seconds;

        // Convert to minutes
        // $total_minutes = ($hours * 60) + $minutes + ($seconds / 60);

        $date = Carbon::createFromFormat('Y-m-d H:i:s', $start_time);
        $date->addSeconds($total_seconds);
        return $date;
    }

    private function getTranscription($call_id)
    {
        $gotcallId = explode('@', $call_id)[0];
        return DB::select("
        SELECT DISTINCT
        a.*,
        b.score,
        b.classification,
        b.calls_transcription_id
        FROM
        calls_transcription a
        LEFT JOIN
        transcription_classifications b
        ON
        a.id = b.calls_transcription_id
        WHERE
        a.call_id = ?
                AND a.content NOT LIKE '%القناة%'
                AND a.content NOT LIKE '%ترجمة نانسي قنقر%'
             AND NOT (
                    a.avg_logprob < 0
                    AND a.no_speech_prob > 0.13
                    AND a.compression_ratio > 8.055
                )
            ORDER BY
                a.duration_from,
                a.duration_to;
        ", [$gotcallId]);
    }
    private function getSummary($call_id)
    {
        $gotcallId = explode('@', $call_id)[0];
        return DB::selectOne("
        SELECT summary
        FROM calls_summary
        WHERE call_id = ?
        ORDER BY id DESC
        LIMIT 1
    ", [$gotcallId]);
    }
    private function getReason($call_id)
    {
        $gotcallId = explode('@', $call_id)[0];

        $row = DB::selectOne("
        SELECT call_type, sub_type
        FROM calls_summary
        WHERE call_id = ?
        ORDER BY id DESC
        LIMIT 1
        ", [$gotcallId]);

        // Return as associative array
        return $row ? ['type' =>trim($row->call_type), 'subtype' => trim($row->sub_type)] : null;
    }

    private function getBadWords($call_id)
    {
        return DB::select("
            SELECT words_detected FROM transcription_bad_words WHERE call_id = ?
        ", [$call_id]);
    }
    public function aiEve1($callId)
    {
        $defaultClassifications = [
            'positive' => 0,
            'negative' => 0,
            'neutral'  => 0,
        ];

        // Fetch classifications from the database
        $classifications = TranscriptionClassifications::selectRaw('
        LOWER(classification) as classification,
        ROUND((COUNT(*) * 100.0) / (SELECT COUNT(*) FROM transcription_classifications WHERE call_id = ?), 1) as average_percentage
        ', [$callId])
            ->where('call_id', $callId)
            ->groupByRaw('LOWER(classification)')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->classification => $item->average_percentage];
            })
            ->toArray();

        $data = array_merge($defaultClassifications, $classifications);

        if ($classifications) {

            return round(100 - $data['negative']);

        } else {
            return 0;
        }
    }
    public function aiEve($callId)
    {
        $defaultClassifications = [
            'positive' => 0,
            'negative' => 0,
            'neutral'  => 0,
        ];

        $interaction = Interaction::where('call_id', $callId)->first();

        if ($interaction && ! is_null($interaction->ai_score) && $interaction->ai_score > 0) {
            return $interaction->ai_score;
        }

        // Fetch classifications from the database
        $classifications = TranscriptionClassifications::selectRaw('
            LOWER(classification) as classification,
            ROUND((COUNT(*) * 100.0) / (SELECT COUNT(*) FROM transcription_classifications WHERE call_id = ?), 1) as average_percentage
        ', [$callId])
            ->where('call_id', $callId)
            ->groupByRaw('LOWER(classification)')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->classification => $item->average_percentage];
            })
            ->toArray();

        $data = array_merge($defaultClassifications, $classifications);

        $aiScore = $classifications ? round(100 - $data['negative']) : 0;

        if ($interaction) {
            $interaction->update(['ai_score' => $aiScore]);
        }

        return $aiScore;
    }
    private function calculateDetailsPercentage($time, $callDetails)
    {
        list($hours, $minutes, $seconds) = explode(':', $time);

        // Convert to total seconds
        $totalSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
        if ($totalSeconds != 0) {
            return [
                "left_channel_duration"  => round(($callDetails?->left_channel_duration / $totalSeconds) * 100) ?? 0,
                "right_channel_duration" => round(($callDetails?->right_channel_duration / $totalSeconds) * 100) ?? 0,
                "silence_duration"       => round(($callDetails?->silence_duration / $totalSeconds) * 100) ?? 0,
                "overtalk_duration"      => round(($callDetails?->overtalk_duration / $totalSeconds) * 100) ?? 0,
                "other_duration"         => round(($callDetails?->other_duration / $totalSeconds) * 100) ?? 0,

            ];
        } else {
            return [
                "left_channel_duration"  => 0,
                "right_channel_duration" => 0,
                "silence_duration"       => 0,
                "overtalk_duration"      => 0,
                "other_duration"         => 0,

            ];
        }
    }

    private function getCallQuality($call_id)
    {
        return DB::select("
            SELECT
            call_id,
            CASE
                WHEN SUM(CASE WHEN final_result = 'BAD'  THEN 1 ELSE 0 END) > 0 THEN 'BAD'
                WHEN SUM(CASE WHEN final_result = 'GOOD' THEN 1 ELSE 0 END) >= 2 THEN 'GOOD'
                ELSE 'UNKNOWN'
            END AS overall_result
            FROM
            quality_checks
            WHERE
            call_id = ?
            GROUP BY
            call_id;

        ", [$call_id]);
    }
    private function getCallIssues($call_id)
    {
        $issues      = [];
        $issuesQuery = DB::select("
           SELECT
            call_id,
            GROUP_CONCAT(issues SEPARATOR ', ') AS merged_issues
            FROM
            quality_checks
            WHERE
            call_id = ?
            GROUP BY
            call_id;

        ", [$call_id]);
        $issues = $issuesQuery ? explode(',', $issuesQuery[0]->merged_issues) : null;
        if ($issues) {
            foreach ($issues as $key => $issue) {
                $issues[$key] = trim($issue);
            }
        }
        return $issues;
    }
    // public function streamAudio(Request $request, $filename)
    // {
    //     // Ensure the user is authenticated
    //     if (!auth()->check()) {
    //         abort(403, 'Unauthorized');
    //     }
    //     $referer = $request->headers->get('referer');

    //     if (!$referer || !str_contains($referer, config('app.url'))) {
    //         abort(403, 'Unauthorized access');
    //     }
    //     $path = Storage::disk('audio')->path('calls/' . $filename);

    //     // Check if the file exists in the storage

    //     if (!file_exists($path)) {
    //         abort(404, 'File not found');
    //     }

    //     // Get the file's mime type
    //     $mimeType = File::mimeType($path);

    //     // Stream the audio file
    //     return response()->stream(function () use ($path) {
    //         $stream = fopen($path, 'rb');
    //         fpassthru($stream);
    //     }, 200, [
    //         "Content-Type" => $mimeType,
    //         "Content-Length" => filesize($path),
    //     ]);
    // }
    public function streamAudio(Request $request, $filename)
    {
        // ✅ Ensure user is authenticated
        if (! auth()->check()) {
            abort(403, 'Unauthorized');
        }

        // ✅ Ensure the referer is from this app
        $referer = $request->headers->get('referer');
        if (! $referer || ! str_contains($referer, config('app.url'))) {
            abort(403, 'Unauthorized access');
        }

        $path = Storage::disk('audio')->path('calls/' . $filename);

        // ✅ If file does not exist locally, fetch it
        if (! file_exists($path)) {
            $callId = explode('_', $filename)[0];

            $interactionData = Interaction::firstWhere('call_id', $callId);
            if (! $interactionData) {
                Log::warning('⚠️ No interaction found for call_id', ['call_id' => $callId]);
                abort(404, 'Call ID not found in interactions');
            }

            $callDate = $interactionData->arrival_time;

            try {
                Log::info('📡 Fetching audio from remote server', [
                    'call_id'   => $callId,
                    'call_date' => $callDate->format('Y-m-d'),
                ]);

                $response = Http::withoutVerifying()
                    ->timeout(120)
                    ->get('https://oms.extwebonline.com/I-log/downloadRecording.php', [
                        'call_id'   => $callId,
                        'call_date' => $callDate->format('Y-m-d'),
                    ]);

                if ($response->successful()) {
                    Log::info('✅ Successfully fetched audio', [
                        'call_id'    => $callId,
                        'size_bytes' => strlen($response->body()),
                    ]);

                    // Save file
                    Storage::disk('audio')->put('calls/' . $filename, $response->body());

                    // Confirm file was written
                    $path = Storage::disk('audio')->path('calls/' . $filename);
                    if (! file_exists($path)) {
                        Log::error('❌ File save failed after download', ['path' => $path]);
                        abort(500, 'Failed to save audio file.');
                    }

                } else {
                    Log::error('❌ Remote audio fetch failed', [
                        'call_id' => $callId,
                        'status'  => $response->status(),
                        'body'    => $response->body(),
                    ]);
                    abort(500, 'Failed to fetch audio from remote server.');
                }

            } catch (\Throwable $e) {
                Log::error('🔥 Exception during audio download', [
                    'call_id' => $callId,
                    'message' => $e->getMessage(),
                    'trace'   => $e->getTraceAsString(),
                ]);
                abort(500, 'Error fetching audio.');
            }
        }

        // ✅ Stream file with support for byte ranges
        $mimeType = File::mimeType($path);
        $fileSize = filesize($path);
        $start    = 0;
        $length   = $fileSize;

        $headers = [
            "Content-Type"  => $mimeType,
            "Accept-Ranges" => "bytes",
        ];

        if ($request->headers->has('Range')) {
            $range = $request->headers->get('Range');
            if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
                $start  = intval($matches[1]);
                $end    = isset($matches[2]) && $matches[2] !== '' ? intval($matches[2]) : $fileSize - 1;
                $length = $end - $start + 1;

                $headers['Content-Range']  = "bytes $start-$end/$fileSize";
                $headers['Content-Length'] = $length;

                return response()->stream(function () use ($path, $start, $length) {
                    $stream = fopen($path, 'rb');
                    fseek($stream, $start);
                    echo fread($stream, $length);
                    fclose($stream);
                }, 206, $headers);
            }
        }

        $headers['Content-Length'] = $fileSize;

        return response()->stream(function () use ($path) {
            $stream = fopen($path, 'rb');
            fpassthru($stream);
            fclose($stream);
        }, 200, $headers);
    }

    public function fetchFromRemote(Request $request)
    {
        try {
            // Validate input
            $validated = $request->validate([
                'call_ids'   => 'required|array',
                'call_ids.*' => 'string',
            ]);

            Log::info('Received call_ids', $validated);

            // Send JSON to remote server
            $remoteResponse = Http::withHeaders([
                'Accept'       => 'application/json',
                'Content-Type' => 'application/json',
            ])
                ->withoutVerifying()
                ->timeout(120)
                ->send('POST', 'https://oms.extwebonline.com/I-log/fetch_channels_list.php', [
                    'body' => json_encode([
                        'call_ids' => $validated['call_ids'],
                    ]),
                ]);

            if (! $remoteResponse->ok()) {
                Log::error('Remote server error', ['status' => $remoteResponse->status(), 'body' => $remoteResponse->body()]);
                return response()->json(['error' => 'Failed to contact internal file API'], 502);
            }

            $fileList = $remoteResponse->json();
            Log::debug('Raw remote response body', ['body' => $remoteResponse->body()]);
            Log::info('Raw request payload', $request->all());
            if (! is_array($fileList) || empty($fileList)) {
                Log::error('Empty or invalid file list from remote');
                return response()->json(['error' => 'No files returned by internal API'], 500);
            }

            // Prepare ZIP
            $zipFileName = 'calls_' . time() . '.zip';
            $zipPath     = storage_path("app/tmp/$zipFileName");

            if (! file_exists(dirname($zipPath))) {
                mkdir(dirname($zipPath), 0755, true);
            }

            $zip = new ZipArchive;
            if ($zip->open($zipPath, ZipArchive::CREATE) !== true) {
                Log::error('Failed to open ZIP for writing', ['path' => $zipPath]);
                return response()->json(['error' => 'Could not create ZIP file'], 500);
            }

            // Add files to ZIP
            $addedFiles = 0;

            foreach ($fileList as $entry) {
                $callId = $entry['callid'];

                foreach ([1, 2] as $channelNum) {
                    $field = "channel{$channelNum}";
                    if (! isset($entry[$field]['filename'])) {
                        continue;
                    }

                    $url      = $entry[$field]['filename'];
                    $filename = basename($url);

                    $wavResponse = Http::withoutVerifying()->timeout(60)->get($url);

                    if ($wavResponse->ok()) {
                        $zip->addFromString($filename, $wavResponse->body());
                        $addedFiles++;
                    } else {
                        Log::warning("Failed to fetch WAV file", ['url' => $url, 'status' => $wavResponse->status()]);
                    }
                }
            }

            $zip->close();

            if (! file_exists($zipPath)) {
                Log::error("ZIP file was not created: $zipPath");
                return response()->json(['error' => 'ZIP creation failed'], 500);
            }

            if ($addedFiles === 0) {
                Log::error("No files were added to ZIP.");
                unlink($zipPath);
                return response()->json(['error' => 'No valid audio files were found.'], 500);
            }

            Log::info("Serving ZIP file: $zipPath");

            return new StreamedResponse(function () use ($zipPath) {
                readfile($zipPath);
                unlink($zipPath); // Clean up after sending
            }, 200, [
                'Content-Type'        => 'application/zip',
                'Content-Disposition' => 'attachment; filename="call_recordings.zip"',
                'Content-Length'      => filesize($zipPath),
            ]);

        } catch (\Throwable $e) {
            Log::error('Exception in fetchFromRemote', ['error' => $e->getMessage()]);
            return response()->json([
                'error'   => 'Internal server error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
