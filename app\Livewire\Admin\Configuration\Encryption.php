<?php

namespace App\Livewire\Admin\Configuration;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Encryption extends Component
{
    use WithPagination,LivewireAlert;

    public $type;
    public $status;

    public $modalId;
    public $modalIdShow;

    public function getListeners()
    {
        return [
            'confirmed','modelFormReset',
            'getEncryption',
        ];
    }

    public function statusUpdateModal($id){

        $this->modalId =$id;

        $fb = \App\Models\Encryption::find($this->modalId);
        $fp = $fb->update(['status' => $fb->status != 0 ? 0 : 1]);
        $this->status = $fb->status;
        $this->alert('success', 'Successfully Updated!',[
            'timerProgressBar' => true,
            'timer' => '6000',
        ]);
    }


    public function getEncryptions()
    {
        try
        {
            return \App\Models\Encryption::query()->get();
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function modelData()
    {
        return [
            'type'          => $this->type,
            'status'        => $this->status,
        ];
    }

    public function rules()
    {
        return [
            'type' =>       ['required','unique:encryptions,type,'.$this->modalId],
            'status'  =>    ['required'],

        ];
    }

    public function update(){


        $this->validate();

        try {
            \App\Models\Encryption::find($this->modalId)->update($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }

    public function closeModal()
    {
        $this->modelFormReset();
    }

    public function showModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
        $this->modalIdShow = "on";
    }

    public function showUpdateModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
    }

    public function modelLoadData()
    {
        $encryption = \App\Models\Encryption::find($this->modalId);
        $this->type = $encryption->type;
        $this->status = $encryption->status;

    }

    public function modelFormReset()
    {
        $this->tpe = "";
        $this->status = "";

        $this->modalId=null;
        $this->modalIdShow=null;

        $this->resetValidation();
    }

    public function render()
    {
        return view('livewire.admin.configuration.encryption', ['encryptions' => $this->getEncryptions()]);
    }
}
