<?php

namespace App\Http\Controllers\Auth;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class Login<PERSON>ontroller extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/analytics-new';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }


    // to remove the default rate limiter 
    protected function hasTooManyLoginAttempts(Request $request)
    {
        return false;
    }


    protected function authenticated(Request $request, $user)
    {

        // Password expiration check every 3 months
        if (Carbon::parse($user?->pass_reset_date)->lt(now()->subMonths(3))) {

            Auth::logout();
            Session::flash('password_reset_required', 'Password Expired, You must reset your password every 3 months.');
            return redirect()->route('password.request');
        }

        // Check if the authenticated user's role is 4 (agents) or 2 (supervisors)
        // and redirect them to the recordings page becuz they dont have access to analytics
        if (in_array($user->role, [4, 2, 6])) {
            return redirect('/recordings');
        }

        return redirect($this->redirectTo);
    }

    public function username()
    {
        return 'agent_id';
    }

    public function login(Request $request)
    {
        $this->validateLogin($request);

        // code here for 5 wrong attempts ------------------------------------------------
        $user = User::where('agent_id', $request->input('agent_id'))->first();

        if ($user?->is_locked >= 5) {
            Auth::logout();
            $user->update(['enabled' => 0]);
            Session::flash('lock_error', 'Your account is locked, please contact the admin');
            return back();
        } else {

            if (Auth::attempt($request->only('agent_id', 'password'))) {
                // If successful, reset `is_locked` to zero
                $user->update(['is_locked' => 0]);
            } else {
                // If login fails, increment `is_locked` by one
                if ($user) {
                    $user->increment('is_locked');
                }
            }
        }

        //********************************************************* */

        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if (
            method_exists($this, 'hasTooManyLoginAttempts') &&
            $this->hasTooManyLoginAttempts($request)
        ) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        if ($this->attemptLogin($request)) {
            if ($request->hasSession()) {
                $request->session()->put('auth.password_confirmed_at', time());
            }

            return $this->sendLoginResponse($request);
        }

        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }
}
