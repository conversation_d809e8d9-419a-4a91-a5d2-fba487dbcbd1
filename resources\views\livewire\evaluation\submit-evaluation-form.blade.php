
@php

$signedUrl = URL::temporarySignedRoute(
    'audio',
    now()->addMinutes(30),
    ['filename' => $callIdToServe . '_final.wav']
    );

@endphp
<div class="container-fluid mt-3 px-4">
    <div class="mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            This is {{ $form_name }} Form
                        </b>
                        {{-- <button class="btn btn-success shadow w-px-200 btn-color float-end " id="open_modal" data-bs-toggle="modal" data-bs-target="#evaluationModal" ><i class="fa fa-plus fa-style"></i> Add New Form</button> --}}

                        <input type="date" class="form-control" wire:model.defer="todayDate" style="border-radius: 5px;background-color: white;float: right;width: 16%;margin-left: 10px;">
                        @error('todayDate')
                            <small class="text-danger" style="right: 4%;position: absolute;margin-top: 45px;"> {{ $message }} </small>
                        @enderror
                        <button type="button" class="btn btn-danger" style="float: right;">{{ $elapsedTime }}</button>

                    </h5>
                    <h6 class="text-muted">
                        <img src="{{ asset('assets/images/evaluation/eye.png') }}" alt="i Icon" width="20" height="20" style="margin-top: -.4%;">
                        <span style="">This page allows you to evaluate based on agent, source, reference Id.</span>
                    </h6>

                </div>
            </div>
        </div>

    </div>

    <div class="section-one shadow">
        <div class="section-one-head"></div>
        <div class="section-one-field">
            <div class="row row-field">
                <div class="col-3">
                    <label for="name" class="col-form-label">Name:</label>
                    <div class="custom-select" style="position: relative">
                        <select class="form-control" wire:model.defer="user_id">
                            <option value="">Select</option>
                            @forelse($Agent ?? [] as $agent)
                                <option value="{{ $agent->id }}">{{ $agent->full_name }}</option>
                            @empty
                                <option value="">No agent found</option>
                            @endforelse
                        </select>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z" />
                        </svg>
                    </div>
                    @error('user_id')
                        <small class="text-danger"> {{ $message }} </small>
                    @enderror
                </div>
                <div class="col-3">
                    <label for="year" class="col-form-label">Year:</label>
                    <div class="custom-select" style="position: relative">
                        <select class="form-control" wire:model.defer="year">
                            {{-- <option value="">Select</option> --}}
                            @php
                                $currYear = date('Y');
                                for($i = -1; $i <1; $i++)
                                {
                                    $year = $currYear + $i;
                                    echo "<option value='$year'>$year</option>";
                                }
                            @endphp
                        </select>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z" />
                        </svg>
                    </div>
                    @error('year')
                        <small class="text-danger"> {{ $message }} </small>
                    @enderror
                </div>
                <div class="col-3">
                    <label for="month" class="col-form-label">Month:</label>
                    <div class="custom-select" style="position: relative">
                        <select class="form-control" wire:model.defer="month">
                            <option value="{{ $month }}">{{ $month }}</option>

                        </select>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z" />
                        </svg>
                    </div>
                    @error('month')
                        <small class="text-danger"> {{ $message }} </small>
                    @enderror
                </div>
                <div class="col-3">
                    <label for="referenceID" class="col-form-label">Reference ID:</label>
                    <input type="text" class="form-control" wire:model.defer="referenceID" @if ($referenceID) readonly @endif />
                    @error('referenceID')
                        <small class="text-danger"> {{ $message }} </small>
                    @enderror
                </div>
            </div>
            <div class="row row-field">
                <div class="col-3">
                    <label for="week" class="col-form-label">Source:</label>
                    <div class="custom-select" style="position: relative">
                        <select class="form-control" wire:model.defer="source">
                            {{-- <option value="">Select</option> --}}
                            <option value="Calls">Calls</option>
                            {{-- <option value="Facebook">Facebook</option>
                            <option value="Instrgram">Instagram</option>
                            <option value="Twitter">Twitter</option>
                            <option value="Whatsapp">Whatsapp</option>
                            <option value="LinKden">linkedin</option>
                            <option value="Youtube">Youtube</option> --}}
                        </select>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z" />
                        </svg>
                    </div>
                    @error('source')
                        <small class="text-danger"> {{ $message }} </small>
                    @enderror
                </div>
                <div class="col-3">
                    <label for="week" class="col-form-label">Week:</label>
                    <input type="text" class="form-control" wire:model="week" readonly />
                    @error('week')
                        <small class="text-danger"> {{ $message }} </small>
                    @enderror
                </div>

                @foreach ($Evaluation_fields as $field)
                    @if ($field->type == 'Dropdown')
                        <div class="col-3">
                            <label for="{{ $field->label }}" class="col-form-label">{{ $field->label }}:</label>
                            <div class="custom-select" style="position: relative">
                                <select class="form-control" wire:model.defer="arrayFields.{{ $field->id }}">
                                    <option value="">Select</option>
                                    @foreach (json_decode($field->value) as $option)
                                        <option value="{{ $option }}">{{ $option }}</option>
                                    @endforeach
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                    <path d="M7 10l5 5 5-5z" />
                                </svg>
                            </div>
                            @error('arrayFields.' . $field->id)
                                <small class="text-danger"> {{ $message }} </small>
                            @enderror
                        </div>
                    @elseif($field->type == 'Comment')
                        <div class="col-3">
                            <label for="{{ $field->label }}" class="col-form-label">{{ $field->label }}:</label>
                            <textarea class="form-control" wire:model.defer="arrayFields.{{ $field->id }}"></textarea>
                            @error('arrayFields.' . $field->id)
                                <small class="text-danger"> {{ $message }} </small>
                            @enderror
                        </div>
                    @elseif($field->type == 'Week')
                    @else
                        <div class="col-3">
                            <label for="{{ $field->label }}" class="col-form-label">{{ $field->label }}:</label>
                            <input type="{{ $field->type }}" class="form-control" wire:model.defer="arrayFields.{{ $field->id }}" />
                            @error('arrayFields.' . $field->id)
                                <small class="text-danger"> {{ $message }} </small>
                            @enderror
                        </div>
                    @endif
                @endforeach

            </div>
        </div>
    </div>
    @if ($referenceID)
        <style>
            .play-button:hover {
                color: #00a34e !important;
            }

            #playButton,
            #pauseButton,
            #openSpeedSelectorButton {
                font-size: 16px;
                cursor: pointer;
                width: 3rem;
                height: 3rem;
            }

            #openSpeedSelectorButton {
                font-size: 16px;
                cursor: pointer;
            }

            #playButton {
                background-color: #abc959;
                color: white;
                border: none;
            }

            #pauseButton {
                background-color: #02a34e;
                color: white;
                border: none;
            }

            #openSpeedSelectorButton {
                background-color: #2196F3;
                color: white;
                border: none;
            }

            #speedSelectorModal {
                display: inline;
            }

            .parent-btn {
                margin-top: 0.5rem;
                display: flex;
                justify-content: center;
                gap: 1rem;
            }

            #duration {
                float: right;
                background-color: #c6d64e;
                border-radius: 0.5rem;
                padding: 0.1rem 0.3rem;
                font-weight: 400;
                color: black;
            }

            #time {
                background-color: #03a34d;
                border-radius: 0.5rem;
                padding: 0.1rem 0.3rem;
                font-weight: 400;
                color: white;
            }

            #playButton,
            #pauseButton {
                /* display: flex; */
                justify-content: center;
                align-items: center;
            }
        </style>
        <div class="row mx-3 d-flex ps-5" wire:ignore>
            {{-- filters card  --}}
            <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column" style="padding: 0px;">
                <div class="card rounded-3 bg-white shadow p-2">
                    <div class="card-body py-3">

                        <div id="waveform" class="mb-3"></div>

                        <div id="time" class="d-inline-block">0:00</div>
                        <div id="duration" class="d-inline-block">00:00</div>
                        <span style="margin-left: 48%" id="loadingMessageText" class="mt-5 text-nowrap"></span>
                        <div id="loadingMessage" class="preloader mt-0 mb-4" style="margin-left: 50%"></div>

                        <div class="parent-btn">
                            <button id="pauseButton" class="rounded-circle align-middle text-center p-0" title="Pause" data-bs-toggle="tooltip">
                                <i class="fa-solid fa-pause fa-lg"></i>
                            </button>

                            <button id="playButton1" style="display: none">Play Audio</button>

                            <button id="playButton" class="rounded-circle align-middle text-center p-0 ps-1" title="Play" data-bs-toggle="tooltip">
                                <i class="fa-solid fa-play fa-lg"></i>
                            </button>

                            <button id="openSpeedSelectorButton" class="rounded-circle text-nowrap" title="Speed" data-bs-toggle="tooltip">1x</button>

                            <span class="modal-content3 shadow" id="speedSelectorModal" style="display: none;padding: 12px;">
                                <select id="speedSelector" class="selectTaq">
                                    <option value="0.5">.5x</option>
                                    <option value="1" selected>1x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2">2x</option>
                                </select>
                            </span>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <script src="https://unpkg.com/wavesurfer.js@7/dist/wavesurfer.min.js"></script>
        <script src="https://unpkg.com/wavesurfer.js@7/dist/plugin/wavesurfer.timeline.min.js"></script>
        <script src="https://unpkg.com/wavesurfer.js@7/dist/plugin/wavesurfer.minimap.min.js"></script>

        <script type="module">
            let voiceFileName;
            // Listen for the voiceFileName event
            document.addEventListener('voice-File', function(event) {

                voiceFileName = event.detail.voiceFileName;
                // voiceFileName = "testAudio.wav";
            });

            // Function to close modal when clicking outside of it
            window.addEventListener('click', function(event) {
                const speedSelectorModal = document.getElementById('speedSelectorModal');
                const openSpeedSelectorButton = document.getElementById('openSpeedSelectorButton');
                const speedSelector = document.getElementById('speedSelector');
                if (event.target !== speedSelectorModal && event.target !== openSpeedSelectorButton && event.target !== speedSelector) {
                    speedSelectorModal.style.display = 'none'; // Hide the modal
                }
            });
            // Function to open modal when the button is clicked
            document.getElementById('openSpeedSelectorButton').addEventListener('click', function() {
                const speedSelectorModal = document.getElementById('speedSelectorModal');
                speedSelectorModal.style.display = 'inline'; // Display the modal
            });

            // Close the modal when clicking outside of it
            window.onclick = function(event) {
                const speedSelectorModal = document.getElementById('speedSelectorModal');
                if (event.target == speedSelectorModal) {
                    speedSelectorModal.style.display = 'none';
                }
            };
            import WaveSurfer from 'https://unpkg.com/wavesurfer.js@7/dist/wavesurfer.esm.js';

            let wavesurfer; // Declare wavesurfer variable in a global scope
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            // Function to initialize WaveSurfer and start playing audio
            const initializeWaveSurfer = () => {
                wavesurfer = WaveSurfer.create({
                    container: '#waveform',
                    waveColor: '#03a34d',
                    progressColor: '#a8c858',
                    url:  @json($signedUrl),
                    backend: 'MediaElement',
                    responsive: true,
                    normalize: false,
                    barWidth: 5,
                    barRadius: 5,
                    responsive: true,
                    height: 80,
                    autoCenter: true,
                });

                const formatTime = (seconds) => {
                    const minutes = Math.floor(seconds / 60)
                    const secondsRemainder = Math.round(seconds) % 60
                    const paddedSeconds = `0${secondsRemainder}`.slice(-2)
                    return `${minutes}:${paddedSeconds}`
                }


                const timeEl = document.querySelector('#time')
                const durationEl = document.querySelector('#duration')
                wavesurfer.on('ready', function() {
                    const duration = wavesurfer.getDuration();
                    durationEl.textContent = formatTime(duration)
                    const speedSelectorModal = document.getElementById('speedSelectorModal');
                    speedSelectorModal.style.display = 'none';

                    document.getElementById('loadingMessage').style.display = 'none'; // Hide loading message
                    document.getElementById('loadingMessageText').style.display = 'none'; // Hide loading message
                    document.getElementById('waveform').style.visibility = 'visible'; // Show waveform
                    document.querySelector('.parent-btn').style.visibility = 'visible';
                });

                // Update time display when playback reaches certain point
                wavesurfer.on('timeupdate', function(time) {
                    const formattedTime = formatTime(time); // Format the current time
                    document.getElementById('time').textContent = formattedTime; // Update the time display in the div
                });
                // Listen for the 'finish' event to detect when the audio playback finishes
                wavesurfer.on('finish', function() {
                    document.getElementById('pauseButton').style.opacity = 1;
                    document.getElementById('playButton').style.opacity = 1;
                });

                // Listen for changes in playback speed
                document.getElementById('speedSelector').addEventListener('change', function() {
                    const selectedSpeed = parseFloat(this.value); // Get the selected playback speed
                    wavesurfer.setPlaybackRate(selectedSpeed); // Set the playback speed of the audio
                    speedSelectorModal.style.display = 'none';
                    document.getElementById('openSpeedSelectorButton').textContent = this.value + "x";
                });
            };
            // Event listener for the button click to initialize WaveSurfer
            // Function to fetch decrypted audio using AJAX
            // const fetchDecryptedAudio = (encryptedFilePath) => {
            //     fetch(`/get_call/decrypt-audio/${encodeURIComponent(encryptedFilePath)}`)
            //         .then(response => response.blob())
            //         .then(blob => {
            //             // Assuming decryption is handled on server side and response is a blob
            //             wavesurfer.loadBlob(blob);
            //         })
            //         .catch(error => console.error('Error fetching audio:', error));
            // };

            // // Initialize WaveSurfer when DOM is ready
            // document.addEventListener('DOMContentLoaded', function() {
            //     initializeWaveSurfer();
            //     const encryptedFilePath = '{{ $pathVoice . '.wav' }}'; // Replace with actual encrypted file path
            //     fetchDecryptedAudio(encryptedFilePath);
            // });


            document.getElementById('playButton1').addEventListener('click', () => {
                // Check if the AudioContext is suspended, and resume it if needed
                if (typeof Tone !== 'undefined' && Tone.context.state === 'suspended') {
                    Tone.context.resume().then(() => {
                        initializeWaveSurfer();
                    });
                } else {
                    initializeWaveSurfer();
                }
            });
            // Event listener for the play button
            document.getElementById('playButton').addEventListener('click', () => {
                if (wavesurfer && !wavesurfer.isPlaying()) {
                    wavesurfer.play();
                    document.getElementById('playButton').style.opacity = .5;
                    document.getElementById('pauseButton').style.opacity = 1;
                }

                // save who played the call
                var call_id = '{{ $pathVoice }}';
                var user_id = '{{ Auth::id() }}';

                $.ajax({
                    url: '{{ route('userPlayedInteraction') }}',
                    type: 'POST',
                    data: {
                        interaction_id: call_id,
                        user_id: user_id,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        console.log(response);
                    },
                    error: function(response) {
                        alert(response.responseJSON.message);
                    }
                });
            });
            // Show loading message when loading starts
            //  wavesurfer.on('loading', function(percentage) {
            //         document.getElementById('loadingMessage').style.display = 'inline'; // Show loading message
            //         document.getElementById('waveform').style.visibility = 'hidden'; // Optionally hide waveform initially
            //         document.getElementById('loadingMessageText').innerHTML = "Fetching: " + percentage + "%"; // Hide loading message
            //         document.getElementById('loadingMessageText').style.display = 'block'; // Hide loading message
            //     });
            // Event listener for the pause button
            document.getElementById('pauseButton').addEventListener('click', () => {
                if (wavesurfer && wavesurfer.isPlaying()) {
                    wavesurfer.pause();
                    document.getElementById('pauseButton').style.opacity = .5;
                    document.getElementById('playButton').style.opacity = 1;
                }
            });
            // Initialize WaveSurfer when the DOM content is loaded
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    document.getElementById('playButton1').click();
                }, 500);

            });
        </script>
    @endif
    <style>
        thead {
            height: 0rem !important;
        }
    </style>
    <div class="section-tow shadow">
        <div class="section-tow-boxes">
            <div class="row-box-one">
                <div class="box">
                    <div class="box-number"><strong>{{ $totalWeightPoints }}</strong></div>
                    <div class="box-text">Total Weight Points</div>
                </div>
                <div class="box">
                    <div class="box-number"><strong>{{ $totalPossibleWeightPoints }}</strong></div>
                    <div class="box-text">Total Possible Weighted Points</div>
                </div>
            </div>
            <div class="row-box-tow">
                <div class="box">
                    <div class="box-number"><strong>{{ $totalScore }}</strong></div>
                    <div class="box-text">Total Score</div>
                </div>
                <div class="box">
                    <div class="box-number"><strong>{{ $totalPossibleScore }}</strong></div>
                    <div class="box-text">Total Possible Score</div>
                </div>
            </div>
        </div>
        <div class="section-tow-chart">
            <div class="section-tow-colors-group">
                <div class="box-color">
                    <div class="green-color"></div>
                    <span class="text-span">80% - 100% Excellent</span>
                </div>
                <div class="box-color">
                    <div class="yellow-color"></div>
                    <span class="text-span">60% - 80% Very Good</span>
                </div>
                <div class="box-color">
                    <div class="beige-color"></div>
                    <span class="text-span">40% - 60% Good</span>
                </div>
                <div class="box-color">
                    <div class="orange-color"></div>
                    <span class="text-span">20% - 40% Poor</span>
                </div>
                <div class="box-color">
                    <div class="red-color"></div>
                    <span class="text-span">0% - 20% Very Poor</span>
                </div>
            </div>
            <div class="section-tow-circle-chart">
                <canvas id="myChart"  height="220px" width="220px"></canvas>
            </div>
        </div>
    </div>
    <div class="section-three shadow">
        <div class="section-three-container">
            <div class="container-color">
                <div class="blue-color-2"></div>
                <span class="text-span-2">Group Header</span>
                <div class="gray-color-2"></div>
                <span class="text-span-2">Question Header</span>
            </div>
            <div class="div-table">
                <table class="table table-bordered">
                    <thead>
                        <tr class="table-header">
                            <th class="table-header-2" style="width: 50%">Groups</th>
                            <th style="width: 15%">Possible Scores</th>
                            <th style="width: 15%">Actual</th>
                            <th style="width: 20%">Comments</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (!empty($array))
                            @foreach ($array as $groupName => $groupWeights)
                                @php
                                    $index = 0;
                                @endphp
                                <tr class="group-table">
                                    <th class="group-table-th group-table-2" colspan="6">{{ $groupName }}</th>
                                </tr>
                                @foreach ($groupWeights as $groupWeight => $dataQuestions)
                                    @foreach ($dataQuestions as $headerName => $dataQuestionArray)
                                        <tr class="group-header">
                                            <th colspan="4" class="group-header-2">{{ $headerName }}</th>
                                        </tr>
                                        @foreach ($dataQuestionArray as $dataQuestion)
                                            <tr>
                                                <td>{{ $dataQuestion['question_name'] }}</td>
                                                <td class="td-style">
                                                    @php
                                                        $data = $dataQuestion['answers'][0]['mark_and_weight'];
                                                        $data = json_decode($data, true);
                                                        $marks = array_column($data, 'weight');
                                                        echo $maxMark = max($marks);
                                                        usort($data, function ($a, $b) {
                                                            return $b['weight'] - $a['weight']; // Sort in descending order based on 'weight'
                                                        });
                                                    @endphp
                                                </td>
                                                <td>
                                                    <div class="custom-select" style="position: relative">
                                                        <select class="form-control select-value" wire:model="arrayAnswers.{{ $groupName }}.{{ $index }}" wire:change="getPointsAndScores()">

                                                            @foreach ($data as $option)
                                                                <option value="{{ $option['weight'] }}">{{ $option['mark'] }}</option>
                                                            @endforeach
                                                            @php
                                                                $dataFatal = explode(',', $dataQuestion['answers'][0]['fatal']);
                                                            @endphp
                                                            @foreach ($dataFatal as $option2)
                                                                @if ($option2)
                                                                    <option value="{{ $option2 }}">{{ $option2 }}</option>
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                                            <path d="M7 10l5 5 5-5z" />
                                                        </svg>
                                                    </div>
                                                </td>
                                                <td>
                                                    <textarea class="form-control text-area" wire:model.defer="arrayComments.{{ $groupName }}.{{ $index }}" placeholder="Type your comment here..."></textarea>
                                                </td>
                                            </tr>
                                            @php
                                                $index++;
                                            @endphp
                                        @endforeach
                                    @endforeach
                                    <tr class="group-weight">
                                        <th class="group-weight-th">Weighted Value</th>
                                        <th class="group-weight-th-number">{{ $groupWeight }}%</th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                @endforeach
                            @endforeach
                            <tr class="group-comment">
                                <th colspan="4" class="group-weight-th">Comment</th>
                            </tr>
                            <tr class="group-comment">
                                <td colspan="4">
                                    <textarea class="form-control text-area-last" wire:model.defer="commentEvaluation" placeholder="Type your comment here..."></textarea>
                                </td>
                            </tr>
                        @else
                            <tr>
                                <td colspan="4">No data available.</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

        </div>
    </div>

    @if (!empty($array))
        <div class="section-four">
            <div class="col-6">
                <a href="{{ route('recordings') }}" class="btn btn-success rounded-3 px-4"
                style="height: 40px; border-color: #01a44f; background: #01a44f;" >
                    <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;"> Previous
                </a>
            </div>
            <div class="col-6 btn-submit-div">
                <button
                class="btn btn-success rounded-3 px-4"
                style="height: 40px; border-color: #01a44f; background: #01a44f;"
                wire:click="store" >
                Submit
            </button>
            </div>
        </div>

    @endif









</div>
