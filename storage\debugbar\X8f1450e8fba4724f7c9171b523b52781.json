{"__meta": {"id": "X8f1450e8fba4724f7c9171b523b52781", "datetime": "2025-07-22 15:33:49", "utime": **********.558451, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753187626.522392, "end": **********.558521, "duration": 3.0361289978027344, "duration_str": "3.04s", "measures": [{"label": "Booting", "start": 1753187626.522392, "relative_start": 0, "end": **********.141137, "relative_end": **********.141137, "duration": 0.6187448501586914, "duration_str": "619ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.141156, "relative_start": 0.6187639236450195, "end": **********.558525, "relative_end": 4.0531158447265625e-06, "duration": 2.4173691272735596, "duration_str": "2.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28151040, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=78\" onclick=\"\">app/Http/Controllers/Auth/LoginController.php:78-131</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.08609, "accumulated_duration_str": "86.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 73}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 196}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}], "start": **********.248258, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "ilog", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = '36' limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 196}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 167}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "guest", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 23}], "start": **********.2825599, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:75", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:75", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=75", "ajax": false, "filename": "EloquentUserProvider.php", "line": "75"}, "connection": "ilog", "start_percent": 0, "width_percent": 5.39}, {"sql": "select * from `users` where `agent_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 83}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.893497, "duration": 0.02271, "duration_str": "22.71ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:83", "source": "app/Http/Controllers/Auth/LoginController.php:83", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=83", "ajax": false, "filename": "LoginController.php", "line": "83"}, "connection": "ilog", "start_percent": 5.39, "width_percent": 26.379}, {"sql": "select * from `users` where `agent_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 92}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.945742, "duration": 0.01061, "duration_str": "10.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "ilog", "start_percent": 31.769, "width_percent": 12.324}, {"sql": "select * from `users` where `agent_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 117}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.947291, "duration": 0.04813, "duration_str": "48.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "ilog", "start_percent": 44.093, "width_percent": 55.907}]}, "models": {"data": {"App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "36", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f739496-a9f8-49cf-a978-40cb2aec3f14\" target=\"_blank\">View in Telescope</a>", "path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-949154399 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-949154399\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-230992916 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-230992916\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-559788835 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rtfWzYehlUX2HCiCrRVzuDdRHPFGc6PIsrTO9REp</span>\"\n  \"<span class=sf-dump-key>agent_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559788835\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1626980024 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1253 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndORVZmZm1CQWx1OVVtcUxhcW9Wbnc9PSIsInZhbHVlIjoiNEJpVnZuTkZPUmgxdk1ndVNLeXltN3hiL3lRMkpmckV4Y3ZFczI4Qm0xaXM0eldYak1IU1NYQitGUlhyVUFsODNvTWVWN3NxV2p2bEpkbEc0R1NVWDF2Q0d5dTgrQTgyQWZTNFBMQTFqY25YU1F2UldyNDhxaUNFZ1FiSSthUWpaakExbUJPM0h2cDlyMHI3YU1BaC82d09tYlRzS29EbFpwRTNYVU5GbGY0clN1bTFBTlQzekk0Wi96RUR5aUdwZWtIRFdGaENSYjBDQjlxNnlNOFdKeFg5dXZWQ0twbGNSMkRGcVFGREJ6QT0iLCJtYWMiOiJjYWYxMjVmN2NiZGM3YzJlMDVmYWQ1NjI3YzdjZGQxZmE0MmYyMTVmZDQ5MGY2NTA2NDU3ODc1NThiNTAwYWFmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpqOG5xNEJFTnpLRkRRbHdqc2lyL3c9PSIsInZhbHVlIjoidk16YUdvZ1BQMjREOHNRWStFT2c5aUllengrbE1ocExxa0k3TWROc0pVa0xJalFzSi85bno1UEIxcE91N001K0tPWWNuOStBeEVvRkVXQ3hkeGxZOHN4Z3h5VkZ1SDhtTlpSUmJFUFk1TzJoVzY0bktTb083VEhKMFdCbkhxdVYiLCJtYWMiOiJmYTY0OTBkOGYyOWUxZGQ2NDJlZTQwZDIxZmY0ODU4YjhmYzQ5NTNmODY3NjE5MTQ0ZDRkMTJjZjYyMGRkYTJkIiwidGFnIjoiIn0%3D; i_log_session=eyJpdiI6ImdUaUZvSzV4YVZPbDEySmVtSEthN2c9PSIsInZhbHVlIjoia1gweFF0OHlTdW1XMThRK3RFV21GemxIMTZzRDN0U0lYVUdPcXNKUlhrcVV4MXRuSm1wdzNWUTd1REZ1RmFEY1Ztb0RYaXdpdVg3VEZQQTlxb3FiQXgwSC9zNXNQMXNkSEczU09YY0trOWozOXVkWHRwTitBQ0toMWxySWo4WnkiLCJtYWMiOiJiYzdjMjEyMTJkOTU3ZDk0ZTMyYzI2NDc4NzhiMjA5M2M2ZDU1YzdlMTIxYTMyM2E1MThmNDNlY2I2NmU3ODA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626980024\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-711143281 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">36|5ib4AQhtLHzATEGHqeW0FyHXea5XYBhkXcIWMbiaY723twUGMepP4FnyoU3L|$2y$12$sKmCpsW.aT14AEFPyOgHeO2lhV7BRWjK7d1ywUnOncv84uKprV4di</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rtfWzYehlUX2HCiCrRVzuDdRHPFGc6PIsrTO9REp</span>\"\n  \"<span class=sf-dump-key>i_log_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Juv2hCcW7XnxgahdYiGKV5evsYOEyVSa0sPP8Wrf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711143281\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2085726686 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 12:33:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/recordings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6IkszWE1HVDYwMkVOeFVReFZDN0dyZ2c9PSIsInZhbHVlIjoiN01VeGhPQW94dFpDZVN4czVOTHJwQy95emUweU5pTlJqMUliMGVVQitlR3l5dm0rU0tQRzBpRFkzY00yWlhCMGRkNCs3ZHVQWWpWK2M2dHNZcUVDRjAzbjZIZXRrdGh3Y0VHa0RTY1ZQWnE5VEdaY3E5SFV6bjlwVjljeDN6Rk0iLCJtYWMiOiJiZjQ4YWQwZWVlMTcxNjJhZWZkMDFkYmQ0MzY0YmZhZDc0ZWZkM2Y4NGMwNDU0ZDA4MjY3NmMzNDIyNTZhMzk0IiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:33:49 GMT; Max-Age=21600; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">i_log_session=eyJpdiI6InF1VW80aUhxbEFjekNYMFpCbmRYT1E9PSIsInZhbHVlIjoiQ2RFOEgzc3VvWUV2M3ROL2FiTjg1V1pQOXdPelpMOGxPK1owVXlnbk1STWg5Sm9EMDNPbTNBY0ZpUE03b2U0bDIxNk9ON05wVTdoNm1JU2dlRVNqN1RRZSthY0QwYkJzYXd5a3BTUXZ2L0VuS1pwUmlSU0puRUVsUGRuUHRvWDciLCJtYWMiOiJjZGYyMTdmMzQ2ZjA2MDI1MzQ1MTllYjI2ZDVlNzMzNmNmMmFjMzUxN2MyMDMyMTY2ZDgwODVlNTA5NGVkNThmIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:33:49 GMT; Max-Age=21600; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkszWE1HVDYwMkVOeFVReFZDN0dyZ2c9PSIsInZhbHVlIjoiN01VeGhPQW94dFpDZVN4czVOTHJwQy95emUweU5pTlJqMUliMGVVQitlR3l5dm0rU0tQRzBpRFkzY00yWlhCMGRkNCs3ZHVQWWpWK2M2dHNZcUVDRjAzbjZIZXRrdGh3Y0VHa0RTY1ZQWnE5VEdaY3E5SFV6bjlwVjljeDN6Rk0iLCJtYWMiOiJiZjQ4YWQwZWVlMTcxNjJhZWZkMDFkYmQ0MzY0YmZhZDc0ZWZkM2Y4NGMwNDU0ZDA4MjY3NmMzNDIyNTZhMzk0IiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:33:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">i_log_session=eyJpdiI6InF1VW80aUhxbEFjekNYMFpCbmRYT1E9PSIsInZhbHVlIjoiQ2RFOEgzc3VvWUV2M3ROL2FiTjg1V1pQOXdPelpMOGxPK1owVXlnbk1STWg5Sm9EMDNPbTNBY0ZpUE03b2U0bDIxNk9ON05wVTdoNm1JU2dlRVNqN1RRZSthY0QwYkJzYXd5a3BTUXZ2L0VuS1pwUmlSU0puRUVsUGRuUHRvWDciLCJtYWMiOiJjZGYyMTdmMzQ2ZjA2MDI1MzQ1MTllYjI2ZDVlNzMzNmNmMmFjMzUxN2MyMDMyMTY2ZDgwODVlNTA5NGVkNThmIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:33:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085726686\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-986709265 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>36</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986709265\", {\"maxDepth\":0})</script>\n"}}