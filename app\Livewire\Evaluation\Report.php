<?php

namespace App\Livewire\Evaluation;

use App\Models\EvaluationSubmission;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

use App\Exports\EvaluationExport;
use App\Exports\Ticket\EvaluationReportsExport;
use App\Livewire\Organizations;
use App\Models\Evaluation;
use App\Models\EvaluationQuestion;
use App\Models\Interaction;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Features\SupportPagination\WithoutUrlPagination;

class Report extends Component
{

    use WithPagination, LivewireAlert, WithoutUrlPagination;

    protected $paginationTheme = 'bootstrap';
    public $role = NULL;
    public $data = NULL;
    public $type = 'evaluation';
    public $year = false;
    public $month = false;
    public $week = false;
    public $date = false;
    public $source = false;
    public $organization = false;
    public $agentNameOpsId = false;
    public $dateFrom = false;
    public $dateTo = false;
    public $referenceID = false;
    public $yearOptions = [];
    public $monthOptions = [];
    public $weekOptions = [];
    public $sourceOptions = [];
    public $referenceIDOptions = [];
    public $yearSelected = NULL;
    public $monthSelected = NULL;
    public $weekSelected = NULL;
    public $sourceSelected = NULL;
    public $organizationSelected = NULL;
    public $agentNameOpsIdSelected = NULL;
    public $referenceIDSelected = NULL;
    public $dateSelected = NULL;
    public $reportType = NULL;
    public $evaluation_id = NULL;
    public $evaluation_question = [];
    public $paginateCount = 15;
    public $evaluationSubmission = [];



    public function interactionsPerPage($value)
    {
        $this->paginateCount = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }

    public function mount($reportType)
    {

        $this->reportType = $reportType;
        $this->getOptions();
        $this->getOrgnizations();
        $this->role = auth()->user()->role;
    }
    public function getOrgnizations()
    {
        return Organization::query()->get();
    }
    public function getType($type)
    {
        $this->emit('changeTypeData');
        $this->type = $type;
        $this->emit('changeType', $this->type);
    }
    public function getEvaluations()
    {

        if ($this->role == 1 || $this->role == 2 || $this->role == 3) {
            return Evaluation::query()->where('status', '1')->get();
        } elseif ($this->role == 5) {
            return Evaluation::query()->whereIn('organization_id', auth()->user()->supervisorOrganizations->pluck('id'))->where('status', '1')->get();
        } else {
            return Evaluation::query()->where('organization_id', auth()->user()->organization->id)->where('status', '1')->get();
        }
    }

    public function getFilterType($type)
    {
        if ($type == 'year') {
            $this->date = false;
            $this->dateSelected = NULL;
            $this->dateFrom = NULL;
            $this->dateTo = NULL;

            $this->year = !$this->year;
            $this->yearSelected = NULL;
        } elseif ($type == 'month') {
            $this->date = false;
            $this->dateSelected = NULL;
            $this->dateFrom = NULL;
            $this->dateTo = NULL;

            $this->month = !$this->month;
            $this->monthSelected = NULL;
        } elseif ($type == 'week') {

            $this->date = false;
            $this->dateSelected = NULL;
            $this->dateFrom = NULL;
            $this->dateTo = NULL;

            $this->week = !$this->week;
            $this->weekSelected = NULL;
        } elseif ($type == 'source') {

            $this->source = !$this->source;
            $this->sourceSelected = NULL;
        } elseif ($type == 'date') {

            $this->week = false;
            $this->weekSelected = NULL;
            $this->month = false;
            $this->monthSelected = NULL;
            $this->year = false;
            $this->yearSelected = NULL;

            $this->date = !$this->referenceID;
            $this->dateSelected = NULL;
        } elseif ($type == 'referenceID') {

            $this->referenceID = !$this->referenceID;
            $this->referenceIDSelected = NULL;
        } elseif ($type == 'organization') {

            $this->organization = !$this->organization;
            $this->organizationSelected = NULL;
        } elseif ($type == 'agentNameOpsId') {

            $this->agentNameOpsId = !$this->agentNameOpsId;
            $this->agentNameOpsIdSelected = NULL;
        }
        $this->render();
    }
    public function resetEvaluationId()
    {
        $this->evaluation_id = null;
    }
    public function getOptions()
    {


        $this->yearOptions = EvaluationSubmission::distinct()
            ->when($this->evaluation_id, function ($query) {
                return $query->where('evaluation_id', $this->evaluation_id);
            })
            ->pluck('year');

        $this->monthOptions = EvaluationSubmission::distinct()
            ->when($this->evaluation_id, function ($query) {
                return $query->where('evaluation_id', $this->evaluation_id);
            })
            ->pluck('month');

        $this->weekOptions = EvaluationSubmission::distinct()
            ->when($this->evaluation_id, function ($query) {
                return $query->where('evaluation_id', $this->evaluation_id);
            })
            ->pluck('week');

        $this->sourceOptions = Interaction::distinct()
            ->whereNotNull('call_type')
            ->where('call_type', '!=', '')
            ->pluck('call_type');

        $this->referenceIDOptions = EvaluationSubmission::distinct()
            ->when($this->evaluation_id, function ($query) {
                return $query->where('evaluation_id', $this->evaluation_id);
            })
            ->pluck('referenceID');
    }
    public function getQuestions()
    {
        $this->evaluation_question = EvaluationQuestion::query()->get();
    }
    public function getReport()
    {
        $this->data = true;
        $report = EvaluationSubmission::query();

        // Common filters
        if ($this->yearSelected) $report->where('year', $this->yearSelected);
        if ($this->monthSelected) $report->where('month', $this->monthSelected);
        if ($this->weekSelected) $report->where('week', $this->weekSelected);
        if ($this->referenceIDSelected) $report->where('referenceID', $this->referenceIDSelected);
        if ($this->sourceSelected) {
            $report->whereHas('Interaction', function ($query) {
                $query->where('call_type', $this->sourceSelected);
            });
        }
        if ($this->date && $this->dateFrom && $this->dateTo) {
            $report->whereBetween('created_at', [
                "{$this->dateFrom} 00:00:00",
                "{$this->dateTo} 23:59:59"
            ]);
        }

        // Role-based filters
        $userId = auth()->user()->id;
        $organizationId = auth()->user()->organization_id;
        $supervisorOrgs = Auth::user()->supervisorOrganizations->pluck('id');

        if ($this->role == '4') {
            $report->where('user_id', $userId);
        } elseif ($this->role == '6') {
            $report->whereHas('user', fn($query) => $query->where('organization_id', $organizationId));
        } elseif (in_array($this->role, ['2', '5'])) {
            $report->whereHas('user.organization', fn($query) => $query->whereIn('id', $supervisorOrgs));
        }
        if ($this->organizationSelected) {
            $report->whereHas('user.organization', fn($query) => $query->where('id', $this->organizationSelected));
        }
        if ($this->agentNameOpsIdSelected) {
            $report->whereHas('user', function ($query) {
                $query->where('agent_id', $this->agentNameOpsIdSelected)
                    ->orWhere('full_name', 'like', '%' . $this->agentNameOpsIdSelected . '%');
            });
        }

        // Specific report type filters
        if (($this->reportType == "evaluation_parameteres_report" || $this->reportType == "account_wise_defect_report") && $this->evaluation_id) {
            $report->where('evaluation_id', $this->evaluation_id);
        }
        $report->orderBy('id', 'desc');
        if ($this->reportType == "evaluation_agent_avg_score_report") {
            return $report->get();
        } elseif ($this->reportType == "account_wise_defect_report") {
            if ($this->evaluation_id) {
                $this->evaluationSubmission = $report->first();
                $firstReport = $report->first();

                if ($firstReport) {
                    $submissionAnswers = $firstReport->submissionAnswers()->get();
                    return $submissionAnswers;
                } else {
                    return [];
                }
                // ->paginate($this->paginateCount)
            }
        } else {
            // $this->dispatch('close-modal');
            return $report->paginate($this->paginateCount);
        }
    }
    public function getData()
    {
        $this->getReport();
        $this->dispatch('close-modal');
    }
    public function export()
    {
        if ($this->reportType == "account_wise_defect_report" || $this->reportType == "evaluation_agent_avg_score_report") {
            $this->dispatch('export-excel');
        } else {
            return Excel::download(new EvaluationReportsExport(
                $this->yearSelected,
                $this->monthSelected,
                $this->weekSelected,
                $this->referenceIDSelected,
                $this->sourceSelected,
                $this->organizationSelected,
                $this->agentNameOpsIdSelected,
                $this->date,
                $this->dateFrom,
                $this->dateTo,
                $this->role,
                $this->reportType,
                $this->evaluation_id
            ), 'evaluation.xlsx');
        }
    }
    public function reloadComponent()
    {
        $reportType = $this->reportType;
        $this->reset([
            'role',
            'data',
            'type',
            'year',
            'month',
            'week',
            'date',
            'source',
            'organization',
            'agentNameOpsId',
            'dateFrom',
            'dateTo',
            'referenceID',
            'yearOptions',
            'monthOptions',
            'weekOptions',
            'sourceOptions',
            'referenceIDOptions',
            'yearSelected',
            'monthSelected',
            'weekSelected',
            'sourceSelected',
            'organizationSelected',
            'agentNameOpsIdSelected',
            'referenceIDSelected',
            'dateSelected',
            'reportType',
            'evaluation_id',
            'evaluation_question',
            'paginateCount',
            'evaluationSubmission',
        ]);

        $this->mount($reportType);
    }


    public function render()
    {
        return view('livewire.evaluation.report', ([
            'options' => $this->getOptions(),
            'report' => $this->getReport(),
            'evaluations' => $this->getEvaluations(),
            'Organizations' => $this->getOrgnizations(),
        ]));
    }
}
