<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\User;
use App\Models\QaFlag;
use Livewire\Component;
use App\Models\Evaluation;
use App\Models\FlagFilter;
use App\Models\Organization;
use App\Models\TimeForFlags;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Illuminate\Validation\ValidationException;

class QaFlags extends Component
{
    use LivewireAlert, WithPagination;


    public $theIdToDelete;

    // create flag 
    public $enabled = true;
    public $name_add = '';
    public $start_date;
    public $end_date;
    public $interactions_number;
    public $per;
    public $criteria;
    public $distribution_level;
    public $searchForms = '';
    public $forms = 'All Forms';
    public $selectedForms_add = [];
    public $interaction_days_add = [1, 2, 3, 4, 5, 6, 7];
    public $interaction_days_field_name = 'All Days'; //what to show in the field when selecting days
    public $interaction_time = 'All Time'; //to show hide the from to in interaction time - add modal
    public $interaction_time_from;
    public $interaction_time_to;
    public $call_type_add = 'All';
    public $screen_capture_add;
    public $selectedOrg_add;
    public $selectedOrgId_add;
    public $interactions_duration_condition = '>';
    public $interactions_duration_from = '00:00';
    public $interactions_duration_to; //null except if the condition is 'between'
    public $selected_agent_add;
    public $selected_lang_add;

    public $searchFlag = '';
    public $selectedUser = [];
    public $sortBy = 'name';
    public $sortDir = 'ASC';

    public $activeTab = 'general';

    // filters
    public $filter_agentId_trigger;
    public $filter_agentId;

    public $filter_silentDuration_trigger;
    public $filter_silentDuration;
    public $filter_silentDuration_condition = '=';
    public $filter_silentDuration_from;
    public $filter_silentDuration_to;

    public $filter_interactionEnder_trigger;
    public $filter_interactionEnder;

    public $filter_holdDuration_trigger;
    public $filter_holdDuration;
    public $filter_holdDuration_condition = '=';
    public $filter_holdDuration_from;
    public $filter_holdDuration_to;


    public $filter_holdCount_trigger;
    public $filter_holdCount;
    public $filter_holdCount_condition = '=';
    public $filter_holdCount_from;
    public $filter_holdCount_to;

    public $filter_agentName_trigger;
    public $filter_agentName;

    public $filter_language;
    public $filter_language_trigger;



    //----------EDIT MODAL--------------//
    public $selectedFlagId;
    public $selected_flagName;
    public $selected_startDate;
    public $selected_endDate;
    public $selected_interactionsNumber;
    public $selected_per;
    public $selected_interval;
    public $selected_distributionLevel;
    public $selected_enabled = true;
    public $selected_evalForms = [];
    public $selected_interactionDays = [];
    public $selected_interactionTimeFrom;
    public $selected_interactionTimeTo;
    public $selected_interactionDurationFrom;
    public $selected_interactionDurationTo;
    public $selected_org_edit;
    public $selected_orgId_edit;
    public $selected_agent_edit;

    protected $paginationTheme = 'bootstrap';

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }
    public function statusUpdateModal()
    {
        $this->selected_enabled = !$this->selected_enabled;
    }
    // add modal
    public function applyFlag()
    {
        if (QaFlag::where(function ($query) {
            $query->where(function ($q) {
                $q->where('start_date', '<=', $this->end_date)
                    ->where('end_date', '>=', $this->start_date);
            });
        })
            ->whereHas('organization', function ($q) {
                $q->where('id', $this->selectedOrgId_add);
            })->exists()
        ) {

            $org_name = Organization::firstWhere('id', $this->selectedOrgId_add)->name;

            $this->alert("error", "$org_name already has a flag during this period", [
                'timerProgressBar' => true,
                'timer' => 4000,
            ]);

            return;
        }

        // try {
        $this->validate();
        // } catch (ValidationException $e) {
        //     $errors = $e->validator->errors();
        //     foreach ($errors->getMessages() as $field => $messages) {
        //         foreach ($messages as $message) {
        //             dump("Validation error on $field: $message");
        //         }
        //     }
        // }


        if ($this->interactions_duration_from) {
            [$minutes, $seconds] = explode(':', $this->interactions_duration_from);

            // Format it to 00:MM:SS
            $interactions_duration_from = sprintf('00:%02d:%02d', $minutes, $seconds);
        } else {
            $interactions_duration_from = null;
        }


        if ($this->interactions_duration_to) {

            [$minutes, $seconds] = explode(':', $this->interactions_duration_to);

            // Convert minutes and seconds to seconds
            $totalSeconds = ($minutes * 60) + $seconds;

            // Convert total seconds to a TIME format (HH:MM:SS)
            $interactions_duration_to = gmdate('H:i:s', $totalSeconds);
        } else {
            $interactions_duration_to = null;
        }


        if ($this->filter_silentDuration_from) {

            [$minutes, $seconds] = explode(':', $this->filter_silentDuration_from);

            // Convert minutes and seconds to seconds
            $totalSeconds = ($minutes * 60) + $seconds;

            // Convert total seconds to a TIME format (HH:MM:SS)
            $filter_silentDuration_from = date('H:i:s', $totalSeconds);
        } else {
            $filter_silentDuration_from = null;
        }

        if ($this->filter_silentDuration_to) {

            [$minutes, $seconds] = explode(':', $this->filter_silentDuration_to);

            // Convert minutes and seconds to seconds
            $totalSeconds = ($minutes * 60) + $seconds;

            // Convert total seconds to a TIME format (HH:MM:SS)
            $filter_silentDuration_to = date('H:i:s', $totalSeconds);
        } else {
            $filter_silentDuration_to = null;
        }

        // if ($this->filter_holdDuration_from) {

        //     [$minutes, $seconds] = explode(':', $this->filter_holdDuration_from);

        //     // Convert minutes and seconds to seconds
        //     $totalSeconds = ($minutes * 60) + $seconds;

        //     // Convert total seconds to a TIME format (HH:MM:SS)
        //     $filter_holdDuration_from = date('H:i:s', $totalSeconds);
        // } else {
        //     $filter_holdDuration_from = null;
        // }

        if ($this->filter_holdDuration_from) {
            [$minutes, $seconds] = explode(':', $this->filter_holdDuration_from);

            // Format it to 00:MM:SS
            $filter_holdDuration_from = sprintf('00:%02d:%02d', $minutes, $seconds);
        } else {
            $filter_holdDuration_from = null;
        }

        if ($this->filter_holdDuration_to) {
            [$minutes, $seconds] = explode(':', $this->filter_holdDuration_to);

            // Format it to 00:MM:SS
            $filter_holdDuration_to = sprintf('00:%02d:%02d', $minutes, $seconds);
        } else {
            $filter_holdDuration_to = null;
        }
        // if ($this->filter_holdDuration_to) {

        //     [$minutes, $seconds] = explode(':', $this->filter_holdDuration_to);

        //     // Convert minutes and seconds to seconds
        //     $totalSeconds = ($minutes * 60) + $seconds;

        //     // Convert total seconds to a TIME format (HH:MM:SS)
        //     $filter_holdDuration_to = date('H:i:s', $totalSeconds);
        // } else {
        //     $filter_holdDuration_to = null;
        // }


        // if interaction time is set to All Time, set the from and to to null
        if ($this->interaction_time == "All Time") {
            $this->interaction_time_from = $this->interaction_time_to = null;
        }

        // active state
        if (Carbon::parse($this->start_date)->startOfDay() <= now()->startOfDay() && Carbon::parse($this->end_date)->startOfDay() >= now()->startOfDay()) {
            $active_state = 1;
        } else {
            $active_state = 0;
        }

        $flag = QaFlag::create([
            'organization_id' => $this->selectedOrgId_add,
            'name' => $this->name_add,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'interactions_number' => $this->interactions_number,
            // 'per' => $this->per,
            'per' => 1,
            'time_interval' => $this->criteria,
            'distribution_level' => $this->distribution_level,
            // 'screen_capture' => $this->screen_capture_add,
            'call_type' => $this->call_type_add,
            'interaction_days' => json_encode(array_values($this->interaction_days_add)),
            'interaction_time_from' => $this->interaction_time_from ? Carbon::parse($this->interaction_time_from) : null,
            'interaction_time_to' => $this->interaction_time_to ? Carbon::parse($this->interaction_time_to) : null,
            'duration_start' => $interactions_duration_from ?? null,
            'duration_end' => $interactions_duration_to ?? null,
            'duration_condition' => $this->interactions_duration_condition,
            'enabled' => $this->enabled,
            'active' => $active_state,
        ]);

        // save the related evaluation forms 
        // if ($this->forms == 'All Forms') {
        //     $flag->evaluationForms()->sync(Evaluation::all()->pluck('id')->toArray());
        // } else {
        //     $flag->evaluationForms()->sync(array_keys($this->selectedForms_add));
        // }


        // save the filters 
        // agent id 
        if ($this->filter_agentId_trigger) {
            $flag->filters()->create([
                'name' => 'agent_id',
                'condition' => '=',
                'first_data' => $this->filter_agentId,
            ]);
        }

        // silent duration 
        // if ($this->filter_silentDuration_trigger) {
        //     $flag->filters()->create([
        //         'name' => 'silent_duration',
        //         'first_data' => $filter_silentDuration_from,
        //         'condition' => $this->filter_silentDuration_condition,
        //         'second_data' => $filter_silentDuration_to
        //     ]);
        // }

        // interaction ender 
        if ($this->filter_interactionEnder_trigger) {
            $flag->filters()->create([
                'name' => 'interaction_ender',
                'first_data' => $this->filter_interactionEnder,
                'condition' => '=',
            ]);
        }

        // hold duration 
        if ($this->filter_holdDuration_trigger) {
            $flag->filters()->create([
                'name' => 'hold_duration',
                'first_data' => $filter_holdDuration_from,
                'condition' => $this->filter_holdDuration_condition,
                'second_data' => $filter_holdDuration_to
            ]);
        }

        // hold count
        if ($this->filter_holdCount_trigger) {
            $flag->filters()->create([
                'name' => 'hold_count',
                'first_data' => $this->filter_holdCount_from,
                'condition' => '=',
                'second_data' => $this->filter_holdCount_to
            ]);
        }

        // Agent name
        if ($this->filter_agentName_trigger) {
            $flag->filters()->create([
                'name' => 'agent_name',
                'first_data' => $this->selected_agent_add,
                'condition' => '=',
            ]);
        }

        // lang
        if ($this->filter_language_trigger) {
            $flag->filters()->create([
                'name' => 'language',
                'first_data' => $this->selected_lang_add,
                'condition' => '=',
            ]);
        }


        if ($flag->time_interval == 'Day') $flag_frequency = 'daily';
        if ($flag->time_interval == 'Week') $flag_frequency = 'weekly';
        if ($flag->time_interval == 'Month') $flag_frequency = 'monthly';

        // add to the time for flags table
        TimeForFlags::create([
            'flag_id' => $flag->id,
            'flag_frequency' => $flag_frequency,
            'time' => $flag->start_date,
        ]);

        $this->alert('success', "QA Flag Created Successfully", [
            'timerProgressBar' => true,
            'timer' => '4000',
        ]);

        $this->closeModal();
    }

    public function editFlag()
    {
        // if (QaFlag::whereActive(1)
        //     ->whereEnabled(1)
        //     ->whereHas('organization', function ($q) {
        //         $q->where('id', $this->selected_orgId_edit);
        //     })
        //     ->where('organization_id', '!=', $this->selected_orgId_edit) // Exclude the currently selected QaFlag
        //     ->exists()
        // ) {

        //     $this->alert("error", 'This Organization already has an active flag', [
        //         'timerProgressBar' => true,
        //         'timer' => 4000,
        //     ]);

        //     return;
        // }


        // try {

        $this->validate();
        // } catch (ValidationException $e) {
        //     $errors = $e->validator->errors();
        //     foreach ($errors->getMessages() as $field => $messages) {
        //         foreach ($messages as $message) {
        //             dump("Validation error on $field: $message");
        //         }
        //     }
        // }

        // extract interaction duration from and to from strings and convert to seconds
        // Split the string by the colon ':' to get minutes and seconds as separate elements in an array

        // if ($this->selected_interactionDurationFrom) {

        //     [$minutes, $seconds] = explode(':', $this->selected_interactionDurationFrom);

        //     // Convert minutes and seconds to seconds
        //     $totalSeconds = ($minutes * 60) + $seconds;

        //     // Convert total seconds to a TIME format (HH:MM:SS)
        //     $selected_interactionDurationFrom = date('H:i:s', $totalSeconds);
        // } else {
        //     $selected_interactionDurationFrom = null;
        // }

        if ($this->selected_interactionDurationFrom) {
            [$minutes, $seconds] = explode(':', $this->selected_interactionDurationFrom);

            // Format it to 00:MM:SS
            $selected_interactionDurationFrom = sprintf('00:%02d:%02d', $minutes, $seconds);
        } else {
            $selected_interactionDurationFrom = null;
        }


        // and for the duration_to
        if ($this->selected_interactionDurationTo) {

            [$minutes, $seconds] = explode(':', $this->selected_interactionDurationTo);

            // Convert minutes and seconds to seconds
            $totalSeconds = ($minutes * 60) + $seconds;

            // Convert total seconds to a TIME format (HH:MM:SS)
            $selected_interactionDurationTo = gmdate('H:i:s', $totalSeconds);
        } else {
            $selected_interactionDurationTo = null;
        }

        // if ($this->selected_interactionTimeFrom) {

        //     [$minutes, $seconds] = explode(':', $this->selected_interactionTimeFrom);

        //     // Convert minutes and seconds to seconds
        //     $totalSeconds = ($minutes * 60) + $seconds;

        //     // Convert total seconds to a TIME format (HH:MM:SS)
        //     $selected_interactionTimeFrom = date('H:i:s', $totalSeconds);
        // } else {
        //     $selected_interactionTimeFrom = null;
        // }


        if ($this->interaction_time == "All Time") {
            $selected_interactionTimeFrom = $selected_interactionTimeTo = null;
        }


        // if ($this->selected_interactionTimeTo) {

        //     [$minutes, $seconds] = explode(':', $this->selected_interactionTimeTo);

        //     // Convert minutes and seconds to seconds
        //     $totalSeconds = ($minutes * 60) + $seconds;

        //     // Convert total seconds to a TIME format (HH:MM:SS)
        //     $selected_interactionTimeTo = gmdate('H:i:s', $totalSeconds);
        // } else {
        //     $selected_interactionTimeTo = null;
        // }


        if ($this->filter_silentDuration_from) {

            [$minutes, $seconds] = explode(':', $this->filter_silentDuration_from);

            // Convert minutes and seconds to seconds
            $totalSeconds = ($minutes * 60) + $seconds;

            // Convert total seconds to a TIME format (HH:MM:SS)
            $filter_silentDuration_from = date('H:i:s', $totalSeconds);
        } else {
            $filter_silentDuration_from = null;
        }

        if ($this->filter_silentDuration_to) {

            [$minutes, $seconds] = explode(':', $this->filter_silentDuration_to);

            // Convert minutes and seconds to seconds
            $totalSeconds = ($minutes * 60) + $seconds;

            // Convert total seconds to a TIME format (HH:MM:SS)
            $filter_silentDuration_to = date('H:i:s', $totalSeconds);
        } else {
            $filter_silentDuration_to = null;
        }

        if ($this->filter_holdDuration_from) {
            [$minutes, $seconds] = explode(':', $this->filter_holdDuration_from);

            // Format it to 00:MM:SS
            $filter_holdDuration_from = sprintf('00:%02d:%02d', $minutes, $seconds);
        } else {
            $filter_holdDuration_from = null;
        }

        if ($this->filter_holdDuration_to) {
            [$minutes, $seconds] = explode(':', $this->filter_holdDuration_to);

            // Format it to 00:MM:SS
            $filter_holdDuration_to = sprintf('00:%02d:%02d', $minutes, $seconds);
        } else {
            $filter_holdDuration_to = null;
        }

        // if interaction time is set to All Time, set the from and to to null
        if ($this->interaction_time == "All Time") {
            $this->selected_interactionTimeFrom = $this->selected_interactionTimeTo = null;
        }

        // active state
        if (Carbon::parse($this->selected_startDate)->startOfDay() <= now()->startOfDay()) {
            $active_state = 1;
        } else {
            $active_state = 0;
        }

        QaFlag::find($this->selectedFlagId)->update([
            'organization_id' => $this->selected_orgId_edit,
            'name' => $this->selected_flagName,
            // 'start_date' => $this->selected_startDate,
            // 'end_date' => $this->selected_endDate,
            'interactions_number' => $this->selected_interactionsNumber,
            // 'per' => $this->selected_per,
            'time_interval' => $this->selected_interval,
            'distribution_level' => $this->selected_distributionLevel,
            // 'screen_capture' => $this->screen_capture_add,
            'call_type' => $this->call_type_add,
            'interaction_days' => json_encode(array_values($this->selected_interactionDays)),
            'interaction_time_from' => $this->selected_interactionTimeFrom ? Carbon::parse($this->selected_interactionTimeFrom) : null,
            'interaction_time_to' => $this->selected_interactionTimeTo ? Carbon::parse($this->selected_interactionTimeTo) : null,
            'duration_start' => $selected_interactionDurationFrom ?? null,
            'duration_end' => $selected_interactionDurationTo ?? null,
            'duration_condition' => $this->interactions_duration_condition,
            'enabled' => $this->selected_enabled,
            'active' => $active_state,
        ]);

        // save the related evaluation forms 
        // if ($this->forms == "All Forms") {
        //     QaFlag::find($this->selectedFlagId)->evaluationForms()->sync(Evaluation::all()->pluck('id')->toArray());
        // } else {
        //     QaFlag::find($this->selectedFlagId)->evaluationForms()->sync(array_keys($this->selected_evalForms));
        // }


        // save the filters 
        // agent id 
        if ($this->filter_agentId_trigger) {
            QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate([
                'name' => 'agent_id',
                'condition' => '=',
                'first_data' => $this->filter_agentId,
            ]);
        } else {
            // Find and delete the filter directly
            QaFlag::find($this->selectedFlagId)->filters()->where('name', 'agent_id')->delete();
        }

        // silent duration 
        // if ($this->filter_silentDuration_trigger) {
        //     QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate(
        //         ['name' => 'silent_duration'],
        //         [
        //             'name' => 'silent_duration',
        //             'first_data' => $filter_silentDuration_from,
        //             'condition' => $this->filter_silentDuration_condition,
        //             'second_data' => $filter_silentDuration_to
        //         ]
        //     );
        // }

        // interaction ender 
        if ($this->filter_interactionEnder_trigger) {
            QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate(
                ['name' => 'interaction_ender'],
                [
                    'name' => 'interaction_ender',
                    'first_data' => $this->filter_interactionEnder,
                    'condition' => '=',
                ]
            );
        } else {
            // Find and delete the filter directly
            QaFlag::find($this->selectedFlagId)->filters()->where('name', 'interaction_ender')->delete();
        }

        // hold duration 
        if ($this->filter_holdDuration_trigger) {
            QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate(
                ['name' => 'hold_duration'],
                [
                    'name' => 'hold_duration',
                    'first_data' => $filter_holdDuration_from,
                    'condition' => $this->filter_holdDuration_condition,
                    'second_data' => $filter_holdDuration_to
                ]
            );
        } else {
            // Find and delete the filter directly
            QaFlag::find($this->selectedFlagId)->filters()->where('name', 'hold_duration')->delete();
        }

        // hold count
        if ($this->filter_holdCount_trigger) {
            QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate(
                ['name' => 'hold_count'],
                [
                    'name' => 'hold_count',
                    'first_data' => $this->filter_holdCount_from,
                    'condition' => '=',
                    'second_data' => $this->filter_holdCount_to
                ]
            );
        } else {
            // Find and delete the filter directly
            QaFlag::find($this->selectedFlagId)->filters()->where('name', 'hold_count')->delete();
        }

        // Agent name
        if ($this->filter_agentName_trigger) {
            QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate(
                ['name' => 'agent_name'],
                [
                    'name' => 'agent_name',
                    'first_data' => $this->filter_agentName,
                    'condition' => '=',
                ]
            );
        } else {
            // Find and delete the filter directly
            QaFlag::find($this->selectedFlagId)->filters()->where('name', 'agent_name')->delete();
        }

        // lang
        if ($this->filter_language_trigger) {
            QaFlag::find($this->selectedFlagId)->filters()->updateOrCreate(
                [
                    'name' => 'language'
                ],
                [
                    'name' => 'language',
                    'first_data' => $this->filter_language,
                    'condition' => '=',
                ]
            );
        } else {
            // Find and delete the filter directly
            QaFlag::find($this->selectedFlagId)->filters()->where('name', 'language')->delete();
        }


        $this->alert('success', "QA Flag Edited Successfully", [
            'timerProgressBar' => true,
            'timer' => '4000',
        ]);

        $this->closeModal();
    }

    public function getListeners()
    {
        return [
            'confirmed'
        ];
    }

    public function confirmed()
    {
        if (QaFlag::find($this->theIdToDelete)->delete() && TimeForFlags::where('flag_id', $this->theIdToDelete)->delete()) {
            $this->alert("success", "Flag Deleted Successfully");
        };
    }

    public function showDeleteAlert($id)
    {
        $this->theIdToDelete = $id;
        $this->alert('warning', 'Are you sure you want to delete this user?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function closeModal()
    {
        $this->activeTab = 'general';
        $this->resetValidation();
        $this->dispatch('closeModal');
        $this->dispatch('close-modal');
    }

    // add modal 
    public function selectOrg_add($id, $name)
    {
        $this->selectedOrg_add = $name;
        $this->selectedOrgId_add = $id;

        // clear the selected agents, if any 
        $this->selected_agent_add = null;
    }

    public function rules()
    {
        $rules = [];

        // if edit qa flag 
        if ($this->selectedFlagId) {
            // if ($this->forms !== 'All Forms') {
            //     $rules['selected_evalForms'] = ['required', 'array', 'min:1'];
            // }

            $rules = [
                'selected_flagName' => ['required', 'string', 'max:255', Rule::unique('qa_flags', 'name')->ignore($this->selectedFlagId)],

                'selected_startDate' => ['required', 'date', 'after:today'],
                'selected_endDate' => ['required', 'date', 'after_or_equal:selected_startDate'],

                'selected_interactionsNumber' => ['required', 'numeric', 'min:1'],
                // 'selected_per' => ['required', 'numeric', 'min:1'],
                'selected_interval' => ['required', 'in:Day,Week,Month'],
                'selected_distributionLevel' => ['required', 'in:Agent'],
                // 'selected_evalForms' => ['required_unless:selected_evalForms, All Forms', 'array', 'min:1'],

                'selected_interactionDays' => ['required', 'array', 'min:1'],

                'selected_interactionTimeFrom' => ['required_unless:interaction_time,All Time'],
                'selected_interactionTimeTo' => ['required_if:selected_interactionTimeCondition,between'],

                // 'selected_interactionDurationFrom' => ['required'],

                'selected_interactionDurationFrom' => [
                    'sometimes', // Only apply validation if the field is present
                    function ($attribute, $value, $fail) {
                        if (!empty($value) && !preg_match('/^[0-5][0-9]:[0-5][0-9]$/', $value)) {
                            // Validation fails if the field is filled and does not match the format
                            $fail('The format should be mm:ss');
                        }
                    },
                ],
                // 'selected_interactionDurationFrom' => [
                //     'required',
                //     function ($attribute, $value, $fail) {
                //         if (!preg_match('/^[0-5][0-9]:[0-5][0-9]$/', $value)) {
                //             $fail('The format should be mm:ss');
                //         }
                //     },
                // ],

                // 'selected_interactionDurationTo' => ['required'],
                'selected_interactionDurationTo' => [
                    'required_if:interactions_duration_condition, between',
                    function ($attribute, $value, $fail) {
                        $from = strtotime($this->selected_interactionDurationFrom);
                        $to = strtotime($value);

                        if ($from >= $to && $this->interactions_duration_condition == 'between') {
                            $fail('The Up to time should be greater than the starting from');
                        }
                    },
                    'nullable'
                ],

                'filter_agentId' => ['required_if:filter_agentId_trigger,true'],

                'filter_silentDuration_from' => ['required_if:filter_silentDuration_trigger,true'],
                // 'filter_silentDuration_to' => ['required_if:filter_silentDuration_condition,between'],

                'filter_silentDuration_to' => [
                    'required_if:filter_silentDuration_condition, between',
                    function ($attribute, $value, $fail) {
                        $from = strtotime($this->filter_silentDuration_from);
                        $to = strtotime($value);

                        if ($from >= $to && $this->filter_silentDuration_condition == 'between') {
                            $fail('The Up to time should be greater than the starting from');
                        }
                    },
                    'nullable'
                ],

                // 'filter_interactionEnder' => ['required_if:filter_interactionEnder_trigger,true', 'not_in:null,""', 'in:agent,Agent,customer,Customer'],

                'filter_interactionEnder' => [
                    'sometimes',
                    function ($attribute, $value, $fail) {
                        if ($this->filter_interactionEnder_trigger) {
                            if (empty($value) || $value === 'null' || $value === '') {
                                $fail("The $attribute field cannot be null or an empty string.");
                            }
                            if (!in_array($value, ['agent', 'Agent', 'customer', 'Customer'])) {
                                $fail("The $attribute field must be one of: agent, Agent, customer, Customer.");
                            }
                        }
                    },
                ],

                'filter_holdDuration_from' => ['required_if:filter_holdDuration_trigger,true'],
                // 'filter_holdDuration_to' => ['required_if:filter_holdDuration_condition,between'],

                'filter_holdDuration_to' => [
                    'required_if:filter_holdDuration_condition,between',
                    function ($attribute, $value, $fail) {
                        $from = strtotime($this->filter_holdDuration_from);
                        $to = strtotime($value);

                        if ($from >= $to && $this->filter_holdDuration_condition == 'between') {
                            $fail('The Up to time should be greater than the starting from');
                        }
                    },
                    'nullable'
                ],


                'filter_holdCount_from' => ['required_if:filter_holdCount_trigger,true', 'nullable', 'numeric', 'min:1'],
                'filter_holdCount_to' => ['required_if:filter_holdCount_condition, between', 'nullable', 'numeric', 'gt:filter_holdCount_from'],

                'filter_agentName' => ['required_if:filter_agentName_trigger,true'],

                'filter_language' => ['required_if:filter_language_trigger,true'],

            ];
        } else {
            $rules = [
                'name_add' => ['required', 'string', 'max:255', 'unique:qa_flags,name'],

                'start_date' => ['required', 'date', 'after:today'],
                'end_date' => ['required', 'date', 'after_or_equal:start_date'],

                'interactions_number' => ['required', 'numeric', 'not_in:0', 'min:1'],
                // 'per' => ['required', 'numeric', 'not_in:0', 'min:1'],
                'criteria' => ['required', 'in:Day,Week,Month'],
                'distribution_level' => ['required', 'in:Agent'],
                // 'selectedForms_add' => ['required_if:forms,Specific Form(s)', 'array'],

                'interaction_days_add' => ['required', 'array', 'min:1'],

                'interaction_time_from' => ['required_unless:interaction_time,All Time'],
                'interaction_time_to' => ['required_if:interaction_time,Specific Time'],

                // 'interactions_duration_from' => 'required',
                'interactions_duration_from' => [
                    'sometimes', // Only apply validation if the field is present
                    function ($attribute, $value, $fail) {
                        if (!empty($value) && !preg_match('/^[0-5][0-9]:[0-5][0-9]$/', $value)) {
                            // Validation fails if the field is filled and does not match the format
                            $fail('The format should be mm:ss');
                        }
                    },
                ],

                'interactions_duration_to' => [
                    'required_if:interactions_duration_condition,between',
                    function ($attribute, $value, $fail) {

                        if (!preg_match('/^[0-5][0-9]:[0-5][0-9]$/', $value)) {
                            $fail('The format should be mm:ss');
                        }

                        $from = strtotime($this->interactions_duration_from);
                        $to = strtotime($value);

                        if ($from >= $to) {
                            $fail('The Up to time should be greater than the starting from');
                        }
                    },
                    'nullable',
                ],

                'selectedOrg_add' => ['required'],

                'filter_agentId' => ['required_if:filter_agentId_trigger,true'],

                'filter_silentDuration_from' => ['required_if:filter_silentDuration_trigger,true'],
                // 'filter_silentDuration_to' => ['required_if:filter_silentDuration_condition,between'],

                'filter_silentDuration_to' => [
                    'required_if:filter_silentDuration_condition,between',
                    function ($attribute, $value, $fail) {
                        $from = strtotime($this->filter_silentDuration_from);
                        $to = strtotime($value);

                        if ($from >= $to) {
                            $fail('The Up to time should be greater than the starting from');
                        }
                    },
                    'nullable'
                ],

                // 'filter_interactionEnder' => ['required_if:filter_interactionEnder_trigger,true', 'not_in:null,""', 'in:agent,Agent,customer,Customer'],

                'filter_interactionEnder' => [
                    'sometimes',
                    function ($attribute, $value, $fail) {
                        if ($this->filter_interactionEnder_trigger) {
                            if (empty($value) || $value === 'null' || $value === '') {
                                $fail("The $attribute field cannot be null or an empty string.");
                            }
                            if (!in_array($value, ['agent', 'Agent', 'customer', 'Customer'])) {
                                $fail("The $attribute field must be one of: agent, Agent, customer, Customer.");
                            }
                        }
                    },
                ],

                'filter_holdDuration_from' => ['required_if:filter_holdDuration_trigger,true'],
                // 'filter_holdDuration_to' => ['required_if:filter_holdDuration_condition,between'],

                'filter_holdDuration_to' => [
                    'required_if:filter_holdDuration_condition,between',
                    function ($attribute, $value, $fail) {
                        $from = strtotime($this->filter_holdDuration_from);
                        $to = strtotime($value);

                        if ($from >= $to) {
                            $fail('The Up to time should be greater than the starting from');
                        }
                    },
                    'nullable'
                ],


                'filter_holdCount_from' => ['required_if:filter_holdCount_trigger,true', 'nullable', 'numeric', 'min:1'],
                'filter_holdCount_to' => ['required_if:filter_holdCount_condition,between', 'nullable', 'numeric', 'gt:filter_holdCount_from'],

                'selected_agent_add' => ['required_if:filter_agentName_trigger,true'],

                'selected_lang_add' => ['required_if:filter_language_trigger,true'],
            ];
        }

        return $rules;
    }

    // add modal 
    public function selectForms($id, $name)
    {
        $this->selectedForms_add[$id] = $name;
        $this->searchForms = '';
    }

    // edit modal 
    public function selectForms_edit($id, $name)
    {
        $this->selected_evalForms[$id] = $name;
        $this->searchForms = '';
    }

    // add modal 
    public function removeForm($id)
    {
        unset($this->selectedForms_add[$id]);
    }

    // add modal 
    public function add_days($id)
    {
        // Check if the day is already in the array
        if ($id && in_array($id, $this->interaction_days_add)) {
            // If the day is already in the array, remove it
            $this->interaction_days_add = array_diff($this->interaction_days_add, [$id]);
        } else {
            // If the day is not in the array, add it
            $this->interaction_days_add[] = $id;
        }

        // Check if all days are selected
        $allDays = range(1, 7); // Change here
        $missingDays = array_diff($allDays, $this->interaction_days_add);

        // Set $interaction_days_field_name based on the result
        $this->interaction_days_field_name = empty($missingDays) ? 'All Days' : 'Custom';
    }

    public function clear()
    {

        $this->selected_org_edit = null;
        $this->selected_orgId_edit = null;
        $this->selectedOrgId_add = null;

        $this->activeTab = 'general';
        $this->resetValidation();
        $this->filter_silentDuration_trigger = false;
        $this->filter_holdDuration_trigger = false;
        $this->filter_agentId_trigger = false;
        $this->filter_interactionEnder_trigger = false;
        $this->filter_holdCount_trigger = false;
        $this->filter_agentName_trigger = false;
        $this->filter_language_trigger = false;

        $this->selected_flagName = null;
        $this->selected_startDate = null;
        $this->selected_endDate = null;
        $this->selected_interactionsNumber = null;
        $this->selected_per = null;
        $this->selected_interval = null;
        $this->selected_distributionLevel = null;
        $this->selected_evalForms = null;
        $this->selected_interactionDays = null;
        $this->selected_interactionTimeFrom = null;
        $this->selected_interactionTimeTo = null;
        $this->selected_interactionDurationFrom = null;
        $this->selected_interactionDurationTo = null;
        $this->filter_agentId = null;
        $this->filter_silentDuration_from = null;
        $this->filter_silentDuration_to = null;
        $this->filter_interactionEnder = null;
        $this->filter_holdDuration_from = null;
        $this->filter_holdDuration_to = null;
        $this->filter_holdCount_from = null;
        $this->filter_holdCount_to = null;
        $this->filter_agentName = null;
        $this->filter_language = null;
        $this->name_add = null;
        $this->start_date = null;
        $this->end_date = null;
        $this->interactions_number = null;
        $this->per = null;
        $this->criteria = null;
        $this->selected_distributionLevel = null;
        $this->selected_evalForms = [];
        $this->selected_interactionDays = [];
        $this->selected_interactionTimeFrom = null;
        $this->selected_interactionTimeTo = null;
        $this->selected_interactionDurationFrom = null;
        $this->selected_interactionDurationTo = null;
        $this->filter_agentId = null;
        $this->filter_silentDuration_from = null;
        $this->filter_silentDuration_to = null;
        $this->filter_interactionEnder = null;
        $this->filter_holdDuration_from = null;
        $this->filter_holdDuration_to = null;
        $this->filter_holdCount_from = null;
        $this->filter_holdCount_to = null;

        $this->filter_language = null;

        $this->filter_interactionEnder = null;

        $this->interactions_duration_condition = '>';
        $this->selected_interactionDurationFrom = null;
        $this->selected_interactionDurationTo = null;
        $this->interactions_duration_from = '00:00';
        $this->interactions_duration_to = null;
        $this->selected_agent_add = null;

        $this->selectedFlagId = null;
        $this->selectedOrgId_add = null;
    }

    public function selectFlag($id)
    {
        $this->selectedFlagId = $id;
        $flag = QaFlag::find($id);
        //========== general ===========
        $this->selected_enabled = (bool) $flag->enabled;
        $this->selected_flagName = $flag->name;
        $this->selected_startDate = $flag->start_date;
        $this->selected_endDate = $flag->end_date;
        $this->selected_interactionsNumber = $flag->interactions_number;
        // $this->selected_per = $flag->per;
        $this->selected_interval = $flag->time_interval;
        $this->selected_distributionLevel = $flag->distribution_level;

        //========= eval forms =========== 
        $all_forms_count = Evaluation::count();
        $selected_evalForms_count = $flag->evaluationForms()->count();

        $this->selected_evalForms = $flag->evaluationForms()->pluck('evaluation_name', 'evaluation_id')->toArray();

        if ($all_forms_count == $selected_evalForms_count) {
            $this->forms = 'All Forms';
        } else {
            $this->forms = 'Specific Forms(s)';
        }

        //========== intreaction days ========
        $this->selected_interactionDays = json_decode($flag->interaction_days);

        if (count($this->selected_interactionDays) < 7) {
            $this->interaction_days_field_name = 'Custom';
        } else {
            $this->interaction_days_field_name = 'All Days';
        }


        //========= interaction time ===========
        $this->selected_interactionTimeFrom = $flag->interaction_time_from ? Carbon::parse($flag->interaction_time_from)->format('H:i') : null;
        $this->selected_interactionTimeTo = $flag->interaction_time_to ? Carbon::parse($flag->interaction_time_to)->format('H:i') : null;

        // if both times are null, means the time is all time
        if ($this->selected_interactionTimeFrom == null && $this->selected_interactionTimeTo == null) {
            $this->interaction_time = 'All Time';
        } else {
            $this->interaction_time = 'Specific Time';
        }

        //======== Interaction Duration ========
        $this->selected_interactionDurationFrom = $flag->duration_start ? Carbon::parse($flag->duration_start)->format('i:s') : null;
        $this->selected_interactionDurationTo = $flag->duration_end ? Carbon::parse($flag->duration_end)->format('i:s') : null;
        $this->interactions_duration_condition = $flag->duration_condition;

        //====== call type ==========
        $this->call_type_add = $flag->call_type;

        //======= org =============
        $this->selected_org_edit = $flag->organization->name;
        $this->selected_orgId_edit = $flag->organization_id;

        //======= filters =======
        foreach ($flag->filters()->get() as $filter) {
            // agent id
            $this->filter_agentId_trigger = $filter->where('name', 'agent_id')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_agentId = $filter->where('name', 'agent_id')->where('qa_flag_id', $flag->id)->first()?->first_data;

            // silent duration
            $this->filter_silentDuration_trigger = $filter->where('name', 'silent_duration')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_silentDuration_condition = $filter->where('name', 'silent_duration')->where('qa_flag_id', $flag->id)->first()?->condition;
            $this->filter_silentDuration_from = $filter->where('name', 'silent_duration')->where('qa_flag_id', $flag->id)->first()?->first_data;
            $this->filter_silentDuration_to = $filter->where('name', 'silent_duration')->where('qa_flag_id', $flag->id)->first()?->second_data;

            // hold duration
            $this->filter_holdDuration_trigger = $filter->where('name', 'hold_duration')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_holdDuration_condition = $filter->where('name', 'hold_duration')->where('qa_flag_id', $flag->id)->first()?->condition;
            $this->filter_holdDuration_from = $filter->where('name', 'hold_duration')->where('qa_flag_id', $flag->id)->first()?->first_data;
            $this->filter_holdDuration_to = $filter->where('name', 'hold_duration')->where('qa_flag_id', $flag->id)->first()?->second_data;

            // interaction ender
            $this->filter_interactionEnder_trigger = $filter->where('name', 'interaction_ender')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_interactionEnder = $filter->where('name', 'interaction_ender')->where('qa_flag_id', $flag->id)->first()?->first_data;

            // hold count
            $this->filter_holdCount_trigger = $filter->where('name', 'hold_count')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_holdCount_condition = $filter->where('name', 'hold_count')->where('qa_flag_id', $flag->id)->first()?->condition;
            $this->filter_holdCount_from = $filter->where('name', 'hold_count')->where('qa_flag_id', $flag->id)->first()?->first_data;
            $this->filter_holdCount_to = $filter->where('name', 'hold_count')->where('qa_flag_id', $flag->id)->first()?->second_data;


            // agent name
            $this->filter_agentName_trigger = $filter->where('name', 'agent_name')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_agentName = $filter->where('name', 'agent_name')->where('qa_flag_id', $flag->id)->first()?->first_data;

            // language
            $this->filter_language_trigger = $filter->where('name', 'language')->where('qa_flag_id', $flag->id)->exists();
            $this->filter_language = $filter->where('name', 'language')->where('qa_flag_id', $flag->id)->first()?->first_data;
        }
    }

    // edit modal 
    public function add_days_edit($id)
    {
        // Check if the day is already in the array
        if ($id && in_array($id, $this->selected_interactionDays)) {
            // If the day is already in the array, remove it
            $this->selected_interactionDays = array_diff($this->selected_interactionDays, [$id]);
        } else {
            // If the day is not in the array, add it
            $this->selected_interactionDays[] = $id;
        }

        // Check if all days are selected
        $allDays = range(1, 7); // Change here
        $missingDays = array_diff($allDays, $this->selected_interactionDays);

        // Set $interaction_days_field_name based on the result
        $this->interaction_days_field_name = empty($missingDays) ? 'All Days' : 'Custom';
    }

    // edit modal 
    public function selectOrg_edit($id, $name)
    {
        $this->selected_org_edit = $name;
        $this->selected_orgId_edit = $id;

        // clear the selected agents, if any 
        $this->selected_agent_edit = null;
    }

    public function removeForm_edit($id)
    {

        unset($this->selected_evalForms[$id]);
    }

    public function removeForm_addModal($id)
    {
        unset($this->selectedForms_add[$id]);
    }

    public function messages()
    {
        return [
            'filter_language.required_if' => 'Please select a language filter'
        ];
    }
    public function getData() 
    {
        $this->applyFlag();
        $this->dispatch('close-modal');
    }
    public function render()
    {
        if (!$this->filter_language_trigger) {
            $this->selected_lang_add = null;
            $this->filter_language = null;
        };

        if (!$this->filter_holdDuration_trigger) {
            $this->filter_holdDuration_from = $this->filter_holdDuration_to = $this->filter_holdDuration_condition = null;
        }

        if (in_array($this->interactions_duration_condition, ['=', '>', "<"])) {
            $this->interactions_duration_to = null;
        }

        return view('livewire.qa-flags', [
            'flags' => QaFlag::where('name', 'like', "%$this->searchFlag%")
                ->orWhere('call_type', 'like', "%$this->searchFlag%")
                ->orWhere('distribution_level', 'like', "%$this->searchFlag%")
                ->orWhere('interactions_number', 'like', "%$this->searchFlag%")
                ->orWhere('start_date', 'like', "%$this->searchFlag%")
                ->orWhere('end_date', 'like', "%$this->searchFlag%")
                ->orderBy($this->sortBy, $this->sortDir)->paginate(15),

            'eval_forms' => Evaluation::where('evaluation_name', 'like', "%$this->searchForms%")->get(),

            'organizations' => Organization::orderBy('name')->get(),

            // agents that appear in the filter in Add modal, they should belong to the selected organization 
            'possible_agents_add' => $this->selectedOrgId_add ? User::where('organization_id', $this->selectedOrgId_add)->where('role', 4)->get() : [],
            'possible_agents_edit' => $this->selected_orgId_edit ? User::where('organization_id', $this->selected_orgId_edit)->where('role', 4)->get() : [],
        ]);
    }
}
