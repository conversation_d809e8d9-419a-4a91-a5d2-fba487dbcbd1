<?php

namespace App\Jobs\Scripts;

use App\Models\CorrectedTranscription;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class CorrectorJob implements ShouldQueue
{
    use Batchable, InteractsWithQueue, Queueable, SerializesModels;

    public $callConversation;
    public $call_id;
    public $timeout = 14400;

    public function __construct($callConversation,$call_id)
    {
        $this->callConversation = $callConversation;
        $this->call_id = $call_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            foreach ($this->callConversation as $textData){

//                $exePath = storage_path('app/python_tool/correct_spelling.py');
                $exePath = storage_path('app/scripts/run_corrector.sh');
                // Prepare the arguments

                $text = $textData->content;

                Log::channel('corrector')->info($textData->content);

                $text = mb_convert_encoding($text, 'UTF-8', 'auto');
                // Execute the .exe file
//                $process = new Process(['python',$exePath, $text]);
                $process = new Process([$exePath, $text]);

                $process->setEnv([
                    'LANG' => 'en_US.UTF-8', // Ensure UTF-8 locale
                    'PYTHONIOENCODING' => 'utf-8' // Force Python to use UTF-8
                ]);
                $process->setTimeout(360);
                $process->run();

                // Check for errors
                if (!$process->isSuccessful()) {
                    Log::channel('corrector')->info($process->getErrorOutput());
                }

                Log::channel('corrector')->info($process->getOutput());

                $data = json_decode($process->getOutput(), true);

                if (!isset($data['error'])){
                    CorrectedTranscription::create([
                        'call_id' => $this->call_id,
                        'language_detected' => $data['language_detected'],
                        'original_content' => $data['original_text'],
                        'corrected_content' => $data['corrected_text'],
                        'corrections' =>  json_encode($data['corrections'], JSON_UNESCAPED_UNICODE),
                        'calls_transcription_id' => $textData->id
                    ]);
                }

            }
        } catch (\Exception $e) {
            Log::channel('corrector')->error('Error in DetectJob: ' . $e->getMessage(), [
                'callConversation' => $this->callConversation,
                'exception' => $e,
            ]);

            throw $e;
        }
    }
}
