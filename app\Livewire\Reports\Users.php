<?php

namespace App\Livewire\Reports;

use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Exports\UserExport;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class Users extends Component
{
    use LivewireAlert, WithPagination;

    public $perPage = 15;

    // filters
    public $filter_name;
    public $filter_agent_id;
    public $filter_user_group;
    public $filter_role;
    public $filter_terminated;
    public $filter_enabled;

    public $filter_user_group_name;
    public $filter_role_name;
    public $filter_terminated_name;
    public $filter_enabled_name;


    public $sortBy = 'full_name';
    public $sortDir = 'ASC';

    public $filtersApplied = false;

    // live search 
    public $searchUser = '';

    // Users modal
    public $activeTab = 'general';

    // public $users;
    public $selectedUserId;
    public $selectedUserName;
    public $selectedUserRole;

    public $email;
    public $name;
    public $password;
    public $role;


    protected $paginationTheme = 'bootstrap';

    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }


    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function applyFilters()
    {
        $this->filtersApplied = true;
    }

    public function clear()
    {
        $this->filter_name = null;
        $this->filter_agent_id = null;
        $this->filter_user_group = null;
        $this->filter_role = null;
        $this->filter_terminated = null;
        $this->filter_enabled = null;

        $this->filter_user_group_name = null;
        $this->filter_role_name = null;
        $this->filter_terminated_name = null;
        $this->filter_enabled_name = null;
        $this->filtersApplied = false;
    }

    public function filterUserGroup($id)
    {
        $this->filter_user_group = $id;
        $this->filter_user_group_name = UserGroup::find($id)->name;
    }

    public function filterRole($id)
    {
        if ($id == 'All') {
            $id = null;
        }

        $this->filter_role = $id;
        $this->filter_role_name = match ($id) {
            1 => 'Admins',
            2 => 'Supervisors',
            3 => 'IT',
            4 => 'Agents',
            5 => 'Quality',
            6 => 'Client',
            default => 'All'
        };
    }

    public function filterTerminated($id)
    {
        $this->filter_terminated = $id;
        $this->filter_terminated_name =  match ($id) {
            'no' => 'Not Terminated',
            'yes' => 'Terminated',
            default => 'All'
        };

        if ($id == 'All') {
            $this->filter_terminated = null;
        }
        if ($id == 'yes') {
            $this->filter_terminated = 1;
        }
        if ($id == 'no') {
            $this->filter_terminated = false;
        }
    }

    public function filterEnabled($id)
    {
        $this->filter_enabled = $id;

        if ($id == 'All') {
            $this->filter_enabled = null;
        }


        $this->filter_enabled_name = match ($id) {
            'zero' => 'Disabled',
            'one' => 'Enabled',
            default => 'All'
        };
    }

    public function export()
    {
        $export = new UserExport($this->filter_name, $this->filter_agent_id, $this->filter_user_group, $this->filter_role, $this->filter_terminated, $this->filter_enabled);

        // Return the export response
        return Excel::download($export, 'users.xlsx');
    }

    public function getData() 
    {
        $this->applyFilters();
        $this->dispatch('close-modal');
    }
    public function render()
    {
        return view('livewire.reports.users', [
            'users' => $this->filtersApplied ?
                User::where('full_name', 'like', "%$this->filter_name%")
                ->when($this->filter_agent_id, function ($query) {
                    return $query->where('agent_id', 'like', "%$this->filter_agent_id%");
                })
                ->when($this->filter_user_group, function ($query) {
                    return $query->where('user_group_id', $this->filter_user_group);
                })
                ->when($this->filter_role, function ($query) {
                    return $query->where('role', $this->filter_role);
                })
                ->when($this->filter_terminated, function ($query) {
                    return $query->where('terminated', $this->filter_terminated);
                })
                ->when($this->filter_enabled, function ($query) {
                    if ($this->filter_enabled == 'zero') $this->filter_enabled = 0;
                    if ($this->filter_enabled == 'one') $this->filter_enabled = 1;
                    return $query->where('enabled', $this->filter_enabled);
                })
                ->orderBy($this->sortBy, $this->sortDir)
                ->paginate($this->perPage)
                :
                User::orderBy($this->sortBy, $this->sortDir)
                ->paginate($this->perPage),

            // user groups 
            'groups' => UserGroup::all()
        ]);
    }
}
