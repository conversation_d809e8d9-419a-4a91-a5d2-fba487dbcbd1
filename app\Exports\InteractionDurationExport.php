<?php
namespace App\Exports;

set_time_limit(0);
ini_set('memory_limit', '5000M');
ini_set('max_execution_time', 0);

use App\Models\Interaction;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class InteractionDurationExport implements FromCollection, WithHeadings, ShouldAutoSize, WithChunkReading, ShouldQueue
{
    
    use Exportable;

    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_agentId;
    public $filter_duration;
    public $filter_calledId;
    public $filter_callerId;
    public $filter_callEnder;
    public $filter_agentName;
    public $filter_account;
    public $filter_language;
    public $filter_uniqueId;
    
    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct(
        $filter_time_name,
        $filter_time_days,
        $custom_date_from,
        $custom_date_to,
        $filter_callId,
        $filter_agentId,
        $filter_duration,
        $filter_calledId,
        $filter_callerId,
        $filter_callEnder,
        $filter_agentName,
        $filter_account,
        $filter_language,
        $filter_uniqueId,
        
    ) {
        $this->filter_time_name = $filter_time_name;
        $this->filter_time_days = $filter_time_days;
        $this->custom_date_from = $custom_date_from;
        $this->custom_date_to = $custom_date_to;
        $this->filter_callId = $filter_callId;
        $this->filter_agentId = $filter_agentId;
        $this->filter_duration = $filter_duration;
        $this->filter_calledId = $filter_calledId;
        $this->filter_callerId = $filter_callerId;
        $this->filter_callEnder = $filter_callEnder;
        $this->filter_agentName = $filter_agentName;
        $this->filter_account = $filter_account;
        $this->filter_language = $filter_language;
        $this->filter_uniqueId = $filter_uniqueId;
        
    }

    public function collection()
    {
        $query = Interaction::query();

        if ($this->filter_calledId) {
            $query->where('called_id', 'like', "%$this->filter_calledId%");
        }
        if ($this->filter_callerId) {
            $query->where('caller_id', 'like', "%$this->filter_callerId%");
        }
        if ($this->filter_callId) {
            $query->where('call_id', 'like', "%$this->filter_callId%");
        }
        if ($this->filter_agentId) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('agent_id', 'like', "%$this->filter_agentId%");
            });
        }
        if ($this->filter_duration) {
            $this->filter_duration = gmdate('H:i:s', $this->filter_duration);
            $query->where('call_duration', 'like', "%$this->filter_duration%");
        }

        if ($this->filter_callEnder) {
            $query->where('call_ender', 'like', "%$this->filter_callEnder%");
        }


        if ($this->filter_agentName) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('full_name', 'like', "%$this->filter_agentName%");
            });
        }
        if ($this->filter_uniqueId) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('Genesys_CallUUID', 'like', "%$this->filter_uniqueId%");
            });
        }


 
        if ($this->filter_time_name == 'Custom') {
            $this->custom_date_from = $this->custom_date_from . ' 00:00:00';
            $this->custom_date_to = $this->custom_date_to . ' 23:59:59';
            $query->whereBetween('arrival_time', [$this->custom_date_from, $this->custom_date_to]);
        }
        if ($this->filter_time_days) {
            $startDate = now()->subDays($this->filter_time_days)->toDateString();
            $query->whereDate('arrival_time', '>=', $startDate);
        }

        if ($this->filter_account) {
            $query->whereHas('agent.organization', function ($q2) {
                $q2->where('name', 'like', "%{$this->filter_account}%");
            });
        }


        $n = 1;
        return $query->get()
            ->map(function ($interaction) use (&$n) {
                return [
                    '#' => $n++,
                    'Account' => $interaction->agent->organization->name ?? '-',
                    'Agent ID' => $interaction?->agent?->agent_id ?? '-',
                    'Agent Name' => $interaction?->agent?->full_name ?? '-',
                    'Call Date Time' => $interaction->arrival_time,
                    'Caller ID' => $interaction->caller_id,
                    'Unique ID' => $interaction->Genesys_CallUUID,
                    'Language' => $interaction->language,
                    'Call Ender' => $interaction->call_ender,
                    'Call Duration' => $interaction->call_duration,
                ];
            });
    }

    public function headings(): array
    {
        return [
            '#',
            'Account',
            'Agent ID',
            'Agent Name',
            'Call Date Time',
            'Caller ID',
            'Unique ID',
            'Language',
            'Call Ender',
            'Call Duration',
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
