<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\QaFlag;
use App\Livewire\QaFlags;
use App\Models\Interaction;
use App\Models\Organization;
use App\Models\TimeForFlags;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetFlagsForInteractions extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'flags:set';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set flags for interactions based on filters in the qa_flags table';


    /**
     * Execute the console command.
     */
    public function handle()
    {

        // flags that are not id 1 (default_flag)
        $daily_flags = QaFlag::where('id', '!=', 1)->where('time_interval', 'Day')->where('active', 1)->where('enabled', 1)->get();
        $weekly_flags = QaFlag::where('id', '!=', 1)->where('time_interval', 'Week')->where('active', 1)->where('enabled', 1)->get();


        // daily flags 
        $daily_scheduled_flags = TimeForFlags::where('flag_frequency', 'daily')->whereNotNull('flag_id')->get();
        $weekly_scheduled_flags = TimeForFlags::where('flag_frequency', 'weekly')->whereNotNull('flag_id')->get();

        foreach ($daily_scheduled_flags as $flag) {
            // Check if the flag is active and enabled 
            if (!QaFlag::firstWhere('id', $flag->flag_id)->active || !QaFlag::firstWhere('id', $flag->flag_id)->enabled) {
                continue;
            } else {
                $qaFlag = QaFlag::firstWhere('id', $flag->flag_id);
                $callsPerAgent = $qaFlag->interactions_number;
                $callType = $qaFlag->call_type;
                $call_duration_from = $qaFlag->duration_start;
                $call_duration_to = $qaFlag->duration_end;
                $call_duration_condition = $qaFlag->duration_condition;
                $call_time_from = $qaFlag->interaction_time_from;
                $call_time_to = $qaFlag->interaction_time_to;
                $selected_days = $qaFlag->interaction_days;

                // Decode the JSON string into an array if needed
                if (is_string($selected_days)) {
                    $selected_days = json_decode($selected_days, true);
                }

                // Ensure it's a proper array
                $selected_days = is_array($selected_days) ? $selected_days : [];

                if ($callType == 'All') $callType = null;

                // language filter
                $language = null;
                if ($qaFlag->filters()?->where('name', 'Language')?->first()?->first_data) {
                    $language = $qaFlag->filters()->where('name', 'like', "%$language%")->first()->first_data;
                }

                // agent_id filter 
                $agent_id = null;
                if ($qaFlag->filters()?->where('name', 'agent_id')?->first()?->first_data) {
                    $agent_id = $qaFlag->filters()->where('name', 'agent_id')->first()->first_data;
                }


                // interaction ender filter 
                $interaction_ender = null;
                if ($qaFlag->filters()?->where('name', 'interaction_ender')?->first()?->first_data) {
                    $interaction_ender = $qaFlag->filters()->where('name', 'interaction_ender')->first()->first_data;
                }

                // Hold Duration Filter
                $hold_duration = $hold_duration_condition = null;
                if ($qaFlag->filters()?->where('name', 'hold_duration')?->first()?->first_data) {
                    $hold_duration_from = $qaFlag->filters()->where('name', 'hold_duration')->first()->first_data;
                    $hold_duration_to = $qaFlag->filters()->where('name', 'hold_duration')->first()?->second_data;
                }
                if ($qaFlag->filters()?->where('name', 'hold_duration')?->first()?->first_data) {
                    $hold_duration_condition = $qaFlag->filters()->where('name', 'hold_duration')->first()->condition;
                }

                // Hold Count Filter
                $hold_count = $hold_count_condition = null;
                if ($qaFlag->filters()?->where('name', 'hold_count')?->first()?->first_data) {
                    $hold_count_from = $qaFlag->filters()->where('name', 'hold_count')->first()->first_data;
                    $hold_count_to = $qaFlag->filters()->where('name', 'hold_count')->first()->second_data;
                }
                if ($qaFlag->filters()?->where('name', 'hold_count')?->first()?->first_data) {
                    $hold_count_condition = $qaFlag->filters()->where('name', 'hold_count')->first()->condition;
                }

                // Agent Name Filter
                $agent_name = null;
                if ($qaFlag->filters()?->where('name', 'agent_name')?->first()?->first_data) {
                    $agent_name = $qaFlag->filters()->where('name', 'agent_name')->first()->first_data;
                }


                $randomInteractions = Interaction::whereBetween('arrival_time', [Carbon::yesterday()->startOfDay(), now()->startOfDay()])
                    ->whereDoesntHave('qaFlags')
                    ->when($callType, function ($q) use ($callType) {
                        $q->where('call_type', $callType);
                    })
                    ->where(function ($query) use ($selected_days) {
                        $query->whereIn(DB::raw('(DAYOFWEEK(arrival_time) % 7) + 1'), $selected_days);
                    })
                    ->when($language, function ($qq) use ($language) {
                        $qq->where('language', $language);
                    })
                    ->when($hold_duration_from && $hold_duration_condition != 'between', function ($qq) use ($hold_duration_from, $hold_duration_condition) {
                        $qq->where('hold_duration', $hold_duration_condition, $hold_duration_from);
                    })
                    ->when($hold_duration_from && $hold_duration_to && $hold_duration_condition == 'between', function ($qq) use ($hold_duration_from, $hold_duration_to) {
                        $qq->whereTime('hold_duration', '>', $hold_duration_from)
                            ->whereTime('hold_duration', '<', $hold_duration_to);
                    })
                    ->when($hold_count_from && $hold_count_condition != 'between', function ($qq) use ($hold_count_from, $hold_count_condition) {
                        $qq->where('hold_count', $hold_count_condition, $hold_count_from);
                    })
                    ->when($hold_count_from && $hold_count_to && $hold_count_condition == 'between', function ($qq) use ($hold_count_from, $hold_count_to) {
                        $qq->where('hold_count', '>', $hold_count_from)
                            ->whereTime('hold_count', '<', $hold_count_to);
                    })
                    ->when($interaction_ender, function ($qq) use ($interaction_ender) {
                        $qq->where('call_ender', 'like', "%$interaction_ender%");
                    })
                    ->when($agent_id, function ($qq1) use ($agent_id) {
                        $qq1->whereHas('agent', function ($q) use ($agent_id) {
                            $q->where('agent_id', $agent_id);
                        });
                    })
                    ->when($agent_name, function ($qq1) use ($agent_name) {
                        $qq1->whereHas('agent', function ($q) use ($agent_name) {
                            $q->where('id', $agent_name);
                        });
                    })
                    ->when($call_duration_condition == 'between' && $call_duration_from && $call_duration_to, function ($q) use ($call_duration_from, $call_duration_to) {
                        $q->whereTime('call_duration', '>', $call_duration_from)
                            ->whereTime('call_duration', '<', $call_duration_to);
                    })
                    ->when($call_duration_condition != 'between' && $call_duration_from, function ($q) use ($call_duration_from, $call_duration_condition) {
                        $q->whereTime('call_duration', $call_duration_condition, $call_duration_from);
                    })
                    ->when($call_time_from, function ($q) use ($call_time_from, $call_time_to) {
                        $q->whereTime('arrival_time', '>=', $call_time_from);
                    })
                    ->when($call_time_from && $call_time_to, function ($q) use ($call_time_from, $call_time_to) {
                        $q->whereTime('arrival_time', '>=', $call_time_from)
                            ->whereTime('arrival_time', '<=', $call_time_to);
                    })
                    ->whereHas('agent.organization', function ($q) use ($flag) {
                        $q->where('id', QaFlag::where('id', $flag->flag_id)->first()->organization_id);
                    })
                    ->inRandomOrder()
                    ->get()
                    ->groupBy('user_id')
                    ->map(function ($interactions) use ($callsPerAgent) {
                        return $interactions->random(min($callsPerAgent, $interactions->count())); // Select up to the specified number of interactions per user
                    })
                    ->flatten(1); // Flatten the collection to a single level

                // add flags 
                foreach ($randomInteractions as $interaction) {
                    $interaction->qaFlags()->attach($flag->flag_id);
                }
            }
        }


        foreach ($weekly_scheduled_flags as $flag) {
            // Check if the flag is active and enabled 
            if (!QaFlag::firstWhere('id', $flag->flag_id)->active || !QaFlag::firstWhere('id', $flag->flag_id)->enabled) {
                continue;
            } else {

                $qaFlag = QaFlag::firstWhere('id', $flag->flag_id);

                $startDate = Carbon::parse($qaFlag->start_date);
                $endDate = Carbon::parse($qaFlag->end_date);
                $now = Carbon::now();

                if ($now->between($startDate, $endDate)) {
                    $diffInDays = $startDate->diffInDays($now);
                    if ($diffInDays % 7 === 0) {
                        $callsPerAgent = $qaFlag->interactions_number;
                        $callType = $qaFlag->call_type;
                        $call_duration_from = $qaFlag->duration_start;
                        $call_duration_to = $qaFlag->duration_end;
                        $call_duration_condition = $qaFlag->duration_condition;
                        $call_time_from = $qaFlag->interaction_time_from;
                        $call_time_to = $qaFlag->interaction_time_to;
                        $selected_days = $qaFlag->interaction_days;

                        // Decode the JSON string into an array if needed
                        if (is_string($selected_days)) {
                            $selected_days = json_decode($selected_days, true);
                        }

                        // Ensure it's a proper array
                        $selected_days = is_array($selected_days) ? $selected_days : [];

                        if ($callType == 'All') $callType = null;

                        // language filter
                        $language = null;
                        if ($qaFlag->filters()?->where('name', 'language')?->first()?->first_data) {
                            $language = $qaFlag->filters()->where('name', 'like', "%$language%")->first()->first_data;
                        }

                        // agent_id filter 
                        if ($qaFlag->filters()?->where('name', 'agent_id')?->first()?->first_data) {
                            $agent_id = $qaFlag->filters()->where('name', 'agent_id')->first()->first_data;
                        }

                        // interaction ender filter 
                        if ($qaFlag->filters()?->where('name', 'interaction_ender')?->first()?->first_data) {
                            $interaction_ender = $qaFlag->filters()->where('name', 'interaction_ender')->first()->first_data;
                        }

                        // Hold Duration Filter
                        $hold_duration = $hold_duration_condition = null;
                        if ($qaFlag->filters()?->where('name', 'hold_duration')?->first()?->first_data) {
                            $hold_duration_from = $qaFlag->filters()->where('name', 'hold_duration')->first()->first_data;
                            $hold_duration_to = $qaFlag->filters()->where('name', 'hold_duration')->first()?->second_data;
                        }
                        if ($qaFlag->filters()?->where('name', 'hold_duration')?->first()?->first_data) {
                            $hold_duration_condition = $qaFlag->filters()->where('name', 'hold_duration')->first()->condition;
                        }

                        // Hold Count Filter
                        $hold_count = $hold_count_condition = null;
                        if ($qaFlag->filters()?->where('name', 'hold_count')?->first()?->first_data) {
                            $hold_count_from = $qaFlag->filters()->where('name', 'hold_count')->first()->first_data;
                            $hold_count_to = $qaFlag->filters()->where('name', 'hold_count')->first()->second_data;
                        }
                        if ($qaFlag->filters()?->where('name', 'hold_count')?->first()?->first_data) {
                            $hold_count_condition = $qaFlag->filters()->where('name', 'hold_count')->first()->condition;
                        }

                        // Agent Name Filter
                        $agent_name = null;
                        if ($qaFlag->filters()?->where('name', 'agent_name')?->first()?->first_data) {
                            $agent_name = $qaFlag->filters()->where('name', 'agent_name')->first()->first_data;
                        }


                        $randomInteractions = Interaction::whereBetween('arrival_time', [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()])
                            ->whereDoesntHave('qaFlags')
                            ->when($callType, function ($q) use ($callType) {
                                $q->where('call_type', $callType);
                            })
                            ->where(function ($query) use ($selected_days) {
                                $query->whereIn(DB::raw('(DAYOFWEEK(arrival_time) % 7) + 1'), $selected_days);
                            })
                            ->when($language, function ($qq) use ($language) {
                                $qq->where('language', $language);
                            })
                            ->when($hold_duration_from && $hold_duration_condition != 'between', function ($qq) use ($hold_duration_from, $hold_duration_condition) {
                                $qq->where('hold_duration', $hold_duration_condition, $hold_duration_from);
                            })
                            ->when($hold_duration_from && $hold_duration_to && $hold_duration_condition == 'between', function ($qq) use ($hold_duration_from, $hold_duration_to) {
                                $qq->whereTime('hold_duration', '>', $hold_duration_from)
                                    ->whereTime('hold_duration', '<', $hold_duration_to);
                            })
                            ->when($hold_count_from && $hold_count_condition != 'between', function ($qq) use ($hold_count_from, $hold_count_condition) {
                                $qq->where('hold_count', $hold_count_condition, $hold_count_from);
                            })
                            ->when($hold_count_from && $hold_count_to && $hold_count_condition == 'between', function ($qq) use ($hold_count_from, $hold_count_to) {
                                $qq->where('hold_count', '>', $hold_count_from)
                                    ->whereTime('hold_count', '<', $hold_count_to);
                            })
                            ->when($interaction_ender, function ($qq) use ($interaction_ender) {
                                $qq->where('call_ender', 'like', "%$interaction_ender%");
                            })
                            ->when($agent_id, function ($qq1) use ($agent_id) {
                                $qq1->whereHas('agent', function ($q) use ($agent_id) {
                                    $q->where('agent_id', $agent_id);
                                });
                            })
                            ->when($agent_name, function ($qq1) use ($agent_name) {
                                $qq1->whereHas('agent', function ($q) use ($agent_name) {
                                    $q->where('id', $agent_name);
                                });
                            })
                            ->when($call_duration_condition == 'between' && $call_duration_from && $call_duration_to, function ($q) use ($call_duration_from, $call_duration_to) {
                                $q->whereTime('call_duration', '>', $call_duration_from)
                                    ->whereTime('call_duration', '<', $call_duration_to);
                            })
                            ->when($call_duration_condition != 'between' && $call_duration_from, function ($q) use ($call_duration_from, $call_duration_condition) {
                                $q->whereTime('call_duration', $call_duration_condition, $call_duration_from);
                            })
                            ->when($call_time_from, function ($q) use ($call_time_from, $call_time_to) {
                                $q->whereTime('arrival_time', '>=', $call_time_from);
                            })
                            ->when($call_time_from && $call_time_to, function ($q) use ($call_time_from, $call_time_to) {
                                $q->whereTime('arrival_time', '>=', $call_time_from)
                                    ->whereTime('arrival_time', '<=', $call_time_to);
                            })
                            ->whereHas('agent.organization', function ($q) use ($flag) {
                                $q->where('id', QaFlag::where('id', $flag->flag_id)->first()->organization_id);
                            })
                            ->get()
                            ->groupBy(['user_id', function ($item) {
                                return $item->arrival_time->format('Y-m-d');
                            }])
                            ->map(function ($groupedByUser) use ($callsPerAgent) {
                                return $groupedByUser->map(function ($interactions) use ($callsPerAgent) {
                                    return $interactions->random(min(1, $interactions->count())); // Select one interaction per day
                                });
                            })
                            ->flatten(2) // Flatten the collection to two levels
                            ->groupBy('user_id')
                            ->map(function ($interactions) use ($callsPerAgent) {
                                return $interactions->take($callsPerAgent); // Take up to the specified number of interactions per user
                            })
                            ->flatten(1); // Flatten the collection to a single level

                        // add flags 
                        foreach ($randomInteractions as $interaction) {
                            $interaction->qaFlags()->attach($flag->flag_id);
                        }
                    }
                }
            }
        }
    }
}
