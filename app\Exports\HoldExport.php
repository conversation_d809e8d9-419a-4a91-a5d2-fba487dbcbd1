<?php

namespace App\Exports;

use App\Models\Interaction;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class HoldExport implements FromCollection, WithHeadings, ShouldAutoSize, WithChunkReading, ShouldQueue
{
    use Exportable;

    public $all_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_agentId;
    public $filter_duration;
    public $filter_callType;
    public $filter_calledId;
    public $filter_callerId;
    public $filter_callEnder;
    public $filter_holdDuration;
    public $filter_holdCount;
    public $filter_isAssigned;
    public $filter_evaluationScore;
    public $filter_isEvaluated;
    public $filter_qaFlaggedInteractions;
    public $filter_flagDate;
    public $filter_agentName;
    public $filter_account;
    public $filter_skillGroup;
    public $filter_callImportance;
    public $filter_pauseCount;
    public $filter_language;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct(
        $filter_ratio,
        $filter_ratio_sign,
        $filter_time_name,
        $filter_time_days,
        $custom_date_from,
        $custom_date_to,
        $filter_callId,
        $filter_agentId,
        $filter_duration,
        $filter_callType,
        $filter_calledId,
        $filter_callerId,
        $filter_callEnder,
        $filter_holdDuration,
        $filter_holdCount,
        $filter_isAssigned,
        $filter_evaluationScore,
        $filter_isEvaluated,
        $filter_qaFlaggedInteractions,
        $filter_flagDate,
        $filter_agentName,
        $filter_account,
        $filter_skillGroup,
        $filter_callImportance,
        $filter_pauseCount,
        $filter_language,
        $filter_selected_languages,
        $filter_holdDuration_sign,
        $filter_holdCount_sign,
        $filter_duration_sign,
    ) {
        $this->filter_ratio = $filter_ratio;
        $this->filter_ratio_sign = $filter_ratio_sign;
        $this->filter_time_name = $filter_time_name;
        $this->filter_time_days = $filter_time_days;
        $this->custom_date_from = $custom_date_from;
        $this->custom_date_to = $custom_date_to;
        $this->filter_callId = $filter_callId;
        $this->filter_agentId = $filter_agentId;
        $this->filter_duration = $filter_duration;
        $this->filter_callType = $filter_callType;
        $this->filter_calledId = $filter_calledId;
        $this->filter_callerId = $filter_callerId;
        $this->filter_callEnder = $filter_callEnder;
        $this->filter_holdDuration = $filter_holdDuration;
        $this->filter_holdCount = $filter_holdCount;
        $this->filter_isAssigned = $filter_isAssigned;
        $this->filter_evaluationScore = $filter_evaluationScore;
        $this->filter_isEvaluated = $filter_isEvaluated;
        $this->filter_qaFlaggedInteractions = $filter_qaFlaggedInteractions;
        $this->filter_flagDate = $filter_flagDate;
        $this->filter_agentName = $filter_agentName;
        $this->filter_account = $filter_account;
        $this->filter_skillGroup = $filter_skillGroup;
        $this->filter_callImportance = $filter_callImportance;
        $this->filter_pauseCount = $filter_pauseCount;
        $this->filter_language = $filter_language;
        $this->filter_selected_languages = $filter_selected_languages;
        $this->filter_duration_sign = $filter_duration_sign;
        $this->filter_holdDuration_sign = $filter_holdDuration_sign;
        $this->filter_holdCount_sign = $filter_holdCount_sign;
    }

    public function collection()
    {
        $query = Interaction::query();

        if ($this->filter_callerId) {
            $query->where('caller_id', 'like', "%$this->filter_callerId%");
        }

        if ($this->filter_agentId) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('agent_id', 'like', "%$this->filter_agentId%");
            });
        }

        if ($this->filter_duration || $this->filter_duration == "0") {
            $this->filter_duration = gmdate('H:i:s', $this->filter_duration);
            $query->where('call_duration', $this->filter_duration_sign, $this->filter_duration);
        }



        if ($this->filter_holdDuration || $this->filter_holdDuration == "0") {
            $this->filter_holdDuration = gmdate('H:i:s', $this->filter_holdDuration);
            $query->where('call_duration', $this->filter_holdDuration_sign, $this->filter_holdDuration);
        }
        if ($this->filter_holdCount) {
            // $this->filter_holdCount = gmdate('H:i:s', $this->filter_holdCount);
            $query->where('hold_count', $this->filter_holdCount_sign, $this->filter_holdCount);
        }
        if ($this->filter_agentName) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('full_name', 'like', "%$this->filter_agentName%");
            });
        }


        if ($this->filter_time_name == 'Custom') {
            $this->custom_date_from = $this->custom_date_from . ' 00:00:00';
            $this->custom_date_to = $this->custom_date_to . ' 23:59:59';
            $query->whereBetween('arrival_time', [$this->custom_date_from, $this->custom_date_to]);
        }
        if ($this->filter_time_days) {
            $startDate = now()->subDays($this->filter_time_days)->toDateString();
            $query->whereDate('arrival_time', '>=', $startDate);
        }

        if ($this->filter_account) {
            $query->whereHas('agent.organization', function ($q2) {
                $q2->where('name', 'like', "%{$this->filter_account}%");
            });
        }

        if ($this->filter_ratio) {
            // Define the comparison operator based on filter_ratio_sign
            $operator = $this->filter_ratio_sign === '>' ? '>' :
                        ($this->filter_ratio_sign === '<' ? '<' :
                        ($this->filter_ratio_sign === '=' ? '=' : null));
    
            if ($operator !== null) {
                // Use a raw expression to calculate the ratio directly in SQL
                $query->whereRaw("
                    CASE
                        WHEN call_duration IS NOT NULL AND call_duration != '00:00:00'
                        THEN (
                            (CAST(SUBSTRING_INDEX(hold_duration, ':', 1) AS UNSIGNED) * 3600
                            + CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(hold_duration, ':', -2), ':', 1) AS UNSIGNED) * 60
                            + CAST(SUBSTRING_INDEX(hold_duration, ':', -1) AS UNSIGNED))
                        ) / (
                            (CAST(SUBSTRING_INDEX(call_duration, ':', 1) AS UNSIGNED) * 3600
                            + CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(call_duration, ':', -2), ':', 1) AS UNSIGNED) * 60
                            + CAST(SUBSTRING_INDEX(call_duration, ':', -1) AS UNSIGNED))
                        ) * 100
                        ELSE 0
                    END
                    $operator ?
                ", [$this->filter_ratio]);
            }
        }

        // if ($this->filter_language) {
        //     $query->where('language', 'like', "%$this->filter_language%");
        // }
        if ($this->filter_selected_languages && $this->filter_selected_languages != $this->all_languages) {
            $lowercaseLanguages = array_map('lcfirst', $this->filter_selected_languages);
            $query->whereIn('language', $lowercaseLanguages);
        }

        $n = 1;
        return $query->get()
            ->map(function ($interaction) use (&$n) {
                return [
                    '#' => $n++,
                    'Account' => $interaction->agent->organization->name ?? '-',
                    'Agent ID' => $interaction?->agent?->agent_id ?? '-',
                    'Agent Name' => $interaction?->agent?->full_name ?? '-',
                    'Call Time' => $interaction->arrival_time,
                    'Caller ID' => $interaction->caller_id,
                    'Language' => $interaction->language,
                    'Call Duration' => $interaction->call_duration,
                    'Hold Duration' => $interaction->hold_duration,
                    'Hold Count' => $interaction->hold_count ? $interaction->hold_count : '0',
                    'Hold to Duration Ratio' => $this->ratio($interaction->hold_duration,$interaction->call_duration )
                ];
            });
    }

    public function ratio($hold, $call)
    {
        $ratio = 0;
        if ($hold && $call && $call != '00:00:00') {

            [$hours, $minutes, $seconds] = explode(':', $call);

            // Calculate the total seconds
            $totalSeconds = $hours * 3600 + $minutes * 60 + $seconds;

            // Convert the total seconds to an integer
            $totalSeconds = (int) $totalSeconds;

            // -------------------------

            [$holdHours, $holdMinutes, $holdSeconds] = explode(':', $hold);

            // Calculate the total seconds
            $totalHoldSeconds = $holdHours * 3600 + $holdMinutes * 60 + $holdSeconds;

            // Convert the total seconds to an integer
            $totalHoldSeconds = (int) $totalHoldSeconds;

            return $ratio = number_format(($totalHoldSeconds / $totalSeconds) * 100, 2) . ' %';
        } else {
            return $ratio = '-';
        }
    }

    public function headings(): array
    {
        return [
            '#',
            'Account',
            'Agent ID',
            'Agent Name',
            'Call Time',
            'Caller ID',
            'Language',
            'Call Duration',
            'Hold Duration',
            'Hold Count',
            'Hold to Duration Ratio',
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
