<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Symfony\Component\HttpFoundation\Response;

class ForcePasswordReset
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        {
            $user = Auth::user();
    
            if ($user && $user->reset_pass_next_login) {
                // Logout the user
                Auth::logout();
    
                // Generate password reset token
                $token = Password::broker()->createToken($user);
    
                // Redirect the user to the password reset page with the token
                return redirect()->route('password.reset', ['token' => $token])->with('warning', 'You are required to reset your password');
            }
    
            return $next($request);
        }
    }
}
