<div class="container-fluid mt-3 px-4">


        {{-- header row  --}}
        <div class="header">
            <div class="row justify-content-end mx-3 ps-lg-5">
                <div class="col-auto">
                    <button
                        wire:target="export"
                        wire:click="export"
                        title="Export"
                        class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                        style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                        wire:loading.attr="disabled">

                        <i
                            wire:loading.remove
                            wire:target="export"
                            class="fas fa-file-excel text-white me-2"
                            style="font-size: 20px;"></i>

                        <span
                            wire:loading.class="spinner-border spinner-border-sm"
                            wire:target="export"
                            style="width: 1rem; height: 1rem;"
                            role="status"
                            aria-hidden="true"></span>

                        <span
                            wire:loading.remove
                            wire:target="export"
                            style="font-size: 17px;">Extract Excel</span>
                    </button>
                </div>
                <div class="col-auto mt-3 mt-sm-0 pe-0">
                    <button
                        data-bs-toggle="modal"
                        data-bs-target="#filterModal"
                        class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                        style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                        <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon" >
                        <span style="font-size: 17px;">Filter</span>
                    </button>
                </div>
            </div>
        </div>


        {{-- <div class="d-flex mb-1">
            <label for="" class="col-md-1 align-self-center me-1 fs-6" style="width: fit-content">Search: </label>
            <input id="searchInput" type="text" class="col-md-1 form-control mb-1 d-inline text-muted p-1" placeholder=' &#xF002;' style="font-family:Arial, FontAwesome;width:9rem" wire:model.live.debounce.300ms="searchCalls" onclick="this.placeholder=''" onblur="this.placeholder='&#xF002;'">
        </div> --}}
        <div class="parent-sections mx-3 ps-5">

            <div class="section-one">
                    <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">

                    <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                        <thead id="thead" class="thead text-muted" style="font-size: 0.7rem">
                            <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                <th scope="col" class="text-center" style="color: white">ID</th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-building fa-xl" style="color: white" title="Account"></i></th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-hashtag fa-xl" style="color: white" title="User ID"></i></th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-id-card fa-xl" title="Agent Name" style="color: white"></i></th>
                                <th colspan="col" scope="col" class="text-center"><i class="fa-regular fa-calendar-days fa-xl" title="Date" style="color: white"></i></th>
                                <th scope="col" class="text-center">
                                    <div style="position: relative; display: inline-block;">
                                        <small style="position: absolute; top: -14px; left: 80%; transform: translateX(-50%); font-size: 0.8rem;">
                                          <i class="fa fa-arrow-up" title="Outgoing Call" style="color: white;"></i>
                                        </small>
                                        <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                      </div>

                                </th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-fingerprint fa-xl" style="color: white" title="Unique ID"></i></th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-language fa-xl" style="color: white" title="Language"></i></th>
                                <th scope="col" class="text-center">
                                    <div style="position: relative; display: inline-block;">
                                        <small style="position: absolute; top: -14px; left: 80%; transform: translateX(-50%); font-size: 0.8rem;">
                                          <i class="fa fa-exchange" title="Mixed Call" style="color: white;"></i>
                                        </small>
                                        <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                      </div>
                                </th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-stopwatch fa-xl" style="color: white" title="Duration"></i></th>

                            </tr>
                        </thead>
                        <tbody class="" style="font-size:0.8rem" id="tbody">
                            @forelse($records as $record)
                                <tr class="align-middle">
                                    <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                    {{-- <td class="text-muted text-center py-3 align-middle" style="min-width: 10rem"> {{ $record->agent->organization->name ?? '-' }} </td> --}}
                                    <td class="text-muted text-center py-3 align-middle" style="min-width: 10rem">
                                        {{  $record->agent?->organization?->name ?? '-' }}
                                    </td>

                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->agent?->agent_id }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->agent?->full_name }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->arrival_time == null || $record->arrival_time == '1970-01-01 00:00:00' ? '-' : Carbon::parse($record->arrival_time)->format('Y-m-d h:i A') }}</td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->caller_id ?? '-' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->Genesys_CallUUID }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ Str::title($record->language) ?? '-' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_ender ?? '-' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_duration ? Carbon::parse($record->call_duration)->format('i:s') : '-' }} </td>



                                </tr>
                            @empty
                                <tr>
                                    <td colspan="25" class="text-muted text-center bg-white"> No Interactions found</td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between mt-3">
                    <!-- Dropdown for Number of Items per Page -->
                    <div>
                        <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                            <option value="10">10</option>
                            <option value="15" selected>15</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>Results Per Page</span>
                    </div>

                    <!-- Pagination Links -->
                    <div>
                        {{ $records->links(data: ['scrollTo' => false]) }}
                    </div>
                </div>


                    {{-- {{ $records->links(data: ['scrollTo' => false]) }} --}}


            </div>
        </div>


    {{-- custom time modal --}}
    <div class="modal fade" id="custom_time_modal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" >

                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa fa-calendar" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Custom Period</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 ">Date From <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="from" id="from" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Date From" wire:model="custom_date_from">
                            @error('custom_date_from')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 ">Date To <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="to" id="to" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Date To" wire:model="custom_date_to">
                            @error('custom_date_to')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border: none;">
                    {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeCustomDate">Close</button>
                    <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply-custom-date" wire:click="apply_custom_date">Apply</button> --}}
                    <button
                        id="closeCustomDate"
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal">
                        <span style="font-size: 17px;">Close</span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="apply_custom_date"
                        wire:loading.attr="disabled"
                        wire:target="apply_custom_date">
                        <span wire:loading.remove wire:target="apply_custom_date" id="apply-custom-date" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="apply_custom_date" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- <div class="mx-3 ps-5">
        <div class="col-6">
            <a href="{{ route('admin.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div> --}}

    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">

                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">
                        <div class="col-md-12">
                            <div class="row">


                                <div class="col-md-6 mt-2">
                                    <label for="agent name" class="mb-2 ">Agent Name <i class="fa-solid fa-id-card fa-lg" title="Agent Name" style="color: #00a34e"></i></label>
                                    <input style="padding-top: 0.46rem;padding-bottom: 0.46rem;font-size:0.85rem" type="text" name="agent-name" id="agent-name" class="form-control" placeholder="Agent Name" wire:model="filter_agentName">
                                </div>
                                {{-- <div class="col-md-6 mt-2">
                                    <label for="account" class="mb-2 ">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e;" title="Account"></i></label>
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_account ?? 'All' }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton" style="height:10rem; overflow:auto">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_account', 'All')">All</span>
                                            </li>
                                            <hr class="m-0">

                                            @forelse ($organizations as $item)
                                                <li>
                                                    <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_account', '{{ $item->name }}')">{{ $item->name }}</span>
                                                </li>
                                                @if (!$loop->last)
                                                    <hr class="m-0">
                                                @endif
                                            @empty
                                            @endforelse
                                        </div>
                                    </div>
                                </div> --}}
                                <div class="col-md-6 mt-2">
                                    <label for="account" class="mb-2">Account
                                        <i class="fa-solid fa-building fa-lg" style="color: #00a34e;" title="Account"></i>
                                    </label>
                                    <select id="account" wire:model="filter_account" class="form-select form-select-sm" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:2.5rem;">
                                        <option value="">All</option>

                                        @foreach ($organizations->sortBy('name') as $item)

                                                <option value="{{ $item->name }}">
                                                    {{   $item->name }}
                                                </option>

                                        @endforeach
                                    </select>
                                </div>


                                <div class="col-md-6 mt-2">
                                    <label for="Agent name" class="mb-2 ">User ID <i class="fa-solid fa-hashtag fa-lg" style="color:#00a34e" title="User ID"></i></label>
                                    <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="text" name="agent" id="agent" style="border: solid 1px #b6b6b6" class="form-control" placeholder="User ID" wire:model="filter_agentId">
                                </div>
                                <div class="col-md-6 mt-2">
                                    <label for="date" class="mb-2 ">Time <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e" title="Date"></i></label>

                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style " type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem;">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_time_name ?? 'All Time' }}</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(null)">All Time</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(1)">Last 24 Hours</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(7)">Last 7 Days</span>
                                            </li>
                                            <hr class="m-0">

                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(30)">Last 30 Days</span>
                                            </li>
                                            <hr class="m-0">

                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(60)">Last 60 Days</span>
                                            </li>
                                            <hr class="m-0">

                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#custom_time_modal">Custom</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mt-2">
                                    <label for="Duration" class="mb-2 ">Duration <i class="fa-solid fa-stopwatch fa-lg" style="color:#00a34e" title="Duration"></i></label>
                                    <div class="input-group mb-2">
                                        <div class="input-group-prepend">
                                            <div>
                                                <select class="form-select mb-2  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px; height:2.35rem" wire:model="filter_duration_sign">
                                                    <option class="mb-2 fw-bold" value="=">=</option>
                                                    <option class="mb-2 fw-bold" value=">">&gt;</option>
                                                    <option class="mb-2 fw-bold" value="<">
                                                        &lt; </option>
                                                </select>
                                            </div>
                                        </div>
                                        <input type="number" name="duration" id="duration" style="border: solid 1px #b6b6b6;height: 2.34rem !important;" class="form-control" placeholder="Duration in seconds" wire:model="filter_duration">
                                    </div>
                                </div>
                                <div class="col-md-6 mt-2">
                                    <label for="called id" class="mb-2 ">Caller ID
                                        <img src="{{ asset('assets/images/callout.png') }}" alt="call type" srcset="" style="width: 1.4rem; height:1.4rem">
                                    </label>
                                    {{-- <label for="caller id" class="mb-2 ">Caller ID <i class="fa-solid fa-arrow-up-wide-short fa-lg" style="color: #00a34e" title="Caller ID"></i></label> --}}
                                    <input style="padding-top: 0.46rem;padding-bottom: 0.46rem; font-size:0.85rem" type="text" name="call-id" id="caller-id" class="form-control" placeholder="Caller ID" wire:model="filter_callerId">
                                </div>
                                <div class="col-md-6">
                                        {{-- <label for="call id" class="mb-2 ">Call Ender <i class="fa-solid fa-phone-slash fa-lg" style="color: #00a34e" title="Call Ender"></i></label> --}}
                                    <label for="call id" class="mb-2 ">Call Ender
                                        <img src="{{ asset('assets/images/decline.png') }}" alt="" srcset="" style="width:1.4rem; height:1.4rem">
                                    </label>
                                    <select wire:model="filter_callEnder"
                                            class="form-select form-select-sm dropdown-toggle-style p-2"
                                            style="padding-top: 0.46rem !important; padding-bottom: 0.46rem !important; width: 100%; font-size: 0.85rem;">
                                        <option value="">All</option>
                                        <option value="Agent">Agent</option>
                                        <option value="Customer">Customer</option>
                                    </select>
                                    {{-- <input type="text" name="call-ender" id="call-ender" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Call Ender" wire:model="filter_callEnder"> --}}
                                </div>
                                <div class="col-md-6 mt-2">
                                    <label for="unique-id" class="mb-2 ">Unique ID <i class="fa-solid fa-fingerprint fa-lg" style="color: #00a34e" title="Unique ID"></i></label>
                                    <input type="text" style="border: solid 1px #b6b6b6; padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%" name="unique-id" id="unique-id" class="form-control" placeholder="Unique ID" wire:model="filter_uniqueId" min="1">
                                </div>
                                <div class="col-md-6 mt-2">
                                    <label for="call id" class="mb-2 ">Language <i class="fa-solid fa-language fa-lg" style="color: #00a34e" title="Language"></i></label>
                                    {{-- <input style="padding-top: 0.46rem;padding-bottom: 0.46rem;font-size:0.85rem" type="text" name="language" id="language" class="form-control" placeholder="Language" wire:model="filter_language"> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">
                                                @php
                                                    $allLanguages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
                                                    $isAllSelected = empty(array_diff($allLanguages, $filter_selected_languages)) && empty(array_diff($filter_selected_languages, $allLanguages));
                                                @endphp
                                                @if ($isAllSelected)
                                                    {{ 'All' }}
                                                @elseif (!$filter_selected_languages || count($filter_selected_languages) <= 1)
                                                    {{ $filter_selected_languages[0] ?? '--' }}
                                                @else
                                                    {{ 'Multi Selection' ?? '--' }}
                                                @endif
                                            </span>
                                        </button>
                                        <ul class="dropdown-menu w-100" wire:key="sdojisaodhn89" onclick="event.stopPropagation()" wire:ignore.self aria-labelledby="dropdownMenuButton1" id="dropdownMenu2323sd" style="height: 10rem; overflow:auto;">
                                            <li>
                                                <label class="dropdown-item">
                                                    <input type="checkbox" wire:key="select-all-checkbox" wire:click="selectAllLanguages()" @if (!array_diff($all_languages, $filter_selected_languages)) checked @endif> All
                                                </label>
                                            </li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="arabic-check" wire:click="filter_languages('Arabic')" @if (in_array('Arabic', $filter_selected_languages)) checked @endif> Arabic </label></li>

                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="english-check" wire:click="filter_languages('English')" @if (in_array('English', $filter_selected_languages)) checked @endif> English </label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="kurdish-check" wire:click="filter_languages('Kurdish')" @if (in_array('Kurdish', $filter_selected_languages)) checked @endif> Kurdish</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="spanish-check" wire:click="filter_languages('Spanish')" @if (in_array('Spanish', $filter_selected_languages)) checked @endif> Spanish</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="german-check" wire:click="filter_languages('German')" @if (in_array('German', $filter_selected_languages)) checked @endif> German</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="french-check" wire:click="filter_languages('French')" @if (in_array('French', $filter_selected_languages)) checked @endif> French</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="italian-check" wire:click="filter_languages('Italian')" @if (in_array('Italian', $filter_selected_languages)) checked @endif> Italian</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="russian-check" wire:click="filter_languages('Russian')" @if (in_array('Russian', $filter_selected_languages)) checked @endif> Russian</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="urdu-check" wire:click="filter_languages('Urdu')" @if (in_array('Urdu', $filter_selected_languages)) checked @endif> Urdu</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="hebrew-check" wire:click="filter_languages('Hebrew')" @if (in_array('Hebrew', $filter_selected_languages)) checked @endif> Hebrew</label></li>
                                            <hr class="m-0">
                                            <li><label class="dropdown-item"><input type="checkbox" wire:key="turkish-check" wire:click="filter_languages('Turkish')" @if (in_array('Turkish', $filter_selected_languages)) checked @endif> Turkish</label></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        wire:click="clear"
                        wire:loading.attr="disabled"
                        wire:target="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="getData"
                        wire:loading.attr="disabled"
                        wire:target="getData">
                        <span wire:loading.remove wire:target="getData" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                    </button>
                </div>
        </div>
    </div>
</div>
