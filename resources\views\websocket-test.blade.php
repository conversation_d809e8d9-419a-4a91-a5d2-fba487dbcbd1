<!DOCTYPE html>
<html>

<head>
    <title>WebSocket Test</title>
</head>

<body>
    <script>
        // Get the `typ` and `d_id` parameters passed from the controller
        const typ = @json($typ);
        const ip = @json($ip);
        console.log(ip);
        console.log(typ);
        // Define the WebSocket connection
        const socket = new WebSocket('ws://' + ip + ':4444');
        let recordingInitiatedByScript = false;

        // Function to send JSON payload to WebSocket server
        function sendMessageToServer(payload) {
            // Check if the WebSocket connection is open
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify(payload));
            } else {
                console.error('WebSocket connection is not open. Unable to send message.');
            }
        }

        // Function to check recording status
        function checkRecordingStatus() {
            const statusMessage = {
                "request-type": "GetRecordingStatus",
                "message-id": "status-check"
            };
            sendMessageToServer(statusMessage);
        }

        // Event listeners for WebSocket events
        socket.addEventListener('open', function(event) {
            console.log('WebSocket connection established');
            // Check recording status before sending StopRecording or StartRecording
            checkRecordingStatus();
        });

        socket.addEventListener('message', function(event) {
            const response = JSON.parse(event.data);

            // Handle recording status check response
            if (response['message-id'] === 'status-check') {
                if (response.status === 'ok') {
                    if (response.recording) {
                        console.log('Recording is already active.');
                        recordingInitiatedByScript = false;
                        if (typ === '0') {
                            Recording('StopRecording');
                        }
                    } else {
                        if (typ === '1') {
                            Recording('StartRecording');
                        }
                        if (typ === '0') {
                            Recording('StopRecording');
                        }
                    }
                } else {
                    console.error('Failed to check recording status:', response.error);
                }
            } else if (response['message-id'] === '3') {
                // Handle StopRecording response
                if (response.status === 'error') {
                    console.error('Error stopping recording:', response.error);
                } else {
                    console.log('Recording stopped successfully.');
                }
            } else if (response['message-id'] === 'start-recording') {
                // Handle StartRecording response
                if (response.status === 'error') {
                    console.error('Error starting recording:', response.error);
                } else {
                    console.log('Recording started successfully.');

                    if (typ === '0') {
                        Recording('StopRecording');
                    }
                }
            }

            if (response['update-type'] === 'RecordingStarted' || response['update-type'] === 'RecordingStopped') {
                const myHeaders = new Headers();
                myHeaders.append("Accept", "application/json");
                myHeaders.append("Content-Type", "application/json");

                const requestOptions = {
                    method: "POST",
                    headers: myHeaders,
                    redirect: "follow",
                    body: JSON.stringify({
                        ip: ip,
                        call_id: 0,
                        updateType: response['update-type'],
                        recordingFilename: response['recordingFilename']
                    })
                };

                fetch('https://development.extensyailog.com//api/video/fetch', requestOptions)
                    .then((response) => response.json()) // Parse the JSON response
                    .then((result) => console.log(result)) // Log the result
                    .catch((error) => console.error('Error:', error));
            }

        });

        socket.addEventListener('error', function(event) {
            console.error('WebSocket error:', event);
        });

        socket.addEventListener('close', function(event) {
            console.log('WebSocket connection closed');
        });

        // Function to start recording
        function Recording(action) {
            const startRecordingMessage = {
                "request-type": action,
                "message-id": "Stop-recording"
            };
            sendMessageToServer(startRecordingMessage);
            recordingInitiatedByScript = true;
        }
    </script>
</body>

</html>
