<?php

use App\Http\Controllers\AudioController;
use App\Http\Controllers\WebSocketController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});


Route::get('/call/fetch/{call_id}', [WebSocketController::class, 'fetchCall']);


Route::get('/video/start/{ip_data}/{call_id}', [WebSocketController::class, 'start']);
Route::get('/video/end/{ip_data}/{call_id}', [WebSocketController::class, 'end']);
Route::post('/fetch-audio', [AudioController::class, 'fetchFromRemote']);

// Route::post('/video/record/{ip}/{call_id}',[WebSocketController::class, 'start']);
//Route::post('/video/fetch', [WebSocketController::class, 'fetch']);
