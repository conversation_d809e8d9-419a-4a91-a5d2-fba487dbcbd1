<?php

namespace Database\Seeders;

use App\Models\QaFlag;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DefaultFlagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        QaFlag::create([
            'organization_id' => 1,
            'name' => 'default_system_flag',
            'start_date' => now(),
            'end_date' => '2070-04-27',
            'interactions_number' => 1,
            'per' => 1,
            'time_interval' => 'Day',
            'distribution_level' => 'Agent',
            // 'screen_capture' => $this->screen_capture_add,
            'call_type' => 'All',
            'interaction_days' => json_encode([1, 2, 3, 4, 5, 6, 7]),
            'interaction_time_from' => null,
            'interaction_time_to' => null,
            'duration_start' => null,
            'duration_end' => null,
            'duration_condition' => '=',
            'enabled' => 1,
            'active' => 1,
        ]);
    }
}
