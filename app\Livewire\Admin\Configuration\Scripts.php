<?php

namespace App\Livewire\Admin\Configuration;

use App\Models\BadWord;
use App\Models\Organization;
use App\Models\ScriptsWord;
use App\Models\UserGroup;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Scripts extends Component
{
    use WithPagination,LivewireAlert;

    public $key;
    public $type = "In Script";
    public $side_type ="Agent";
    public $script_type = null;
    public $language = null; 
    public $organization_id = null;
    public $getUserGroup = [];
    public $user_group_id;

    public $modalId;
    public $modalIdShow;

    public function getListeners()
    {
        return [
            'confirmed','modelFormReset',
            'getEncryption',
        ];
    }


    public function BadWord()
    {
        try
        {
            return ScriptsWord::query()->get();
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function modelData()
    {
        return [
            'key'             => $this->key,
            'type'            => $this->type,
            'side_type'       => $this->side_type,
            'script_type'     => $this->script_type,
            'language'        => $this -> language, // Added language
            'organization_id' => $this->organization_id,
            'user_group_id'   => $this->user_group_id,

        ];
    }

    public function rules()
    {
        return [
            'key' =>             ['required'],
            'type' =>            ['required'],
            'side_type' =>       ['required'],
            'script_type' =>     ['required'],
            'language'    =>     ['required'], // Added language validation
            'organization_id' => ['required'],
            'user_group_id' =>   ['required'],
        ];
    }

    public function update(){
        $this->validate();

        try {
            // Check if combination exists (excluding current record)
            $existingRecord = $this->checkCombinationExists($this->modalId);

            if ($existingRecord) {
                // Updated error message to include Language
                $this->addError('language', "This combination of Organization, LOB, Script Type, and Language already exists.");
                return;
            }

            ScriptsWord::find($this->modalId)->update($this->modelData());

            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
    }

    public function store(){
        $this->validate();

        try {
            // Check if combination exists
            $existingRecord = $this->checkCombinationExists();

            if ($existingRecord) {
                 // Updated error message to include Language
                $this->addError('language', "This combination of Organization, LOB, Script Type, and Language already exists.");
                return;
            }

            ScriptsWord::create($this->modelData());

            $this->alert('success', 'Successfully Created!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
    }

    /**
     * Check if the Organization, LOB, Script Type and Language combination already exists
     * @param int|null $excludeId ID to exclude from the check (for updates)
     * @return bool Whether the combination exists
     */
    private function checkCombinationExists($excludeId = null)
    {
        $query = ScriptsWord::where('organization_id', $this->organization_id)
            ->where('user_group_id', $this->user_group_id)
            ->where('language', $this->language); // Added language to the query

        // Handle null script_type correctly
        if ($this->script_type) {
            $query->where('script_type', $this->script_type);
        } else {
            $query->whereNull('script_type');
        }

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    public function closeModal()
    {
        $this->modelFormReset();
    }

    public function showModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
        $this->modalIdShow = "on";
    }

    public function showDeleteAlert($id)
    {
        $this->modalId = $id;
        $this->alert('warning', 'Are you sure you want to delete this group?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirmed()
    {
        if (ScriptsWord::find($this->modalId)->delete()) {
            $this->alert("success", "Group Deleted Successfully");
        }
    }

    public function showUpdateModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
    }

    public function modelLoadData()
    {
        $encryption = ScriptsWord::find($this->modalId);
        $this->key = $encryption->key;
        $this->type = $encryption->type;
        $this->side_type = $encryption->side_type;
        $this->script_type = $encryption->script_type;
        $this->language = $encryption->language; // Load language
        $this->organization_id = $encryption->organization_id;
        $this->getUserGroup = UserGroup::where('organization_id',$this->organization_id)->get();
        $this->user_group_id = $encryption->user_group_id;

    }

    public function modelFormReset()
    {
        $this->key = "";
//       $this->type = ""; // Consider if these should be reset to default or kept
//       $this->side_type = ""; // Consider if these should be reset to default or kept
        $this->script_type = null;
        $this->language = null; // Reset language
        $this->organization_id = "";
        $this->user_group_id = "";

        $this->modalId=null;
        $this->modalIdShow=null;

        $this->resetValidation();
    }

    public function getOrganizations()
    {
        return Organization::all();
    }

    public function storeGet()
    {

        $this->user_group_id = null;
        $this->getUserGroup = UserGroup::where('organization_id',$this->organization_id)->get();

    }

    public function changePage($data)
    {
        $this->dispatch('changePage', $data);

    }

    public function render()
    {
        return view('livewire.admin.configuration.scripts', ['transcriptionBadWords' => $this->BadWord(),'organizations'=>$this->getOrganizations()]);
    }
}