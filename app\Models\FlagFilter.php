<?php

namespace App\Models;

use App\Models\QaFlag;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FlagFilter extends Model
{
    use HasFactory;
    protected $guarded = [];

    // public function qaFlags()
    // {
    //     return $this->belongsToMany(QaFlag::class)
    //         ->withPivot('first_data', 'condition', 'second_data');
    // }
    public function qaFlags()
    {
        return $this->belongsTo(QaFlag::class);
    }
}
