@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Question Page')

{{-- Style Section --}}
@section('style')

    <style>
        .mx-costum{
            margin-right: 1.7rem !important;
            margin-left: 1.7rem !important;
            padding: 0px;
        }

        .p-10 {
            padding: 10px;
        }

        .b-radius-10 {
            border-radius: 10px;
        }


        .div-search {
            padding-left: 0px;
        }
        .fa-style {
            margin-right: 15px;
            font-size: 18px;
        }
        .filters-block {
            display: contents;
        }
        .mt-10 {
            margin-right: 12px;
        }

        .modal-button-search {
            position: relative;
            /* Higher z-index value to bring the button to the front */
        }
        .modal-content-search {
            position: absolute !important;
            top: 158px !important;
            right: 150px !important;
            background-color: #fff !important;
            box-shadow: 0 0.125rem 7.25rem #00000013 !important;
            width: 500px;
            border-radius: 10px !important;
            z-index: 1 !important;
            display: none;
        }
        .fa-magnifying-glass {
            position: absolute;
            left: 2%;
            top: 35%;
        }
        :focus-visible {
            outline: none;
        }

        .style-btn-search {
            margin: 5px;
            padding: 0px !important;
            width: 88%;
            background-color: #00a34e;
            border-color: #00a34e;
        }

        .input-search {
            width: 100%;
            border: none !important;
            vertical-align: sub;
            margin-top: 8px;
            padding: 0px;
        }

        .table{
            text-align: center;
            vertical-align: middle
        }
        .span_icon{
            vertical-align: sub;
            float: inline-end;
            cursor: pointer;
        }

        .but{
            padding-left: 20px;
            padding-right: 20px;
            border-radius: 40px;
            border-color: #e0f4ea;
            background-color: #e0f4ea;
            border: 2px;
            padding-top: 5px;
            padding-bottom: 5px;
        }



        .arrow-icon {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            pointer-events: none;
            
            width: 36px;
            height: 32px;
        }
        .new-header-style{
            text-align: end;
            text-decoration: underline;
            color: #00a34e;
            padding-top: 5px;
            cursor: pointer;
        }
        /* This css is for normalizing styles. You can skip this. */
        *, *:before, *:after {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }





    /*                              ------      CheckBox Style      ------                   */
        .checkbox-parent , .radio-parent{
            display: flex;
        }
        .form-group {

            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 3px;

        }

        .form-group input {
            padding: 0;
            height: initial;
            width: initial;
            margin-bottom: 0;
            display: none;
            cursor: pointer;
        }

        .form-group label {
            position: relative;
            cursor: pointer;
        }

        .form-group label:before {
            content: '';
            -webkit-appearance: none;
            background-color: transparent;
            border: 2px solid #e1e7e4;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
            padding: 8px;
            display: inline-block;
            position: relative;
            vertical-align: middle;
            cursor: pointer;
            margin-right: 15px;
        }

        .form-group input:checked + label:after {
            content: '';
            display: block;
            position: absolute;
            top: 2px;
            left: 7px;
            width: 6px;
            height: 14px;
            border: solid #00a34e;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .form-group-raido{
            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 3px;
        }
        .form-group-raido input {

            cursor: pointer;
            height: 17px;
            width: 18px;
            vertical-align: sub;
            border: 2px solid #cacaca;
        }
    /*                              ------      End CheckBox Style      ------                   */
    .dropdown-style{
        border: 1px solid #e3e3e3;
        padding: 10px;
        border-radius: 10px;
        cursor: pointer;
    }
    .circle {
            cursor: pointer;
            width: 40px;
            height: 34px;
            border-radius: 50%;
            background-color: #00a34e;
            display: flex;
            justify-content: center;
            align-items: center;
            padding-left: 3.5%;
            
    }
        /*
            new styles
            */
            .switche{
                    font-size: 150%;
                    cursor: pointer;
                }
                .table{
                    text-align: center;
                    vertical-align: middle
                }
                .span_icon{
                    vertical-align: sub;
                    float: inline-end;
                    cursor: pointer;
                }
                td{
                    vertical-align: middle;
                    font-size: 12px;
                }
                thead{
                    height: 50px;
                    vertical-align: middle;
                    
                }
                thead tr th{
                    vertical-align: middle;
                    background-color: #40798c !important;
                    color:white !important;
                    font-size: 15px;
                }
                .parent-sections {
                    height: 70vh;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    margin-top: 1.5%;
                    margin-bottom: 3%;
                }

                .section-one{
                    width: 100%;
                    height: 100%;
                }
                .div-table{
                    /* border: 1px solid #d0caca; */
                    border-radius: 0px;
                    width: 100%;
                    height: 100%;
                    overflow: auto;
                }
                .form-control , .form-select , .dropdown-toggle-style{
                    background-color: #eff3f4 !important;
                    border: none !important;
                    height: 40px;
                }
                label{
                    color : #40798c !important;
                    font-size: 17px;
                    
                }
                .previous{
                    margin-bottom: 5px;
                }
        /*
        end new styles
        */
        /*
        pagination styles
        */
                #searchInput {
                    height: 2.8rem !important;
                    width: 100% !important;
                    /* Increase the height for a larger input */
                    padding-left: 2.5rem !important;
                    /* Increase padding for better spacing */
                    border: none !important;
                    /* Slightly darker border */
                    border-radius: 0.5rem;
                    /* Rounded corners */
                    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
                    /* Subtle shadow */
                    transition: box-shadow 0.3s ease, border-color 0.3s ease;
                    /* Smooth transition */
                    font-size: 1.2rem;
                    /* Slightly larger text size */
                    background-position: left 0.5rem center;
                    /* Icon positioning */
                }

                /* Focus styles */
                #searchInput:focus {
                    outline: none;
                    /* Remove default outline */
                    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
                    /* Larger shadow on focus */
                    border-color: rgba(0, 0, 0, 0.3);
                    /* Slightly darker border on focus */
                }

                /* Placeholder styling */
                #searchInput::placeholder {
                    font-family: inherit;
                    /* Use inherited font style */
                    color: #01A44F;
                    /* Green placeholder text */
                    font-size: 1.2rem;
                    /* Match placeholder size with input text */
                }

                .main-buttons-container button {
                    height: 2.9rem;
                    font-size: 15px;
                }

                .main-buttons-container button:hover {
                    background-color: #018F3E !important;
                }

                /* pagination  */
                ul.pagination {
                    gap: 0.3rem;
                }

                ul.pagination li button,
                ul.pagination li span {
                    padding: 0.9rem;
                    padding-top: 0.5rem;
                    padding-bottom: 0.5rem;
                }

                ul.pagination li button:hover {
                    background-color: rgb(196, 183, 183) !important;
                }

                ul.pagination>li>button,
                ul.pagination>li>span {
                    color: black !important;
                    font-weight: 600 !important;
                    background-color: white;
                }

                .page-item span,
                .page-item button {
                    border-radius: 0.7rem !important;
                }

                .page-item.active span,
                .page-item.active button {
                    border-radius: 0.5rem !important;
                }

                .page-item.active>span {
                    background-color: #00a34e !important;
                    color: white !important;
                }

                div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
                    font-size: 0.9rem;
                }

                div.tab-pane label {
                    font-weight: 600 !important;
                }

                div.tab-pane hr {
                    display: none;
                }
        /*
        end pagination styles
        */


</style>




@endsection

{{-- Content Section --}}
@section('content')

        <div class="container-fluid">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
            <livewire:evaluation.create-question :evaluation_id="$evaluation_id" :group_id="$group_id" />
        </div>
  
@endsection

{{-- Script Section --}}

<script type="module">

document.addEventListener('DOMContentLoaded', function() {
    var searchButton = document.getElementById('search_button');
    var search = document.getElementById('search');

        searchButton.addEventListener('click', function() {
                if (search.style.display === 'none') {
                    search.style.display = 'block';
                } else {
                    search.style.display = 'none';
                }
            });
        });
        window.addEventListener('close-modal', event => {
            document.getElementById('closeModal').click()
        });
        window.addEventListener('open-modal', event => {
            document.querySelector('#open_modal').click()
        });
        window.addEventListener('close-modal-search', event => {
            if (document.getElementById('search').style.display === 'none') {
                // Do something when the element is hidden
            } else {
                document.querySelector('#search_button').click();
            }
            
        });
        window.addEventListener('load', () => {
            document.querySelector('#search').style.display = 'none';
        });
        document.addEventListener('DOMContentLoaded', function () {
            var searchButton = document.getElementById('searchButton');

            if (searchButton) {
                searchButton.addEventListener('click', function (event) {
                    event.stopPropagation(); // Stop the event propagation
                });
            }
        });
        window.addEventListener('click-on-fatal-checkbox', event => {
            console.log('44');
            document.getElementById('Fatal').click()
        });
        window.addEventListener('click-on-na-checkbox', event => {
            document.getElementById('NA').click()
        });








    </script>

