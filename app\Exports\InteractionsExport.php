<?php
namespace App\Exports;

ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 300); // 5 minutes


use App\Models\Interaction;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class InteractionsExport implements FromCollection, WithHeadings, ShouldAutoSize, WithChunkReading, ShouldQueue
{
    use Exportable;

    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_agentId;
    public $filter_duration;
    public $filter_callType;
    public $filter_calledId;
    public $filter_callerId;
    public $filter_callEnder;
    public $filter_holdDuration;
    public $filter_holdCount;
    public $filter_isAssigned;
    public $filter_evaluationScore;
    public $filter_isEvaluated;
    public $filter_qaFlaggedInteractions;
    public $filter_flagDate;
    public $filter_agentName;
    public $filter_account;
    public $filter_skillGroup;
    public $filter_callImportance;
    public $filter_pauseCount;
    public $filter_language;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct(
        $all_languages,
        $filter_time_name,
        $filter_time_days,
        $custom_date_from,
        $custom_date_to,
        $filter_callId,
        $filter_agentId,
        $filter_duration,
        $filter_callType,
        $filter_calledId,
        $filter_callerId,
        $filter_callEnder,
        $filter_holdDuration,
        $filter_holdCount,
        $filter_isAssigned,
        $filter_evaluationScore,
        $filter_isEvaluated,
        $filter_qaFlaggedInteractions,
        $filter_flagDate,
        $filter_agentName,
        $filter_account,
        $filter_skillGroup,
        $filter_callImportance,
        $filter_pauseCount,
        $filter_language,
        $filter_selected_languages,
        $filter_duration_sign,
        $filter_holdDuration_sign,
        $filter_holdCount_sign,

    ) {
        $this->all_languages = $all_languages;
        $this->filter_time_name = $filter_time_name;
        $this->filter_time_days = $filter_time_days;
        $this->custom_date_from = $custom_date_from;
        $this->custom_date_to = $custom_date_to;
        $this->filter_callId = $filter_callId;
        $this->filter_agentId = $filter_agentId;
        $this->filter_duration = $filter_duration;
        $this->filter_callType = $filter_callType;
        $this->filter_calledId = $filter_calledId;
        $this->filter_callerId = $filter_callerId;
        $this->filter_callEnder = $filter_callEnder;
        $this->filter_holdDuration = $filter_holdDuration;
        $this->filter_holdCount = $filter_holdCount;
        $this->filter_isAssigned = $filter_isAssigned;
        $this->filter_evaluationScore = $filter_evaluationScore;
        $this->filter_isEvaluated = $filter_isEvaluated;
        $this->filter_qaFlaggedInteractions = $filter_qaFlaggedInteractions;
        $this->filter_flagDate = $filter_flagDate;
        $this->filter_agentName = $filter_agentName;
        $this->filter_account = $filter_account;
        $this->filter_skillGroup = $filter_skillGroup;
        $this->filter_callImportance = $filter_callImportance;
        $this->filter_pauseCount = $filter_pauseCount;
        $this->filter_language = $filter_language;
        $this->filter_selected_languages = $filter_selected_languages;
        $this->filter_duration_sign = $filter_duration_sign;
        $this->filter_holdDuration_sign = $filter_holdDuration_sign;
        $this->filter_holdCount_sign = $filter_holdCount_sign;
    }

    public function collection()
    {
        $query = Interaction::query();

        if ($this->filter_calledId) {
            $query->where('called_id', 'like', "%$this->filter_calledId%");
        }
        if ($this->filter_callerId) {
            $query->where('caller_id', 'like', "%$this->filter_callerId%");
        }
        if ($this->filter_callId) {
            $query->where('call_id', 'like', "%$this->filter_callId%");
        }
        if ($this->filter_agentId) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('agent_id', 'like', "%$this->filter_agentId%");
            });
        }
        if ($this->filter_duration) {
            $this->filter_duration = gmdate('H:i:s', $this->filter_duration);
            $query->where('call_duration', $this->filter_duration_sign, $this->filter_duration);
        }
        if ($this->filter_callType) {
            $query->where('call_type', $this->filter_callType);
        }
        if ($this->filter_callEnder) {
            $query->where('call_ender', 'like', "%$this->filter_callEnder%");
        }
        if ($this->filter_holdCount) {
            $query->where('hold_count', $this->filter_holdCount_sign, $this->filter_holdCount);
        }
        if ($this->filter_holdDuration) {
            $this->filter_holdDuration = gmdate('H:i:s', $this->filter_holdDuration);
            $query->where('call_duration', $this->filter_holdDuration_sign, $this->filter_holdDuration);
        }
        if ($this->filter_agentName) {
            $query->whereHas('agent', function ($q2) {
                $q2->where('full_name', 'like', "%$this->filter_agentName%");
            });
        }
        if ($this->filter_evaluationScore) {
            $query->whereHas('callEvaluation', function ($q2) {
                $q2->where('quality_percentage', 'like', "%$this->filter_evaluationScore%");
            });
        }
        if ($this->filter_callType) {
            $query->where('call_type', 'like', "%$this->filter_callType%");
        }
        if ($this->filter_time_name == 'Custom') {
            $this->custom_date_from = $this->custom_date_from . ' 00:00:00';
            $this->custom_date_to = $this->custom_date_to . ' 23:59:59';
            $query->whereBetween('arrival_time', [$this->custom_date_from, $this->custom_date_to]);
        }
        if ($this->filter_time_days) {
            $startDate = now()->subDays($this->filter_time_days)->toDateString();
            $query->whereDate('arrival_time', '>=', $startDate);
        }
        if ($this->filter_isEvaluated) {
            Log::info($this->filter_isEvaluated);
            $this->filter_isEvaluated == 'Evaluated' ? $query->whereHas('callEvaluation') : $query->whereDoesntHave('callEvaluation');
        }
        if ($this->filter_account) {
            $query->whereHas('agent.organization', function ($q2) {
                $q2->where('name', 'like', "%{$this->filter_account}%");
            });
        }
        if ($this->filter_pauseCount) {
            $query->where('pause_count', $this->filter_pauseCount);
        }
        // if ($this->filter_language) {
        //     $query->where('language', 'like', "%$this->filter_language%");
        // }
        if ($this->filter_selected_languages && $this->filter_selected_languages != $this->all_languages) {
            $lowercaseLanguages = array_map('lcfirst', $this->filter_selected_languages);
            $query->whereIn('language', $lowercaseLanguages);
        }

        $n = 1;
        return $query->get()
            ->map(function ($interaction) use (&$n) {
                return [
                    // 'ID' => $n++,
                    // 'Skill Name' => $interaction->name,
                    // 'ACDID' => $interaction->acdid,
                    // 'Type' => $interaction->type,
                    // 'Language' => $interaction->language,
                    // 'Created At' => $interaction->created_at,
                    '#' => $n++,
                    'Account' => $interaction->agent->organization->name ?? '-',
                    'Call Type' => $interaction->call_type,
                    'Is Flagged' => $interaction->qaFlags()->exists() ? 'Yes' : 'No',
                    'Contains Comment(s)' => $interaction->comments()->exists() ? 'Yes' : 'No',
                    'Is Evaluated' => $interaction->callEvaluation()->exists() ? 'Yes' : 'No',
                    'Agent ID' => $interaction?->agent?->agent_id ?? '-',
                    'Agent Name' => $interaction?->agent?->full_name ?? '-',
                    'Evaluator Name' => $interaction?->callEvaluation?->evaluator?->full_name ? $interaction->callEvaluation->evaluator->full_name : '-',
                    'Called ID' => $interaction->called_id,
                    'Caller ID' => $interaction->caller_id,
                    'Call Ender' => $interaction->call_ender,
                    'Call Duration' => $interaction->call_duration,
                    'Evaluation Score' => $interaction?->callEvaluation?->quality_percentage ? $interaction->callEvaluation->quality_percentage.'%' : '-',
                    'Evaluation Form' => $interaction?->callEvaluation?->evaluation->evaluation_name ?? '-',
                    'Extension' => $interaction->agent_extension,
                    'Group' => $interaction->agent->userGroup->name ?? '-',
                    'Hold Count' => $interaction->hold_count,
                    'Hold Duration' => $interaction->hold_duration,
                    'Ring' => $interaction->ring,
                    // 'Skill Group' => $interaction->,
                    // 'Skill Group ACDID' => $interaction->,
                    'Language' => $interaction->language,
                    'Call Time' => $interaction->arrival_time,
                ];
            });
    }

    public function headings(): array
    {
        return [
            '#',
            'Account',
            'Call Type',
            'Is Flagged',
            'Contains Comment(s)',
            'Is Evaluated',
            'Agent ID',
            'Agent Name',
            'Evaluator Name',
            'Called ID',
            'Caller ID',
            'Call Ender',
            'Call Duration',
            'Evaluation Score',
            'Evaluation Form',
            'Extension',
            'Group',
            'Hold Count',
            'Hold Duration',
            'Ring',
            // 'Ring',
            // 'Skill Group',
            // 'Skill Group ACDID',
            'Language',
            'Call Time',
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
