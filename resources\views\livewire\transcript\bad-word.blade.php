<div class="col-12 events-container rounded">
    <div class="d-flex p-3 w-full details-header px-3" style='background-color:rgb(239 243 244) !important; position: sticky;top:0'>

        <select class="form-control"  wire:change="aiTypess" wire:model="aiType" style=" width:300px;margin-right: 20px">
            <option class="fw-bold" value="Keywords">Keywords</option>
            <option class="fw-bold" value="Scripts">Scripts</option>
            <option class="fw-bold" value="Classification">Classification</option>
        </select>


        @if($aiType == "Keywords")
                <select class="form-control" wire:change="seywordsType"  wire:model="keywordsType" style=" width:300px;margin-right: 20px">
                    <option class="fw-bold" value="">--Keyword Type--</option>
                    <option class="fw-bold" value="Bad Words">Bad Words</option>

                    <option class="fw-bold" value="Incorrect Phrases">Incorrect Phrases</option>
                    <option class="fw-bold" value="Unrecognized Terms">Unrecognized Terms</option>
                    <option class="fw-bold" value="Overused Words">Overused Words</option>
                    <option class="fw-bold" value="Context Issues">Context Issues</option>
                </select>
                    <div id="search-wrapper" style="margin-right: 20px">
                        <input type="text" id="search" style="width: 270px;" wire:model="searchTerm" placeholder="Search by Defined Word">
                        <i class="search-icon fas fa-search" wire:click="filteredTopics" style="cursor: pointer"></i>
                    </div>


            @elseif($aiType == "Scripts")

                <div id="search-wrapper" style="margin-right: 20px">
                    <input type="text" id="search" style="width: 270px;" wire:model="searchTerm2" placeholder="Search by Script Word">
                    <i class="search-icon fas fa-search" wire:click="filteredTopicsS" style="cursor: pointer"></i>
                </div>

            @else
            <select class="form-control" wire:change="classification"  wire:model="classificationType" style=" width:300px;margin-right: 20px">
                <option class="fw-bold" value="">--Classification Type--</option>
                <option class="fw-bold" value="NEUTRAL">NEUTRAL</option>
                <option class="fw-bold" value="POSITIVE">POSITIVE</option>
                <option class="fw-bold" value="NEGATIVE">NEGATIVE</option>

            </select>
                                                <button class="btn btn-success ms-auto" data-bs-toggle="modal" data-bs-target="#sentimentInfoModal">
    How does sentiment analysis work?
</button>
            @endif


    </div>
    @if($aiType == "Keywords")
    <div class="col-12" style="margin-top: 0px;">
        <table class="table table-striped table-bordered">
            <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 70px; z-index: 4;">
            <tr style="text-align: -webkit-center;">
                <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 20% !important;">Defined Word</th>
                <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 20% !important;">Actual Word</th>
                <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 20% !important;">From Side</th>
                <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 20% !important;">Type</th>

                <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 20% !important;">Total Detected</th>

            </tr>
            </thead>
            <tbody>
            <tr></tr>
            @forelse($bads as $kye2 => $bad)
{{--                @dd($bads)--}}
{{--                @php--}}
{{--                    // Decode the JSON string--}}
{{--                    $decodedTopics = json_decode($bad->words_detected, true);--}}
{{--                @endphp--}}
                @if ($decodedTopics)
                    @foreach ($bad as $key => $values)
                        @if($key != "type" && $key != 'side_type')
                        <tr style="text-align: -webkit-center;">
                            <td style="width: 25%; !important;font-size: 1.0rem;color:#424242 !important">
                                @if (is_array($values))
                                    {{ $values[0] }}
                                @else
                                    {{ $values }}
                                @endif
                            </td>
                            <td style="width: 20% !important;font-size: 1.0rem;color:#424242 !important">{{ $key }}</td>
                            <td style="width: 20% !important;font-size: 1.0rem;color:#424242 !important">{{$bad['side_type']}}</td>
                            <td style="width: 20% !important;font-size: 1.0rem;color:#424242 !important">{{$bad['type']}}</td>
                            <td style="width: 20% !important;font-size: 1.0rem;color:#424242 !important">
                                @if (is_array($values))
                                    {{ count($values) }}
                                @else
                                    {{ 1 }}
                                @endif
                            </td>

                        </tr>
                        @endif
                    @endforeach
                @endif
            @empty
                <tr class="text-center">
                    <td class="text-center" colspan="4">No bad words detected</td>
                </tr>
            @endforelse

            </tbody>
        </table>
    </div>
    @elseif($aiType == "Scripts")
        <div class="col-12" style="margin-top: 0px;">
            <table class="table table-striped table-bordered">
                <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 70px; z-index: 4;">
                <tr style="text-align: -webkit-center;">
                    <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 33% !important;">Script</th>
                    <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 33% !important;">Text</th>
                    <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 33% !important;">Ratio</th>
                    <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 33% !important;">Type</th>

{{--                    <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 25% !important;">Type</th>--}}
                </tr>
                </thead>
                <tbody>
                <tr></tr>
                @if($transcriptionScripts)
                @foreach ( $transcriptionScripts as $script )
                <tr>
                    <td style="text-align: center;">{{ $script?->best_script ?? '' }}</td>
                    <td style="text-align: center;">{{ $script?->input_text ?? ''}}</td>
                    <td style="text-align: center;">{{ round($script?->best_ratio,0 ) ?? ''}}%</td>
                    <td style="text-align: center;">{{ $script?->scripts_type ?? 'Greeting' }}</td>

                </tr>
                @endforeach

            @else
                <tr class="text-center">
                    <td colspan="3">No classification available</td>
                </tr>
            @endif
                </tbody>
            </table>
        </div>
    @else
        <div class="col-12" style="margin-top: 0px;">
            <table class="table table-striped table-bordered">
                <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 70px; z-index: 4;">
                    <tr style="text-align: -webkit-center;">
                        <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Classification</th>
                        <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">View</th>
                     {{--    <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Score</th> --}}

                    </tr>
                </thead>
                <tbody>
                <tr></tr>

                @php
                $types = ['positive', 'negative', 'neutral'];
                @endphp

                @foreach ($types as $type)
                    <tr class="text-center">
                        <td style="font-size: 1rem;">{{ strtoupper($type) }}</td>
                        <td>
                            <i class="fas fa-eye" style="cursor: pointer;" wire:click="showModal('{{ $type }}')"></i>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            <div class="modal modal-lg fade" id="classificationModal" tabindex="-1" wire:ignore.self>
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">

                        {{-- Modal Header --}}
                        <div class="modal-header" style="border: none;">
                            <div class="d-flex">

                                <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3"
                                     style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                    <i class="fa fa-brain" style="font-size: 30px; color: #01a44f !important;"></i>
                                </div>
                                <h4 class="modal-title d-flex justify-content-center align-items-center" id="classificationModalLabel" style="font-size: 30px;">
                                    {{ ucfirst($modalType) }} Classifications
                                </h4>
                            </div>
                            <div class="d-flex align-items-center gap-2">

                            <button type="button"
                            class="btn btn-light rounded-circle"
                            onclick="exportClassificationTableToCSV()"
                            title="Export CSV"
                            style="width: 42px; height: 42px;">
                        <i class="fas fa-file-export text-success"></i>
                    </button>

                            <button type="button" class="border-0 bg-transparent" onclick="closeClassificationModal()"
                                    aria-label="Close">
                                <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                            </button>
                        </div>

                        </div>

                        {{-- Modal Body --}}
                        <div class="modal-body">
                            <div class="section-one">
                                <div class="div-table">
                                    <table class="table table-bordered" style="margin-bottom: 0px; border-radius: 10px;">
                                        <thead class="thead">
                                            <tr>
                                                <th scope="col" style="    text-align: -webkit-center;
    background: #40798c;
    margin: 0 !important;
    font-size: 16px;
    color: white !important;">Text</th>
                                                <th style="    text-align: -webkit-center;
                                                background: #40798c;
                                                margin: 0 !important;
                                                font-size: 16px;
                                                color: white !important;" scope="col">Confidence</th>
                                            </tr>
                                        </thead>
                                        <tbody style="text-align: center;">
                                            @forelse($modalItems as $entry)
                                                <tr>
                                                    <td>{{ $entry['text'] }}</td>
                                                    <td>{{ round($entry['score'] * 100, 0) }}%</td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="2" class="text-muted">No results found for this classification.</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        {{-- Modal Footer --}}
                        <div class="modal-footer" style="border: none;">
                            <button type="button" onclick="closeClassificationModal()"
                                    class="btn btn-secondary rounded-3 px-4"
                                    style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">
                                Close
                            </button>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    @endif

<div class="modal modal-lg fade" id="sentimentInfoModal" tabindex="-1" wire:ignore.self>
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            {{-- Modal Header --}}
            <div class="modal-header" style="border: none;">
                <div class="d-flex">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3"
                         style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                        <i class="fas fa-project-diagram" style="font-size: 30px; color: #01a44f !important;"></i>
                    </div>
                    <h4 class="modal-title d-flex justify-content-center align-items-center" style="font-size: 24px;">
                        Sentiment Analysis Info
                    </h4>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="border-0 bg-transparent" onclick="closeSentimentInfoModal()" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
            </div>

            {{-- Modal Body --}}
            <div class="modal-body">
                <div class="section-one">
                    <div class="px-2">

                        <h6 class="mt-3 text-success">🧠 How It Works</h6>
                        <ul>
                            <li>We use an advanced AI model.</li>
                            <li>It reads the text and predicts if the emotion behind it is <strong>positive</strong>, <strong>neutral</strong>, or <strong>negative</strong>.</li>
                            <li>This model is specially tuned for tweets and short texts across many languages.</li>
                        </ul>

                        <h6 class="mt-4 text-success">🎯 What Affects Accuracy</h6>
                        <ul>
                            <li><strong>Clarity:</strong> Clear, direct text gives the best results.</li>
                            <li><strong>Language:</strong> While multilingual, the model performs best on English and common languages.</li>
                            <li><strong>Context:</strong> Sarcasm, irony, or slang may be misunderstood.</li>
                            <li><strong>Length:</strong> Very long or very short inputs may reduce accuracy.</li>
                        </ul>

                        <h6 class="mt-4 text-success">⚠️ Limitations</h6>
                        <ul>
                            <li>It doesn't understand deeper meaning or real intent — just the surface tone.</li>
                            <li>May misclassify jokes, sarcasm, or cultural references.</li>
                            <li>Biases in training data can affect predictions.</li>
                        </ul>

                        <h6 class="mt-4 text-success">🚀 Improving All The Time</h6>
                        <ul>
                            <li>We continuously update and test models to ensure fairer and smarter results.</li>
                            <li>Your feedback helps improve future sentiment detection!</li>
                        </ul>

                    </div>
                </div>
            </div>

            {{-- Modal Footer --}}
            <div class="modal-footer" style="border: none;">
                <button type="button" onclick="closeSentimentInfoModal()"
                        class="btn btn-secondary rounded-3 px-4 "
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">
                    Close
                </button>
            </div>

        </div>
    </div>
</div>
</div>


<script>
    function closeSentimentInfoModal() {

         $('#sentimentInfoModal').modal('hide');

        // Remove lingering backdrop and modal-open class (Bootstrap 4 fix)
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('padding-right', '');
    }
</script>

<script>
    window.addEventListener('show-classification-modal', () => {
        var modal = new bootstrap.Modal(document.getElementById('classificationModal'));
        modal.show();
    });
</script>
<script>
    let classificationModalInstance;

    window.addEventListener('show-classification-modal', () => {
        $('#classificationModal').modal('show');
    });

    function closeClassificationModal() {
        $('#classificationModal').modal('hide');

        // Remove lingering backdrop and modal-open class (Bootstrap 4 fix)
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('padding-right', '');
    }
</script>

<script>
    function exportClassificationTableToCSV() {
        const table = document.querySelector('#classificationModal table');
        let csv = [];

        for (let row of table.rows) {
            let cols = Array.from(row.cells).map(cell => `"${cell.innerText.trim()}"`);
            csv.push(cols.join(','));
        }

        // UTF-8 BOM fix (for Arabic and RTL languages)
        const BOM = "\uFEFF";  // Byte Order Mark
        const blob = new Blob([BOM + csv.join('\n')], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'classification_results.csv';
        link.click();
    }
</script>


