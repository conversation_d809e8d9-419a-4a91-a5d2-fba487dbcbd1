<div class="container-fluid mt-3 px-4">
    {{-- header row  --}}
    {{-- <div class="row mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            Organizations
                        </b>
                    </h5>
                    <h6 class="text-muted">
                        View and manage organizations
                        <i class="fa-solid fa-building fa-2xl float-end" style="color: #00a34e"></i>
                    </h6>
                </div>
            </div>
        </div>
    </div> --}}


    {{-- bottom row  --}}
    <div class="row mx-3 d-flex ps-5">
        <div class="col-12 col-md-12 col-lg-12" style="letter-spacing: 1px;" wire:key="table">
            {{-- <div class="col-12 col-md-12 col-lg-12 border bg-white shadow pt-3 rounded-3 px-3" style="letter-spacing: 1px;" wire:key="table"> --}}
            <div class="d-flex justify-content-between mb-2">

                <div class="col-2 mb-4">
                    <div class="d-flex mb-1">
               {{--          <input id="searchInput" type="text" class="form-control mb-1 text-muted p-1" placeholder="Search..." style="width: 15rem; background: url('{{ asset('assets/SVG/assets-v2/88.svg') }}') no-repeat left 0.5rem center; background-size: 1rem; padding-left: 2rem;" wire:model.live.debounce.300ms="searchOrg" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';"> --}}
                        <div class="d-flex align-items-center ps-2 rounded-2 bg-color w-100 w-lg-auto">
                            <i class="fas fa-search me-2 color"></i>
                            <input type="text"  class="rounded-2 form-control border-0 color shadow-none text-secondary" placeholder="Search..."  wire:model.live.debounce.300ms="searchOrg" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';">
                        </div>
                    </div>

                </div>

                {{-- <div class="col-6 d-flex justify-content-end">
                    <button class="btn btn-md rounded-2" style="background-color: #00a34e; color:white" data-bs-toggle="modal" data-bs-target="#add-organization"><i class="fa-solid fa-plus"></i><span> Add Organization</span></button>
                </div> --}}
                <div class="col-9 d-flex justify-content-end main-buttons-container">
                    <button class="btn btn-md rounded-3" style="background-color: #01A44F; color: white;" data-bs-toggle="modal" data-bs-target="#add-skill-group">
                        <span>Add Skills</span>
                        <img src="{{ asset('assets/SVG/assets-v2/New Icons Export/Star 4.svg') }}" alt="icon" style="width: 2.5rem; height: 1.3rem; margin-left: 0.5rem;">
                    </button>
                    <button class="btn btn-md rounded-3 ms-2" style="background-color: #01A44F; color: white;" data-bs-toggle="modal" data-bs-target="#add-group">
                        <span>Add Groups</span>
                        <img src="{{ asset('assets/SVG/assets-v2/87.svg') }}" alt="icon" style="width: 2.5rem; height: 1.3rem; margin-left: 0.5rem;">
                    </button>
                    <button class="btn btn-md rounded-3 ms-2" style="background-color: #01A44F; color: white;" data-bs-toggle="modal" data-bs-target="#add-organization">
                        <span>Add Organization</span>
                        <img src="{{ asset('assets/SVG/assets-v2/89.svg') }}" alt="icon" style="width: 2.5rem; height: 1.3rem; margin-left: 0.5rem;">
                    </button>
                </div>


            </div>

            <div class="table-responsive px-0 rounded-2" style="height: fit-content; max-height:90vh">
                <table class="table table-striped overflow-auto mb-0" id="table" style="width:100%">
                    <thead id="thead" class="text-muted" style="font-size: 0.7rem">
                        <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                            {{-- <th scope="col" class="text-center align-middle">ID</th> --}}
                            <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('name')">
                                Organization
                                @if ($sortBy !== 'name')
                                    <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                @elseif ($sortDir === 'ASC')
                                    <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                @else
                                    <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                @endif
                            </th>
                            <th scope="col" class="text-center align-middle">
                                NO. of Groups
                                {{-- @if ($sortBy !== 'role')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                    @endif --}}
                            </th>

                            <th scope="col" class="text-center align-middle text-nowrap">
                                NO. of Supervisors
                                {{-- @if ($sortBy !== 'organization_id')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                    @endif --}}
                            </th>
                            <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                                Created At
                                @if ($sortBy !== 'created_at')
                                    <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                @elseif ($sortDir === 'ASC')
                                    <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                @else
                                    <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                @endif
                            </th>
                            <th colspan="col" scope="col" class=" text-center align-middle"></th>
                        </tr>
                    </thead>
                    <tbody class="" style="font-size:0.8rem" id="tbody">

                        @forelse($organizations as $org)
                            <tr class="align-middle" data-row-id="{{ $org->id }}">
                                <td class="text-muted text-center py-3 align-middle"> {{ $org->name }} </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="cursor: pointer" wire:click="toggleOpenMenu('{{ $org->id }}')"> {{ $org->userGroups->count() }}
                                    @if ($org->userGroups->count() > 0)
                                        <span>
                                            <img src="{{ asset('assets/SVG/assets-v2/Polygon 1.svg') }}" alt="" style="width:0.5rem;" id="toggle-arrow-{{ $org->id }}">
                                        </span>
                                    @endif
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="cursor: pointer" wire:click="toggleSupervisorMenu('{{ $org->id }}')"> {{ $org->users->where('role', 2)->count() }}
                                    @if ($org->users->where('role', 2)->count() > 0)
                                        <span><img src="{{ asset('assets/SVG/assets-v2/Polygon 1.svg') }}" alt="" style="width:0.5rem;"></span>
                                    @endif
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Carbon::parse($org->created_at)->format('Y-m-d h:i A') }}</td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    @if (in_array(Auth::user()->role, [1, 3, 2]))
                                        <div class="d-flex justify-content-center">
                                            <!-- Edit Button -->
                                            <img src="{{ asset('assets/SVG/assets-v2/union-1.svg') }}" alt="" style="width: 1.5rem; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#edit-organization" wire:click="selectOrg('{{ $org->id }}')" title="Edit Org" />

                                            <!-- Custom Styled Toggle Switch -->
                                            <div class="d-flex align-items-center gap-2 justify-content-center ms-3 pt-2">
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="customSwitch{{ $org->id }}" @checked($org->is_enabled) wire:click="toggleOrg('{{ $org->id }}')" />
                                                    <label for="customSwitch{{ $org->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $org->is_enabled ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="
                                                             width: 18px;
                                                             height: 18px;
                                                             background-color: {{ $org->is_enabled ? '#ffffff' : '#FF5E60' }};
                                                             border-radius: 50%;
                                                             top: 3px;
                                                             left: {{ $org->is_enabled ? '22px' : '3px' }};
                                                             transition: left 0.3s, background-color 0.3s;">
                                                            @if ($org->is_enabled)
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path
                                                                        d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                        fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        -
                                    @endif
                                </td>

                            </tr>

                            {{-- user groups sub menu  --}}
                            @if ($openMenu == $org->id)
                                <tr id="subtable-row-{{ $org->id }}" class="subtable-row {{ $openMenu == $org->id ? 'open' : '' }}">
                                    <td colspan="100%" class="p-4" id="subTableTd">
                                        <table class="w-100 sub-table table-bordered">
                                            <tr class="text-center" style="border-top: none;">
                                                <th>Group</th>
                                                <th>NO. Of Users</th>
                                                <th>Skill</th>
                                                <th>ACDID</th>
                                                <th>Created At</th>
                                                <th></th>
                                            </tr>
                                            @foreach ($org->userGroups as $group)
                                                <tr class="text-center" style="border-bottom: none !important">
                                                    <td style="background-color: white !important;border-bottom: none !important">{{ $group->name }}</td>
                                                    <td style="background-color: white !important;border-bottom: none !important">{{ $group->users->count() + $group->supervisors->count() }}</td>
                                                    <td style="background-color: white !important;border-bottom: none !important">{{ $group->skill ?? '-' }}</td>
                                                    <td style="background-color: white !important;border-bottom: none !important">{{ $group->acdid ?? '-' }}</td>
                                                    <td style="background-color: white !important;border-bottom: none !important">{{ $group->created_at->format('Y-m-d') }}</td>
                                                    <td style="background-color: white !important;border-bottom: none !important" class="d-flex justify-content-center">
                                                        <!-- Edit Button -->
                                                        <img src="{{ asset('assets/SVG/assets-v2/union-1.svg') }}" alt="" style="width: 1.5rem; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#edit-group" wire:click="selectGroup('{{ $group->id }}')" title="Edit Group" />

                                                        <!-- Toggle Switch -->
                                                        <div class="d-flex align-items-center gap-2 justify-content-center align-items-center ms-3 pt-2">
                                                            <div class="position-relative">
                                                                <input type="checkbox" class="d-none" id="customSwitch{{ $group->id }}" @checked($group->is_enabled) wire:click="toggleUserGroup('{{ $group->id }}')" />
                                                                <label for="customSwitch{{ $group->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $group->is_enabled ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                                    <div class="switch-handle position-absolute"
                                                                        style="
                                                                            width: 18px;
                                                                            height: 18px;
                                                                            background-color: {{ $group->is_enabled ? '#ffffff' : '#FF5E60' }};
                                                                            border-radius: 50%;
                                                                            top: 3px;
                                                                            left: {{ $group->is_enabled ? '22px' : '3px' }};
                                                                            transition: left 0.3s, background-color 0.3s;">
                                                                        @if ($group->is_enabled)
                                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                                <path
                                                                                    d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                                    fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                            </svg>
                                                                        @else
                                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                            </svg>
                                                                        @endif
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        </div>

                                                    </td>
                                                </tr>
                                            @endforeach
                                        </table>

                                    </td>
                                </tr>
                            @endif

                            {{-- supervisors sub menu  --}}
                            @if ($openSupervisorsMenu == $org->id)
                                <tr>
                                    <td colspan="100%" style="padding:8rem; padding-top:1rem; padding-bottom:1rem" id="subTableTd">
                                        <table class="w-100 sub-table table-bordered">
                                            <tr class="text-center" style="border-top: none;">
                                                <th colspan="4" style="background-color: white !important;">Supervisor</th>
                                                <th style="background-color: white !important;"></th>
                                            </tr>
                                            @foreach ($correspondingSupers as $super)
                                                <tr class="text-center" style="border-bottom: none; height:0.5rem !important">
                                                    <td colspan="3" style="background-color: white !important; border-bottom:none !important">{{ $super->full_name }}</td>
                                                    <td colspan="2" style="background-color: white !important; border-bottom:none !important">
                                                        <!-- Custom Styled Toggle Switch -->
                                                        <div class="d-flex align-items-center gap-2 justify-content-center align-items-center ms-3 pt-2">
                                                            <div class="position-relative">
                                                                <input type="checkbox" class="d-none" id="customSwitch{{ $super->id }}_{{ $org->id }}" @checked($super->supervisorOrganizations->contains($org->id)) wire:change="{{ $super->supervisorOrganizations->contains($org->id) ? 'removeSupervisor(' . $super->id . ', ' . $org->id . ')' : 'addSupervisor(' . $super->id . ', ' . $org->id . ')' }}" />
                                                                <label for="customSwitch{{ $super->id }}_{{ $org->id }}" class="custom-switch-label position-relative d-inline-block"
                                                                    style="width: 44px; height: 24px; background-color: {{ $super->supervisorOrganizations->contains($org->id) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                                    <div class="switch-handle position-absolute"
                                                                        style="
                                                                                width: 18px;
                                                                                height: 18px;
                                                                                background-color: {{ $super->supervisorOrganizations->contains($org->id) ? '#ffffff' : '#FF5E60' }};
                                                                                border-radius: 50%;
                                                                                top: 3px;
                                                                                left: {{ $super->supervisorOrganizations->contains($org->id) ? '22px' : '3px' }};
                                                                                transition: left 0.3s, background-color 0.3s;">
                                                                        @if ($super->supervisorOrganizations->contains($org->id))
                                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                                <path
                                                                                    d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                                    fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                            </svg>
                                                                        @else
                                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                            </svg>
                                                                        @endif
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </table>
                                    </td>
                                </tr>
                            @endif

                        @empty
                            <tr>
                                <td colspan="18" class="text-muted text-center bg-white"> No Organizations found</td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{-- <div class=" mt-2 pe-0">
                {{ $organizations->links(data: ['scrollTo' => false]) }}
            </div> --}}

            <div class="d-flex justify-content-between mt-3">
                <!-- Dropdown for Number of Items per Page -->
                <div>
                    <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                        <option value="10">10</option>
                        <option value="15" selected>15</option>
                        <option value="30">30</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span>Results Per Page</span>
                </div>

                <!-- Pagination Links -->
                <div>
                    {{ $organizations->links(data: ['scrollTo' => false]) }}
                </div>
            </div>



            {{-- </div> --}}
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="edit-organization" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <img src="{{ asset('assets/SVG/assets-v2/89 - green.svg') }}" alt="" class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef">
                    <h5 class="modal-title" id="staticBackdropLabel">Edit Organization</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedOrgName" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                            @error('selectedOrgName')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Groups:</label>
                            @php
                                $selectedOrgUserGroups = $selectedOrgUserGroups ?? [];
                            @endphp
                            @forelse ($selectedOrgUserGroups as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="removeUserGroup_edit('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse

                            <div class="input-group relative" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                <span id="searchIcon" class="input-group-text border-0" style="width: 2.8rem; background-color:#eff3f4 !important;">
                                    <i class="fa-solid fa-magnifying-glass"></i>
                                </span>
                                <input type="text" class="form-control border-0" wire:model.live.debounce.300ms="searchUserGroupsEditModal" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0; background-color:#eff3f4 !important;" aria-describedby="searchIcon">

                                @if ($searchUserGroupsEditModal != '')

                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                        @forelse ($possibleUserGroups as $group)
                                            <li class="list-group-item" wire:click="selectUserGroup('{{ $group->id }}')" style="cursor: pointer">{{ $group->name }}</li>
                                        @empty
                                            <li class="list-group-item" style="cursor: pointer">No Groups</li>
                                        @endforelse
                                    </ul>
                                @endif
                            </div>
                        </div>

                        {{-- <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Administrators:</label>
                            @php
                                $selectedOrgAdmins = $selectedOrgAdmins ?? [];
                            @endphp
                            @forelse ($selectedOrgAdmins as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="removeAdmin({{ $index }})"></button>
                                </span>
                            @empty
                            @endforelse

                            <div class="input-group relative">
                                <span id="searchIcon" class="input-group-text">
                                    <i class="fa-solid fa-magnifying-glass"></i>
                                </span>
                                <input type="text" class="form-control" wire:model.live.debounce.300ms="searchAdminsEditModal" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                @if ($searchAdminsEditModal != '')
                                    <ul class="list-group mt-2 w-100 position-absolute top-100 dropdown-menu" style="height: 8rem; position: absolute; overflow:auto;z-index:999999">
                                        @foreach ($possibleAdmins as $admin)
                                            <li class="list-group-item" wire:click="selectAdmin('{{ $admin->id }}')" style="cursor: pointer">{{ $admin->full_name }}</li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                        </div> --}}

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Description:</label>
                            <textarea type="text" class="form-control bg-white" rows="5" id="recipient-name" wire:model.live.debounce.300ms="selectedOrgDescription" style="color:black; font-weight:600; background-color:#eff3f4 !important"></textarea>
                            @error('selectedOrgDescription')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        {{-- <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Organization:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>

                        @php
                            $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                        @endphp
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Supervisors:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="searchSupervisors" autocomplete="off">
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Agents:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName">
                            @php
                                $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                            @endphp
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div> --}}




                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="closeEdit">Close</button>
                    <button type="button" class="btn btn-primary modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="editOrg">Apply</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Add Organization Modal -->
    <div class="modal fade" id="add-organization" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <img src="{{ asset('assets/SVG/assets-v2/89 - green.svg') }}" alt="" class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef">
                    <h5 class="modal-title" id="staticBackdropLabel">Add Organization</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization Name:</label>
                            <input type="text" class="form-control " id="recipient-name" wire:model="add_orgName" placeholder="Type a name" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                            @error('add_orgName')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>


                        {{-- <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Administrators:</label>
                            @php
                                $selectedAdmins = $selectedAdmins ?? [];
                            @endphp
                            @forelse ($selectedAdmins as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="remove_admin('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selectedAdmin ?? 'Select Admin' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($allAdmins as $admin)
                                        <li><a class="dropdown-item" wire:click="select_admin('{{ $admin->id }}')">{{ $admin->full_name }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item text-muted">No Admins Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div> --}}

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Groups:</label>
                            @php
                                $selectedUserGroups_add = $selectedUserGroups_add ?? [];
                            @endphp
                            @forelse ($selectedUserGroups_add as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="removeUserGroup_add('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selectedAdmin ?? 'Select Groups' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($possibleUserGroups_add as $group)
                                        <li><a class="dropdown-item" wire:click="selectUserGroup_add('{{ $group->id }}')">{{ $group->name }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item text-muted">No Groups Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Description:</label>
                            <textarea type="text" class="form-control bg-white border-0" id="recipient-name" wire:model.live.debounce.300ms="add_description" rows="6" style="color:black; font-weight:600; background-color:#eff3f4 !important"></textarea>
                            @error('add_description')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>


                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="closeAdd">Close</button>
                    <button type="button" class="btn btn-primary modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="addOrg">Apply</button>
                </div>
            </div>
        </div>
    </div>


    {{-- Add SKill modal  --}}
    <div class="modal fade" id="add-skill-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <img src="{{ asset('assets/SVG/assets-v2/New Icons Export/Star 4 - green.svg') }}" alt="" class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef">
                    <h5 class="modal-title" id="staticBackdropLabel">Add Skill Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Type:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $added_type ?? '--' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" wire:click="addType('Inbound')">Inbound</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" wire:click="addType('Outbound')">Outbound</a></li>
                                </ul>
                                @error('added_type')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Language:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $added_lang ?? '--' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="addlang('Arabic')">Arabic</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="addlang('English')">English</a></li>
                                </ul>
                                @error('added_lang')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="added_name" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                            @error('added_name')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">ACDID:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="added_acdid" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                            @error('added_acdid')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Description:</label>
                            <textarea type="text" class="form-control bg-white" id="recipient-name" wire:model="added_desc" style="color:black; font-weight:600; background-color:#eff3f4 !important"></textarea>
                            @error('added_desc')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="close-skill">Close</button>
                    <button type="button" class="btn btn-success modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="addGroup">Apply</button>
                </div>
            </div>
        </div>
    </div>


    {{-- Add User Group Modal  --}}
    <div class="modal fade" id="add-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <img src="{{ asset('assets/SVG/assets-v2/87 - green.svg') }}" alt="" class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef">
                    <h5 class="modal-title" id="staticBackdropLabel">Add User Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>
                @php
                    $allOrgs = \App\Models\Organization::all()->sortBy('name');
                @endphp
                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Group Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="added_group_name" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                            @error('added_group_name')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $added_org ?? '--' }}
                                </button>
                                @error('added_org')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($allOrgs as $org)
                                        <li><a class="dropdown-item" wire:click="add_org('{{ $org->id }}')">{{ $org->name }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item">No Organizations Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Supervisors:</label>
                            @php
                                $selected_supervisors = $selected_supervisors ?? [];
                            @endphp
                            @forelse ($selected_supervisors as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="remove_supervisor('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selected_supervisor ?? 'Select Supervisors' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($possibleSupervisors as $index => $supervisor)
                                        <li><a class="dropdown-item" wire:click="select_supervisor_addModal('{{ $index }}')">{{ $supervisor }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item text-muted">No Supervisors Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Agents:</label>
                            @php
                                $selected_agents = $selected_agents ?? [];
                            @endphp
                            @forelse ($selected_agents as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="remove_agent('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selected_agent ?? 'Select Agents' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($possibleAgents as $index => $agent)
                                        <li><a class="dropdown-item" wire:click="select_agent_addModal('{{ $index }}')">{{ $agent }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item text-muted">No Agents Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>




                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="close">Close</button>
                    <button type="button" class="btn btn-success modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="submitAddGroup">Apply</button>
                </div>
            </div>
        </div>
    </div>

    {{-- edit user group modal  --}}
    <div class="modal fade" id="edit-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <img src="{{ asset('assets/SVG/assets-v2/87 - green.svg') }}" alt="" class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef">
                    <h5 class="modal-title" id="staticBackdropLabel">Edit User Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Group Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedGroupName" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                            @error('selectedGroupName')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selectedGroupOrg ?? '--' }}
                                </button>
                                @error('selectedGroupOrg')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($allOrgs as $org)
                                        <li><a class="dropdown-item" wire:click="changeOrg({{ $org->id }})">{{ $org->name }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item">No Organizations Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Supervisors:</label>
                            @php
                                $selectedGroupSupervisors = $selectedGroupSupervisors ?? [];
                            @endphp
                            @forelse ($selectedGroupSupervisors as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="remove_supervisor_edit('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selected_supervisor ?? 'Select Supervisors' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($possibleSupervisors as $index => $supervisor)
                                        <li><a class="dropdown-item" wire:click="select_supervisor_editModal('{{ $index }}')">{{ $supervisor }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item text-muted">No Supervisors Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Agents:</label>
                            @php
                                $selectedGroupAgents = $selectedGroupAgents ?? [];
                            @endphp
                            @forelse ($selectedGroupAgents as $index => $item)
                                <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                    {{ $item }}
                                    <button class="btn-close btn-close-white" wire:click="remove_agent_edit('{{ $index }}')"></button>
                                </span>
                            @empty
                            @endforelse
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selected_agent ?? 'Select Agents' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 10rem; position: absolute; overflow:auto;z-index:999999">
                                    @forelse ($possibleAgents as $index => $agent)
                                        <li><a class="dropdown-item" wire:click="select_agent_editModal('{{ $index }}')">{{ $agent }}</a></li>
                                        @if (!$loop->last)
                                            <hr class="m-0">
                                        @endif
                                    @empty
                                        <li><a class="dropdown-item text-muted">No Agents Found</a></li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="close">Close</button>
                    <button type="button" class="btn btn-success modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="submitEditGroup">Apply</button>
                </div>
            </div>
        </div>
    </div>


    <script>

    </script>


</div>
