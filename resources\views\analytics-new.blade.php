@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Analytics')

@section('style')
    <style>
        /* progress bar  */
        .text-white {
            color: white !important;
        }

        .text-black {
            color: black;
        }

        .ml-6vw {
            margin-left: 6vw;
        }

        .ml-1-5rem {
            margin-left: 1.5rem;
        }

        /* ********************** */

        /* Progress bar container */
        /* Progress bar container */
        .progress-bar {
            width: 100%;
            /* Full width */
            height: 25px;
            /* Set the height of the bar */
            background-color: #e0e0e0;
            /* Light gray background */
            border-radius: 15px;
            /* Rounded edges */
            overflow: hidden;
            /* Clip the progress fill to the container */
            position: relative;
            /* For positioning the fill and text */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            /* Optional shadow for better appearance */
        }

        .section-one,
        .card-2,
        .card {
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.15) !important;
            border-radius: 0.25rem !important;
        }

        /* Progress bar fill */
        .progress-fill {
            height: 100%;
            /* Fill entire height of the container */
            background-color: #01A44F;
            /* Green color for progress */
            border-radius: 15px 0 0 15px;
            /* Round the left edge of the fill */
            /* color: white; */
            /* White text */
            font-weight: bold;
            /* Bold text for better readability */
            line-height: 25px;
            /* Vertically center the text */
            text-align: center;
            /* Horizontally center the text */
            transition: width 0.3s ease-in-out;
            /* Smooth animation for width changes */
            font-size: 14px;
            /* Adjust font size as needed */
        }


        .header {}

        .form-control,
        .form-select,
        .dropdown-toggle-style {
            background-color: #eff3f4 !important;
            color: #40798c !important;
            border: none !important;
            height: 40px;
        }

        .paginationTable {
            color: #40798c;
        }

        .dropdown-toggle-style {
            width: 100%;
        }

        .dropdown-toggle-style:hover {
            color: #40798c !important;
        }

        .color {
            color: #40798c !important;
        }

        .dropdown-item:hover {
            color: white !important;
            background-color: #58798b !important;
        }

        .bg-color {
            background-color: #eff3f4 !important;
        }

        small {
            color: #40798c !important;
        }

        .bg-purple {
            background-color: #eed7ff;
        }

        .bg-purple strong {
            color: #c577ff;
        }

        .table-borderless td {
            border: none;
        }

        .main-row {

            border-radius: 0.5rem;
        }

        .details-row {

            overflow-y: auto;
        }

        /* Target odd rows and apply red background color */
        .table-striped tbody tr:nth-child(odd) td {
            background-color: #eff3f4 !important;
            /* Red for odd rows */
            color: black !important;
            /* Optional: Text color for contrast */
        }

        .table-striped tbody tr:nth-child(even) td {
            background-color: white !important;
            /* Yellow for even rows */
            color: black !important;
            /* Optional: Text color for contrast */
        }

        .table-striped>tbody>tr:nth-of-type(odd)>* {
            --bs-table-bg-type: unset !important;
            background-color: transparent !important;
        }

        /* Override even row styles to remove background */
        .table-striped>tbody>tr:nth-of-type(even)>* {
            --bs-table-bg-type: unset !important;
            background-color: transparent !important;
        }

        .main-row {
            border-radius: 0.5rem;
            /* Optional if you want a universal radius */
            overflow: hidden;
            /* Ensures corners are rounded */
        }

        .rounded-start {
            border-top-left-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
        }

        .rounded-end {
            border-top-right-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
        }

        .filter {
            /* padding: 0px !important;
                                                                                                                                                                margin: 0px !important; */
        }

        .table-groups {
            /* background-color: #eff3f4 !important; */
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 0 !important;
            border: none !important;
            max-height: 65px;
        }

        .parent-cards {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .card {
            border: none !important;
            padding: 10px 20px;
        }

        .card-2 {
            border: none !important;
            padding-left: 40px !important;
            padding-right: 40px !important;
            padding: 2%;

        }

        .cards-section-div {
            overflow-y: scroll;

        }

        .custom-card-col {
            width: 33.7% !important;
            cursor: pointer;
            /* margin-right: 4%; */
        }

        .custom-card-col.hidden {
            visibility: hidden;
            position: absolute;
        }

        @media (min-width: 1200px) {
            .custom-col {
                width: 19% !important;
            }

            .custom-col-n {
                width: 39.5% !important;
            }

            .custom-col-n-chart {
                width: 39.5% !important;
            }

        }

        .bg-color1 {
            background-color: #faf3ff !important;
        }

        .bg-color2 {
            background-color: #fffbf3 !important;
        }

        .bg-color3 {
            background-color: #f3f8ff !important;
        }

        .border-buttom {
            border-bottom: 3px solid #e5e5ef;
            padding-bottom: 10px !important;
        }

        .card {
            border: 1px solid #f2f2f2 !important;
        }

        .group {
            cursor: pointer;
        }

        .circle-x-y {
            background: #ffd7d8;
            position: absolute;
            border-radius: 50%;
            top: -10px;
            right: -10px;
            cursor: pointer;
            width: 23px !important;
        }

        .motion-cards {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .motion-cards:hover {
            background: #f8f8f8 !important;
            transform: scale(1.05) !important;
            /* Make the card slightly larger */
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3);
            /* Add a larger shadow */
        }
    </style>
    <style>
        /* Slider container */
        .slider {
            position: relative;
            overflow: hidden;
            width: 100%;
            height: 300px;
        }

        /* Wrapper for slides */
        .slider-wrapper {
            display: flex;
            scroll-snap-type: x mandatory;
            overflow-x: auto;
            scroll-behavior: smooth;
            width: 100%;
        }

        /* Individual slides */
        .slider-slide {
            flex: 0 0 100%;
            scroll-snap-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f0f0f0;
            margin-right: 10px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        /* Buttons for navigation */
        .slider-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            border: none;
            padding: 10px;
            cursor: pointer;
            z-index: 10;
            border-radius: 50%;
        }

        .slider-button.prev {
            left: 10px;
        }

        .slider-button.next {
            right: 10px;
        }

        /* Pagination */
        .slider-pagination {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .slider-pagination span {
            width: 10px;
            height: 10px;
            background: #ccc;
            border-radius: 50%;
            cursor: pointer;
        }

        .slider-pagination span.active {
            background: #000;
        }

        .zoom-out {
            /* -webkit-transform: scale(0.9);
                                                                                                        /* -moz-transform: scale(0.9);
                                                                                                        -ms-transform: scale(0.9);
                                                                                                        -o-transform: scale(0.9);
                                                                                                        transform: scale(0.9); */
            transform-origin: top left;
            */
            /* zoom: 0.87; */
            zoom: 0.76 !important;

        }

        .swiper-slide {
        margin-right: 4%;
        width: 33.3% !important;
    }


    </style>
@endsection

@section('content')

    <div class="container-fluid">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <livewire:analytics-new>
    </div>

@endsection
<script type="module">
    document.addEventListener("DOMContentLoaded", function() {
        Livewire.dispatch('callBackendMethod');
    });


    // overlay
    document.addEventListener("click_all", function() {
        // Create the overlay element
        const overlay = document.createElement("div");
        overlay.style.position = "fixed";
        overlay.style.top = "0";
        overlay.style.left = "0";
        overlay.style.width = "100%";
        overlay.style.height = "100%";
        overlay.style.backgroundColor = "rgba(255, 255, 255, 0.8)"; // Semi-transparent white
        overlay.style.zIndex = "9999"; // Ensure it appears on top of everything
        overlay.style.pointerEvents = "none"; // Prevent interaction while overlay is visible
        document.body.appendChild(overlay);

        // Trigger the click event
        const allAccountsButton = document.getElementById('all_accounts');
        if (allAccountsButton) {
            allAccountsButton.click();
        }

        // Remove the overlay after a delay (to ensure the event is processed)
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 1000); // Adjust delay as needed (e.g., 500ms)
    });


    const sliderWrapper = document.querySelector('.slider-wrapper');
    const slides = document.querySelectorAll('.slider-slide');
    const pagination = document.querySelector('.slider-pagination');

    let currentIndex = 0;

    // Create pagination indicators
    slides.forEach((_, index) => {
        const dot = document.createElement('span');
        dot.classList.add(index === 0 ? 'active' : '');
        dot.addEventListener('click', () => goToSlide(index));
        pagination.appendChild(dot);
    });

    const updatePagination = () => {
        document.querySelectorAll('.slider-pagination span').forEach((dot, index) => {
            dot.classList.toggle('active', index === currentIndex);
        });
    };

    const goToSlide = (index) => {
        currentIndex = index;
        sliderWrapper.scrollTo({
            left: sliderWrapper.offsetWidth * currentIndex,
            behavior: 'smooth',
        });
        updatePagination();
    };

    const prevSlide = () => {
        currentIndex = (currentIndex - 1 + slides.length) % slides.length;
        goToSlide(currentIndex);
    };

    const nextSlide = () => {
        currentIndex = (currentIndex + 1) % slides.length;
        goToSlide(currentIndex);
    };
</script>


<script type="module">
    window.addEventListener('close-modal', event => {
        document.getElementById('closeCustomDate').click()
    });
    window.addEventListener('style-row', event => {
        let groupId = event.detail[0].groupId; // Access groupId from the event

        // Get all rows with the 'main-row' class
        let rows = document.querySelectorAll('.main-row');

        // Remove the border from all rows
        rows.forEach(row => {
            row.style.border = ''; // Reset border
        });

        // Apply the border style to the specific row
        let rowElement = document.getElementById('row' + groupId);
        if (rowElement) {
            rowElement.style.border = '1px solid #bdbdbd'; // Apply the border style
        }
    });


    const table = document.querySelector('.table-responsive');
    table.addEventListener('wheel', function(event) {
        event.preventDefault();
        table.scrollBy({
            top: event.deltaY > 0 ? 50 : -50, // Adjust the value (e.g., 50) to slow down the scroll
            behavior: 'smooth'
        });
    });


    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.querySelector('.toggle-details');
        const tableResponsive = document.querySelector('.table-responsive');
        let isExpanded = false;

        toggleButton.addEventListener('click', function() {
            isExpanded = !isExpanded;

            // Adjust the max-height of the table-responsive class
            if (isExpanded) {
                tableResponsive.style.maxHeight = '250px';
            } else {
                tableResponsive.style.maxHeight = '100px';
            }

            // Update the button text and icon
            toggleButton.innerHTML = isExpanded ?
                'Minimize <i class="ms-2 fa-solid fa-caret-up"></i>' :
                'Expand <i class="ms-2 fa-solid fa-caret-down"></i>';
        });


    });
</script>


<script type="module">
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('doughnutChart').getContext('2d');

        // Initialize the chart with default data
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Calls', 'Calls'], // Labels for hover titles
                datasets: [{
                    data: [0, 1], // Default values
                    backgroundColor: ['#01A44F', '#EFF3F4'],
                    hoverOffset: 0,
                    borderWidth: 5,
                    borderRadius: 3,
                }]
            },
            options: {
                responsive: true,
                layout: {
                    padding: {
                        top: 0,
                        bottom: 0
                    }
                },
                cutout: '78%',
                // cutout: '76%',
                rotation: -90,
                circumference: 180,
                plugins: {
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function(tooltipItem) {
                                const dataset = tooltipItem.dataset;
                                const dataIndex = tooltipItem.dataIndex;
                                const value = dataset.data[dataIndex];
                                return `${chart.data.labels[dataIndex]}: ${value}%`;
                            }
                        }
                    },
                    legend: {
                        display: false
                    }
                },
                interaction: {
                    mode: 'nearest',
                    intersect: true
                }
            },
            plugins: [{
                id: 'customLabel',
                beforeDraw(chart) {
                    const {
                        ctx,
                        chartArea
                    } = chart;
                    const centerX = (chartArea.left + chartArea.right) / 2;
                    const centerY = chartArea.bottom;

                    const inboundPercentage = chart.data.datasets[0].data[0];
                    const chartTitle = chart.data.labels ? chart.data.labels[0] :
                        'Calls'; // Use labels for the title

                    // Draw percentage text
                    ctx.save();
                    ctx.font = 'bold 40px Poppins';
                    ctx.fillStyle = '#1F363D';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${inboundPercentage}%`, centerX, centerY - 80);

                    // Draw label
                    ctx.font = '12px Poppins';
                    ctx.fillStyle = '#40798C';
                    ctx.fillText(chartTitle, centerX, centerY - 55);
                    ctx.restore();
                }
            }]
        });

        // Listen for Livewire event to update chart
        window.addEventListener('update-chart', event => {
            const {
                chartTitle,
                percentage_inbound,
                percentage_outbound
            } = event.detail[0];

            // Update chart data
            chart.data.datasets[0].data = [percentage_inbound, 100 - percentage_inbound];
            chart.data.labels = ['Inbound Calls',
                'Outbound Calls'
            ]; // Update labels dynamically if needed

            // Redraw chart
            chart.update();
        });
    });
</script>
<script>
    window.addEventListener('closeModal', () => {
        document.querySelector('#close').click();
        document.querySelector('#closeEdit').click();
    });
</script>


{{-- <script type="module">
    document.addEventListener('DOMContentLoaded', () => {
        const borderColors = [
            'rgba(75, 192, 192, 1)', // 1
            'rgba(255, 99, 132, 1)', // 2
            'rgba(54, 162, 235, 1)', // 3
            'rgba(153, 102, 255, 1)', // 4
            'rgba(255, 159, 64, 1)', // 5
            'rgba(201, 203, 207, 1)', // 6
            'rgba(0, 204, 102, 1)', // 7
            'rgba(255, 206, 86, 1)', // 8
            'rgba(102, 102, 255, 1)', // 9
            'rgba(255, 51, 153, 1)', // 10
            'rgba(54, 162, 235, 1)', // 11
            'rgba(54, 162, 235, 1)', // 11
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
        ];

        const easingTypes = [
            'easeInSine', // 1
            'easeOutBounce', // 2
            'easeInQuad', // 3
            'easeOutExpo', // 4
            'easeInOutCubic', // 5
            'easeInElastic', // 6
            'easeOutElastic', // 7
            'easeInOutQuart', // 8
            'easeInBounce', // 9
            'easeOutCirc', // 10
            'easeInQuad', // 11
            'easeInQuad', // 11
            'easeOutExpo',
            'easeInQuad', // 3
            'easeOutExpo', // 4
            'easeInOutCubic', // 5
        ];

        const trendUpDataset = [0, 5, 15, 10, 20, 30, 15, 30, 10, 15, 0];
        const trendDownDataset = [30, 20, 25, 28, 18, 20, 25, 15, 8, 5, 2];

        function createChart(id, borderColor, easingType, trend = trendDownDataset) {
            const canvas = document.getElementById(id);
            if (!canvas) {
                console.error(`Canvas with id "${id}" not found`);
                return;
            }
            const ctx = canvas.getContext('2d');
            const data = {
                labels: ["", "", "", "", "", "", "", "", "", ""], // Dummy labels (hidden)
                datasets: [{
                    // data: [30, 20, 25, 28, 18, 20, 25, 15, 8, 5, 2],
                    data: trend,
                    borderColor: borderColor,
                    borderWidth: 3, // Line thickness
                    tension: 0.0, // Smooth curve
                    pointRadius: 0 // No points
                }]
            };

            const config = {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            display: false // No legend
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: easingType, // Apply specific easing type
                    },
                    scales: {
                        x: {
                            display: false // Hide x-axis
                        },
                        y: {
                            display: false // Hide y-axis
                        }
                    }
                }
            };

            new Chart(ctx, config); // Create chart for each canvas
        }


        // TRENDS
        let chart1Trend;
        let chart2Trend;
        let chart3Trend;
        let chart4Trend;
        let chart5Trend;
        let chart6Trend;
        let chart7Trend;
        let chart8Trend;
        let chart9Trend;
        let chart10Trend;
        let chart11Trend;
        let chart12Trend;

        for (let i = 1; i <= 12; i++) {
            window.addEventListener(`chart${i}Trend`, (e) => {
                const chartTrendVariable = `chart${i}Trend`;

                if (e.detail[0] === true) {
                    window[chartTrendVariable] = trendUpDataset;
                } else {
                    window[chartTrendVariable] = trendDownDataset;
                }
            });
        }


        // Call createChart function with unique IDs, border colors, and easing types
        createChart('chart1', borderColors[0], easingTypes[0], chart1Trend);
        createChart('chart2', borderColors[1], easingTypes[1], chart2Trend);
        createChart('chart3', borderColors[2], easingTypes[2], chart3Trend);
        createChart('chart4', borderColors[3], easingTypes[3], chart4Trend);
        createChart('chart5', borderColors[4], easingTypes[4], chart5Trend);
        createChart('chart6', borderColors[5], easingTypes[5], chart6Trend);
        createChart('chart7', borderColors[6], easingTypes[6], chart7Trend);
        createChart('chart8', borderColors[7], easingTypes[7], chart8Trend);
        createChart('chart9', borderColors[8], easingTypes[8], chart9Trend);
        createChart('chart10', borderColors[9], easingTypes[9], chart10Trend);
        createChart('chart11', borderColors[10], easingTypes[10], chart11Trend);
        createChart('chart12', borderColors[11], easingTypes[11], chart12Trend);

        createChart('chart13', borderColors[12], easingTypes[12]);
        createChart('chart14', borderColors[13], easingTypes[13]);
        createChart('chart15', borderColors[14], easingTypes[14]);

    });
</script> --}}

<script type="module">
    document.addEventListener('DOMContentLoaded', () => {
        const borderColors = [
            'rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)',
            'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)', 'rgba(201, 203, 207, 1)',
            'rgba(0, 204, 102, 1)', 'rgba(255, 206, 86, 1)', 'rgba(102, 102, 255, 1)',
            'rgba(255, 51, 153, 1)', 'rgba(54, 162, 235, 1)', 'rgba(54, 162, 235, 1)'
        ];

        const easingTypes = [
            'easeInSine', 'easeOutBounce', 'easeInQuad', 'easeOutExpo', 'easeInOutCubic',
            'easeInElastic', 'easeOutElastic', 'easeInOutQuart', 'easeInBounce', 'easeOutCirc',
            'easeInQuad', 'easeInQuad'
        ];

        const trendUpDataset = [0, 5, 15, 10, 20, 30, 15, 30, 10, 15, 0];
        const trendDownDataset = [30, 20, 25, 28, 18, 20, 25, 15, 8, 5, 2];

        // Store trends and chart instances
        const trends = {};
        const chartInstances = {};

        // Function to create or update charts
        function createOrUpdateChart(id, borderColor, easingType) {
            const canvas = document.getElementById(id);
            if (!canvas) {
                console.error(`Canvas with id "${id}" not found`);
                return;
            }

            const ctx = canvas.getContext('2d');
            const trend = trends[id] || trendDownDataset; // Default to trendDownDataset

            if (chartInstances[id]) {
                // Update existing chart
                chartInstances[id].data.datasets[0].data = trend;
                chartInstances[id].update();
            } else {
                // Create a new chart
                const data = {
                    labels: ["", "", "", "", "", "", "", "", "", ""], // Dummy labels
                    datasets: [{
                        data: trend,
                        borderColor: borderColor,
                        borderWidth: 3,
                        tension: 0.0,
                        pointRadius: 0,
                    }]
                };

                const config = {
                    type: 'line',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                display: false
                            },
                        },
                        animation: {
                            duration: 1000,
                            easing: easingType,
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false
                            },
                        },
                    },
                };

                chartInstances[id] = new Chart(ctx, config); // Store the chart instance
            }
        }

        // Add event listeners to dynamically update trends
        for (let i = 1; i <= 12; i++) {
            window.addEventListener(`chart${i}Trend`, (e) => {
                trends[`chart${i}`] = e.detail[0] ? trendUpDataset : trendDownDataset;
                createOrUpdateChart(`chart${i}`, borderColors[i - 1], easingTypes[i - 1]);
            });
        }

        // Create initial charts
        for (let i = 1; i <= 12; i++) {
            if (document.getElementById(`chart${i}`)) {
                createOrUpdateChart(`chart${i}`, borderColors[i - 1], easingTypes[i - 1]);
            }
        }
    });
</script>





<script type="module">
    window.addEventListener('clickFirstCard', () => {
        const firstCardElement = document.getElementById("firstCard");
        if (firstCardElement) {
            firstCardElement.click();
        }
    });


    document.addEventListener("DOMContentLoaded", function() {
        // Create the overlay element
        const overlay = document.createElement("div");
        overlay.style.position = "fixed";
        overlay.style.top = "0";
        overlay.style.left = "0";
        overlay.style.width = "100%";
        overlay.style.height = "100%";
        overlay.style.backgroundColor = "rgba(255, 255, 255, 0.8)"; // Semi-transparent white
        overlay.style.zIndex = "9999"; // Ensure it appears on top of everything
        overlay.style.pointerEvents = "none"; // Prevent blocking user interactions
        document.body.appendChild(overlay);

        // Your existing logic
        const firstCardElement = document.getElementById("firstCard");
        if (firstCardElement) {
            firstCardElement.click();
        }


        // Wait half a second before removing the overlay
        setTimeout(function() {

            // remove the white overlay of loading
            document.body.removeChild(overlay);

        }, 1000);


        setTimeout(function() {

            // this is to make counter for the supervisor flags
            window.addEventListener("updateSupervisorFlags", (event) => {
                const counterElement = document.getElementById("counter2");

                if (counterElement) {
                    const target = parseInt(event.detail, 10); // Access the dispatched value
                    const duration = 2000; // Duration of the animation in milliseconds
                    const interval = 20; // Update interval in milliseconds

                    let current = 0;
                    const step = target / (duration / interval);

                    const updateCounter = () => {
                        current += step;

                        if (current >= target) {
                            counterElement.textContent = target.toLocaleString();
                            return;
                        }

                        // Change color based on the current value
                        let color = current <= 10 ? 'green' : (current > 10 && current <= 20) ? 'orange' : 'red';
                        counterElement.style.color = color;

                        counterElement.textContent = Math.floor(current).toLocaleString();
                        setTimeout(updateCounter, interval);
                    };

                    updateCounter();
                }
            });

        }, 2000);
    });





    document.addEventListener('DOMContentLoaded', function() {
        // Default chart settings
        const data = {
            labels: [], // To be filled with dates
            datasets: [{
                cubicInterpolationMode: 'monotone',
                data: [], // To be filled with daily average durations in minutes
                label: 'Avg. Duration (minutes)', // Default chart label
                fill: true,
                tension: 0.4,
                pointHoverRadius: 6,
                pointHoverBackgroundColor: '#1f363d',
                pointRadius: 0,
                pointBackgroundColor: 'transparent',
                borderColor: '#01a44f', // Green color for the line
                backgroundColor: function(context) {
                    const bgColor = ['rgb(179, 228, 202)', 'rgba(181, 228, 203, 0)'];
                    if (!context.chart.chartArea) {
                        return;
                    }
                    const {
                        ctx,
                        chartArea: {
                            top,
                            bottom
                        }
                    } = context.chart;
                    const gradientBg = ctx.createLinearGradient(0, top, 0, bottom);
                    gradientBg.addColorStop(0, bgColor[0]);
                    gradientBg.addColorStop(0.85, bgColor[1]);
                    return gradientBg;
                }
            }]
        };

        // Handle no data case
        if (data.labels.length === 0 || data.datasets[0].data.length === 0) {
            data.labels = ['Point 1', 'Point 2']; // Default x-axis labels
            data.datasets[0].data = [0, 0]; // Default y-axis values (zeros)
            data.datasets[0].label = 'No Filters Chosen'; // Default label for the chart
            data.datasets[0].borderColor = '#01a44f'; // Keep the green line style
            data.datasets[0].backgroundColor = function(context) {
                const bgColor = ['rgb(179, 228, 202)', 'rgba(181, 228, 203, 0)'];
                if (!context.chart.chartArea) {
                    return;
                }
                const {
                    ctx,
                    chartArea: {
                        top,
                        bottom
                    }
                } = context.chart;
                const gradientBg = ctx.createLinearGradient(0, top, 0, bottom);
                gradientBg.addColorStop(0, bgColor[0]);
                gradientBg.addColorStop(0.85, bgColor[1]);
                return gradientBg;
            };
        }

        const ctx = document.getElementById('chartCanvas').getContext('2d');
        const chartInstance = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: true,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: {
                    legend: {
                        display: false // Disable the legend visibility
                    }
                },
                scales: {
                    y: {
                        display: true, // Keep the y-axis visible
                        grid: {
                            drawOnChartArea: false, // Removes the horizontal lines inside the chart
                            drawBorder: true // Keeps the axis border line
                        },
                        min: 0, // Set the minimum value for the y-axis to 0
                        ticks: {
                            stepSize: 1, // Set the interval between tick marks to 1
                        }
                    },
                    x: {
                        display: true, // Keep the x-axis visible
                        grid: {
                            drawOnChartArea: false, // Removes the vertical lines inside the chart
                            drawBorder: true // Keeps the axis border line
                        }
                    }
                }
            }
        });





        // Listen for the update-chart2 event
        window.addEventListener('update-chart2', function(event) {
            const {
                chartTitle,
                dates,
                avgDurations,
                isDuration = false
            } = event.detail[0];
            console.log(avgDurations);

            // Update chart title and data
            chartInstance.data.labels = dates; // Set dates as x-axis labels
            chartInstance.data.datasets[0].data = avgDurations; // Set the avg durations for y-axis
            chartInstance.data.datasets[0].label = chartTitle; // Set the chart title

            // Update the chart with new data
            // chartInstance.options.scales.y.ticks.callback = function(value) {
            //     return isDuration ? value.toFixed(2) : value.toFixed(
            //         0); // Show durations with 2 decimals or counts as integers
            // };
            chartInstance.update();
        });
    });
</script>
<script>
    /*     let activelist = 3;
    let inactivelist = 0;

    function showHide(currentindex = 0) {
        let counter = 0;
        let all = document.querySelectorAll('.custom-card-col');
        console.log(all.length);

        all.forEach((div) => {
            if ((activelist)  < all.length - inactivelist || currentindex == counter) {
                console.log(div.classList.add('hidden'));
                inactivelist++;
            }

        });

    }
 */


    // COUNTER
    window.addEventListener("DOMContentLoaded", () => {
        const counter = document.querySelectorAll(".counter");
        counter.forEach(singleCounter => {
            const target = parseInt(singleCounter.getAttribute("data-target"), 10);
            const duration = 2000; // Duration of the animation in milliseconds
            const interval = 20; // Update interval in milliseconds

            let current = 0;
            const step = target / (duration / interval);

            const updateCounter = () => {
                current += step;
                if (current >= target) {
                    singleCounter.textContent = target.toLocaleString();
                    return;
                }
                let color = current <= 10 ? 'green' : (current > 10 && current <= 20) ? 'orange' : 'red';
                singleCounter.style.color = color;

                singleCounter.textContent = Math.floor(current).toLocaleString();
                setTimeout(updateCounter, interval);
            };

            updateCounter();
        });

    });





    // QA FLAGS
    window.addEventListener("update-qaflags", (event) => {
        const singleCounter = document.getElementById("counter"); // Target element by ID
        const targetValue = event.detail[0].val; // Get the value from Livewire

        // Dynamically update 'data-target' for the element
        singleCounter.setAttribute("data-target", targetValue);

        const target = parseInt(singleCounter.getAttribute("data-target"), 10);
        const duration = 2000; // Duration of the animation in milliseconds
        const interval = 20; // Update interval in milliseconds

        let current = 0;
        const step = target / (duration / interval);

        const updateCounter = () => {
            current += step;
            if (current >= target) {
                singleCounter.textContent = target.toLocaleString();
                singleCounter.style.color = 'orange'; // Final color at 20
                return;
            }

            let color = current <= 10 ? 'green' : (current > 10 && current <= 20) ? 'orange' : 'red';
            singleCounter.style.color = color;

            singleCounter.textContent = Math.floor(current).toLocaleString();
            setTimeout(updateCounter, interval);
        };

        updateCounter();
    });
</script>
