<?php

namespace App\Livewire\Reports;

use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\SkillGroup;
use Livewire\WithPagination;
use App\Exports\SkillGroupExport;
use Maatwebsite\Excel\Facades\Excel;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class SkillGroups extends Component
{
    use LivewireAlert, WithPagination;

    // filters
    public $filter_name;
    public $filter_acdid;
    public $filter_type;
    public $filter_language;

    public $sortBy = 'name';
    public $sortDir = 'ASC';

    public $filtersApplied = false;

    protected $paginationTheme = 'bootstrap';

    public function export()
    {
        $export = new SkillGroupExport($this->filter_name, $this->filter_acdid, $this->filter_type, $this->filter_language);

        // Return the export response
        return Excel::download($export, 'skill-groups.xlsx');
    }

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function filterLang($lang)
    {
        $this->filter_language = $lang;
        if ($lang == 'All') $this->filter_language = null;
    }

    public function filterType($type)
    {
        $this->filter_type = $type;
        if ($type == 'All') $this->filter_type = null;
    }

    public function applyFilters()
    {
        $this->filtersApplied = true;
    }

    public function clear()
    {
        $this->filter_name = null;
        $this->filter_acdid = null;
        $this->filter_type = null;
        $this->filter_language = null;

        $this->filtersApplied = false;
    }

    public function getData() 
    {
        $this->applyFilters();
        $this->dispatch('close-modal');
    }
    public function render()
    {
        return view('livewire.reports.skill-groups', [
            'skillGroups' => $this->filtersApplied ?
                SkillGroup::where('name', 'like', "%$this->filter_name%")
                ->when($this->filter_acdid, function ($query) {
                    return $query->where('acdid', 'like', "%$this->filter_acdid%");
                })
                ->when($this->filter_type, function ($query) {
                    return $query->where('type', 'like', "%" . $this->filter_type . "%");
                })
                ->when($this->filter_language, function ($query) {
                    return $query->where('language', 'like', "%" . $this->filter_language . "%");
                })
                ->orderBy($this->sortBy, $this->sortDir)
                ->paginate(15)
                :
                SkillGroup::orderBy($this->sortBy, $this->sortDir)
                ->paginate(15),
        ]);
    }
}
