<?php

namespace App\Livewire\Evaluation;

use App\Models\EvaluationAnswer;
use App\Models\EvaluationGroup;
use App\Models\EvaluationHeader;
use App\Models\EvaluationQuestion;
// use HTMLPurifier;
// use HTMLPurifier_Config;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class EditQuestion extends Component
{
    use WithPagination,LivewireAlert;


    public $order_by = null;
    public $sort_by = null;
    public $limit = null;


    public $search_question_name;
    public $flag = false;
    public $flag2 = false;
    public $flag3 = false;
    public $i=0;
    public $groupID;
    public $questionShow;
    public $user_id;
    public $modalId;
    public $modalIdShow;
    public $groupSelected='Header Name';

    public $evaluation_id;
    public $group_id;
    public $question_id;

    public $header_name;
    public $create_new_header = false;
    public $new_header;
    public $question_name;
    public $fatal = false;
    public $fatal_type;
    public $mark_type;
    public $selected_mark_type='Select Type...';
    public $arrayMarkAndWeight;
    public $mark;
    public $weight;
    public $evaluationQuestionId;
    public $evaluation_header;
    public $fatalNA = false;
    public $fatalArray;
    public $userid;
    public $status;
    public $messages;

    public $inputs;
    public $counter;

    public $dropdownIsOpen = false;

    protected $paginationTheme = 'bootstrap';

    public function mount($evaluation_id,$group_id,$question_id)
    {

        $this->evaluation_id = $evaluation_id;
        $this->group_id = $group_id;
        $this->question_id = $question_id;

        $this->getHeaderName();
        $this->userid = auth()->id();
        $this->questionShow=false;
        $this->inputs = [];
        $this->counter = 1;
        $this->arrayMarkAndWeight = array();
        $this->fatalArray = [];
        $this->getQuestionData($group_id,$question_id);
        $this->getFatalType();
    }

    public function getQuestionData($group_id, $question_id)
    {
    // Fetch the question with its related header and answers
    $question = EvaluationQuestion::with('header', 'answers')
    ->where('id', $question_id)
    ->first();


        // Assign header and question name
        $this->header_name = $question->header->id ?? null;
        $this->question_name = $question->question_name;

        // Assign answer type
        $this->selected_mark_type = $question->answer_type;
        $this->mark_type = $question->answer_type;

        // Handle answers array safely
        $answer = $question->answers[0] ?? null;

        if ($answer && isset($answer->fatal)) {
            $fatalArray = explode(',', $answer->fatal);

            if (in_array('NA', $fatalArray)) {
                $this->fatalNA = true;
            }

            if (in_array('Fatal Critical', $fatalArray)) {
                $this->fatal = true;
                $this->fatal_type = 'Fatal Critical';
            } elseif (in_array('Fatal Per Group', $fatalArray)) {
                $this->fatal = true;
                $this->fatal_type = 'Fatal Per Group';
            } else {
                $this->flag = false;
            }
        }

        // Initialize mark and weight array
        $this->arrayMarkAndWeight = [];

        if ($answer && isset($answer->mark_and_weight)) {
            $decoded = json_decode($answer->mark_and_weight, true);

            if (is_array($decoded)) {
                $this->arrayMarkAndWeight = $decoded;

                $i = 0;
                foreach ($decoded as $item) {
                    if ($i === 1) {
                        $this->addNewElement($this->counter);
                    }
                    $i = 1;
                }
            }
        }
    }

    public function getHeaderName(){

        return EvaluationHeader::query()->get();

    }

    public function getEvaluationQuestion(){
        try
        {
            if($this->search_question_name){
                return EvaluationQuestion::query()
                    ->select('id', 'question_name','status')
                    ->where('question_name', 'like', '%' . $this->search_question_name . '%')
                    ->where('evaluation_group_id', $this->group_id)
                    ->get();
            }else {
                return EvaluationQuestion::query()
                    ->select('id', 'question_name','status')
                    ->where('evaluation_group_id', $this->group_id)
                    ->get();
            }
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function statusUpdateModal($id){


        $this->modalId =$id;

        $fb = EvaluationQuestion::find($this->modalId);
        $fp = $fb->update(['status' => $fb->status != 0 ? 0 : 1]);
        $this->status = $fb->status;
        $this->alert('success', 'Successfully Updated!',[
            'timerProgressBar' => true,
            'timer' => '6000',
        ]);
    }



    public function modelData(){

        // $config = HTMLPurifier_Config::createDefault();
        // $purifier = new HTMLPurifier($config);

        // $this->question_name = $purifier->purify($this->question_name);

        return [
            'evaluation_group_id'            => $this->group_id,
            'created_by'           => $this->userid,
            'question_name'           => $this->question_name,
            'answer_type'           => $this->mark_type,
            'evaluation_header_id'           => $this->evaluation_header,

        ];
    }
    public function modelDataAnswer(){

        return [
            'evaluation_question_id'            => $this->evaluationQuestionId,
            'created_by'           => $this->userid,
            'mark_and_weight'           => json_encode($this->arrayMarkAndWeight),
            'fatal'           => implode(",", $this->fatalArray),
        ];
    }
    public function rules(){
        $rules = [
            'question_name' => ['required', 'string'],
            'mark_type' => ['required'],

        ];

        for ($i = 0; $i <= count($this->inputs); $i++) {
            $rules['arrayMarkAndWeight.'.$i.'.mark'] = 'required';
            $rules['arrayMarkAndWeight.'.$i.'.weight'] = 'required|numeric|min:0|max:100';
        }
        return $rules;

    }
    public function messages()
    {
        $messages = [];

        for ($i = 0; $i < count($this->arrayMarkAndWeight); $i++) {
            $messages['arrayMarkAndWeight.'.$i.'.mark.required'] = 'The mark field is required.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.required'] = 'The weight field is required.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.numeric'] = 'The weight field must be a number.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.min'] = 'The weight field must be at least 0.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.max'] = 'The weight field must not exceed 1000.';
        }

        return $messages;
    }
    public function store(){

        $this->validate();
//dd($this->arrayMarkAndWeight);
        try {
            if($this->create_new_header && $this->new_header){

                $evaluationHeader = EvaluationHeader::create(['header_name' => $this->new_header]);
                $this->evaluation_header = $evaluationHeader->id;

            }elseif($this->header_name){

                $this->evaluation_header = $this->header_name;
            }else{
                $headerExists = EvaluationHeader::where('header_name', 'Without Header')->exists();

                if (!$headerExists) {
                    $headerExistsYes = EvaluationHeader::create(['header_name' => 'Without Header']);
                    $this->evaluation_header = $headerExistsYes->id;
                } else {
                    $newHeader = EvaluationHeader::where('header_name', 'Without Header')->first();
                    $this->evaluation_header = $newHeader->id;

                }
            }
//            $evaluationQuestion = EvaluationQuestion::create($this->modelData());
            $evaluationQuestion = EvaluationQuestion::findOrFail($this->question_id)->update($this->modelData());

            $this->evaluationQuestionId = $this->question_id;
            if($this->fatalNA){$this->fatalArray[] = 'NA';}
            if($this->fatal_type){$this->fatalArray[] = $this->fatal_type;}

            $updated = EvaluationAnswer::where('evaluation_question_id', $this->question_id)
            ->update($this->modelDataAnswer());

            if ($updated === 0) {
                // No record was updated — meaning no match found
                // Handle the case here, e.g., insert new or show error
                EvaluationAnswer::create(array_merge(
                    ['evaluation_question_id' => $this->question_id],
                    $this->modelDataAnswer()
                ));
            }
// dd($this->question_id);

            $this->alert('success', 'Successfully Added !',[
                //'position' => 'bottom-end',
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->flag2 = true;
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){

//            dd($e);
            $this->alert('error', $e,[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

        $this->modelFormReset();
        $this->dispatch('close-modal');
    }


    public function closeModal()
    {
        $this->modelFormReset();

    }

    public function showModal($id){
        $this->modalId =$id;
        $this->modalIdShow = "on";
    }

    public function createNewHeader(){
        if($this->create_new_header){

        }else{
            $this->header_name='';
            $this->create_new_header = true;
        }

    }

    public function getFatalType(){


        if($this->i == 0){

            $this->i = 1;
            $this->flag2 = false;
        }else{
                $this->flag = true;
                if(!$this->flag2) {
                    $this->fatal_type = '';
                    $this->fatal = !$this->fatal;
                }
        }

    }

    public function clearMessages()
    {
        $this->messages = []; // Assuming $this->messages is an array

    }
    public function showDropdown()
    {
        $this->clearMessages();
        $this->dropdownIsOpen = !$this->dropdownIsOpen;

    }
    public function getMarkType($value){
        $this->mark_type=$value;
        $this->dropdownIsOpen = false;
        $this->selected_mark_type=$value;
        $this->arrayMarkAndWeight = array();
        $this->inputs = [];
        $this->counter = 1;
    }

    public function modelFormReset(){



        $this->i = 1;
        $this->questionShow=false;
        $this->inputs = [];
        $this->counter = 1;
        $this->arrayMarkAndWeight = array();
        $this->fatalArray = [];
        $this->mark_type = '';
        $this->mark = '';
        $this->weight = '';
        $this->evaluationQuestionId = '';
        $this->evaluation_header = '';
        $this->question_name = '';
        $this->dropdownIsOpen = false;
        $this->create_new_header = false;
        $this->header_name = '';
        $this->new_header = '';
        $this->selected_mark_type = 'Select Type...';
//        if($this->fatalNA){$this->dispatch('click-on-NA-checkbox');}
        $this->flag2 = false;


        if($this->search_question_name){
            $this->search_question_name='';
            $this->dispatch('close-modal-search');
        }
        $this->getQuestionData($this->group_id,$this->question_id);
    }

    public function addNewElement($cunter){
        $this->counter = $cunter + 1;
        array_push($this->inputs,$cunter);
    }
    public function removeElement($key){
        unset($this->inputs[$key]);
        unset($this->arrayMarkAndWeight[$key+1]);
    }
    public function getFatalNA()
    {

        $this->fatalNA = !$this->fatalNA;
        $this->flag3 = true;
    }

    public function render()
    {

        return view('livewire.evaluation.edit-question', [
            'Evaluation_question' => $this->getEvaluationQuestion(),
            'headers' => $this->getHeaderName()
        ]);

    }

}
