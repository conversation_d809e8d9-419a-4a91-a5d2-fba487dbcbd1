<?php
namespace App\Jobs\Scripts;

use App\Models\BadWord;
use App\Models\Interaction;
use App\Models\TranscriptionBadWord;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DetectJob implements ShouldQueue
{
    use Batchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public $callConversation;
    public $call_id;
    public $timeout = 7200;

    public function __construct($callConversation, $call_id)
    {
        $this->callConversation = $callConversation;
        $this->call_id          = $call_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
                       // Log::channel('detect')->info('callConversation: ' . $callConversation);

            $Interaction = Interaction::where('call_id', 'like', '%' . $this->call_id . '%')->first();

            $newCallId = $Interaction->call_id;

            $bards = BadWord::where('organization_id', $Interaction->organization_id)
                ->where('user_group_id', $Interaction->user_group_id)
                ->get();

            Log::channel('detect')->info('bads: ' . $bards);


            if ($bards->isNotEmpty()) {
                // Prepare regex-safe, quoted bad words
                $badWords = $bards->pluck('key')->map(function ($word) {
                    return preg_quote(trim($word), '/');
                });

                // Regex pattern: exact word matches
                $pattern = '/\b(' . $badWords->implode('|') . ')\b/iu';

                foreach ($this->callConversation as $textData) {
                    $text = mb_convert_encoding($textData->content, 'UTF-8', 'auto');
                    Log::channel('detect')->info($text);

                    preg_match_all($pattern, $text, $matches);

                    if (! empty($matches[0])) {
                        // Group matches by bad word
                        $detectedGrouped = collect($matches[0])
                            ->map(fn($w) => mb_strtolower($w))
                            ->groupBy(fn($w) => $w)
                            ->toArray();

                        foreach ($detectedGrouped as $badWord => $detectedWords) {
                            $typeof = BadWord::where('key', $badWord)->first();
                            if (! $typeof) {
                                continue;
                            }

                            $type       = $typeof->type;
                            $typeSource = null;

                            if ($textData->source === "right" && $typeof->side_type === "Agent") {
                                $typeSource = "Agent";
                            } elseif ($textData->source === "left" && $typeof->side_type === "Customer") {
                                $typeSource = "Customer";
                            }

                            if ($typeSource) {
                                try {
                                    TranscriptionBadWord::create([
                                        'bad_word'               => $badWord,
                                        'type'                   => $type,
                                        'side_type'              => $typeSource,
                                        'words_detected'         => json_encode([$badWord => $detectedWords], JSON_UNESCAPED_UNICODE),
                                        'call_id'                => $newCallId,
                                        'calls_transcription_id' => $textData->id,
                                    ]);

                                    Interaction::where('call_id', $newCallId)->update(['topics_flag' => 1]);
                                } catch (\Exception $e) {
                                    Log::channel('detect')->error("Failed to insert bad word record", [
                                        'bad_word'               => $badWord,
                                        'words_detected'         => implode(',', $detectedWords),
                                        'call_id'                => $this->call_id,
                                        'calls_transcription_id' => $textData->id,
                                        'error'                  => $e->getMessage(),
                                    ]);
                                }
                            }
                        }

                        Log::channel('detect')->info("Detected bad words: " . json_encode($detectedGrouped, JSON_UNESCAPED_UNICODE));
                    }
                }
            }
        } catch (\Exception $e) {
            Log::channel('detect')->error('Error in DetectJob: ' . $e->getMessage(), [
                'callConversation' => $this->callConversation,
                'exception'        => $e,
            ]);

        }
    }
}
