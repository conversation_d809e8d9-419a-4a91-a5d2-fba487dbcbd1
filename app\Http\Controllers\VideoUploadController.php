<?php

namespace App\Http\Controllers;

use App\Models\Encryption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\JsonResponse;

class VideoUploadController extends Controller
{
    /**
     * Handle the file upload and optionally encrypt it.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function upload(Request $request): JsonResponse
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:mp4|max:51200000', // 50MB in kilobytes
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422); // Unprocessable Entity
        }

        // Fetch encryption status from the database
        $encryptionStatus = Encryption::where('type', 'video')->first();

        // Check if a file is uploaded
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            // Generate a unique filename to avoid overwriting
//            $filename =  $file->getClientOriginalName();

            // Extract file information
            $originalName = $file->getClientOriginalName();
            $originalName = str_replace('_encoded', '', $originalName);

            $fileNameWithoutExt = pathinfo($originalName, PATHINFO_FILENAME); // Filename without extension
            $fileNameWithoutExt = str_replace('_encoded', '', $fileNameWithoutExt);
            $fileExt = pathinfo($originalName, PATHINFO_EXTENSION); // File extension

            // Define the suffix to check
            $suffix = '@10.202.1.66';

            // Check if the filename ends with the suffix before the extension
            if (!str_ends_with($fileNameWithoutExt, $suffix)) {
                // Append the suffix if it's not present
                $fileNameWithoutExt = str_replace('_encoded', '', $fileNameWithoutExt);
                $filename = $fileNameWithoutExt . $suffix . '.' . $fileExt;
            } else {
                $filename = $originalName; // No change needed if suffix is present
            }


            $filePath = 'video/' . $filename;

            try {
                if ($encryptionStatus && $encryptionStatus->status) {
                    // Encrypt the file content
                    $fileContent = file_get_contents($file->getRealPath());
//                    $encryptedContent = Crypt::encrypt($fileContent); // Use Laravel’s Crypt facade
                    $encryptedContent =encrypt($fileContent, config('app.key'));



                    // Store the encrypted file content
                    Storage::disk('public')->put($filePath, $encryptedContent);
                } else {
                    // Store the file directly
                    Storage::disk('public')->put($filePath, file_get_contents($file->getRealPath()));
                }

                return response()->json([
                    'success' => true,
                    'message' => 'File has been uploaded successfully.',
                    'filename' => $filename
                ]);
            } catch (\Exception $e) {
                // Handle exceptions and errors during file upload and encryption
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while uploading the file.',
                    'error' => $e->getMessage()
                ], 500); // Internal Server Error
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'No file was uploaded or there was an error.'
        ], 400); // Bad Request
    }
}
