<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\SkillGroup;
use App\Models\Organization;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class SkillGroups extends Component
{
    use LivewireAlert, WithPagination;

    // add modal 
    public $added_type;
    public $added_lang;
    public $added_name;
    public $added_acdid;
    public $added_desc;

    // edit modal
    public $selectedGroupId;
    public $selectedGroup;
    public $selectedGroupType;
    public $selectedGroupLanguage;
    public $selectedGroupAcdid;
    public $selectedGroupDesc;


    // live search 
    public $searchGroup = '';
    public $sortBy = 'name';
    public $sortDir = 'ASC';


    public $theIdToDelete;

    protected $paginationTheme = 'bootstrap';

    public function getListeners()
    {
        return [
            'confirmed'
        ];
    }

    public function clear()
    {
        $this->resetValidation();
        $this->added_type = null;
        $this->added_lang = null;
        $this->added_name = null;
        $this->added_acdid = null;
        $this->added_desc = null;
        $this->selectedGroupId = null;
    }

    public function addType($type)
    {
        $this->added_type = $type;
    }

    public function addlang($lang)
    {
        $this->added_lang = $lang;
    }

    public function showDeleteAlert($id)
    {
        $this->theIdToDelete = $id;
        $this->alert('warning', 'Are you sure you want to delete this group?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirmed()
    {
        if (SkillGroup::find($this->theIdToDelete)->delete()) {
            $this->alert("success", "Group Deleted Successfully");
        };
    }

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function closeModal()
    {
        $this->dispatch('closeModal');
    }

    public function rules()
    {
        $rules = [];

        // Add rules for editing if selectedGroup is present
        if ($this->selectedGroupId) {
            $rules = [
                'selectedGroup' => ['required', 'string', 'max:255', Rule::unique('skill_groups', 'name')->ignore($this->selectedGroupId)],
                'selectedGroupType' => 'required|string|in:Inbound,Outbound',
                'selectedGroupLanguage' => 'required|in:Arabic,English',
                'selectedGroupAcdid' => ['required', 'numeric', 'not_in:0'],
            ];
        }

        // Add rules for adding
        else {
            $rules = [
                'added_name' => ['required', 'string', 'max:255', 'unique:skill_groups,name'],
                'added_acdid' => ['required', 'numeric', 'not_in:0'],
                'added_lang' => 'required|in:Arabic,English',
                'added_type' => 'required|string|in:Inbound,Outbound',
            ];
        }

        return $rules;
    }


    public function addGroup()
    {
        $this->validate();

        SkillGroup::create([
            'name' => $this->added_name,
            'type' => $this->added_type,
            'language' => $this->added_lang,
            'acdid' => $this->added_acdid,
            'description' => $this->added_desc,
        ]);

        $this->alert("success", "Group Added Successfully");

        $this->closeModal();
    }

    public function selectGroup($id)
    {
        $this->selectedGroupId = $id;
        $group = SkillGroup::find($id);
        $this->selectedGroup = $group->name;
        $this->selectedGroupType = $group->type;
        $this->selectedGroupLanguage = $group->language;
        $this->selectedGroupAcdid = $group->acdid;
        $this->selectedGroupDesc = $group->description;
    }

    public function editGroup()
    {
        $this->validate();

        SkillGroup::find($this->selectedGroupId)->update([
            'name' => $this->selectedGroup,
            'type' => $this->selectedGroupType,
            'language' => $this->selectedGroupLanguage,
            'acdid' => $this->selectedGroupAcdid,
            'description' => $this->selectedGroupDesc,
        ]);

        $this->alert("success", "Group Edited Successfully");

        $this->closeModal();
    }


    public function render()
    {
        return view('livewire.skill-groups', [
            'skillGroups' => SkillGroup::search($this->searchGroup)->orderBy($this->sortBy, $this->sortDir)->paginate(5),
            'organizations' => Organization::paginate(5),
            // 'userGroups' => UserGroup::search($this->searchGroup)->orderBy($this->sortBy, $this->sortDir)->paginate(5),
            'userGroups' => [],
            'possibleOrgs' => Organization::all(),
            // 'possibleSupervisors' => User::where('full_name', 'like', "%$this->searchSupervisors%")
            //     ->where('role', 2)
            //     ->whereHas('supervisorOrganizations', function ($q) {
            //         $q->where('organization_id', $this->selectedGroupOrg_id);
            //     })
            //     ->orderBy('full_name', 'ASC')->get(),
        ]);
    }
}
