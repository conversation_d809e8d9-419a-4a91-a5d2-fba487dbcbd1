{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "beyondcode/laravel-websockets": "^1.13", "doctrine/dbal": "^3.8", "guzzlehttp/guzzle": "^7.8", "jantinnerezo/livewire-alert": "^3.0", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/telescope": "^5.7", "laravel/tinker": "^2.8", "laravel/ui": "^4.4", "league/flysystem": "^3.28", "league/flysystem-ftp": "^3.24", "livewire/livewire": "^3.4", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.2", "phpseclib/phpseclib": "^3.0", "predis/predis": "2.0", "pusher/pusher-php-server": "^7.0", "ratchet/pawl": "^0.4.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.10", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}