<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Reader\Xml\Style\Alignment;

class UserPermissionsExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $filter_name;
    protected $filter_agentId;
    protected $filter_role;
    protected $filter_org;

    public function __construct($filter_name, $filter_agentId, $filter_role, $filter_org)
    {
        $this->filter_name = $filter_name;
        $this->filter_agentId = $filter_agentId;
        $this->filter_role = $filter_role;
        $this->filter_org = $filter_org;
    }
    /**
     * @return \Illuminate\Support\Collection
     */

    public function collection()
    {
        $query = User::query();

        if ($this->filter_name) {
            $query->where('full_name', 'like', "%{$this->filter_name}%");
        }

        if ($this->filter_agentId) {
            $query->where('agent_id', 'like', "%{$this->filter_agentId}%");
        }

        if ($this->filter_role) {
            $query->where('role', $this->filter_role);
        }

        if ($this->filter_org) {
            $query->where('organization_id', $this->filter_org)->orWhereHas('supervisorOrganizations', function ($q) {
                $q->where('organizations.id', $this->filter_org);
            });
        }

        $n = 1;
        return $query->orderBy('full_name')->get()
            ->map(function ($user) use (&$n) {
                return [
                    '#' => $n++,
                    'Full Name' => $user->full_name,
                    // 'Organization' => $user->organization->name ?? 'Multi',
                    'Organization' => $this->getOrg($user->id, $user->role),
                    'Role' => $this->getRoleName($user->role),
                    'ID' => $user->agent_id,
                    'ACTIVE' => ($user->enabled && !$user->terminated) ? '✔' : '❌',
                    'ALLOW ACCESS TELEPHONY' => $user->permissions()->where('permission_id', 1)->exists() ? '✔' : '❌',
                    'PLAY RECORDED INTERACTIONS' => $user->permissions()->where('permission_id', 2)->exists() ? '✔' : '❌',
                    'VIEW EVALUATION REPORTS' => $user->permissions()->where('permission_id', 10)->exists() ? '✔' : '❌',
                    'VIEW OTHER EVALUATORS COMMENTS' => $user->permissions()->where('permission_id', 5)->exists() ? '✔' : '❌',
                    // 'VIEW INTERACTIONS FLAGGING' => $user->parameters()->where('parameter_id', 23)->exists() ? '✔' : '❌',
                    'VIEW INTERACTION EVALUATION' => $user->parameters()->where('parameter_id', 22)->exists() ? '✔' : '❌',
                ];
            });
    }

    public function headings(): array
    {
        return [
            '#',
            'Full Name',
            'Organization',
            'Role',
            'ID',
            'ACTIVE',
            'ALLOW ACCESS TELEPHONY',
            'PLAY RECORDED INTERACTIONS',
            'VIEW EVALUATION REPORTS',
            'VIEW OTHER EVALUATORS COMMENTS',
            // 'VIEW INTERACTIONS FLAGGING',
            'VIEW INTERACTION EVALUATION'
        ];
    }


    private function getRoleName($roleId)
    {
        $roles = [
            1 => 'Admin',
            2 => 'Supervisor',
            4 => 'Agent',
            5 => 'Quality',
        ];

        return $roles[$roleId] ?? 'Unknown';
    }

    private function getOrg($id, $ops)
    {
        if ($ops == 4) {
            $org = User::find($id)?->organization?->name ?? '-';
        } else {
            $org = 'Multi';
        }

        return $org;
    }
}
