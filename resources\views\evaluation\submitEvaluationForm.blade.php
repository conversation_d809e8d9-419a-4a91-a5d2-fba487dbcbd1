@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Evaluation Form Page')

{{-- Style Section --}}
@section('style')

    <style>
        thead {
            height: 0rem !important;
        }
        .fa-xl {
            line-height: 1.1em !important;
        }
        .d-block {
         /*    width: 60px;
            height: 59px; */
        }
        .p-10 {
            padding: 10px;
        }
        .b-radius-10 {
            border-radius: 10px;
        }
        .header-field{
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .section-one {
            border-radius: 10px;
            height: 18rem;
            background-color: white;
            margin: 2% 0;
            margin-left: 4rem !important;
            margin-right: 1rem !important;
        }
        .section-voice{
            border-radius: 10px;
            height: 12rem;
            background-color: white;
            margin: 2% 0;
            margin-left: 4rem !important;
            margin-right: 1rem !important;
        }
        .section-one-head{
            height: 20%;
            /* background-image: linear-gradient(to right, #02a34e , #abc959); */
            background-image: linear-gradient(to right, #40798c , #7a9fab);
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        .section-one-field{
            height: 80%;
            padding: 2%;
            overflow: auto;
        }
        .shadow{
            box-shadow: 0 0.1rem 1rem #00000026!important;
        }
        .custom-select select {

            border-radius: 0px;
            cursor: pointer;
        }
        .arrow-icon {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            pointer-events: none;
            fill: #00a34e;
            width: 36px;
            height: 32px;
        }
        .form-control{
            font-weight: 300;
            border-radius: 0;
        }
        .col-form-label{
            font-weight: 500;
        }
        .section-tow{
            border-radius: 10px;
            height: 18rem;
            background-color: white;
            margin: 2% 0;
            display: flex;
            flex-direction: row;
            margin-left: 4rem !important;
            margin-right: 1rem !important;
        }
        .section-tow-boxes{
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .section-tow-chart{
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: row;
            position: relative;
        }
        .section-tow-chart::before {
            content: '';
            position: absolute;
            left: 0;
            top: 22px;
            height: 84%;
            width: 2px;
            background-color: #d0caca;
        }
        .row-box-one{
            height: 50%;
            padding: 4% 4% 1%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
        .row-box-tow{
            height: 50%;
            padding: 2% 4% 3%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
        .box{
            box-shadow: 0 0.0rem .5rem #00000026!important;
            width: 45%;
            height: 100%;
            border-radius: 10px;
            padding: 2%;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            cursor: pointer;
        }
        .box-number{
            font-size: 20px;
        }
        .box-text{
            font-weight: 400;
        }
        .green-color{
            background-color: #08a54f;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .yellow-color{
            background-color: #bbcf34;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .beige-color{
            background-color: #e8cf4d;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .orange-color{
            background-color: #cf810b;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .red-color{
            background-color: #a82020;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .section-tow-colors-group{
            width: 45%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 3%;
        }
        .section-tow-circle-chart{
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 75%;
            margin: 0px 0%;
        }

        .box-color{
            height: 13%;
            display: flex;
            flex-direction: row;
        }
        .text-span{
            margin-left: 7%;
            margin-top: 1.7%;
        }
        .section-three{
            border-radius: 10px;
            margin-bottom: 2%;
             margin-left: 4rem !important;
            margin-right: 1rem !important;
        }
        .section-three-container{

            padding: 1% 2%;

        }
        .container-color{
            width: 100%;
            height: 50px;
            display: flex;
            flex-direction: row;
            padding-top: 1%;
            margin-bottom: 1%;
        }
        .green-color-2{
            background-color: #08a54f;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .yellow-color-2{
            background-color: #bbcf34;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }

        .blue-color-2{
            background-color: #40798c;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }

        .gray-color-2{
            background-color: #e4e3e3;
            width: 31px;
            height: 28px;
            border-radius: 5px;
        }
        .text-span-2{
            margin-left: .5%;
            margin-right: 2%;
            padding-top: 4px;
        }
        .div-table{
            border: 1px solid #d0caca;
            border:none;
        }
        .table{
            margin-bottom: 0;
            background-color: #f8fafc !important;
        }
        .table-header{
            text-align: center;
            background-color: #f8fafc;
            /* color: #707f77; */
            /* height: 55px !important; */
            vertical-align: middle;

        }
        .table-header th , td{
            font-size: .8rem
        }
        .table-header-2{
            text-align: center;
            /* background-color: #f3f3f3; */
            color: #707f77;
            vertical-align: middle;
            background-color: #f8fafc;
        }
        textarea{
            resize: none;
        }
        .group-table{
            /* height: 45px !important; */
           /*  background-color: #08a54f !important; */
           background-color: #40798c !important;;
            color: white;
            vertical-align: middle;

        }

        .group-table-2{
           /*  background-color: #08a54f !important; */
           background-color: #40798c !important;;
            color: white !important;

        }
        .group-table-th{
            border: none;
            text-align: center;
            font-size: .8rem
            font-family: none !important;
        }
        .group-header{
            /* background-color: #bbcf34 !important; */
            background-color: #e4e3e3 !important ;
            color: black !important;
            font-size: .8rem

        }
        .group-header-2{
            /* background-color: #bbcf34 !important; */
            background-color: #e4e3e3 !important ;
            color: black !important;

        }
        .td-style{
            text-align: center;
        }
        .text-area{
            height: 37.22px !important;
            overflow: hidden;
        }
        .group-weight{
            background-color: #ececec !important;
            /* height: 45px !important; */
        }
        .group-weight-th{
            font-weight: 500;
        }
        .group-weight-th-number{
            font-weight: 500;
            text-align: center;
        }
        .section-four{
            display: flex;
            margin-bottom: 5%;
            margin-left: 4rem !important;
            margin-right: 1rem !important;
        }
        .btn-submit-div{
            text-align: end;
        }
        .btn-color {
            width: 142px;
            height: 39px;
            background-color: #00a34e;
            border-color: #00a34e;
        }
        #myChart{
            height: 250px !important;
            width: 210px !important;

        }
        .select-value{
            text-align: center;
        }
        td,th{
            word-break: break-word;
        }
        .container-table{
            margin-bottom: 3%;
        }
        textarea:focus{
            background-color: white;
        }
        .text-area-last{
            background-color: #eaebeb;
        }
        .group-comment{
            text-align: center;
            height: 45px !important;
            background-color: #08a54f !important;
            color: white;
            vertical-align: middle;
        }

        #playButton, #pauseButton  {
        font-size: 16px;
        cursor: pointer;
        border-radius: 5px;
        padding: 3px 30px;
        }

        #playButton {
        background-color: #4CAF50;
        color: white;
        border: none;
        }

        #pauseButton {
        background-color: #f44336;
        color: white;
        border: none;
        }
        .parent-btn{
            text-align: center;
        }
        #loadingMessage {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: inline-block;
            border-top: 4px solid #FFF;
            border-right: 4px solid transparent;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
        }

        #loadingMessage::after {
            content: '';
            box-sizing: border-box;
            position: absolute;
            /* left: 0; */
            /* top: 0; */
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border-left: 4px solid #c6d64e;
            border-bottom: 4px solid transparent;
            animation: rotateLoader 0.5s linear infinite reverse;
        }
        .parent-btn {
            margin-top: 0.5rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        @keyframes rotateLoader {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
        .box:hover{
            background: #f8f8f8 !important;
            transform: scale(1.15) !important; /* Make the card slightly larger */
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3); /* Add a larger shadow */
        }
    </style>
<style>
    /*
    new styles
    */




        .form-control , .form-select , .dropdown-toggle-style{
            background-color: #eff3f4 !important;
            border: none !important;
            height: 40px;
        }
        label{
            color : #40798c !important;
            font-size: 17px;
            font-weight: 700 !important;
        }
        .previous{
            margin-bottom: 5px;
        }
    /*
    end new styles
    */

</style>



@endsection

{{-- Content Section --}}
@section('content')
    <div class="container-fluid">

        <livewire:evaluation.submit-evaluation-form :evaluation_id="$evaluation_id" :callIdToServe="$callIdToServe"  />

    </div>
@endsection

{{-- Script Section --}}

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {{-- <script src="https://unpkg.com/wavesurfer.js@7"></script> --}}

    <script>
        let startTime;
        let elapsedTime = 0; // in seconds
        let timerInterval;

        // Function to format time as HH:MM:SS
        function formatTime(seconds) {
            const hours = String(Math.floor(seconds / 3600)).padStart(2, '0');
            const minutes = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
            const secs = String(seconds % 60).padStart(2, '0');
            return `${hours}:${minutes}:${secs}`;
        }

        // Function to start tracking time
        function startTracking() {
            startTime = new Date().getTime();
            timerInterval = setInterval(() => {
                elapsedTime = Math.floor((new Date().getTime() - startTime) / 1000);
                const formattedTime = formatTime(elapsedTime);
                console.log(formattedTime);

                Livewire.dispatch('updateElapsedTime',{time : formattedTime});

            }, 1000);
        }

        // Function to stop tracking time
        function stopTracking() {
            clearInterval(timerInterval);
        }

        // Event listeners
        window.addEventListener('load', startTracking);
        window.addEventListener('beforeunload', stopTracking);
    </script>



    <script type="module">

        document.addEventListener('DOMContentLoaded', function() {

            Livewire.dispatch('callFunctionFromJS');

        window.addEventListener('open-modal', event => {
            document.getElementById('modal').style.display='';
            document.getElementById('dropZone').style.display='none';
        });
        window.addEventListener('close-modal', event => {
            document.getElementById('modal').style.display='none';
            document.getElementById('dropZone').style.display='';
        });
        window.addEventListener('off-checkbox', event => {

        });
        let myChart = null;
        let textValueCenter = null;
        window.addEventListener('chart', (event) => {

            const qualityPercentage = event.detail[0].qualityPercentage;
            const chartColor = event.detail[0].chartColor;
            document.getElementById('myChart').innerHTML=null;
            const ctx = document.getElementById('myChart');
            if (myChart !== null) {
                myChart.destroy(); // Destroy the chart if it exists
                myChart = null; // Reset the chart variable
            }
            textValueCenter = 'Quality Percentage  '+ qualityPercentage + '%', 'myChart';
            const data = {
                labels: [
                    'AHT',],
                datasets: [{

                    data: [qualityPercentage, 100 - qualityPercentage],
                    backgroundColor: [
                        chartColor,
                        '#b8e6ce'

                    ],
                    borderWidth: 1,
                    hoverOffset: 0
                }],


            };
            const options = {
                cutout: 90,
                responsive: true,
                aspectRatio: 1,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                        position: 'chartArea',
                        align: 'center',
                        labels: {
                            color: 'darkred',
                            font: {
                                weight: 'bold'
                            }
                        }
                    }
                }

            };
            const config = {
                type: 'doughnut',
                data: data,
                options, options,
                radius: 1
            };

            myChart = new Chart(ctx, config, options);
            textCenter('Quality Percentage  '+ qualityPercentage + '%', 'myChart');
            function textCenter(val, containerName) {

                Chart.register({
                    id: 'myGraphText',
                    beforeDraw: function (chart) {
                        if (chart.canvas.id != containerName) {
                            return 0;
                        }

                        var width = chart.width,
                            height = chart.height,
                            ctx = chart.ctx;

                        ctx.restore();
                        val = textValueCenter;
                        var fontSize = (height / 250).toFixed(2);

                        ctx.font = fontSize + "em sans-serif";
                        ctx.textBaseline = "middle";
                        // console.log(val);
                        var words = val.split(' ');
                        var textY = height / 2 - ((words.length - 1) * 10);
                        var x = 0;
                        words.forEach(function (word) {
                            var textX = Math.round((width - ctx.measureText(word).width) / 2);

                            if (x < 2) {
                                ctx.font = `20px em sans-serif`;
                                ctx.fillStyle = '#d0caca';
                                ctx.fillText(word, textX, textY);
                            } else {
                                ctx.fillStyle = 'black';
                                ctx.font = `bold ${fontSize}em sans-serif`;
                                ctx.fillText(word, textX, textY);
                            }

                            textY += 20;
                            x += 1;
                        });

                        ctx.save();
                    }
                });
            }
        });
        });

        window.addEventListener('scroll-up', event => {

            window.scrollTo(0, 0);

        });
    </script>

<script>

