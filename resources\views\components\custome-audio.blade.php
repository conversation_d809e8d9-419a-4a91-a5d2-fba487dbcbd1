@props(['callPath', 'callDate', 'agent_name', 'agent_id', 'callAccount', 'callType', 'callId'])
<style>

@media screen and (max-width: 1023px) {
    /* For smaller devices (tablets and mobiles): */
    .audio-card {
        padding-bottom: 10rem; /* Adjust the value as needed */
    }
    #chartContainer{
        max-height: 15rem;
    }
}

@media screen and (min-width: 1024px) and (max-width: 1280px) {
    /* For desktop: */
    .audio-card {
        padding-bottom: 21.0rem;
    }
}

/* @media screen and (min-width: 1281px) and (max-width: 1335px) {
    .audio-card {
        padding: 110px 11px;
    }
    .card {
        max-height: 13.75rem;
    }
    .chartsContainer{
        max-height: 10rem !important;

    }
}
 */


@media screen and (min-width: 1281px) and (max-width: 1600px) {
    .audio-card {
        padding: 110px 11px;
    }
    .card {
        max-height: 239px;
    }
    .chartsContainer{
        max-height: 165px !important;

    }
}

@media screen and (min-width: 1601px) and (max-width: 1920px) {
    .audio-card {
        padding: 39px 11px;
    }
    .card {
        max-height: 339px;
    }
    .chartsContainer{
        /* max-height: 12.5rem !important;
 */
    }
}


@media screen and (min-width: 1921px) {
    .audio-card {
        padding: 39px 11px;
    }
}
</style>
<div class="audio-card col-12 123" style="flex: 1;">
    <div class="row">
        <div class="col-12 p-0 mb-4">
            <div class="audio-card-left ">
                <div class="avatar">
                    <img src="{{ asset('assets/SVG/assets-v2/75.svg') }}" alt="Avatar" />
                </div>
                <div class="audio-card-content ms-3">
                    <div class="audio-card-header">
                        <span class="date-time">{{ $callDate->format('Y/m/d - H:i:s') }}</span>
                        <span class="name-id mb-2">{{ $agent_name }} - {{ $agent_id }}</span>
                        <span class="call-id">{{-- {{ $callId }} --}} {{ $callAccount }} |
                            {{ $callType }} Call</span>
                    </div>

                </div>
            </div>

        </div>

        <div class="col-12">
            <div class="audio-controls m-3">
                {{-- <button class="rewind">↩ <span id="rewind-duration">15s</span></button> --}}
                <button class="rewind"
                    style="padding-left: 0 !important; padding-right: 0 !important"><span
                        id="rewind-duration">15s</span><img
                        src="{{ asset('assets/SVG/assets-v2/rewind.png') }}" alt="rewind"
                        style="width: 1.5rem; margin-bottom:0.8rem !important"></button>
                <div class="timeline">
                    <div id="progress" class="progress"></div>
                </div>
                {{-- <button class="forward"><span id="forward-duration">15s</span> ↪</button> --}}
                <button class="forward"
                    style="padding-left: 0 !important; padding-right: 0 !important"><img
                        src="{{ asset('assets/SVG/assets-v2/forward.png') }}" alt="rewind"
                        style="width: 1.5rem; margin-bottom:0.8rem !important"><span
                        id="forward-duration">15s</span></button>
                <span id="current-time">00:00</span>
                <button class="more-options">...</button>
                <button class="play-pause-button" id="playPauseButton">▶</button>
                {{-- <button class="video-btn play-pause-button" id="video-btn" d data-bs-toggle="modal" data-bs-src="https://www.youtube.com/embed/JJUo8Fe3_JY" data-bs-target="#videoModal">📹</button> --}}
                <x-chunks.video-modal :callId="$callId" />
            </div>
        </div>
    </div>



</div>

<!-- Include Peaks.js -->
<script>
    document.addEventListener("DOMContentLoaded", () => {
        // Initialize audio element
        const audio = new Audio(@json(asset($callPath)));
        const playPauseButton = document.getElementById("playPauseButton");
        const timeline = document.querySelector(".timeline");
        const progress = document.getElementById("progress");
        const currentTime = document.getElementById("current-time");
        const rewindButton = document.querySelector(".rewind");
        const forwardButton = document.querySelector(".forward");
        const rewindDurationDisplay = document.getElementById("rewind-duration");
        const forwardDurationDisplay = document.getElementById("forward-duration");
        let isMetadataLoaded = false;

        // Preload metadata
        audio.preload = "metadata";

        // Metadata loaded event
        audio.addEventListener("loadedmetadata", () => {
            if (audio.duration && !isNaN(audio.duration)) {
                isMetadataLoaded = true;
                console.log("Audio metadata loaded. Duration:", audio.duration);

                // Calculate and update rewind/forward button durations (10% of total duration)
                const duration10Percent = Math.floor(audio.duration * 0.1); // 10% of total duration
                rewindDurationDisplay.textContent = `${duration10Percent}s`;
                forwardDurationDisplay.textContent = `${duration10Percent}s`;
            } else {
                console.error("Failed to load metadata. Duration is invalid.");
            }
        });

        // Play/pause functionality
        playPauseButton.addEventListener("click", () => {
            if (!isMetadataLoaded) {
                console.error("Metadata not loaded. Cannot play/pause.");
                return;
            }

            if (audio.paused) {
                audio.play();
                playPauseButton.textContent = "⏸"; // Change to pause icon
                console.log("Audio playing...");
            } else {
                audio.pause();
                playPauseButton.textContent = "▶"; // Change to play icon
                console.log("Audio paused.");
            }
        });

        // Update progress bar and current time display
        audio.addEventListener("timeupdate", () => {
            if (!isMetadataLoaded || audio.duration === 0) return;

            const percentage = (audio.currentTime / audio.duration) * 100;
            progress.style.width = `${percentage}%`;

            const minutes = Math.floor(audio.currentTime / 60).toString().padStart(2, "0");
            const seconds = Math.floor(audio.currentTime % 60).toString().padStart(2, "0");
            currentTime.textContent = `${minutes}:${seconds}`;

            console.log(`Current time: ${audio.currentTime}s`);
        });

        // Seek functionality when clicking on the timeline
        timeline.addEventListener("click", (event) => {
            if (!isMetadataLoaded || audio.duration === 0) {
                console.error("Metadata not loaded or invalid duration.");
                return;
            }

            const timelineWidth = timeline.offsetWidth;
            const clickX = event.offsetX;
            const newTime = (clickX / timelineWidth) * audio.duration;

            console.log(`Seeked to: ${newTime}s`);
            audio.currentTime = newTime;
        });

        // Reset play button when audio ends
        audio.addEventListener("ended", () => {
            playPauseButton.textContent = "▶"; // Reset to play icon
            console.log("Audio ended.");
        });

        // Rewind by 10% of the total duration
        rewindButton.addEventListener("click", () => {
            if (!isMetadataLoaded || audio.duration === 0) {
                console.error("Metadata not loaded. Cannot rewind.");
                return;
            }

            const rewindTime = Math.floor(audio.duration * 0.1);
            const newTime = Math.max(0, audio.currentTime - rewindTime); // Ensure it doesn't go below 0
            audio.currentTime = newTime;

            console.log(`Rewinded to: ${newTime}s`);
        });

        // Forward by 10% of the total duration
        forwardButton.addEventListener("click", () => {
            if (!isMetadataLoaded || audio.duration === 0) {
                console.error("Metadata not loaded. Cannot forward.");
                return;
            }

            const forwardTime = Math.floor(audio.duration * 0.1); // 10% of the total duration
            const newTime = Math.min(audio.duration, audio.currentTime + forwardTime); // Ensure it doesn't exceed duration
            audio.currentTime = newTime;

            console.log(`Forwarded to: ${newTime}s`);
        });
    });
</script>

