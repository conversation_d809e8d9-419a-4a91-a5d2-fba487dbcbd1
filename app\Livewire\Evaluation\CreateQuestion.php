<?php

namespace App\Livewire\Evaluation;

use App\Models\Evaluation;
use App\Models\EvaluationAnswer;
use App\Models\EvaluationGroup;
use App\Models\EvaluationHeader;
use App\Models\EvaluationQuestion;
// use HTMLPurifier;
// use HTMLPurifier_Config;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class CreateQuestion extends Component
{

    use WithPagination,LivewireAlert;



    public $order_by = null;
    public $sort_by = null;
    public $limit = null;



    public $search_question_name;



    public $groupID;
    public $questionShow;
    public $user_id;
    public $modalId;
    public $modalIdShow;
    public $groupSelected='Header Name';

    public $evaluation_id;
    public $group_name;
    public $group_id;
    public $header_name;

    public $create_new_header = false;
    public $new_header;
    public $question_name;
    public $fatal = false;
    public $fatal_type;
    public $mark_type;
    public $selected_mark_type='Select Type...';
    public $arrayMarkAndWeight;
    public $mark;
    public $weight;
    public $evaluationQuestionId;
    public $evaluation_header;
    public $fatalNA = false;
    public $fatalArray;
    public $status;
    public $userid;

    public $inputs;
    public $counter;

    public $dropdownIsOpen = false;

    protected $paginationTheme = 'bootstrap';

    public function mount($evaluation_id,$group_id)
    {

        $this->evaluation_id = $evaluation_id;
        $this->group_id = $group_id;
        $this->getGroupName($this->group_id);
        $this->getHeaderName();
        $this->userid = auth()->id();
        $this->questionShow=false;
        $this->inputs = [];
        $this->counter = 1;
        $this->arrayMarkAndWeight = array();
        $this->fatalArray = [];

    }
    public function getGroupName($group_id){

        $group = EvaluationGroup::query()
            ->where('id', $group_id)
            ->pluck('group_name');
        $this->group_name=$group[0];

    }
    public function getHeaderName(){

        return EvaluationHeader::query()->get();

    }

    public function getEvaluationQuestion(){
        try
        {
            if($this->search_question_name){
                return EvaluationQuestion::query()
                    ->select('id', 'question_name','status')
                    ->where('question_name', 'like', '%' . $this->search_question_name . '%')
                    ->where('evaluation_group_id', $this->group_id)
                    ->get();
            }else {
                return EvaluationQuestion::query()
                    ->select('id', 'question_name','status')
                    ->where('evaluation_group_id', $this->group_id)
                    ->get();
            }
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function statusUpdateModal($id){


        $this->modalId =$id;

        $fb = EvaluationQuestion::find($this->modalId);
        $fp = $fb->update(['status' => $fb->status != 0 ? 0 : 1]);
        $this->status = $fb->status;
        $this->alert('success', 'Successfully Updated!',[
            'timerProgressBar' => true,
            'timer' => '6000',
        ]);
    }



    public function modelData(){

        // $config = HTMLPurifier_Config::createDefault();
        // $purifier = new HTMLPurifier($config);

        // $this->question_name = $purifier->purify($this->question_name);

        return [
            'evaluation_group_id'            => $this->group_id,
            'created_by'           => $this->userid,
            'question_name'           => $this->question_name,
            'answer_type'           => $this->mark_type,
            'evaluation_header_id'           => $this->evaluation_header,

        ];
    }
    public function modelDataAnswer(){

        return [
            'evaluation_question_id'            => $this->evaluationQuestionId,
            'created_by'           => $this->userid,
            'mark_and_weight'           => json_encode($this->arrayMarkAndWeight),
            'fatal'           => implode(",", $this->fatalArray),
        ];
    }
    public function rules(){
        $rules = [
            'question_name' => ['required', 'string'],
            'mark_type' => ['required'],

        ];

        for ($i = 0; $i <= count($this->inputs); $i++) {
            $rules['arrayMarkAndWeight.'.$i.'.mark'] = 'required';
            $rules['arrayMarkAndWeight.'.$i.'.weight'] = 'required|numeric|min:0|max:100';
        }
        return $rules;

    }
    public function messages()
    {
        $messages = [];

        for ($i = 0; $i < count($this->arrayMarkAndWeight); $i++) {
            $messages['arrayMarkAndWeight.'.$i.'.mark.required'] = 'The mark field is required.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.required'] = 'The weight field is required.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.numeric'] = 'The weight field must be a number.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.min'] = 'The weight field must be at least 0.';
            $messages['arrayMarkAndWeight.'.$i.'.weight.max'] = 'The weight field must not exceed 1000.';
        }

        return $messages;
    }
    public function store(){

        $this->validate();
        try {
            if($this->create_new_header && $this->new_header){

                $evaluationHeader = EvaluationHeader::create(['header_name' => $this->new_header]);
                $this->evaluation_header = $evaluationHeader->id;

            }elseif($this->header_name){

                $this->evaluation_header = $this->header_name;
            }else{
                $headerExists = EvaluationHeader::where('header_name', 'Without Header')->exists();

                if (!$headerExists) {
                  $headerExistsYes = EvaluationHeader::create(['header_name' => 'Without Header']);
                    $this->evaluation_header = $headerExistsYes->id;
                } else {
                    $newHeader = EvaluationHeader::where('header_name', 'Without Header')->first();
                    $this->evaluation_header = $newHeader->id;
                }
            }
            $evaluationQuestion = EvaluationQuestion::create($this->modelData());
            $this->evaluationQuestionId = $evaluationQuestion->id;
            if($this->fatalNA){$this->fatalArray[] = 'NA';}
            if($this->fatal_type){$this->fatalArray[] = $this->fatal_type;}


            EvaluationAnswer::create($this->modelDataAnswer());

            $this->alert('success', 'Successfully Added !',[
                //'position' => 'bottom-end',
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){

//            dd($e);
            $this->alert('error', $e,[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

        $this->modelFormReset();
        $this->dispatch('close-modal');
    }


    public function closeModal()
    {
        $this->modelFormReset();

    }

    public function showModal($id){
        $this->modalId =$id;
        $this->modalIdShow = "on";
    }

    public function createNewHeader(){
        if($this->create_new_header){

        }else{
            $this->header_name='';
            $this->create_new_header = true;
        }

    }

    public function getFatalType(){

        $this->fatal = !$this->fatal;
        $this->fatal_type='';

    }


    public function showDropdown()
    {

        $this->dropdownIsOpen = !$this->dropdownIsOpen;

    }
    public function getMarkType($value){
        $this->mark_type=$value;
        $this->dropdownIsOpen = false;
        $this->selected_mark_type=$value;
        $this->arrayMarkAndWeight = array();
        $this->inputs = [];
        $this->counter = 1;
    }

    public function modelFormReset(){


        $this->questionShow=false;
        $this->inputs = [];
        $this->counter = 1;
        $this->arrayMarkAndWeight = array();
        $this->fatalArray = [];
        $this->fatal_type = '';
        $this->mark_type = '';
        $this->mark = '';
        $this->weight = '';
        $this->evaluationQuestionId = '';
        $this->evaluation_header = '';
        $this->question_name = '';
        $this->dropdownIsOpen = false;
        $this->create_new_header = false;
        $this->header_name = '';
        $this->new_header = '';
        $this->selected_mark_type = 'Select Type...';
        if($this->fatalNA){$this->dispatch('click-on-fatal-checkbox');}
        if($this->fatal){$this->dispatch('click-on-na-checkbox');}
//        $this->fatalNA = false;
//        $this->fatal = false;
            $this->search_question_name='';
            $this->dispatch('close-modal-search');
        
    }


    public function addNewElement($cunter){
        $this->counter = $cunter + 1;
        array_push($this->inputs,$cunter);
    }
    public function removeElement($key){
        unset($this->inputs[$key]);
        unset($this->arrayMarkAndWeight[$key+1]);
    }
    public function getFatalNA()
    {
        $this->fatalNA = !$this->fatalNA;
    }
    public function render()
    {
//        dd($this->getHeaderName());
        return view('livewire.evaluation.create-question', [
            'Evaluation_question' => $this->getEvaluationQuestion(),
            'headers' => $this->getHeaderName()
        ]);
    }
}
