<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\Organization;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class AgentsEvaluationList extends Component
{
    use LivewireAlert, WithPagination;

    // search supervisor (modal edit user group)
    public $searchSupervisors = '';
    public $supers = [];

    public $perPage = 15;

    // whether filters are applied or not 
    public $filtersApplied;


    // live search - table
    public $searchAgents = '';

    public $sortBy = 'id';
    public $sortDir = 'DESC';

    public $searchOrganizations = '';


    public $searchUserGroups = '';

    // filters 
    public $filtered_org;
    public $filtered_org_id;

    public $filtered_group_id;
    public $filtered_group;
    public $filtered_group_name;

    public $evaluation_filter;

    // possible User Groups 
    public $possible_userGroups_ids = [];
    public $possible_userGroups = [];


    // public $users;
    public $selectedUserId;
    public $selectedUserName;
    public $selectedUserRole;

    public $theIdToDelete;

    public $email;
    public $name;
    public $password;
    public $role;
    public $eventDate;
    public $userName;

    protected $paginationTheme = 'bootstrap';


    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }

    public function mount()
    {
        $this->filtersApplied = false;
    }

    public function applyFilters()
    {
        // if ($this->filtered_org_id) {
        // }
        // if ($this->filtered_group_id) {
        // }
        // if ($this->evaluation_filter) {
        // }
        
        $this->filtersApplied = true;
        $this->dispatch('closeModal');
    }

    public function filterOrg($id)
    {
        
        $this->filtered_org_id = $id;
        if($this->filtered_org_id == 27) $this->filtered_org_id = 46; //kease 27, ewa 46
        $organization = Organization::find($this->filtered_org_id);

        // Handle potential null case safely
        $this->filtered_org = $organization?->name ?? '--';

        // select the possible user groups 
        $groups = UserGroup::whereHas('organization', function ($q) {
            $q->where('id', $this->filtered_org_id);
        });

        $this->possible_userGroups_ids = $groups->pluck('id');
        $this->possible_userGroups = $groups->pluck('name');
        
        // for the code in the render 
        $this->filtered_group_name = null;
    }

    public function filterUserGroup($id)
    {
        $this->filtered_group = $id;
        $this->filtered_group_name = UserGroup::find($id)->name;
    }

    public function filterEvaluated($id)
    {
        if ($id == 'true') {
            $this->evaluation_filter = 'Evaluated Agents';
        } else if ($id == 'false') {
            $this->evaluation_filter = 'Non Evaluated Agents';
        } else {
            $this->evaluation_filter = 'All';
        }
    }

    public function clear()
    {
        $this->filtersApplied = false;
        $this->evaluation_filter = null;
        $this->filtered_group_id = null;
        $this->filtered_group = null;
        $this->filtered_group_name = null;
        $this->filtered_org_id = null;
        $this->filtered_org = null;
        $this->eventDate = null;
        $this->userName = null;
        $this->possible_userGroups_ids = [];
        $this->possible_userGroups = [];
    }

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }


    public function render()
    {
        // for quality, client, and supervisor they can only see the agents in their accounts 
        if (in_array(Auth::user()->role, [2, 5])) {
            $organizations = Organization::whereIn('id', Auth::user()->supervisorOrganizations->pluck('id'))->orderByDesc('name')->get();
        }
        if (Auth::user()->role == 6) {
            $organizations = Organization::where('id', Auth::user()->organization_id)->get();
        }
        if (Auth::user()->role == 4) {
            $organizations = Organization::where('id', Auth::user()->organization_id)->get();
        }


        return view('livewire.agents-evaluation-list', [
            'agents' => $this->filtersApplied === true ?
                (User::where('role', 4)
                    ->when(Auth::user()->role == 4, function ($query) {
                        $query->where('id', Auth::id());
                    })
                    ->when(Auth::user()->role == 6, function ($query) {
                        $query->where('organization_id', Auth::user()->organization_id);
                    })
                    ->when(Auth::user()->role == 5 || Auth::user()->role == 2, function ($query) {
                        $query->whereIn('organization_id', Auth::user()->supervisorOrganizations->pluck('id'));
                    })
                    ->when($this->filtered_group_id, function ($query) {
                        $query->where('user_group_id', $this->filtered_group);
                    })
                    ->when($this->filtered_org_id, function ($query) {
                        $query->where('organization_id', $this->filtered_org_id);
                    })
                    ->when($this->userName, function($query) {
                        $query->where('full_name', 'like', "%$this->userName%")
                            ->orWhere('username', 'like', "%$this->userName%");
                    })
                    ->whereHas('agentEvaluation', function ($qq) {
                        $qq->where('created_at', 'like', '%' . $this->eventDate . '%');
                    })
                    ->when($this->searchAgents, function ($query) {
                        $query->where(function ($q) {
                            $q->where('full_name', 'like', "%$this->searchAgents%")
                                ->orWhere('username', 'like', "%$this->searchAgents%")
                                ->orWhere('agent_id', 'like', "%$this->searchAgents%")
                                ->orWhereHas('organization', function ($q) {
                                    $q->where('name', 'like', "%$this->searchAgents%");
                                });
                        });
                    })
                    // Count the number of agent evaluations
                    ->orderBy($this->sortBy, $this->sortDir)
                    ->paginate($this->perPage))

                : (User::where('role', 4)
                    ->when(Auth::user()->role == 4, function ($query) {
                        $query->where('id', Auth::id());
                    })
                    ->when(Auth::user()->role == 5 || Auth::user()->role == 2, function ($query) {
                        $query->whereIn('organization_id', Auth::user()->supervisorOrganizations->pluck('id'));
                    })
                    ->when(Auth::user()->role == 6, function ($query) {
                        $query->where('organization_id', Auth::user()->organization_id);
                    })
                    // ->when($this->searchAgents, function ($query) { //if table real time search applied                        
                    //     $query->where(function ($q) {
                    //         $q->where('full_name', 'like', "%$this->searchAgents%")
                    //             ->orWhere('username', 'like', "%$this->searchAgents%")
                    //             ->orWhere('agent_id', 'like', "%$this->searchAgents%")
                    //             ->orWhereHas('organization', function ($q) {
                    //                 $q->where('name', 'like', "%$this->searchAgents%");
                    //             });
                    //     });
                        
                    // })
                    ->when($this->searchAgents, function ($query) { // if table real-time search applied                        
                        // Check if search term is "kease" or "Kease" and replace it with "Ewa" for the query
                        $searchTerm = strtolower($this->searchAgents) === 'kease' ? 'Ewa' : $this->searchAgents;
                    
                        $query->where(function ($q) use ($searchTerm) {
                            $q->where('full_name', 'like', "%$searchTerm%")
                                ->orWhere('username', 'like', "%$searchTerm%")
                                ->orWhere('agent_id', 'like', "%$searchTerm%")
                                ->orWhereHas('organization', function ($q) use ($searchTerm) {
                                    $q->where('name', 'like', "%$searchTerm%");
                                });
                        });
                    })

                    ->orderBy($this->sortBy, $this->sortDir)
                    ->paginate($this->perPage)),

            // to show in filters 
            // 'orgs' => Organization::orderBy('name', 'asc')->get(),
            'orgs' => $organizations,
            // 'groups' => $this->filtered_org_id ? Organization::find($this->filtered_org_id)->userGroups()->orderBy('name')->get() : UserGroup::all(),
            'groups' => $this->filtered_org_id ? Organization::find($this->filtered_org_id)->userGroups()->orderBy('name')->get() : array(),
        ]);
    }
}
