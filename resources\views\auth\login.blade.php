<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Recordings</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])


    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins');

        * {
            margin: 0;
            padding: 0;
            font-family: 'Poppins', sans-serif;
            scrollbar-color: #00a34e rgba(0, 0, 0, .3);
        }

        body {
            background-color: #f8fafc;
            /* background-image: url('assets/images/group-49.png'); */
            background-repeat: repeat-y;
            background-position: right TOP;
            background-size: 32%;
            height: 100vh;
            box-sizing: border-box;
        }

        .login-lable-h1 {
            position: relative;

        }


        .login-lable-h1::before {
            content: "";
            display: block;
            width: 36%;
            height: 2px;
            position: absolute;
            background: #e0e0e0;
            left: 0;
            top: 50%;


        }

        .login-lable-h1::after {

            content: "";
            display: block;
            width: 36%;
            height: 2px;
            position: absolute;
            background: #e0e0e0;
            right: 0;
            top: 50%;


        }

        .login-lable-h1.reset::before {
            width: 22%;

        }

        .login-lable-h1.reset::after {
            width: 22%;
        }

        .login-form {
            background-color: white;
        }

        /*input {*/
        /*    color: #e0e0e0;*/
        /*    border: none;*/
        /*    border-bottom: 2px solid #e0e0e0;*/
        /*    border-radius: 0;*/
        /*    height: 20px;*/
        /*}*/

        label {
            /*color: #d5d5d5;*/

        }

        input::placeholder {
            color: #9a9a9a !important;
        }


        input:focus::placeholder {
            border-color: green !important;
        }

        input[type=password]:focus {


            border-color: green !important;


        }

        input[type=email]:focus {


            border-color: green !important;


        }

        input[type=text]:focus {


            border-color: green !important;


        }

        /*input[type=checkbox] {*/
        /*    border: 1px solid #00a34e !important;*/
        /*}*/

        /*input[type=value] {*/
        /*    border: 1px solid #00a34e !important;*/
        /*    background-color: white !important;*/
        /*}*/

        .anyhelp-section {
            place-content: center;
        }

        .anyhelp-section .anyhelp-section-span {
            background-color: #00a34e;
        }

        .under-button {
            border: 1px solid #d5d5d5 !important;
            color: #d5d5d5 !important;
            width: 59% !important;
            border-radius: 0 9px 9px 0 !important;
        }

        /*bootstrap reset*/
        .form-control {
            background-color: white !important;
        }

        input:-webkit-autofill {
            -webkit-text-fill-color: black;
            -webkit-box-shadow: 0 0 0 1000px white inset;
        }

        input:-moz-autofill {
            -moz-text-fill-color: black;
            -moz-box-shadow: 0 0 0 1000px white inset;
        }

        input:-o-autofill {
            -o-text-fill-color: black;
            -o-box-shadow: 0 0 0 1000px white inset;
        }

        input:-khtml-autofill {
            -khtml-text-fill-color: black;
            -khtml-box-shadow: 0 0 0px 1000px white inset;
        }

        input:focus:-webkit-autofill {
            -webkit-text-fill-color: black;
            -webkit-box-shadow: 0 0 0 1000px white inset;
        }

        input:focus:-moz-autofill {
            -moz-text-fill-color: black;
            -moz-box-shadow: 0 0 0 1000px white inset;
        }

        input:focus:-o-autofill {
            -o-text-fill-color: black;
            -o-box-shadow: 0 0 0 1000px white inset;
        }

        input:focus:-khtml-autofill {
            -khtml-text-fill-color: black;
            -khtml-box-shadow: 0 0 0px 1000px white inset;
        }

        @media (min-width: 1200px) {
            #home-container {
                margin-top: 0%;
            }
        }
        .form-switch {
            display: flex;
            /*align-items: center;*/
        }

        .switch-input {
            position: absolute;
            opacity: 0;
        }

        .switch-input + .switch-label {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
            background: #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .switch-input + .switch-label:before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .switch-input:checked + .switch-label {
            background: #28a745;
        }

        .switch-input:checked + .switch-label:before {
            transform: translateX(20px);
        }

        .form-check-label {
            /*margin-left: 10px;*/
            font-size: 14px;
        }
    </style>

</head>

<body style="
    overflow: hidden;
">
    <div class="login-section px-4 pt-1 pb-0" style="height: 83vh;">

        <div class="container">
{{--            <div class="header py-4">--}}
{{--                <h4 class="text-center mb-3" style="font-weight: 600;word-spacing: 2px;letter-spacing: 1px;"># Welcome to our recording system at <strong><span style="font-size: 27px;color: #00a34e;">Extensya</span></strong></h4>--}}
{{--            </div>--}}

            <div class="row align-items-center px-5 p-1">
                        <div class="col-lg-4 offset-lg-1 mx-auto d-flex justify-content-center" id="home-container" style="height: 98vh;align-items: center;">

                            <div class="col-xl-12 col-12">
                                <form class="border rounded-4 pb-3 shadow login-form" method="POST" action="{{ route('login') }}" style="width:100%;margin-left:15%;padding: 8px;">
                                    @csrf
                                    <div class=" pt-4 login-lable mb-4">

                                        <div class="row">
                                            <div class="col-4" >
                                                <img src="{{asset('assets/images/logo-login.png')}}" alt="extensya" width="80px" style="margin: 0 48%;">
                                            </div>
                                            <div class="col-8" style="padding-left: 25px">
                                                <h3 style="font-weight: bold" >Extensya</h3>
                                                <h6>I-Sentiment System</h6>
                                            </div>
                                        </div>


                                    </div>

                                    <div class=" pt-5 pb-2 login-lable mt-4 mb-2">
                                        <h3 class="text-center" style="font-weight: bold"> <strong>Nice to see you again</strong></h3>
                                    </div>
                                    <div class="px-3 px-md-4 ">
                                        <div class=" mb-2">
                                            <label for="floatingInput" class="form-label form-label px-1">Username</label>
                                            <input name="agent_id" value="{{ old('agent_id') }}" placeholder="Enter Username" type="text" class="form-control rounded-3 shadow mb-4" id="floatingInput" style="height: 48px;">
                                            <small class="text-danger fw-bold">{{ session('lock_error') }}</small>
                                            @if (session('status'))
                                                <small class="text-danger fw-bold">{{ session('status') }}</small>
                                            @endif
                                            @error('agent_id')
                                            <span class="text-danger" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                        <div class=" mb-4" style="position: relative">
                                            <label for="floatingInput" class="form-label form-label px-1">Password</label>
                                            <input class="form-control rounded-3  shadow" placeholder="Enter Password" type="password" name="password" id="password" style="height: 48px;">
                                            <i class="fa-solid fa-eye" style="position: absolute;top:48px;right: 5px;color: #e0e0e0;cursor: pointer" id="viewPassword"></i>
                                            @error('password')
                                            <span class="text-danger" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                        <div class="form-check mb-3" style="padding: 0; font-size: 13px;">
                                            <div class="form-check form-switch" style="padding: 0;display: inline">
                                                <input class="form-check-input switch-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                                                <label class="form-check-label switch-label" for="remember" style="padding: 0;">

                                                </label>
                                                <span>
{{--                                                     {{ __('Remember Me') }}--}}
                                                </span>



                                            </div>
                                            <div style="float: right">
                                                @if (Route::has('password.request'))
                                                    <a href="{{ route('password.request') }}" aria-describedby="basic-addon1" style="text-decoration: none;color: #00a34e">{{ __('Forgot Password?') }}</a>
                                                @endif
                                            </div>


{{--                                                <div class="input-group mb-2 anyhelp-section">--}}
{{--                                                    <span class="input-group-text anyhelp-section-span" id="basic-addon1"><img src="{{ asset('assets/images/<EMAIL>') }}" alt="" width="20px"></span>--}}
{{--                                                </div>--}}
                                        </div>

                                        <div class="col-12 text-center mt-5 mb-5">
                                            <button class="w-100 btn  btn-success" type="submit" style="background:#00a34e">Login</button>
                                        </div>
                                    </div>
{{--                                    <div class="form-floating py-3 login-lable">--}}
{{--                                        <h6 class="login-lable-h1 text-body-tertiary text-center"><span>Any Help ?</span></h5>--}}
{{--                                    </div>--}}
{{--                                    @if (Route::has('password.request'))--}}
{{--                                        <div class="input-group mb-2 anyhelp-section">--}}
{{--                                            <span class="input-group-text anyhelp-section-span" id="basic-addon1"><img src="{{ asset('assets/images/<EMAIL>') }}" alt="" width="20px"></span>--}}
{{--                                            <a href="{{ route('password.request') }}" aria-describedby="basic-addon1" class="btn btn-sm under-button">{{ __('Forgot Your Password?') }}</a>--}}
{{--                                        </div>--}}
{{--                                    @endif--}}
                                </form>
                            </div>
                        </div>
                        <div class="col-lg-8 text-center  text-md-start">
                            <div class="text-center">
                                <img src="{{ asset('assets/images/Log in 1.png') }}" class="text-center" alt="" width="100%" style="width: 53%;overflow: hidden;position: absolute;top: 6px;ri;right: 126px;ckground: none;">
{{--                                <img src="{{ asset('assets/images/giphy.gif') }}" class="text-center" alt="" width="100%">--}}
                            </div>
                        </div>


                    </div>


        </div>
    </div>
</body>

<script>
    const passwordField = document.getElementById('password');
    const viewPasswordIcon = document.getElementById('viewPassword');

    viewPasswordIcon.addEventListener('click', function() {
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            viewPasswordIcon.classList.remove('fa-eye');
            viewPasswordIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            viewPasswordIcon.classList.remove('fa-eye-slash');
            viewPasswordIcon.classList.add('fa-eye');
        }
    });
</script>

</html>
