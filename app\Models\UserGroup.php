<?php

namespace App\Models;

use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserGroup extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $table = 'user_groups';

    // agents 
    public function users()
    {
        return $this->hasMany(User::class, 'user_group_id');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function supervisors()
    {
        return $this->belongsToMany(User::class, 'supervisor_group')->where('role', 2);
    }

    public function admins()
    {
        return $this->belongsToMany(User::class, 'supervisor_group')->where('role', 1);
    }

    public function callInteraction()
    {
        return $this->hasMany(Interaction::class);
    }

    public function itUsers()
    {
        return $this->belongsToMany(User::class, 'supervisor_group');
    }

    // for the table in user groups page 
    public function scopeSearch($q, $value)
    {
        $q->where('name', 'like', "%$value%")->orWhereHas('organization', function ($query) use ($value) {
            $query->where('name', 'like', "%$value%");
        });
    }
}
