<div>
    <style>
        /* Positive Circle */
        .positive {
            background: conic-gradient(#40788b 0% {{$result['positive']}}%, #e5e7eb {{$result['positive']}}% 100%);
        }

        /* Neutral Circle */
        .neutral {
            background: conic-gradient(#03a24c 0% {{$result['neutral']}}%, #e5e7eb {{$result['neutral']}}% 100%);
        }

        /* Negative Circle */
        .negative {
            background: conic-gradient(#ef4444 0% {{$result['negative']}}%, #e5e7eb {{$result['negative']}}% 100%);
        }
        .thead {
            height: 50px;
            vertical-align: middle;
        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        table td {
            border-bottom: none !important;
        }

        table {
            position: relative;
        }
        thead th {
            background-color: #40798C !important;
            color: #FFFFFF !important;
            /* font-size: medium; */
            font-size: small;
            height: 0rem;
        }

        tbody td {
            /* font-size: medium !important; */
            font-size: small !important;
            height: 0rem;
            font-weight: 600;
            border-bottom: none;
        }

        thead {
            height: 1rem !important;
        }
        tbody {
            height: 1rem !important;
        }
    </style>

    <div class="row justify-content-center" style="margin: 0px 20px 0px 20px;">
        <div class="col-4 justify-content-center" style="text-align: -webkit-center;cursor: pointer" data-bs-toggle="modal" data-bs-target="#exampleModal1">
            <div class="progress-circle positive">
                <span class="percentage">{{$result['positive']}} %</span>
            </div>
            <div class="label">Positive</div>
        </div>
        <div class="col-4 justify-content-center" style="text-align: -webkit-center;cursor: pointer" data-bs-toggle="modal" data-bs-target="#exampleModal2">
            <div class="progress-circle neutral">
                <span class="percentage">{{$result['neutral']}} %</span>
            </div>
            <div class="label">Neutral</div>
        </div>
        <div class="col-4 justify-content-center" style="text-align: -webkit-center;cursor: pointer" data-bs-toggle="modal" data-bs-target="#exampleModal3">
            <div class="progress-circle negative">
                <span class="percentage">{{$result['negative']}} %</span>
            </div>
            <div class="label">Negative</div>
        </div>
    </div>


    <!-- Modal -->
    <div class="modal fade" id="exampleModal1" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-user-check" style="font-size: 25px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 25px;">Positive Classification</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-bordered">
                                   <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 0; z-index: 4;">

                        <tr style="text-align: -webkit-center;">
                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Text</th>
{{--                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Classification</th>--}}
                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Score</th>



                        </tr>

                        </thead>
                        <tbody>
                        @forelse($classificationPositive as $classification)
                            <tr style="text-align: -webkit-center;">

                                <td style="text-align: -webkit-center;">{{$classification->text}}</td>
{{--                                <td style="text-align: -webkit-center;">{{\Illuminate\Support\Str::upper($classification->classification)}}</td>--}}
                                <td style="text-align: -webkit-center;">%{{round($classification->score, 1)}}</td>


                            </tr>
                        @empty
                            <tr class="text-center">
                                <td colspan="3"  class="text-center">No classification available</td>
                            </tr>
                        @endforelse

                        </tbody>
                    </table>

                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" id="exampleModal2" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-user-check" style="font-size: 25px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 25px;">Neutral Classification</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-bordered">
                                   <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 0; z-index: 4;">

                        <tr style="text-align: -webkit-center;">
{{--                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Classification</th>--}}
                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Text</th>
                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Score</th>


                        </tr>

                        </thead>
                        <tbody>
                        @forelse($classificationNeutral as $classification)
                            <tr style="text-align: -webkit-center;">


{{--                                <td style="text-align: -webkit-center;">{{\Illuminate\Support\Str::upper($classification->classification)}}</td>--}}
                                <td style="text-align: -webkit-center;">{{$classification->text}}</td>
                                <td style="text-align: -webkit-center;">%{{round($classification->score, 1)}}</td>

                            </tr>
                        @empty
                            <tr  class="text-center">
                                <td  class="text-center" colspan="3">No classification available</td>
                            </tr>
                        @endforelse

                        </tbody>
                    </table>

                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" id="exampleModal3" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-user-check" style="font-size: 25px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 25px;">Negative Classification</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-bordered">
                                   <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 0; z-index: 4;">

                        <tr style="text-align: -webkit-center;">
{{--                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Classification</th>--}}
                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Text</th>
                            <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white !important;">Score</th>


                        </tr>

                        </thead>
                        <tbody>
                        @forelse($classificationNegative as $classification)
                            <tr style="text-align: -webkit-center;">


{{--                                <td style="text-align: -webkit-center;">{{\Illuminate\Support\Str::upper($classification->classification)}}</td>--}}
                                <td style="text-align: -webkit-center;">{{$classification->text}}</td>
                                <td style="text-align: -webkit-center;">%{{round($classification->score, 1)}}</td>

                            </tr>
                        @empty
                            <tr  class="text-center">
                                <td  class="text-center" colspan="3">No classification available</td>
                            </tr>
                        @endforelse

                        </tbody>
                    </table>

                </div>

            </div>
        </div>
    </div>
</div>
