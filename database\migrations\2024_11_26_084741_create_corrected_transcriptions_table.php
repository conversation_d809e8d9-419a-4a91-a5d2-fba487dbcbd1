<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('corrected_transcriptions', function (Blueprint $table) {
            $table->id();
            $table->string('call_id');
            $table->string('language_detected');
            $table->string('original_content');
            $table->string('corrected_content');
            $table->json('corrections');
            $table->integer('calls_transcription_id');
            $table->foreign('calls_transcription_id')
                ->references('id')->on('calls_transcription')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('corrected_transcriptions');
    }
};
