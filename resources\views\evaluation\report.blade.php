@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Report Page')

{{-- Style Section --}}
@section('style')

    <style>
        /* .d-block {
                    width: 60px;
                    height: 59px;
                } */

        .div-table {
            height: fit-content !important;
            max-height: 90vh !important;
        }

        td {
            white-space: nowrap;
        }

        .fa-magnifying-glass {
            position: absolute;
            left: 2%;
            top: 25%;
        }

        .p-10 {
            padding: 10px;
        }

        .b-radius-10 {
            border-radius: 10px;
        }

        .header-field {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .b-radius-10 {
            border-radius: 10px;
        }

        :focus-visible {
            outline: none;
        }

        .div-search {
            padding-left: 0px;
        }

        .filters-block {
            display: inline-flex;
            width: 36%;
            justify-content: end;
            float: right;
            position: absolute;
            right: 30px;
            padding-top: 4px;
        }

        .mt-10 {
            margin-right: 12px;
        }

        .btn-color {
            height: 43px;
            background-color: #00a34e;
            border-color: #00a34e;
            width: 100%;
        }

        .btn-dark {
            height: 43px;
        }

        .modal-button-search {
            position: relative;
            /* Higher z-index value to bring the button to the front */
        }

        .modal-content-search {
            position: absolute !important;
            top: 125% !important;
            left: -9% !important;
            background-color: #fff !important;
            box-shadow: 0 0.125rem 7.25rem #00000013 !important;
            width: 500px;
            border-radius: 10px !important;
            z-index: 1 !important;
            display: none;
        }

        .style-btn-search {
            margin: 5px;
            padding: 0px;
            width: 100%;
            background-color: #00a34e;
            border-color: #00a34e;
        }

        .input-search {
            width: 100%;
            border: var(--bs-border-width) var(--bs-border-style) #ffffff !important;
            vertical-align: sub;
            margin-top: 5px;
            padding: 0px;
        }

        .search-icon {
            margin-top: 5px;
        }

        .style-border {
            border-radius: 10px;
            border: 1px solid #dddddd;
        }

        .from-to-date {
            border: 1px solid #d7d7d7;
            height: 100%;
            margin: 0px;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            background-color: #eceff7;
            cursor: pointer;
        }

        .date-field {
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 0.0rem .5rem #00000026 !important;
            height: 100%;
        }

        .parent-sections {
            height: 70vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: 1.5%;
            margin-bottom: 3%;
        }

        .section-one {
            width: 100%;
            height: 100%;
        }

        .div-table {
            /* border: 1px solid #d0caca; */
            border-radius: 0px;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        .table {
            margin-bottom: 0;
            text-align: center;
        }

        th {
            border-bottom-color: currentColor;
            vertical-align: middle;
        }

        .thead {
            height: 50px;
            vertical-align: middle;

        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            font-size: initial;
        }

        .table tbody tr td {
            background-color: white !important;
            vertical-align: middle;
            font-size: small;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: #eff3f4 !important;
        }

        .shadow {
            box-shadow: 0 0.1rem .5rem #00000026 !important;
        }

        .filter-header {
            height: 10%;
            width: 100%;
            padding: 4%;
        }

        hr {
            color: #00a34e;
            margin: 0;
        }

        .filter-body {
            padding: 5% 7%;
            height: 70%;
            overflow: auto;
        }

        .category {
            background-color: #eff3f4 !important;
            width: fit-content !important;
            border: 1px solid #e3e3e3;
            border-radius: 40px;
            padding: 1%;
            display: flex;
            flex-direction: row;
            margin-right: 5%;
            margin-bottom: 4%;
            cursor: pointer;
            background-color: white;
            display: flex;
            flex-direction: row;
        }

        .spanText {
            margin-left: 10px !important;
        }

        .category-exc1 {}

        .category-exc2 {
            width: fit-content !important;
        }

        .text-box {
            margin-left: 20px;
        }

        .main-filter {
            display: flex;
            flex-direction: row;
        }

        .main-filter {
            margin-bottom: 7%;
        }

        .custom-select select {

            border-radius: 0px;
            cursor: pointer;
        }

        .arrow-icon {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            pointer-events: none;
            fill: #00a34e;
            width: 36px;
            height: 32px;
        }

        .field-type {
            padding: 10% 8%;
        }

        .filter-footer {
            padding: 0% 20%;
        }

        .from-date {
            margin-right: 3%;
        }

        .form-control,
        .form-select {
            background-color: #eff3f4 !important;
            border: none !important;
            height: 40px;
        }

        label {
            color: #40798c;
            font-size: 17px;
        }

        /*
                    pagination styles
                            */
        #searchInput {
            height: 2.8rem !important;
            width: 100% !important;
            /* Increase the height for a larger input */
            padding-left: 2.5rem !important;
            /* Increase padding for better spacing */
            border: none !important;
            /* Slightly darker border */
            border-radius: 0.5rem;
            /* Rounded corners */
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow */
            transition: box-shadow 0.3s ease, border-color 0.3s ease;
            /* Smooth transition */
            font-size: 1.2rem;
            /* Slightly larger text size */
            background-position: left 0.5rem center;
            /* Icon positioning */
        }

        /* Focus styles */
        #searchInput:focus {
            outline: none;
            /* Remove default outline */
            box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
            /* Larger shadow on focus */
            border-color: rgba(0, 0, 0, 0.3);
            /* Slightly darker border on focus */
        }

        /* Placeholder styling */
        #searchInput::placeholder {
            font-family: inherit;
            /* Use inherited font style */
            color: #01A44F;
            /* Green placeholder text */
            font-size: 1.2rem;
            /* Match placeholder size with input text */
        }

        .main-buttons-container button {
            height: 2.9rem;
            font-size: 15px;
        }

        .main-buttons-container button:hover {
            background-color: #018F3E !important;
        }

        /* pagination  */
        ul.pagination {
            gap: 0;
        }

        ul.pagination li button,
        ul.pagination li span {
            padding: 0.7rem;
            padding-top: 0.4rem;
            padding-bottom: 0.4rem;
        }

        ul.pagination li button:hover {
            background-color: rgb(196, 183, 183) !important;
        }

        ul.pagination>li>button,
        ul.pagination>li>span {
            color: black !important;
            font-weight: 600 !important;
            background-color: white;
        }

        .page-item span,
        .page-item button {
            border-radius: 0.7rem !important;
        }

        .page-item.active span,
        .page-item.active button {
            border-radius: 0.5rem !important;
        }

        .page-item.active>span {
            background-color: #00a34e !important;
            color: white !important;
        }

        div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
            font-size: 0.9rem;
        }

        div.tab-pane label {
            font-weight: 600 !important;
        }

        div.tab-pane hr {
            display: none;
        }

        .table tr th {
            font-size: small !important;
            font-weight: 600;
        }

        table td {
            font-size: small !important;
            border-bottom: none;
        }

        /*
                end pagination styles
                */
    </style>




@endsection

{{-- Content Section --}}
@section('content')
    <div class="container-fluid">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">

        <livewire:evaluation.report :reportType="$reportType" />

    </div>
@endsection

{{-- Script Section --}}

<script type="module">
    window.addEventListener('export-excel', event => {
        document.getElementById('exportButton').click()
    });
    window.addEventListener('close-modal', event => {
        document.getElementById('closeModal').click()
    });

    document.getElementById("exportButton").addEventListener("click", function() {
        // Get the table by ID
        var table = document.getElementById("dataTable");

        // Initialize an empty array to store table data
        var data = [];

        // Iterate through table rows
        for (var i = 0, row; row = table.rows[i]; i++) {
            var rowData = [];

            // Iterate through each cell in the row
            for (var j = 0, col; col = row.cells[j]; j++) {
                rowData.push(col.innerText);
            }

            // Add row data to the data array
            data.push(rowData);
        }

        // Convert data to Excel format
        var csvContent = "data:text/csv;charset=utf-8,";
        data.forEach(function(rowArray) {
            var row = rowArray.join(",");
            csvContent += row + "\r\n";
        });

        // Create a link element and trigger the download
        var encodedUri = encodeURI(csvContent);
        var link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "report.csv");
        document.body.appendChild(link); // Required for Firefox
        link.click();
    });

    $(document).ready(function() {
        var searchButton = $('#search_button');
        var search = $('#search');

        searchButton.click(function() {
            if (search.css('display') === 'none') {
                search.css('display', 'block');
            } else {
                search.css('display', 'none');
            }
        });
    });


    window.addEventListener('close-modal', event => {
        document.getElementById('closeModal').click()
    });

    document.addEventListener("DOMContentLoaded", function() {
        const fromDateText = document.getElementById("fromDateText");
        const fromDateInput = document.getElementById("fromDateInput");

        // Check if input field has a value on page load
        if (fromDateInput.value.trim() !== '') {
            fromDateInput.style.display = '';
            fromDateText.style.display = 'none';
        } else {
            fromDateInput.style.display = 'none';
            fromDateText.style.display = '';
        }

        fromDateText.addEventListener("click", function() {
            fromDateInput.style.display = '';
            fromDateInput.focus(); // Automatically focus on the input
            fromDateText.style.display = 'none';
        });

        fromDateText.addEventListener("focus", function() {
            fromDateInput.style.display = '';
            fromDateInput.focus(); // Automatically focus on the input
            fromDateText.style.display = 'none';
        });

        fromDateInput.addEventListener("blur", function() {
            if (fromDateInput.value.trim() === '') {
                fromDateInput.style.display = 'none';
                fromDateText.style.display = '';
            }
        });
    });

    document.addEventListener("DOMContentLoaded", function() {
        const toDateText = document.getElementById("toDateText");
        const toDateInput = document.getElementById("toDateInput");

        // Check if input field has a value on page load
        if (toDateInput.value.trim() !== '') {
            toDateInput.style.display = '';
            toDateText.style.display = 'none';
        } else {
            toDateInput.style.display = 'none';
            toDateText.style.display = '';
        }

        toDateText.addEventListener("click", function() {
            toDateInput.style.display = '';
            toDateInput.focus(); // Automatically focus on the input
            toDateText.style.display = 'none';
        });

        toDateText.addEventListener("focus", function() {
            toDateInput.style.display = '';
            toDateInput.focus(); // Automatically focus on the input
            toDateText.style.display = 'none';
        });

        toDateInput.addEventListener("blur", function() {
            if (toDateInput.value.trim() === '') {
                toDateInput.style.display = 'none';
                toDateText.style.display = '';
            }
        });
    });
</script>
