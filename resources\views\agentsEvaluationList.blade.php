@extends('layouts.app')

@section('title', 'Agents Evaluation List')

@section('style')
    <style>
        .dropdown-menu::-webkit-scrollbar {
            display: none
        }

        input {
            border: solid 1px #b6b6b6 !important;
            /* border-radius: 0.6rem !important; */
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .list-group-item:hover {
            color: white;
            background-color: #00a34e;
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 0 !important;
        }

        .table th,
        .table td {
            font-size: small !important;
            height: 1rem !important;
            font-weight: 600 !important;
            white-space: nowrap;
        }

        .table td:first-child {
            padding-left: 3rem;
        }

        #dropDownList {
            border-radius: 7px !important;
        }

        .dropdown-toggle::after {
            vertical-align: top !important;
        }

        input::placeholder {
            font-size: 0.85rem;
        }

        .dropdown-menu.w-100.show {
            transform: translate3d(0px, 39.2px, 0px) !important;
        }


        /* pagination  */
        ul.pagination {
            gap: 0;
        }

        ul.pagination li button,
        ul.pagination li span {
            padding: 0.7rem;
            padding-top: 0.4rem;
            padding-bottom: 0.4rem;
        }

        ul.pagination li button:hover {
            background-color: rgb(196, 183, 183) !important;
        }

        ul.pagination>li>button,
        ul.pagination>li>span {
            color: black !important;
            font-weight: 600 !important;
            background-color: white;
        }

        .page-item span,
        .page-item button {
            border-radius: 0.7rem !important;
        }

        .page-item.active span,
        .page-item.active button {
            border-radius: 0.5rem !important;
        }

        .page-item.active>span {
            background-color: #00a34e !important;
            color: white !important;
        }

        div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
            font-size: 0.9rem;
        }

        div.tab-pane label {
            font-weight: 600 !important;
        }

        div.tab-pane hr {
            display: none;
        }

        /* modal  */
        #add-multi .modal-header {
            background-color: transparent !important;
        }

        .modal-title {
            color: black;
            font-weight: 600;
        }

        legend {
            color: #40798C !important;
        }

        .tab-button {
            border: none !important;
            background-color: white !important;
            color: black !important;
            font-weight: 500;
            border: 1px solid rgb(235, 226, 226) !important;
            border-radius: 3rem !important;
        }

        .tab-button.active {
            color: #00a34e !important;
        }

        .modal-content,
        .modal-header {
            background-color: white !important;
        }

        .modal-header {
            border: none !important;
        }

        .modal-apply-close-btns {
            width: 10rem;
        }

        .modal-footer {
            border: none !important;
        }

        #searchInput {
            height: 2.5rem !important;
            /*width: 100% !important;*/
            /* Increase the height for a larger input */
            padding-left: 2.5rem !important;
            /* Increase padding for better spacing */
            border: none !important;
            /* Slightly darker border */
            border-radius: 0.5rem;
            /* Rounded corners */
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow */
            transition: box-shadow 0.3s ease, border-color 0.3s ease;
            /* Smooth transition */
            font-size: 1rem;
            /* Slightly larger text size */
            background-position: left 0.5rem center;
            /* Icon positioning */
            background-color: white !important;
        }

        /* Focus styles */
        #searchInput:focus {
            outline: none;
            /* Remove default outline */
            box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
            /* Larger shadow on focus */
            border-color: rgba(0, 0, 0, 0.3);
            /* Slightly darker border on focus */
        }

        /* Placeholder styling */
        #searchInput::placeholder {
            font-family: inherit;
            /* Use inherited font style */
            color: #01A44F;
            /* Green placeholder text */
            font-size: 1rem;
            /* Match placeholder size with input text */
        }

        .form-control,
        .form-select,
        .dropdown-toggle-style {
            background-color: #eff3f4 !important;
            border: none !important;
            height: 40px;
        }
        .main-buttons-container button {
            height: 2.9rem;
            font-size: 15px;
        }

        .main-buttons-container button:hover {
            background-color: #018F3E !important;
        }

        thead th {
            background-color: #40798C !important;
            color: #FFFFFF !important;
            font-size: 10px !important;
        }

        tbody td {
            font-size: larger !important;
            height: 4.5rem;
            font-weight: 600;
            border-bottom: none !important;
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 1rem !important;
        }

        .page-link[aria-label="« Previous"],
        .page-link[aria-label="Next »"] {
            padding: 0.8rem !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }
         .color {
            color: #40798c !important;
        }
        .bg-color {
            background-color: #eff3f4 !important;
        }
        .parent-sections {
            /* height: 90vh !important; */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: 1.5%;
            margin-bottom: 3%;
        }


        .thead {
            height: 50px;
            vertical-align: middle;
        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        table td {
            border-bottom: none !important;
        }

        table {
            position: relative;
        }

        tr th {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        tr td {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }
    </style>
@endsection


@section('content')
    @livewire('agents-evaluation-list')
@endsection

<script>
    window.addEventListener('closeModal', () => {
        document.querySelector('#closeModal').click();
    });
</script>
