<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationSubmission extends Model
{
    use HasFactory;

    public $guarded = [];

    public function submissionAnswers()
    {
        return $this->hasMany(EvaluationSubmissionAnswer::class);
    }

    public function evaluation()
    {
        return $this->belongsTo(Evaluation::class,'evaluation_id');
    }

    public function evaluator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function agent()
    {
        return $this->belongsTo(User::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class,'user_id');
    }
  
    public function Interaction()
    {
        return $this->belongsTo(Interaction::class,'referenceID'); //agent has role 2
    }
    public function callInteraction()
    {
        return $this->belongsTo(Interaction::class); //agent has role 2
    }
   
}
