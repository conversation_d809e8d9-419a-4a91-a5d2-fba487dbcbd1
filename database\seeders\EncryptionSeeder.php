<?php

namespace Database\Seeders;

use App\Models\Encryption;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EncryptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Encryption::create([
            'type'=>'call',
            'status'=>0
        ]);
        Encryption::create([
            'type'=>'video',
            'status'=>0
        ]);
    }
}
