@extends('layouts.app')

@section('title', 'Recording Playback')

@section('style')
    <style>
        input {
            border: solid 1px #b6b6b6 !important;
            border-radius: 0.6rem !important;
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .play-button:hover {
            color: #00a34e !important;
        }

        #playButton,
        #pauseButton,
        #openSpeedSelectorButton {
            font-size: 16px;
            cursor: pointer;
            width: 3rem;
            height: 3rem;
        }

        #openSpeedSelectorButton {
            font-size: 16px;
            cursor: pointer;
        }

        #playButton {
            background-color: #abc959;
            color: white;
            border: none;
        }

        #pauseButton {
            background-color: #02a34e;
            color: white;
            border: none;
        }

        #openSpeedSelectorButton {
            background-color: #2196F3;
            color: white;
            border: none;
        }

        #speedSelectorModal {
            display: inline;
        }

        .parent-btn {
            margin-top: 0.5rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        #duration {
            float: right;
            background-color: #c6d64e;
            border-radius: 0.5rem;
            padding: 0.1rem 0.3rem;
            font-weight: 400;
            color: black;
        }

        #time {
            background-color: #03a34d;
            border-radius: 0.5rem;
            padding: 0.1rem 0.3rem;
            font-weight: 400;
            color: white;
        }

        #loadingMessage {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: inline-block;
            border-top: 4px solid #FFF;
            border-right: 4px solid transparent;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
        }

        #loadingMessage::after {
            content: '';
            box-sizing: border-box;
            position: absolute;
            /* left: 0; */
            /* top: 0; */
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border-left: 4px solid #c6d64e;
            border-bottom: 4px solid transparent;
            animation: rotateLoader 0.5s linear infinite reverse;
        }

        @keyframes rotateLoader {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .nav-link {
            border-radius: var(--bs-nav-tabs-border-radius) !important;
            color: #40798c !important;

        }

        .nav-tabs .nav-link.active,
        .nav-tabs .nav-item.show .nav-link {
            border-radius: var(--bs-nav-tabs-border-radius) !important;
            color: white !important;
            background: #40798c !important;
            padding-bottom: 5%;
            border-radius: 12px !important;


        }

        .conv-text-container {
            display: flex;
        }

        .conv-text-container-agent {

            justify-content: flex-end;

        }

        .conv-text-container-customer {
            justify-content: flex-start;
        }

        .conv-text {

            /*  width: fit-content; */
            font-weight: bold;
            border-radius: 12px;
            font-size: 1.1rem !important;
            color: #38798b;
        }

        .conv-text-container-customer>.conv-text {

            background: #f3f8ff;
            border: 1px solid #f3f8ff;
        }

        .conv-text-container-agent>.conv-text {

            background: #ffeeee;
            border: 1px solid #fbf3fe;
        }

        .conv-controllers {
            /* background: #aliceblue; */
            width: fit-content;
            border: 1px solid #fbf3fe;
            border-radius: 12px;
            font-size: 0.95rem !important;
            border: none !important;
        }

        .conv-row {
            display: flex;
            align-items: baseline;
        }

        .transcript-header {
            background-color: #ececec;
            /*border-top-left-radius: 1.2rem;*/
            /*border-top-right-radius: 1.2rem;*/
            color: blue;
            font-weight: bolder;
        }

        .tab-pane .transcription-container {
            max-height: 54vh;
            overflow-x: hidden;
            overflow-y: scroll;
            border-right: 3px solid #eff3f4;
        }

        .events-container {
            max-height: 54vh;
            overflow-x: hidden;
            overflow-y: scroll;
            border-left: 3px solid #eff3f4;
        }

        .events-div {
            display: flex;
            flex-direction: row;
            align-items: center;

            gap: 3%;

        }

        .tab-content {
            box-shadow: #eff3f4 0px 0px 3px 3px;
        }
/*
        #search-wrapper {
            display: flex;
            border: 1px solid rgba(0, 0, 0, 0.276);
            align-items: stretch;
            border-radius: 0.6rem;
            background-color: #fff;
            overflow: hidden;
            max-width: 50%;
            box-shadow: 2px 1px 5px 1px rgba(0, 0, 0, 0.273);

        } */

        #search {
            border: none !important;
            width: 100%;
            font-size: 15px;
        }

        #search:focus {
            outline: none;
        }

        .search-icon {
            margin: 10px;
            /*             color: rgba(0, 0, 0, 0.564);
     */
            color: #40798c !important;
            font-size: larger !important;
        }

        #search-button {
            border: none;
            cursor: pointer;
            color: #fff;
            background-color: #1dbf73;
            padding: 0px 10px;
        }

        #search::-webkit-input-placeholder {
            /* Chrome, Safari, Edge */
            color:  #40798c !important;;
        }

        #search::-moz-placeholder {
            /* Firefox 19+ */
            color:  #40798c !important;;
        }

        #search:-ms-input-placeholder {
            /* IE 10+ */
            color:  #40798c !important;;
        }

        #search::-ms-input-placeholder {
            /* Microsoft Edge */
            color:  #40798c !important;;
        }

        #search::placeholder {
            /* Standard Syntax */
            color:  #40798c !important;;
        }

        .events-div>div>h5 {
            text-decoration: underline;
        }

        .events-div>div>h4 {

            font-weight: bold;
        }

        .all-tabs {
            padding-left: 0 !important;
            width: 100% !important;
        }



        /* player  */
        .audio-card {
            display: flex;
            align-items: center;
            border-radius: 8px;
            border: none;
            padding: 10px 15px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
            max-width: 700px;
            max-height: 40vh;
        }

        /* Left Section: Avatar */
        .audio-card-left {
            padding: 1rem;
            flex: 0 0 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .avatar img {
            width: 8rem;
            height: 8rem;
            padding: 1rem;
            border-radius: 50%;
            background-color: #d8d8f6;
        }

        /* Content Section */
        .audio-card-content {
            flex: 1;
        }

        /* Header */
        .audio-card-header {
            display: flex;
            flex-direction: column;
            margin-bottom: 8px;
        }

        .audio-card-header .date-time {
            color: #40798C;
            /* Muted text */
            font-size: 14px;
            margin-bottom: 2px;
        }

        .audio-card-header .name-id {
            font-weight: 1000;
            letter-spacing: 1px !important;
            color: #333;
        }

        .audio-card-header .call-id {
            font-size: 0.9rem;
            color: #969696;
        }

        /* Controls Section */
        .audio-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Rewind Button */
        .rewind {
            background-color: transparent;
            border: none;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .forward {
            background-color: transparent;
            border: none;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        /* Timeline */
        /* Timeline */
        .timeline {
            flex: 1;
            height: 6px;
            background: #333;
            /* Black background for timeline */
            border-radius: 3px;
            position: relative;
            /* Important for positioning .progress inside */
            margin: 0 10px;
            overflow: hidden;
            cursor: pointer;
            /* Ensures .progress stays within bounds */
        }

        /* Progress Bar */
        .timeline .progress {
            height: 100%;
            /* Matches the height of the .timeline */
            width: 0%;
            /* Initially no progress */
            background: #03a34d;
            /* Green progress */
            border-radius: 3px;
            position: absolute;
            /* Ensure it's positioned relative to .timeline */
            top: 0;
            /* Aligns to the top of the .timeline */
            left: 0;
            /* Start from the left */
            z-index: 1;
            /* Ensure it appears above the .timeline background */
        }

        /* Current Time */
        #current-time {
            font-size: 14px;
            color: #333;
        }

        /* More Options Button */
        .more-options {
            background-color: transparent;
            border: none;
            font-size: 18px;
            color: #333;
            cursor: pointer;
        }

        /* Play/Pause Button */
        .play-pause-button {
            background-color: #03a34d;
            color: #fff;
            border: none;
            border-radius: 50%;
            font-size: 20px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            padding: 1rem;
            padding-right: 0.9rem;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .play-pause-button:hover {
            background-color: #eaeaea !important;
        }

        #comment-input {
            width: 100%;
            height: 3.2rem;
            border: none !important;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 14px;
            color: #495057;
            background-color: #eff3f4 !important;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #comment-input:focus {
            outline: none;
            box-shadow: 0 0 2px rgba(109, 200, 225, 0.15);
        }

        .speed-menu {
            background-color: #eff3f4;
            /* Comment input background color */
            border-radius: 5px;
            /* Rounded corners */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            /* Subtle shadow for depth */
            padding: 5px;
            /* Padding around the menu */
            position: absolute;
            /* Positioning for dropdown */
            display: none;
            /* Hidden by default */
            z-index: 1000;
            /* Ensure it stays on top */
        }

        .speed-menu div {
            font-size: 12px;
            /* Smaller font */
            text-align: center;
            /* Center the text */
            color: #495057;
            /* Text color */
            padding: 5px;
            /* Space inside each option */
            cursor: pointer;
            /* Pointer cursor for interaction */
            border-radius: 5px;
            /* Rounded corners for each option */
        }

        .speed-menu div:hover {
            background-color: #dfe4e6;
            /* Slightly darker on hover */
        }

        .nav-link {
            border-radius: var(--bs-nav-tabs-border-radius) !important;
            color: #40798c !important;

        }

        .nav-tabs .nav-link.active,
        .nav-tabs .nav-item.show .nav-link {
            border-radius: var(--bs-nav-tabs-border-radius) !important;
            color: white !important;
            background: #40798c !important;
            padding-bottom: 5%;
            border-radius: 12px !important;


        }

        .rec-tabs-link {
            font-size: 1.1rem !important;
            border-radius: 12px !important;
            padding-top: 3% !important;
            padding-bottom: 2% !important;
            /*             font-weight: bold !important;
         */
            font-size: 0.9rem;
        }

        .rec-tabs-link:not(.active) {

            font-weight: bold !important;

        }

        .conv-text-container {
            display: flex;
        }

        .conv-text-container-agent {

            justify-content: flex-end;

        }

        .conv-text-container-customer {
            justify-content: flex-start;
        }

        .conv-text {

            /*  width: fit-content; */
            font-weight: bold;
            border-radius: 12px;
            font-size: 1.1rem !important;
            color: #38798b;
        }

        .conv-text-container-customer>.conv-text {

            background: #f3f8ff;
            border: 1px solid #f3f8ff;
        }

        .conv-text-container-agent>.conv-text {

            background: #ffeeee;
            border: 1px solid #fbf3fe;
        }

        .conv-controllers {
            /* background: #aliceblue; */
            width: fit-content;
            border: 1px solid #fbf3fe;
            border-radius: 12px;
            font-size: 0.95rem !important;
            border: none !important;
        }

        .conv-row {
            display: flex;
            align-items: baseline;
        }

        .transcript-header {
            background-color: #ececec;
            /*border-top-left-radius: 1.2rem;*/
            /*border-top-right-radius: 1.2rem;*/
            color: blue;
            font-weight: bolder;
        }

        .tab-pane .transcription-container {
            max-height: 54vh;
            overflow-x: hidden;
            overflow-y: scroll;
            border-right: 3px solid #eff3f4;
        }

        .events-container {
            max-height: 54vh;
            overflow-x: hidden;
            overflow-y: scroll;
            border-left: 3px solid #eff3f4;
        }

        .events-div {
            display: flex;
            flex-direction: row;
            align-items: center;

            gap: 3%;

        }

        .tab-content {
            box-shadow: #eff3f4 0px 0px 3px 3px;
        }

        #search-wrapper {
            display: flex;
/*             border: 1px solid rgba(0, 0, 0, 0.276);
 */            align-items: stretch;
            border-radius: 0.6rem;
            background-color: #fff;
            overflow: hidden;
            max-width: 50%;
/*             box-shadow: 2px 1px 5px 1px rgba(0, 0, 0, 0.273);
 */
        }

        #search {
            border: none !important;
            width: 100%;
            font-size: 15px;
        }

        #search:focus {
            outline: none;
        }

        .search-icon {
            margin: 10px;
            color: rgba(0, 0, 0, 0.564);
        }

        #search-button {
            border: none;
            cursor: pointer;
            color: #fff;
            background-color: #1dbf73;
            padding: 0px 10px;
        }

        .events-div>div>h5 {
            text-decoration: underline;
        }

        .events-div>div>h4 {

            font-weight: bold;
        }

        .all-tabs {
            padding-left: 0 !important;
            padding-right: 0 !important;
            width: 100% !important;
        }

        .table-striped>tbody>tr:nth-of-type(odd)>* {
            background: #eff3f4 !important;
            color: #40798c !important;
        }

        .table-striped>tbody>tr>* {

            color: #40798c !important;
        }

        /* General Circle Styling */

        .progress-circle {
            width: 105px;
            height: 105px;
            border-radius: 50%;
            position: relative;
            background: conic-gradient(#10b981 0% 75%, #e5e7eb 75% 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #000;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }


        .progress-circle::before {
            content: "";
            position: absolute;
            width: 95px;
            height: 95px;
            background: #fff;
            border-radius: 50%;
            z-index: 1;
        }


        .percentage {
            position: relative;
            z-index: 2;
            font-size: 24px;
        }


        .label {
            margin-top: 10px;
            font-size: 14px;
            text-align: center;
            color: #6b7280;
        }

        .details-header>h4 {
            color: #40798c;
        }

        .details-text-container {}

        .details-text-container>h5 {
            font-weight: bold;
        }

        .details-text {
            color: #40798c;
            font-size: 1.1rem
        }

        .progress-bar {
            border-radius: 1rem;
        }

        .progress-bar-text {
            display: inline-block;
        }

        .progress {
            display: inline-flex !important;
            width: 80%
        }

        .circle-quality {

            background: conic-gradient({{ $callEvaluation >= 85 ? '#03a24c' : ($callEvaluation >= 70 && $callEvaluation <= 85 ? '#40788b' : '#ef4444') }} 0% {{ $callEvaluation }}%,
                    #e5e7eb {{ $callEvaluation }}% 100%);
        }

        .circle-ai {

            background: conic-gradient({{ $aiEvaluation >= 85 ? '#03a24c' : ($aiEvaluation >= 70 && $aiEvaluation <= 85 ? '#40788b' : '#ef4444') }} 0% {{ $aiEvaluation }}%,
                    #e5e7eb {{ $aiEvaluation }}% 100%);
        }

        .light-span {
            font-size: 0.9rem;
            color: #969696;
        }

        thead th {
            background-color: #40798C !important;
            color: #FFFFFF !important;
            /* font-size: medium; */
            font-size: small;
            height: 0rem;
        }

        tbody td {
            /* font-size: medium !important; */
            font-size: small !important;
            height: 0rem;
            font-weight: 600;
            border-bottom: none;
        }

        thead {
            height: 1rem !important;
        }
        tbody {
            height: 1rem !important;
        }

        tr th {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        tr td {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        /* Neutral Circle */
    </style>
@endsection


@section('content')
    <div class="container-fluid mt-3 px-4">
        {{-- <div class="row mx-3 ps-5 d-flex justify-content-start mb-5">
            <div class="col-3" style="border-radius: 20px; background-color: #fff; padding: 10px; display: inline-block; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
                <p style="color: #01A44F; margin: 0; font-size: 14px; text-align: center;">
                    {{ $callAccount }} | {{ $callType }} Call
                </p>
            </div>
        </div> --}}

        <div class="row">
            <div class="col-6 py-0">
                <div class="row  ps-5 mb-5 gap-3" style=" display: flex; justify-content: center; margin: 0 20px;">
                    <!-- Left Card -->

                    <div class="audio-card col-12" style="padding: 174px 11px;flex: 1;">
                        <div class="row">
                            <div class="col-12 p-0 mb-4">
                                <div class="audio-card-left ">
                                    <div class="avatar">
                                        <img src="{{ asset('assets/SVG/assets-v2/75.svg') }}" alt="Avatar" />
                                    </div>
                                    <div class="audio-card-content ms-3">
                                        <div class="audio-card-header">
                                            <span class="date-time">{{ $callDate->format('Y/m/d - H:i:s') }}</span>
                                            <span class="name-id mb-2">{{ $agent_name }} - {{ $agent_id }}</span>
                                            <span class="call-id">{{-- {{ $callId }} --}} {{ $callAccount }} | {{ $callType }} Call</span>
                                        </div>

                                    </div>
                                </div>

                            </div>
                            <div class="col-12">
                                <div class="audio-controls m-3">
                                    {{-- <button class="rewind">↩ <span id="rewind-duration">15s</span></button> --}}
                                    <button class="rewind" style="padding-left: 0 !important; padding-right: 0 !important"><span id="rewind-duration">15s</span><img src="{{ asset('assets/SVG/assets-v2/rewind.png') }}" alt="rewind" style="width: 1.5rem; margin-bottom:0.8rem !important"></button>
                                    <div class="timeline">
                                        <div id="progress" class="progress"></div>
                                    </div>
                                    {{-- <button class="forward"><span id="forward-duration">15s</span> ↪</button> --}}
                                    <button class="forward" style="padding-left: 0 !important; padding-right: 0 !important"><img src="{{ asset('assets/SVG/assets-v2/forward.png') }}" alt="rewind" style="width: 1.5rem; margin-bottom:0.8rem !important"><span id="forward-duration">15s</span></button>
                                    <span id="current-time">00:00</span>
                                    <button class="more-options">...</button>
                                    <button class="play-pause-button" id="playPauseButton">▶</button>
                                    {{-- <button class="video-btn play-pause-button" id="video-btn" d data-bs-toggle="modal" data-bs-src="https://www.youtube.com/embed/JJUo8Fe3_JY" data-bs-target="#videoModal">📹</button> --}}
                                    <x-chunks.video-modal :callId="$callId" />
                                </div>
                            </div>
                        </div>




                    </div>
{{--                        <div class="col-12 m-0 p-0">--}}
{{--                            <!-- Bottom Right Card -->--}}
{{--                            <div class="card border-0"--}}
{{--                                 style="flex: 1; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); border-radius: 8px; background-color: #fff; padding: 15px;">--}}
{{--                                <div class="row">--}}
{{--                                    <div class="col-12 text-center">--}}
{{--                                        <span class="light-span"> Having a trouble ?</span>--}}
{{--                                        <button class="btn btn-warning  btn-sm text-white">Create a Ticket </button>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
                </div>
            </div>
            <div class="col-6">
                <!-- Right Card -->
                <div class="row">
                    <div class=" col-3  p-0">

                        <div class="card border-0" style="flex: 1; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); border-radius: 8px; background-color: #fff; padding: 15px; margin-bottom: 15px;max-height:25vh;">


                                @if ($callEvaluation != 0)
                                    <div class=" justify-content-center" style="text-align: -webkit-center;">
                                        <div class="progress-circle circle-quality">
                                            <span class="percentage">{{ $callEvaluation }}</span>
                                        </div>
                                        <div class="label">Quality Evaluation</div>

                                    </div>
                                @else
                                    <div class=" justify-content-center" style="text-align: -webkit-center;">
                                        <div class="progress-circle circle-quality">
                                            <span class="percentage">?</span>
                                        </div>
                                        <div class="label">Quality Evaluation</div>

                                    </div>
                                @endif

                        </div>


                    </div>
                    <div class=" col-9 ">

                        <div class="card border-0" style="flex: 1; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); border-radius: 8px; background-color: #fff; padding: 15px; margin-bottom: 15px;max-height:25vh;">

                                <div class=" justify-content-center" style="text-align: -webkit-center;">
                                    <div class="progress-circle circle-ai">
                                        <span class="percentage">{{ $aiEvaluation }}</span>
                                    </div>
                                    <div class="label">AI Evaluation</div>
                                </div>


                        </div>


                    </div>
                    <div class=" col-3  p-0">

                        <div class="card border-0 " style="flex: 1; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); border-radius: 8px; background-color: #fff; padding: 15px; margin-bottom: 15px;max-height:25vh;">

                            <div class="label">

                                Bad Words: <span class="badge text-bg-danger">3</span>
                            </div>

                        </div>


                        <div class="card border-0"
                             style="flex: 1; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); border-radius: 8px; background-color: #fff; padding: 15px;">
                            <div class="row">
                                <div class="col-12 text-center">
                                    <div class="mb-2"> <span class="light-span"> Having a trouble ? </span> </div>
                                    <button class="btn btn-warning  btn-sm text-white">Create a Ticket </button>
                                </div>
                            </div>
                        </div>


                    </div>
                    <div class="col-9">
                        <div class="card border-0" style="flex: 1; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); border-radius: 8px; background-color: #fff; padding: 15px; margin-bottom: 15px;max-height:25vh;">

                            <livewire:transcript.classification :callId="$callId" />

                        </div>
                    </div>
                </div>

            </div>
        </div>


        <div class="row mx-3 d-flex ps-5">
            <ul class="nav nav-tabs mb-3 py-2" role="tablist">
                <li class="nav-item">
                    <a href="#Details" role="tab" data-toggle="tab" class="nav-link  rec-tabs-link me-1 active"> Details </a>
                </li>
                <li class="nav-item">
                    <a href="#timeline" role="tab" data-toggle="tab" class="nav-link rec-tabs-link mx-1 "> Timeline </a>
                </li>
                {{--                 <li class="nav-item">
                    <a href="#qasummary" role="tab" data-toggle="tab" class="nav-link rec-tabs-link"> Quality Summary
                    </a>
                </li> --}}
                <li class="nav-item">
                    <a href="#transcript" role="tab" data-toggle="tab" class="nav-link rec-tabs-link mx-1 "> Transcript </a>
                </li>

                <li class="nav-item">
                    <a href="#topics" role="tab" data-toggle="tab" class="nav-link rec-tabs-link mx-1 "> Topics </a>
                </li>

                <li class="nav-item">
                    <a href="#bodword" role="tab" data-toggle="tab" class="nav-link rec-tabs-link mx-1"> Bad Words </a>
                </li>
            </ul>

            <div class="tab-content all-tabs ">
                <div class="tab-pane active" role="tabpanel" id="Details">

                    <x-call-details :callId="$callId" :timeData="$timeData" :startTime="$callDate" :callType="$callType"
                        :callDetails="$callDetails" />
                </div>
                <div class="tab-pane " role="tabpanel" id="timeline">
                    <h3> Timeline </h3>
                    <p> Lorem ipsum dolorem </p>
                </div>
                {{--               <div class="tab-pane" role="tabpanel" id="qasummary">
                    <h3>Quality Summary </h3>
                    <p> Sit amet </p>
                </div> --}}
                <div class="tab-pane" role="tabpanel" id="transcript">
                    <x-transcript :transcription="$transcript" :callId="$callId" />
                </div>

                <div class="tab-pane" role="tabpanel" id="topics">
{{--                    <livewire:transcript.right.index :callId="$callId" />--}}
                    <livewire:transcript.topics :callId="$callId" />

                </div>


                <div class="tab-pane" role="tabpanel" id="bodword">
                    <livewire:transcript.bad-word :callId="$callId" />
                </div>

                {{--                <div class="tab-pane" role="tabpanel" id="transcript"> --}}
                {{--                    <x-transcript :transcription="$transcript" :callId="$callId" /> --}}
                {{--                </div> --}}
            </div>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous">
    </script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
        integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous">
    </script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"
        integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous">
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {

            const audio = new Audio(@json(asset($callPath)));
            //const audio = new Audio('https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3');
            // const audio = new Audio('https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3');
            const playPauseButton = document.getElementById("playPauseButton");
            const timeline = document.querySelector(".timeline");
            const progress = document.getElementById("progress");
            const currentTime = document.getElementById("current-time");
            const rewindButton = document.querySelector(".rewind");
            const forwardButton = document.querySelector(".forward");
            const moreOptionsButton = document.querySelector(".more-options");
            const rewindDurationDisplay = document.getElementById("rewind-duration");
            const forwardDurationDisplay = document.getElementById("forward-duration");
            let isMetadataLoaded = false;

            // Create and style the speed menu
            const speedMenu = document.createElement("div");
            speedMenu.className = "speed-menu";
            speedMenu.style.position = "absolute";
            speedMenu.style.backgroundColor = "#eff3f4"; // Match input color
            speedMenu.style.border = "1px solid #ccc";
            speedMenu.style.borderRadius = "5px";
            speedMenu.style.padding = "5px";
            speedMenu.style.display = "none";
            speedMenu.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
            speedMenu.style.zIndex = "1000";
            document.body.appendChild(speedMenu);

            // Define playback speed options
            const speeds = [{
                    label: "x0.25",
                    value: 0.25
                },
                {
                    label: "x0.5",
                    value: 0.5
                },
                {
                    label: "Normal",
                    value: 1
                },
                {
                    label: "x1.5",
                    value: 1.5
                },
                {
                    label: "x2",
                    value: 2
                }
            ];

            speeds.forEach((speed) => {
                const option = document.createElement("div");
                option.textContent = speed.label;
                option.className = "speed-menu-option"; // Add class for consistent styling
                option.style.padding = "5px";
                option.style.cursor = "pointer";
                option.style.borderRadius = "5px";

                option.addEventListener("click", () => {
                    audio.playbackRate = speed.value;
                    console.log(`Playback speed set to: ${speed.label}`);
                    speedMenu.style.display = "none"; // Hide menu after selection
                });

                // Hover effect
                option.addEventListener("mouseover", () => {
                    option.style.backgroundColor = "#dfe4e6";
                });
                option.addEventListener("mouseout", () => {
                    option.style.backgroundColor = "";
                });

                speedMenu.appendChild(option);
            });

            // Show/hide speed menu when clicking the more-options button
            moreOptionsButton.addEventListener("click", (event) => {
                event.stopPropagation(); // Prevent closing the menu immediately

                if (speedMenu.style.display === "none") {
                    const rect = moreOptionsButton.getBoundingClientRect();
                    speedMenu.style.top = `${rect.bottom + window.scrollY}px`;
                    speedMenu.style.left = `${rect.left + window.scrollX}px`;
                    speedMenu.style.display = "block";
                } else {
                    speedMenu.style.display = "none";
                }
            });

            // Close speed menu on clicking anywhere else
            document.addEventListener("click", () => {
                speedMenu.style.display = "none";
            });

            // Preload metadata to get audio duration
            audio.preload = "metadata";

            audio.addEventListener("loadedmetadata", () => {
                isMetadataLoaded = true;
                console.log("Audio metadata loaded. Duration:", audio.duration);
                console.log("Audio Source:", audio.src);
                currentTime.textContent = "00:00"; // Initialize time display

                // Calculate 20% of the duration and update button text
                const duration20Percent = Math.floor(audio.duration * 0.1); // 20% of the duration
                rewindDurationDisplay.textContent = `${duration20Percent}s`;
                forwardDurationDisplay.textContent = `${duration20Percent}s`;
            });

            // Play or pause the audio
            playPauseButton.addEventListener("click", () => {
                if (!isMetadataLoaded) {
                    console.error("Audio metadata is not loaded yet.");
                    return;
                }

                if (audio.paused) {
                    audio.play();
                    console.log("Audio playing");
                    playPauseButton.textContent = "⏸"; // Change to pause icon
                } else {
                    audio.pause();
                    console.log("Audio paused");
                    playPauseButton.textContent = "▶"; // Change to play icon
                }
            });

            // Update the progress bar and current time as the audio plays
            audio.addEventListener("timeupdate", () => {
                if (!isMetadataLoaded || audio.duration === 0) return;

                const percentage = (audio.currentTime / audio.duration) * 100;
                progress.style.width = `${percentage}%`;

                const minutes = Math.floor(audio.currentTime / 60).toString().padStart(2, "0");
                const seconds = Math.floor(audio.currentTime % 60).toString().padStart(2, "0");
                currentTime.textContent = `${minutes}:${seconds}`;

                console.log(`Audio time updated: ${audio.currentTime} seconds`);
            });

            // Seek audio position when clicking on the timeline
            timeline.addEventListener("click", (event) => {
                if (!isMetadataLoaded || audio.duration === 0) {
                    console.error("Audio metadata is not loaded yet or invalid duration.");
                    return;
                }

                const timelineWidth = timeline.offsetWidth;
                const clickX = event.offsetX;
                const newTime = (clickX / timelineWidth) * audio.duration;

                console.log(`Timeline Width: ${timelineWidth}`);
                console.log(`Click X Position: ${clickX}`);
                console.log(`Calculated New Time: ${newTime}`);

                audio.currentTime = newTime;
            });

            // Reset play button when the audio ends
            audio.addEventListener("ended", () => {
                playPauseButton.textContent = "▶"; // Reset to play icon
                console.log("Audio ended");
            });

            // Rewind by 20% of the audio duration
            rewindButton.addEventListener("click", () => {
                if (!isMetadataLoaded || audio.duration === 0) return;

                const rewindTime = Math.floor(audio.duration * 0.1); // 20% of the duration
                const newTime = Math.max(0, audio.currentTime - rewindTime); // Ensure we don't go below 0
                audio.currentTime = newTime;
            });

            // Forward by 20% of the audio duration
            forwardButton.addEventListener("click", () => {
                if (!isMetadataLoaded || audio.duration === 0) return;

                const forwardTime = Math.floor(audio.duration * 0.1); // 20% of the duration
                const newTime = Math.min(audio.duration, audio.currentTime +
                forwardTime); // Ensure we don't exceed duration
                audio.currentTime = newTime;
            });
        });
    </script>



@endsection
