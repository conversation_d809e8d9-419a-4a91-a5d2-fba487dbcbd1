<?php

namespace App\Models;

use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Organization extends Model
{
    use HasFactory;

    protected $guarded = [];

    // public function admins()
    // {
    //     return $this->hasMany(User::class, 'organization_id')->where('role', 1);
    // }

    // public function supervisors()
    // {
    //     return $this->hasMany(User::class, 'organization_id')->where('role', 2);
    // }

    public function users()
    {
        return $this->belongsToMany(User::class, 'organization_user', 'organization_id', 'user_id');
    }

    // admins
    public function admins()
    {
        return $this->belongsToMany(User::class)->where('role', 1);
    }

    public function userGroups()
    {
        return $this->hasMany(UserGroup::class, 'organization_id');
    }
    public function evaluationForm()
    {
        return $this->hasMany(Evaluation::class);
    }
    public function callInteraction()
    {
        return $this->hasMany(Interaction::class);
    }
    public function scopeSearch($q, $value)
    {
        $q->where('name', 'like', "%$value%");
    }
}
