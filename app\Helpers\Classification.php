<?php
namespace App\Helpers;

use App\Models\TranscriptionClassifications;
use Livewire\Component;

class Classification extends Component
{




    private $callId;
    public function __construct($call_id)
    {
    $this->callId = $call_id;
    }
    public function classificationResult()
    {
        $defaultClassifications = [
            'positive' => 0,
            'negative' => 0,
            'neutral' => 0,
        ];

        // Fetch classifications from the database
        $classifications = TranscriptionClassifications::selectRaw('
        LOWER(classification) as classification,
        ROUND((COUNT(*) * 100.0) / (SELECT COUNT(*) FROM transcription_classifications WHERE call_id = ?), 1) as average_percentage
    ', [$this->callId])
            ->where('call_id', $this->callId)
            ->groupByRaw('LOWER(classification)')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->classification => $item->average_percentage];
            })
            ->toArray();

        return array_merge($defaultClassifications, $classifications);
    }

    public function classification()
    {
        $classification = TranscriptionClassifications::where('call_id', $this->callId)->get();

        return $classification;
    }

    public function classificationPositive()
    {
        $classification = TranscriptionClassifications::whereRaw('UPPER(classification) = ?', ['POSITIVE'])
            ->where('call_id', $this->callId)
            ->get();

        return $classification;
    }

    public function classificationNeutral()
    {
        $classification = TranscriptionClassifications::whereRaw('UPPER(classification) = ?', ['NEUTRAL'])
            ->where('call_id', $this->callId)
            ->get();


        return $classification;
    }

    public function classificationNegative()
    {
        $classification = TranscriptionClassifications::whereRaw('UPPER(classification) = ?', ['NEGATIVE'])
            ->where('call_id', $this->callId)
            ->get();

        return $classification;
    }

    public function getResults()
    {
        return ['result'=>$this->classificationResult(),$this->classification()
        ,"classificationPositive"=>$this->classificationPositive()
            ,"classificationNeutral"=>$this->classificationNeutral()
        ,"classificationNegative"=>$this->classificationNegative()
        ];
    }
}
