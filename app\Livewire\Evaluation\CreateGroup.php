<?php

namespace App\Livewire\Evaluation;

use App\Models\Evaluation;
use App\Models\EvaluationGroup;
use App\Models\EvaluationQuestion;
// use HTMLPurifier;
// use HTMLPurifier_Config;
use http\Env\Request;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class CreateGroup extends Component
{


    use WithPagination,LivewireAlert;



    public $order_by = null;
    public $sort_by = null;
    public $limit = null;



    public $search_group_name;

    public $form_name;
    public $form_id;
    public $group_name;
    public $group_weight;

    public $groupID;
    public $questionShow;
    public $user_id;
    public $userid;
    public $status;
    public $modalId;
    public $modalIdShow;
    public $fromDate;
    public $groupSelected='Header Name';

    protected $paginationTheme = 'bootstrap';

    public function mount($id)
    {

        $this->form_id=$id;
        $this->getFormName($this->form_id);
        $this->userid = auth()->id();
        $this->questionShow=false;
        $this->dispatch('close-modal-search');
    }
    public function getFormName($form_id){

       $form = Evaluation::query()
            ->where('id', $form_id)
            ->pluck('evaluation_name');
       $this->form_name=$form[0];

    }

    public function getQuestion($question_id){
        return EvaluationQuestion::query()->where('evaluation_group_id', $question_id)->get();
    }

    public function getEvaluationGroup(){
        try
        {
            // dd($this->search_group_name);
            if($this->search_group_name){
                return EvaluationGroup::query()
                    ->where('evaluation_id',$this->form_id)
                    ->where('group_name', 'like', '%' . $this->search_group_name . '%')
                    ->get();
            }else {
                return EvaluationGroup::query()
                    ->where('evaluation_id',$this->form_id)
                    ->get();
            }
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function modelData(){

        // $config = HTMLPurifier_Config::createDefault();
        // $purifier = new HTMLPurifier($config);

        // $this->form_name = $purifier->purify($this->form_name);

        return [
            'evaluation_id'            => $this->form_id,
            'created_by'           => $this->userid,
            'group_name'           => $this->group_name,
            'group_weight'           => $this->group_weight,
        ];
    }

    public function rules(){
        return [
            'group_name' => ['required','string'],
            'group_weight' => ['required', 'numeric', 'min:0', 'max:100'],
        ];

    }

    public function store(){

        $this->validate();

        try {
           $groupNameExist = EvaluationGroup::select('group_name')
           ->where('group_name',$this->group_name)
           ->get();
           if($groupNameExist->isEmpty()){

               EvaluationGroup::create($this->modelData());

               $this->alert('success', 'Successfully Added !', [
                   //'position' => 'bottom-end',
                   'timerProgressBar' => true,
                   'timer' => '6000',
               ]);

               $this->modelFormReset();
               $this->dispatch('close-modal');

           }else {
               $this->alert('error', 'This group name already exist',[
                   'timerProgressBar' => true,
                   'timer' => '6000',
               ]);
           }
        }
        catch (\Exception $e){

            $this->alert('error', $e,[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }


    }

    public function updateWeight(){

        $this->validate();

        try {
            $fb = EvaluationGroup::find($this->modalId);
            $fp = $fb->update(['group_weight' => $this->group_weight , 'updated_by' => $this->userid]);
            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal-weight');
        }
        catch (\Exception $e){

            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal-weight');

        }
//        $this->dispatch('show-edit', ['id' => $this->modalId]);

    }

    public function updateGroupName(){

        $this->validate();

        try {
            $fb = EvaluationGroup::find($this->modalId);
            $fp = $fb->update(['group_name' => $this->group_name , 'updated_by' => $this->userid]);
            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal-group-name');
        }
        catch (\Exception $e){

            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal-group-name');

        }

    }


    public function closeModal()
    {
        $this->modelFormReset();

    }

    public function showModal($id){

        $this->modalId =$id;
        $this->modalIdShow = "on";
    }
    public function addModal(){


        $this->modalId='';
        $this->modalIdShow='';
        $this->group_name = "";
        $this->group_weight = "";


        // $this->dispatch('close-modal-search');
    }



    public function statusUpdateModal($id){

        $this->modalId =$id;

        $fb = EvaluationGroup::find($this->modalId);
        $fp = $fb->update(['status' => $fb->status != 0 ? 0 : 1]);
        $this->status = $fb->status;
        $this->alert('success', 'Successfully Updated!',[
            'timerProgressBar' => true,
            'timer' => '6000',
        ]);
    }
    public function showEdit($id){

        $fb = EvaluationGroup::find($id);
        $this->groupSelected=$fb->group_name;
        $this->groupID =$id;
        $this->questionShow=true;
        $this->getQuestion($id);
        $this->dispatch('show-edit', ['id' => $id]);

    }
    public function showUpdateModalWeight($id){
        $this->modalId =$id;
        $fb = EvaluationGroup::find($this->modalId);
        $this->group_name = $fb->group_name;
        $this->group_weight = $fb->group_weight;
        $this->status = $fb->status;
        $this->resetValidation();
        $this->dispatch('open-modal-edit',  ['id' => $id]);
    }

    public function showUpdateModalGroupName($id){

        $this->modalId =$id;
        $fb = EvaluationGroup::find($this->modalId);
        $this->group_name = $fb->group_name;
        $this->group_weight = $fb->group_weight;
        $this->status = $fb->status;
        $this->resetValidation();
        $this->dispatch('open-modal-edit2',  ['id' => $id]);

    }

    public function showDeleteAlert($id){
        $this->modalId =$id;
    }

    public function modelFormReset(){

        $this->group_name = "";
        $this->group_weight = "";
    }


    public function reloadComponent()
    {
        $this->questionShow=false;
        $this->groupSelected='Header Name';
        $this->modalId='';
        $this->modalIdShow='';
        $this->group_name = "";
        $this->group_weight = "";
        $this->dispatch('reset-tabel');
        $this->search_group_name='';
        // $this->dispatch('close-modal-search');
        
    }

    public function render()
    {
        return view('livewire.evaluation.create-group', [
            'Evaluation_group' => $this->getEvaluationGroup(),
            'Evaluation_question' => $this->getQuestion($this->groupID)
        ]);
    }
}
