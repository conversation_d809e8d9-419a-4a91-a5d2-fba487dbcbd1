<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
   
    public function up(): void
    {
        Schema::table('transcription_scripts', function (Blueprint $table) {
            $table->string('scripts_type')->nullable();
            $table->string('language')->nullable();
        });
    }

    
    public function down(): void
    {
        Schema::table('transcription_scripts', function (Blueprint $table) {
            $table->dropColumn(['scripts_type', 'language']);
        });
    }
};
