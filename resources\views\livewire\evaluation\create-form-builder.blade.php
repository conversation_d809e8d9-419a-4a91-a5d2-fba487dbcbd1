<div class="container-fluid mt-3 px-4">

    <div class="row mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow shadow py-2 px-3">
                <div class="card-body">
                    <h5>
                        <b>
                            <span style="margin-left: 1%;">{{$form_name}} Evaluation Admin</span>
                        </b>
                    
                    </h5>
                    <h6 class="text-muted">
                        <img  src="{{ asset('assets/images/evaluation/e!.png') }}" alt="i Icon" width="35" height="35">
                        <span style="color: #ec2121;margin-left: -6px;">This allows you to add, edit, and delete fields for the evaluator.</span>
                        
                    </h6>
                    
                </div>
            </div>
        </div>
      
    </div>
   
    <div class="row parent-sections mx-3">
        <div class="section-one" style="width: 53%;" >
            <div class="section-one-header">
                <h4>Drag and drop element to create fields</h4>
            </div>
            @php
                $evaluationFieldsArray = $Evaluation_Fields->toArray();

                $countTextFields = count(array_filter($evaluationFieldsArray, function($field) {
                    return $field['type'] != 'Week';
                }));
                $weekFieldExist = count(array_filter($evaluationFieldsArray, function($field) {
                    return $field['type'] == 'Week';
                }));

            @endphp
            

            @if($countTextFields == 3)
                <div class="section-one-boxes">
                    <div class="alert alert-warning" role="alert" style="width: {{ count($Evaluation_Fields) == 4 ? '100%' : '64%' }}">
                    <strong>Note!</strong> You have exceeded the limit of adding elements (max three elements plus a week field)
                </div>
                    @if(count($Evaluation_Fields) != 4)
                    <div class="section-one-boxes-box draggable" draggable="true" data-type="Week">
                        <div class="parent-box-content">
                            <div class="div-icon">
                                <img  src="{{ asset('assets/images/evaluation/week.png') }}" alt="i Icon" width="30" height="30">
                            </div>
                            <div class="div-text">
                                <strong>Week Definition</strong>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            @else
            <div class="section-one-boxes">
                <div class="section-one-boxes-box draggable" draggable="true" data-type="Text" style="width: 24%">
                    <div class="parent-box-content">
                    <div class="div-icon custome-box-style">
                        <img  src="{{ asset('assets/images/evaluation/text.png') }}" alt="i Icon" width="30" height="30">
                    </div>
                    <div class="div-text">
                        <strong>Text</strong>
                    </div>
                    </div>
                </div>
                <div class="section-one-boxes-box draggable" draggable="true" data-type="Dropdown">
                    <div class="parent-box-content">
                    <div class="div-icon">
                        <img  src="{{ asset('assets/images/evaluation/dropdown.png') }}" alt="i Icon" width="30" height="30">
                    </div>
                    <div class="div-text">
                        <strong>Dropdown List</strong>
                    </div>
                    </div>
                </div>


                @if($weekFieldExist != 1)
                <div class="section-one-boxes-box draggable" draggable="true" data-type="Week">
                    <div class="parent-box-content">
                    <div class="div-icon">
                        <img  src="{{ asset('assets/images/evaluation/week.png') }}" alt="i Icon" width="30" height="30">
                    </div>
                    <div class="div-text">
                        <strong>Week Definition</strong>
                    </div>
                    </div>
                </div>
                @endif
            </div>
            @endif




            <div class="section-one-info" id="dropZone" wire:ignore.self wire:loading.class="opacity-50">
                <div>
                    <div class="section-one-circle">
                        <img src="{{ asset('assets/images/evaluation/up.png') }}" width="35" height="35" alt="">
                    </div>
                    <div class="section-one-text">
                        Drop element here
                    </div>
                </div>
            </div>



            <div class="section-one-details" id="modal" wire:ignore.self style="display: none">
                    <div class="section-one-details-header">
                        <h5>{{$itemType}}</h5>
                    </div>
                    <div class="section-one-details-content">
                        <div class="row mandatory">
                                <div class="form-group" style="margin-right: 3%;width: 25%;" >
                                    <input type="checkbox" id="required" wire:click="getRequired">
                                    <label for="required" class="text-muted">Mandatory</label>
                                </div>
                        </div>


                            <div class="row label">
                                <label for="title" class="col-form-label">Label:</label>
                                <input type="text" class="form-control" wire:model.defer="label" id="label" @if($itemType == 'Week') readonly @endif/>
                                @error('label')<small class="text-danger"> {{ $message }} </small> @enderror
                            </div>

                        @if($itemType == 'Text')
                        <div class="row label">
                            <label for="title" class="col-form-label">Type:</label>
                            <div class="custom-select" style="position: relative;padding: 0">
                                <select class="form-control"  wire:model.defer="type" style="cursor: pointer">
                                    <option value="">Select...</option>
                                    <option value="Text">Text</option>
                                    <option value="Number">Number</option>
                                    <option value="Email">Email</option>
                                    <option value="Comment">Comment</option>
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                    <path d="M7 10l5 5 5-5z"/>
                                </svg>
                            </div>
                            @error('type')<small class="text-danger"> {{ $message }} </small> @enderror
                        </div>
                        @elseif($itemType == 'Week')
                            <div class="row label">
                                <label for="title" class="col-form-label">Type:</label>
                                <div class="custom-select" style="position: relative;padding: 0">
                                    <select class="form-control"  wire:model.defer="value.0" style="cursor: pointer">
                                        <option value="">Select...</option>
                                        <option value="48 Weeks">48 Weeks</option>
                                        <option value="52 Weeks">52 Weeks</option>
                                    </select>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                        <path d="M7 10l5 5 5-5z"/>
                                    </svg>
                                </div>
                                @error('type')<small class="text-danger"> {{ $message }} </small> @enderror
                            </div>
                        @elseif($itemType == 'Dropdown')
                            <div class="row label weight col-6">
                                <label for="title" class="col-form-label weight-lable">Option:</label>
                                <input type="text" class="form-control" wire:model="value.0" placeholder="Write Option..." />
                                @error('value.0')<small class="text-danger"> {{ $message }} </small> @enderror
                            </div>
                            @foreach($inputs as $key => $valuee)

                                <div class="row label" style="display: flex !important;margin-top: 1%;">

                                    <input type="text" class="form-control" wire:model="value.{{$valuee}}" style="width: 100%;width: 42.6%;" placeholder="Write Option..." />

                                    <div class="mb-2 col-2" style="padding-top: 1%;cursor: pointer;    display: inline !important;" wire:click="removeElement({{$key}})">
                                        <img  src="{{ asset('assets/images/evaluation/delete.png') }}" alt="i Icon" width="20" height="20">
                                    </div>
                                    @error('value.' .$valuee)<small class="text-danger"> {{ $message }} </small> @enderror
                                </div>

                            @endforeach
                            <div class="row label" style="margin-top: 2%;">
                                <div class="circle" wire:click="addNewElement({{$counter}})">
                                    <i class="fa fa-plus fa-style" style="font-size: 19px;color: white" ></i>
                                </div>
                            </div>
                        @endif
                        <div class="footer-btn" style="height: 15%;margin: 3%;">

                                <button 
                                    type="button" 
                                    class="btn btn-secondary rounded-3 px-4" 
                                    style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" 
                                    data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal">
                                    Cancel
                                </button>

                                    <button 
                                        class="btn btn-success rounded-3 px-4" 
                                        style="height: 40px; border-color: #01a44f; background: #01a44f;" 
                                        wire:click="{{ $modalUpdate ? 'update' : 'store' }}" >
                                        {{ $modalUpdate ? 'Update' : 'Submit' }}
                                    </button>

                        </div>
                    </div>

            </div>



        </div>




        <div class="section-tow shadow" style="width: 45%;">
            <div class="header-section-tow">
                Click on element to show fields
            </div>

            <div class="content-section-tow">

                @forelse($Evaluation_Fields ?? [] as $fields)
                    @if(!$modalId && $fields->id != $modalId || $modalId && $fields->id != $modalId)
                    <div class="div-content" wire:click="getFieldToUpdate({{$fields->id}})">
                        <div class="div-content-text">
                            {{$fields->type}}
                        </div>
                        <div class="div-content-remove" wire:click="deleteField({{$fields->id}})">
                            <img  src="{{ asset('assets/images/evaluation/delete.png') }}" alt="i Icon" width="20" height="20">
                        </div>
                    </div>
                    @elseif($modalId && $fields->id == $modalId)
                        <div class="div-content-selected">
                            <div class="div-content-text">
                                {{$fields->type}}
                            </div>
                            <div class="div-content-remove" wire:click="deleteField({{$fields->id}})">
                                <img  src="{{ asset('assets/images/evaluation/deleteWhitColor.png') }}" alt="i Icon" width="25" height="25">
                            </div>
                        </div>
                    @endif
                @empty
                @endforelse


            </div>

        </div>

    </div>


    <div class="mx-3 ps-5">
        <div class="col-6">
            <a href="{{route('evaluation.index')}}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div>
    
        {{-- <div class="mx-3 ps-5" style="margin-top: 3%;margin-left: 1.8rem !important;">
            <div class="col-12">

            <a href="{{ route('evaluation.index') }}" class="btn btn-dark mx-1 shadow" style="padding-top: 8px;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;"> Previous
            </a>
        </div>
    </div> --}}

   
    <script type="module">
        document.addEventListener('DOMContentLoaded', function () {
            
       
            let dropZone = document.getElementById('dropZone');
            let draggableItems = document.querySelectorAll('.draggable');

            draggableItems.forEach(item => {
                item.addEventListener('dragstart', (event) => {
                    console.log('start');
                    document.getElementById('dropZone').style.border = '4px dashed red';
                    event.dataTransfer.setData('type', event.target.dataset.type);
                });
            });

            dropZone.addEventListener('dragover', (event) => {
                event.preventDefault(); // Prevent default to allow drop
                console.log('dragover');
            });

            dropZone.addEventListener('dragenter', (event) => {
                // event.preventDefault(); // Prevent default to allow drop
                document.getElementById('dropZone').style.backgroundColor = 'lightblue';
                document.getElementById('dropZone').style.border = '4px dashed #00a34e';
                console.log('dragenter');

            });

            dropZone.addEventListener('dragleave', (event) => {
                event.preventDefault(); // Allow drop
                const target = event.relatedTarget;
                if (!target || (target !== dropZone && !dropZone.contains(target))) {
                    dropZone.style.backgroundColor = 'white';
                    dropZone.style.border = '4px dashed red'; // Change border when leaving dropZone
                    console.log('dragleave');
                }
            });
           
            dropZone.addEventListener('drop', (event) => {
                event.preventDefault(); // Prevent default drop behavior
                let data = event.dataTransfer.getData('type');
                Livewire.dispatch('dropItem', { data: data })
                document.getElementById('dropZone').style.backgroundColor = 'white';
                document.getElementById('dropZone').style.border = '4px dashed #ccc';
                console.log('Item dropped'); // Perform actions with the dropped item
            });

            document.addEventListener('dragend', (event) => {
                event.preventDefault(); // Prevent default behavior
                document.getElementById('dropZone').style.border = '4px dashed #ccc';
                console.log('Drag ended');
            });

        });
      
    </script>

</div>
