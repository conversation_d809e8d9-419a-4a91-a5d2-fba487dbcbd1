<?php
namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAISummarizationService
{
    public function summarizeFromCallId($callId, $lang = 'ar'): string
    {

        $gotcallId       = explode('@', $callId)[0];
        $existingSummary = DB::table('calls_summary')
            ->where('call_id', $gotcallId)
            ->value('summary');

/*         if ($existingSummary) {
            return $existingSummary; removed as per the request to resummarize it each time
        } */

        $text = DB::table('calls_transcription')
            ->where('call_id', $gotcallId)
            ->orderBy('duration_from')
            ->orderBy('duration_to')
            ->pluck('content') // 👈 or 'text' or whatever column holds the dialogue
            ->implode("\n");

        $systemPrompt = $lang === 'en'
        ? 'You are an expert assistant that summarizes phone conversations in English and classifies them accurately.'
        : 'أنت مساعد خبير في تلخيص المحادثات الهاتفية باللغة العربية وتصنيفها بدقة.';

        $userPrompt = $lang === 'en'
        ? <<<EOT
Please summarize the following phone conversation in a concise paragraph.

Then, on a new line, write the call classification in this format:
Type: [Choose from: Inquiry, Complaint, Escalation, Feedback, Other] - Subtype: [Write a short descriptive phrase (2–3 words) like: Order cancellation, Delivery delay, Agent misconduct, etc.]

Choose based only on what is actually discussed in the conversation. Do **not** use generic terms like "Reservation" if the conversation is about shipping or online orders — use more accurate descriptions like "Order issue" or "Shipping delay."

Conversation:
$text
EOT
        :<<<EOT
قم بتلخيص المحادثة الهاتفية التالية في فقرة موجزة.

ثم في سطر جديد، اكتب تصنيف المكالمة بهذا الشكل تمامًا:
النوع: [اختر من: استفسار، شكوى، تصعيد، ملاحظات، غير ذلك] - التصنيف الفرعي: [اكتب وصفًا دقيقًا من كلمتين إلى ثلاث يعبر عن الموضوع الفعلي مثل: إلغاء طلب، مشكلة في التوصيل، سلوك غير لائق، إلخ]

اختر فقط بناءً على ما تم ذكره فعلاً في المحادثة. لا تستخدم "حجز" إذا كانت المحادثة تتعلق بتوصيل، شحنة، أو طلب عبر الإنترنت. استخدم وصفًا طبيعيًا أقرب لما قاله المتصل.

المحادثة:
$text
EOT;

        $payload = [
            'model'       => 'gpt-4',
            'messages'    => [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt],
            ],
            'temperature' => 0.5,
        ];

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('OPENAI_API_KEY'),
            'Content-Type'  => 'application/json',
        ])->post('https://api.openai.com/v1/chat/completions', $payload);
        Log::channel('AI Response')->critical(['content' => $response->json()['choices'][0]['message']['content']]);

        return $response->json()['choices'][0]['message']['content'] ?? 'خطأ في التلخيص';
    }
}
