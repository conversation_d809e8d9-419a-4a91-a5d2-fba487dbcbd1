<?php

namespace App\Events\Capture;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StartEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public $ip;
    public $callId;

    public function __construct($ip,$callId)
    {
        $this->ip = $ip;
        $this->callId = $callId;
    }

    public function broadcastWith()
    {
        return [
            'event_type'    =>'start',
            'call_id'       =>$this->callId,
        ];
    }

    public function broadcastOn()
    {
        return new Channel('capturing.'.$this->ip);
    }
}
