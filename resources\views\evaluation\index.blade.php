@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Evaluation Page')

{{-- Style Section --}}
@section('style')

    <style>
        /*
        new styles
        */
            .switche{
                font-size: 150%;
                cursor: pointer;
            }
            .table{
                text-align: center;
                vertical-align: middle
            }

            .table tr td {
                border-bottom: none !important;
            }

            .span_icon{
                vertical-align: sub;
                float: inline-end;
                cursor: pointer;
            }
            td{
                vertical-align: middle;
                font-size: small !important;
            }
            thead{
                height: 50px;
                vertical-align: middle;
                
            }
            thead tr th{
                vertical-align: middle;
                background-color: #40798c !important;
                color:white !important;
                font-size: small !important;
            }
            .parent-sections {
                height: 70vh;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-top: 1.5%;
                margin-bottom: 3%;
            }

            .section-one{
                width: 100%;
                height: 100%;
            }
            .div-table{
                /* border: 1px solid #d0caca; */
                border-radius: 0px;
                width: 100%;
                height: 100%;
                overflow: auto;
            }
            .form-control , .form-select , .dropdown-toggle-style{
                background-color: #eff3f4 !important;
                border: none !important;
                height: 40px;
            }
            label{
                color : #40798c !important;
                font-size: 17px;
                /* font-weight: 700 !important; */
            }
            .previous{
                margin-bottom: 5px;
            }
        /*
        end new styles
        */
        /*
        pagination styles
        */
                #searchInput {
                    height: 2.8rem !important;
                    width: 100% !important;
                    /* Increase the height for a larger input */
                    padding-left: 2.5rem !important;
                    /* Increase padding for better spacing */
                    border: none !important;
                    /* Slightly darker border */
                    border-radius: 0.5rem;
                    /* Rounded corners */
                    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
                    /* Subtle shadow */
                    transition: box-shadow 0.3s ease, border-color 0.3s ease;
                    /* Smooth transition */
                    font-size: 1.2rem;
                    /* Slightly larger text size */
                    background-position: left 0.5rem center;
                    /* Icon positioning */
                }

                /* Focus styles */
                #searchInput:focus {
                    outline: none;
                    /* Remove default outline */
                    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
                    /* Larger shadow on focus */
                    border-color: rgba(0, 0, 0, 0.3);
                    /* Slightly darker border on focus */
                }

                /* Placeholder styling */
                #searchInput::placeholder {
                    font-family: inherit;
                    /* Use inherited font style */
                    color: #01A44F;
                    /* Green placeholder text */
                    font-size: 1.2rem;
                    /* Match placeholder size with input text */
                }

                .main-buttons-container button {
                    height: 2.9rem;
                    font-size: 15px;
                }

                .main-buttons-container button:hover {
                    background-color: #018F3E !important;
                }

                /* pagination  */
                ul.pagination {
                    gap: 0.3rem;
                }

                ul.pagination li button,
                ul.pagination li span {
                    padding: 0.9rem;
                    padding-top: 0.5rem;
                    padding-bottom: 0.5rem;
                }

                ul.pagination li button:hover {
                    background-color: rgb(196, 183, 183) !important;
                }

                ul.pagination>li>button,
                ul.pagination>li>span {
                    color: black !important;
                    font-weight: 600 !important;
                    background-color: white;
                }

                .page-item span,
                .page-item button {
                    border-radius: 0.7rem !important;
                }

                .page-item.active span,
                .page-item.active button {
                    border-radius: 0.5rem !important;
                }

                .page-item.active>span {
                    background-color: #00a34e !important;
                    color: white !important;
                }

                div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
                    font-size: 0.9rem;
                }

                div.tab-pane label {
                    font-weight: 600 !important;
                }

                div.tab-pane hr {
                    display: none;
                }
        /*
        end pagination styles
        */
    </style>

@endsection

{{-- Content Section --}}
@section('content')

<div class="container-fluid">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
            <livewire:evaluation.index />
        </div>

@endsection
{{-- Script Section --}}
<script type="module">

    // $(document).ready(function() {
    //     var searchButton = $('#search_button');
    //     var search = $('#search');

    //     searchButton.click(function() {
    //         if (search.css('display') === 'none') {
    //             search.css('display', 'block');
    //         } else {
    //             search.css('display', 'none');
    //         }
    //     });
    // });
    window.addEventListener('close-modal', event => {
        document.getElementById('closeModal').click()
    });
    window.addEventListener('open-modal', event => {
        document.getElementById('open_modal').click()
    });
    window.addEventListener('close-modal-search', event => {
        document.getElementById('search_button').click()
    });

   
window.addEventListener('show-edit', (event) => {

        var id = event.detail[0].id;

        // Remove styling from all other tr elements and i tag
        document.querySelectorAll('tr').forEach(tr => {
                tr.style.border = '';
                tr.style.boxShadow = '';
                document.querySelectorAll('.pencel_icon_one').forEach(icon => {
                    // Set the visibility of each element to 'hidden'
                    icon.style.visibility = 'hidden';
                });
        });

        var rowElement = document.querySelector('#row' + id);
  
        if (rowElement) {
            
            rowElement.style.border = '3px solid #40e690';
            rowElement.style.boxShadow = '-18px -1px 3px 0px rgb(0, 163, 78)';
        }
       var icon = document.querySelector('#pencel_icon' + id);
       var icon1 = document.querySelector('#pencel_icon1' + id);
        if (icon) {
            icon.style.visibility = 'visible'; // Show the icon
        }
        if (icon1) {
            icon1.style.visibility = 'visible'; // Show the icon
        }
    });

    document.addEventListener('DOMContentLoaded', function () {
        var searchButton = document.getElementById('searchButton');

        if (searchButton) {
            searchButton.addEventListener('click', function (event) {
                event.stopPropagation(); // Stop the event propagation
            });
        }
    });
    //Index Livewire







</script>