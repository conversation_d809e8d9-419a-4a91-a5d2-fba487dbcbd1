    <div class="container-fluid mt-3 px-4">
        {{-- header row  --}}
        <div class="row mx-3 ps-5">
            <div class="col-12 mb-5">
                <div class="card bg-white shadow py-2 px-3">
                    <div class="card-body p-4">
                        <h5>
                            <b>
                                Recordings
                            </b>
                        </h5>
                        {{-- <i class="fa-regular fa-file-excel fa-2xl float-end" style="cursor: pointer"></i> --}}
                        <h6 class="text-muted">
                            Manage and view all calls' recordings
                            <i class="fa-solid fa-microphone-lines fa-2xl float-end" style="color: #00a34e"></i>
                        </h6>
                    </div>
                </div>
            </div>
        </div>


        {{-- bottom row  --}}
        <div class="row mx-3 d-flex ps-5">
            {{-- filters card  --}}
            <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column">
                <div class="card rounded-3 bg-white shadow p-2">
                    <div class="card-body py-3">
                        <h5 class="fw-bold">Filters</h5>
                        <hr>

                        {{-- <div class="col-md-5">
                            <label for="account" class="mb-2 fw-bold">Filter Type</label>
                            <select class="form-select mb-3" wire:change="filterTypeChange($event.target.value)">
                                <option value="general">General</option>
                                <option value="telephony" data-divider="true">Telephony</option>
                                <option value="quality">Quality</option>
                                <option value="user_action" data-divider="true">Users & Action</option>
                                <option value="custom_attributes">Telephony Custom Attributes</option>
                            </select>
                        </div> --}}
                        <div class="col-md-3 mb-3">
                            <label for="exampleInputEmail1">
                                <h6 id="" class="fw-bold" style="color: #00a34e">Filter Type</h6>
                            </label>
                            <div class="dropdown">
                                <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown">
                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filterType }}</span>
                                </button>
                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                    <li>
                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTypeChange('General')">General</span>
                                    </li>
                                    <hr class="m-0">
                                    <li>
                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTypeChange('Telephony')">Telephony</span>
                                    </li>
                                    <hr class="m-0">

                                    <li>
                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTypeChange('Quality')">Quality</span>
                                    </li>
                                    <hr class="m-0">

                                    <li>
                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTypeChange('Users & Action')">Users & Action</span>
                                    </li>
                                    <hr class="m-0">
                                    <li>
                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterTypeChange('Telephony Custom Attributes')">Telephony Custom Attributes</span>
                                    </li>

                                </div>
                            </div>
                        </div>

                        @if ($filterType == 'General')
                            <form class="row g-2 mb-3">
                                <div class="col-md-3">
                                    <label for="date" class="mb-2 fw-bold">Time <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e" title="Date"></i></label>
                                    {{-- <select wire:model="selectedTime" class="form-select form-select mb-3">
                                        <option selected value="all">All Time</option>
                                        <option value="1">Last 24 Hours</option>
                                        <option value="7">Last 7 Days</option>
                                        <option value="40">Last 30 Days</option>
                                        <option value="60">Last 60 Days</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All Time</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Last 24 Hours</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Last 7 Days</span>
                                            </li>
                                            <hr class="m-0">

                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Last 30 Days</span>
                                            </li>
                                            <hr class="m-0">

                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Last 60 Days</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Call ID <i class="fa-solid fa-address-book fa-lg" style="color:#00a34e"></i></label>
                                    <input type="text" name="call-id" id="call-id" class="form-control bg-white" style="border: solid 1px #b6b6b6" placeholder="Call #" wire:model="selectedCallId">
                                </div>
                                <div class="col-md-3">
                                    <label for="Agent name" class="mb-2 fw-bold">Agent ID <i class="fa-solid fa-hashtag fa-lg" style="color:#00a34e" title="Agent ID"></i></label>
                                    <input type="text" name="agent" id="agent" style="border: solid 1px #b6b6b6" class="form-control bg-white" placeholder="Ops ID" wire:model="selectedAgent">
                                </div>
                                <div class="col-md-3">
                                    <label for="Duration" class="mb-2 fw-bold">Duration <i class="fa-solid fa-stopwatch fa-lg" style="color:#00a34e" title="Duration"></i></label>
                                    <input type="number" name="duration" id="duration" style="border: solid 1px #b6b6b6" class="form-control bg-white" placeholder="Duration in seconds" wire:model="duration">
                                </div>
                            </form>
                        @elseif ($filterType == 'Telephony')
                            <form class="row g-2 mb-3">
                                <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">Call Type <i class="fa-solid fa-phone fa-lg" style="color: #00a34e" title="Call Type"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="callType">
                                        <option value="all">All</option>
                                        <option value="incoming">Incoming</option>
                                        <option value="outgoing">Outgoing</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Incoming</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Outgoing</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Called ID <i class="fa-solid fa-arrow-down-wide-short fa-lg" style="color: #00a34e" title="Called ID"></i></label>
                                    <input type="text" name="call-id" id="called-id" class="form-control bg-white" placeholder="Called ID" wire:model="calledId">
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Caller ID <i class="fa-solid fa-arrow-up-wide-short fa-lg" style="color: #00a34e" title="Caller ID"></i></label>
                                    <input type="text" name="call-id" id="caller-id" class="form-control bg-white" placeholder="Caller ID" wire:model="callerId">
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Call Ender <i class="fa-solid fa-phone-slash fa-lg" style="color: #00a34e" title="Call Ender"></i></label>
                                    <input type="text" name="call-ender" id="call-ender" class="form-control bg-white" placeholder="Call Ender" wire:model="callEnder">
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Extension <i class="fa-solid fa-tty fa-lg" title="Extension" style="color: #00a34e"></i></label>
                                    <input type="text" name="extension" id="extension" class="form-control bg-white" placeholder="Extension" wire:model="extension">
                                </div>
                                <div class="col-md-3">
                                    <label for="Duration" class="mb-2 fw-bold">Pause Duration <i class="fa-regular fa-circle-pause fa-lg" style="color: #00a34e" title="Pause Duration"></i></label>
                                    <input type="number" name="pause-duration" id="pause-duration" class="form-control bg-white" placeholder="Duration in seconds" wire:model="pauseDuration">
                                </div>
                                <div class="col-md-3">
                                    <label for="Duration" class="mb-2 fw-bold">Hold Duration <i class="fa-solid fa-phone-flip fa-lg" style="color: #00a34e" title="Hold Duration"></i></label>
                                    <input type="number" name="hold-duration" id="hold-duration" class="form-control bg-white" placeholder="Duration in seconds" wire:model="holdDuration">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="Duration" class="mb-2 fw-bold">Hold Count <i class="fa-solid fa-circle-pause fa-lg" style="color: #00a34e" title="Hold Count"></i></label>
                                    <input type="number" name="hold-count" id="hold-count" class="form-control bg-white" placeholder="Hold Count" wire:model="holdCount">
                                </div>
                                <div class="col-md-3">
                                    <label for="Duration" class="mb-2 fw-bold">Ring <i class="fa-solid fa-phone-volume fa-lg" style="color: #00a34e" title="Ring"></i></label>
                                    <input type="number" name="ring" id="ring" class="form-control bg-white" placeholder="Duration in seconds" wire:model="ring">
                                </div>
                                <div class="col-md-3">
                                    <label for="Duration" class="mb-2 fw-bold">Digits Count</label>
                                    <input type="number" name="digits-count" id="digits-count" class="form-control bg-white" placeholder="# of digits" wire:model="digitsCount">
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Transferred To <i class="fa-solid fa-cloud-arrow-up fa-lg" style="color: #00a34e" title="Transferred To"></i></label>
                                    <input type="text" name="transferred-to" id="transferred-to" class="form-control bg-white" placeholder="Trnasferred To" wire:model="transferredTo">
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Transferred From <i class="fa-solid fa-cloud-arrow-down fa-lg" style="color: #00a34e" title="Transferred From"></i></label>
                                    <input type="text" name="transferred-from" id="transferred-from" class="form-control bg-white" placeholder="Transferred From" wire:model="transferredFrom">
                                </div>

                            </form>
                        @elseif ($filterType == 'Quality')
                            <form class="row g-2 mb-3">
                                <div class="col-md-3">
                                    <label for="Is Assigned" class="mb-2 fw-bold">Is Assigned <i class="fa-solid fa-people-arrows fa-lg" title="Is Assigned" style="color: #00a34e"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="isAssigned">
                                        <option value="all">All</option>
                                        <option value="assigned">Assigned</option>
                                        <option value="notAssigned">Not Assigned</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Assigned</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Not Assigned</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="Duration" class="mb-2 fw-bold">Evaluation Score <i class="fa-solid fa-ranking-star fa-lg" style="color: #00a34e" title="Evaluation Score"></i></label>
                                    <input type="number" name="evaluation-score" id="evaluation-score" class="form-control bg-white" placeholder="Evaluation Score" wire:model="evaluationScore">
                                </div>
                                <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">Evaluated Using</label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="evaluatedUsing">
                                        <option value="all">All</option>
                                        <option value="incoming">reports list</option>
                                        <option value="outgoing">reports list</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Report List</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Report List</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">Fatal Errors <i class="fa-solid fa-ban fa-lg" style="color: #00a34e" title="Includes Fatal Error"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="fatalErrors">
                                        <option value="all">All</option>
                                        <option value="includes">Includes</option>
                                        <option value="doesntInclude">Does not include</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Includes</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Does Not Include</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">Is Evaluated <i class="fa-solid fa-user-tie fa-lg" style="color: #00a34e" title="Evaluator"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="isEvaluated">
                                        <option value="all">All</option>
                                        <option value="evaluated">Evaluated</option>
                                        <option value="notEvaluated">Not Evaluated</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Evaluated</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Not Evaluated</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">Penalties <i class="fa-solid fa-hammer fa-lg" style="color: #00a34e" title="Penalties"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="penalties">
                                        <option value="all">All</option>
                                        <option value="includes">Includes</option>
                                        <option value="doesntInclude">Does not include</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Includes</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Does Not Include</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                {{-- <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">Agent Feedback</label>
                                    <select class="form-select form-select mb-3" wire:model="agentFeedback">
                                        <option value="all">All</option>
                                        <option value="satisfied">Satisfied</option>
                                        <option value="notSatisfied">Not Satisfied</option>
                                        <option value="na">NA</option>
                                    </select>
                                </div> --}}
                                <div class="col-md-3">
                                    <label for="call type" class="mb-2 fw-bold">QA Flagged Interactions</label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="QaFlaggedInteractions">
                                        <option value="all">All Flags</option>
                                        <option value="noFlags">No Flags</option>
                                        <option value="list">GCC Flags</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">No Flags</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">GCC Flags</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="Is Assigned" class="mb-2 fw-bold">Flag Date</label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="flagDate">
                                        <option value="all">All</option>
                                        <option value="selectDate">Select Date</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Select Date</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        @elseif ($filterType == 'Users & Action')
                            <form class="row g-2 mb-3">
                                <div class="col-md-3">
                                    <label for="agent name" class="mb-2 fw-bold">Agent Name <i class="fa-solid fa-id-card fa-lg" title="Agent Name" style="color: #00a34e"></i></label>
                                    <input type="text" name="agent-name" id="agent-name" class="form-control bg-white" placeholder="Agent Name" wire:model="agentName">
                                </div>
                                <div class="col-md-3">
                                    <label for="account" class="mb-2 fw-bold">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Account"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="selectedAccount">
                                        <option selected value="all">All</option>
                                        <option value="gen01">gen01</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">gen01</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">SHEIN</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="account" class="mb-2 fw-bold">Skill Group <i class="fa-solid fa-people-roof fa-lg" style="color: #00a34e" title="Skill Group"></i></label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="skillGroup">
                                        <option selected value="all">All</option>
                                        <option value="gen01">gen01 - Outbound</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Gen01 - Outbound</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="account" class="mb-2 fw-bold">Call Importance</label>
                                    {{-- <select class="form-select form-select mb-3" wire:model="callImportance">
                                        <option selected value="all">All</option>
                                        <option value="normal">Normal</option>
                                        <option value="urgent">Urgent</option>
                                    </select> --}}
                                    <div class="dropdown">
                                        <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                        </button>
                                        <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Normal</span>
                                            </li>
                                            <hr class="m-0">
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Urgent</span>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        @elseif ($filterType == 'Telephony Custom Attributes')
                            <form class="row g-2 mb-3">
                                <div class="col-md-3">
                                    <label for="pause count" class="mb-2 fw-bold">Pause Count <i class="fa-solid fa-pause fa-lg" style="color: #00a34e" title="Pause Count"></i></label>
                                    <input type="number" name="pause-count" id="pause-count" class="form-control bg-white" placeholder="Pause Count" wire:model="pauseCount">
                                </div>
                                <div class="col-md-3">
                                    <label for="call id" class="mb-2 fw-bold">Language <i class="fa-solid fa-language fa-lg" style="color: #00a34e" title="Language"></i></label>
                                    <input type="text" name="language" id="language" class="form-control bg-white" placeholder="Language" wire:model="language">
                                </div>
                            </form>
                        @endif


                        <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply" wire:click="filter">Apply</button>
                        <button class="btn btn-md btn-outline-success ms-1" id="clear" wire:click="clear">Clear</button>
                    </div>
                </div>
            </div>

            {{-- table  --}}
            <div class="col-12 col-md-12 col-lg-12 d-flex flex-column mt-5" style="letter-spacing: 0;" wire:key="table" wire:ignore>
                {{-- <div class="table-responsive shadow p-2 mb-0 pb-0 overflow-hidden" style="height: 27.85rem;"> <!-- 26.5rem --> --}}
                <table class="table table-hover table-bordered" id="table" style="overflow-x:auto; width:100%">
                    <thead id="thead" class="text-secondary table-light small table-header-color">
                        <tr class="" style="vertical-align:middle;">
                            <th scope="col" class="text-center">#</th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-building fa-xl" style="color: #00a34e" title="Account"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-circle-exclamation fa-xl" style="color: #00a34e" title="Urgent"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-phone fa-xl" style="color: #00a34e" title="Call Type"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-hammer fa-xl" style="color: #00a34e" title="Penalties"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-ban fa-xl" style="color: #00a34e" title="Includes Fatal Error"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-comment-dots fa-xl" style="color: #00a34e" title="Contains Comment"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-newspaper fa-xl" style="color: #00a34e" title="Evaluated By"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-hashtag fa-xl" style="color: #00a34e" title="Agent ID"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-id-card fa-xl" title="Agent Name" style="color: #00a34e"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-people-arrows fa-xl" title="Is Assigned" style="color: #00a34e"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-person-circle-check fa-xl" title="Assigned For" style="color: #00a34e"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-user-tie fa-xl" style="color: #00a34e" title="Evaluator"></i></th>
                            {{-- <th scope="col" class="text-center">Call ID</th> --}}
                            <th scope="col" class="text-center"><i class="fa-solid fa-arrow-down-wide-short fa-xl" style="color: #00a34e" title="Called ID"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-arrow-up-wide-short fa-xl" style="color: #00a34e" title="Caller ID"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-phone-slash fa-xl" style="color: #00a34e" title="Call Ender"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-stopwatch fa-xl" style="color: #00a34e" title="Duration"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-microphone-lines fa-xl" title="Record" style="color: #00a34e"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-ranking-star fa-xl" style="color: #00a34e" title="Evaluation Score"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-tty fa-xl" title="Extension" style="color: #00a34e"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-people-group fa-xl" title="Group" style="color: #00a34e"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-circle-pause fa-xl" style="color: #00a34e" title="Hold Count"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-phone-flip fa-xl" style="color: #00a34e" title="Hold Duration"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-phone-volume fa-xl" style="color: #00a34e" title="Ring"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-server fa-xl" style="color: #00a34e" title="Server Name"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-people-roof fa-xl" style="color: #00a34e" title="Skill Group"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-people-roof fa-xl" style="color: #00a34e" title="Skill Group (ACDID)"></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-cloud-arrow-down fa-xl" style="color: #00a34e" title="Transferred From"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-cloud-arrow-up fa-xl" style="color: #00a34e" title="Transferred To"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-pause fa-xl" style="color: #00a34e" title="Pause Count"></i></th>
                            <th scope="col" class="text-center"><i class="fa-solid fa-language fa-xl" style="color: #00a34e" title="Language"></i></th>
                            <th scope="col" class="text-center"><i class="fa-regular fa-circle-pause fa-xl" style="color: #00a34e" title="Pause Duration"></i></th>
                            <th colspan="col" scope="col" class="text-center"><i class="fa-regular fa-calendar-days fa-xl" title="Date" style="color: #00a34e"></i></th>
                        </tr>
                    </thead>
                    <tbody class="" style="font-size:13px" id="tbody">

                        {{-- @forelse($records as $record)
                                <tr class="align-middle">
                                    <td class="text-muted text-center pt-4"> {{ $loop->index + 1 }}</td>
                                    <td class="text-muted text-center pt-4"> gen01 </td>
                                    <td class="text-muted text-center pt-4 text-nowrap"> {{ $record['agent_extenstion'] }} </td>
                                    <td class="text-muted text-center pt-4 text-nowrap"> {{ $record['agent_extenstion'] }} </td>
                                    <td class="text-muted text-center pt-4 text-nowrap"> {{ $record['call_id'] }}</td>
                                    <td class="text-muted text-center">
                                        <audio controls>
                                            <source src="" type="audio/ogg">
                                            <source src="" type="audio/mpeg">
                                            Your browser does not support the audio element.
                                        </audio>
                                    </td>
                                    <td class="text-muted text-center pt-4 text-nowrap"> {{ Carbon::parse($record['insert_date_time'])->format('Y-m-d h:i A') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="18" class="text-muted text-center bg-white"> There is no data found</td>
                                </tr>
                            @endforelse --}}

                    </tbody>
                </table>
                {{-- </div> --}}
            </div>
            {{-- </div> --}}
            {{-- </div> --}}
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script>
        window.addEventListener('renderTable', (event) => {

            $('#tbody').html("");
            $('#table').DataTable().clear().destroy();

            let data = event.detail[0];

            data.forEach((element, index) => {
                // start index from 1 
                index += 1;

                // format date and time 
                let dateObj = new Date(element['arrival_time']);

                var dateTimeOptions = {
                    year: "numeric",
                    month: "2-digit",
                    day: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                };

                // Format the date using toLocaleString, if the date is null from DB, show it as -
                var formattedDateTime = element['arrival_time'] == null ? '-' : dateObj.toLocaleString("en-US", dateTimeOptions);

                // put - instead of null for agent extension
                element['agent_extenstion'] = element['agent_extenstion'] == null ? '-' : element['agent_extenstion'];

                // voice file 'remove everything after the @'
                if (element['call_id'].includes('@')) {
                    voice = `https://oms.extwebonline.com/Extensya_APIs/recording/voice/${element['call_id']}.wav`;
                } else {
                    voice = `https://oms.extwebonline.com/Extensya_APIs/recording/voice/${element['call_id']}@10.202.1.66.wav`;
                }


                $('#tbody').append(`
                    <tr class='align-middle'>
                        <td class='text-muted text-center align-middle' style='cursor:pointer;'>${index}</td>
                        <td class='text-muted text-center'>gen01</td>
                        <td class='text-muted text-center' style='max-width:1rem; padding:0 !important'><i class='fa-solid fa-exclamation text-danger' title='Urgent'></i></td>
                        <td class='text-muted text-center'>In</td>
                        <td class='text-muted text-center'>y</td>
                        <td class='text-muted text-center'>fatal error</td>
                        <td class='text-muted text-center'><i class='fa-solid fa-plus fw-bold' style='color:#00a34e; cursor:pointer'></i></td>
                        <td class='text-muted text-center'><i class="fa-solid fa-file-circle-plus fw-bold"></i></td>
                        <td class='text-muted text-center' style='min-width:6rem; padding:1rem !important'><span class='agentOps'>${element['agent_extenstion']}</span></td>
                        <td class='text-muted text-center' style='min-width:25rem'>${element['agent_extenstion']}</td>
                        <td class='text-muted text-center' style='min-width:1rem;padding:1rem !important'>${element['agent_extenstion']}</td>
                        <td class='text-muted text-center' style='min-width:25rem'>${element['agent_extenstion']}</td>
                        <td class='text-muted text-center' style='min-width:25rem'>${element['agent_extenstion']}</td>
                        <td class='text-muted text-center' style='min-width:25rem; padding:1rem !important'>${element['call_id']}</td>
                        <td class='text-muted text-center' style='min-width:25rem; padding:1rem !important'>${element['call_id']}</td>
                        <td class='text-muted text-center' style='min-width:6rem; padding:1rem !important'>${element['agent_extenstion']}</td>
                        <td class='text-muted text-center' style='min-width:6rem; padding:1rem !important'>${element['agent_extenstion']}</td>
                        <td class='text-muted text-center'>
                            <audio controlsList='nodownload' preload='metadata' controls>
                                <source src='' type='audio/ogg'>
                                <source src='${voice}' type='audio/mpeg'>Your browser does not support the audio element.
                            </audio>
                        </td>
                        <td style='min-width:5rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${element['agent_extenstion']}</td>
                        <td style='min-width:5rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${element['agent_extenstion']}</td>
                        <td style='min-width:5rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${element['agent_extenstion']}</td>
                        <td style='min-width:5rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${element['agent_extenstion']}</td>
                        <td style='min-width:5rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${element['agent_extenstion']}</td>
                        <td style='min-width:4rem; max-width:4rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'10'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'EXT-123AMM'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'Skill Group'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'Skill Group ACDID'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'Random'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'Random'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'6'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'Arabic'}</td>
                        <td style='min-width:10rem; max-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${'Arabic'}</td>
                        <td style='min-width:10rem; padding:0.5rem !important' class='text-muted text-center pt-4'>${formattedDateTime}</td>
                    </tr>`)
            });

            $('#table').DataTable({
                scrollY: 336,
                columnDefs: [{
                    width: '1000',
                    targets: [4, 6]
                }, ],
                scrollX: true,
                scrollCollapse: true,
            });
        });
    </script>
    </div>
