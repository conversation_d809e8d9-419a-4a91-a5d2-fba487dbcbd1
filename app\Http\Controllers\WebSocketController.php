<?php

namespace App\Http\Controllers;

use App\Events\CallDataRequested;
use App\Events\Capture\EndEvent;
use App\Events\Capture\StartEvent;
use App\Jobs\FetchExternalCallJob;
use App\Jobs\Scripts\DetectJob;
use App\Mail\ScriptsMail;
use App\Models\CallRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;


class WebSocketController extends Controller
{
    public function start(Request $request)
    {

        broadcast(new StartEvent($request->ip_data, $request->call_id));

        return true;
    }

    public function end(Request $request)
    {

        broadcast(new EndEvent($request->ip_data, $request->call_id));

        return true;
    }


    public function fetchCall(Request $request)
    {
        $callId = $request->call_id;

        $call = CallRequest::where('call_id',$callId)->first();

        if (!$call){

             CallRequest::create([
                'call_id' => $callId,
                'status' => 'pending',
                'status_attachment' => 'pending'
            ]);

            FetchExternalCallJob::dispatch($callId)->onQueue('fetchCallDetails');

            //event(new CallDataRequested($callId));
        }



        return response()->json(['status' => 'processing', 'call_id' => $callId]);

    }

}
