<!-- WaveSurfer.js CDN -->
@props(['callPath', 'callDate', 'agent_name', 'agent_id', 'callAccount', 'callType', 'callId', 'transcription', 'userGroup'])

{{-- <script src="https://unpkg.com/wavesurfer.js@7"></script>
<script src="https://unpkg.com/wavesurfer.js@7/dist/plugins/regions.min.js"></script>
<script src="https://unpkg.com/wavesurfer.js@7/dist/plugins/hover.min.js"></script> --}}
<script src="{{ asset('js/wavesurfer.min.js') }}"></script>
<script src="{{ asset('js/regions.min.js') }}"></script>
<script src="{{ asset('js/hover.min.js') }}"></script>
<style>
    #waveform {
        width: 100%;

    }

    .controls {
        /*         margin-top: 10px;
 */
        display: flex;
        justify-content: center;
        gap: 10px;
        align-items: center;
    }

    .custom-audio-card {
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
        border-radius: 8px;

        border: none;

        box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
        padding-bottom: 1%;

    }

    #waveform::part(hover-label):before {
      content: '⏱️ ';
    }

  </style>
</style>
<div class="custom-audio-card col-12" style="">
    <div class="row">
        <div class="col-8 p-0 mb-1">
            <div class="audio-card-left ">
                <div class="avatar">
                    <img src="{{ asset('assets/SVG/assets-v2/75.svg') }}" alt="Avatar" />
                </div>
                <div class="audio-card-content ms-3">
                    <div class="audio-card-header">
                        <span class="date-time">{{ $callDate->format('Y/m/d - H:i:s') }}</span>
                        <span class="name-id mb-2">{{ $agent_name }} - {{ $agent_id }}</span>
                        <span class="call-id">{{-- {{ $callId }} --}} {{ $callAccount }} | <b>{{$userGroup}}</b> |
                            {{ $callType }} Call</span>
                    </div>

                </div>
            </div>

        </div>
        <div class="col-4 text-center pt-3">
            <div class="mb-2"> <span class="light-span"> Having a trouble ? </span> </div>
            <button class="btn btn-warning  btn-sm text-white" onclick="window.open('https://pqms05.extensya.com/oms/index.php/help-desk-v3-2/development-ticket', '_blank')">Create a Ticket </button>
        </div>
    </div>
    <div id="waveform"></div>
    <div class="controls mt-2 mb-2 pb-3">


        {{-- <button class="rewind">↩ <span id="rewind-duration">15s</span></button> --}}
        <button class="rewind" style="padding-left: 0 !important; padding-right: 0 !important"><span
                id="rewind-duration">15s</span><img src="{{ asset('assets/SVG/assets-v2/rewind.png') }}" alt="rewind"
                style="width: 1.5rem; margin-bottom:0.8rem !important"></button>
        <div class="timeline">
            <div id="progress" class="progress"></div>
        </div>
        {{-- <button class="forward"><span id="forward-duration">15s</span> ↪</button> --}}
        <button class="forward" style="padding-left: 0 !important; padding-right: 0 !important"><img
                src="{{ asset('assets/SVG/assets-v2/forward.png') }}" alt="rewind"
                style="width: 1.5rem; margin-bottom:0.8rem !important"><span id="forward-duration">15s</span></button>
        <span id="current-time">00:00</span>
        {{-- <button class="more-options">...</button> --}}
        <button class="play-pause-button" id="playPauseBtn">▶</button>
        {{-- <button class="video-btn play-pause-button" id="video-btn" d data-bs-toggle="modal" data-bs-src="https://www.youtube.com/embed/JJUo8Fe3_JY" data-bs-target="#videoModal">📹</button> --}}
        <x-chunks.video-modal :callId="$callId" />

        {{--        <button class="play-pause-button" id="playPauseBtn">▶</button>
        <x-chunks.video-modal :callId="$callId" /> --}}

    </div>
</div>

<script>
    const regions = WaveSurfer.Regions.create({});
    const hover = WaveSurfer.Hover.create({
        lineColor: '#ff0000',
      lineWidth: 2,
      labelBackground: '#555',
      labelColor: '#fff',
      labelSize: '11px',
      content: ''
    });
    const progress = document.getElementById("progress");
    let totalduration = 0;
    let globalcurrentTime = 0;
    const rewindButton = document.querySelector(".rewind");
    const forwardButton = document.querySelector(".forward");
    const currentTimeDuration = document.getElementById("current-time");
    const rewindDurationDisplay = document.getElementById("rewind-duration");
    const forwardDurationDisplay = document.getElementById("forward-duration");
    let peaks = [0.0218, 0.0183, 0.0165, 0.0198, 0.2137, 0.2888, 0.2313, 0.15, 0.2542, 0.2538, 0.2358, 0.1195, 0.1591, 0.2599, 0.2742, 0.1447, 0.2328, 0.1878, 0.1988, 0.1645, 0.1218, 0.2005, 0.2828, 0.2051, 0.1664, 0.1181, 0.1621, 0.2966, 0.189, 0.246, 0.2445, 0.1621, 0.1618, 0.189, 0.2354, 0.1561, 0.1638, 0.2799, 0.0923, 0.1659, 0.1675, 0.1268, 0.0984, 0.0997, 0.1248, 0.1495, 0.1431, 0.1236, 0.1755, 0.1183, 0.1349, 0.1018, 0.1109, 0.1833, 0.1813, 0.1422, 0.0961, 0.1191, 0.0791, 0.0631, 0.0315, 0.0157, 0.0166, 0.0108];

@php
$signedUrl = URL::temporarySignedRoute(
    'audio',
    now()->addMinutes(30),
    ['filename' => $callId . '_final.wav']
    );
@endphp
    const audioUrl = @json($signedUrl)// Replace with your audio URL

    const wavesurfer = WaveSurfer.create({
        container: '#waveform',
        waveColor: '#696969',
        progressColor: '#000',
        height: 105,
        responsive: true,
        barWidth: 6,
        mediaControls: false,
        url: audioUrl,
        peaks: peaks,
        minPxPerSec: 1,
        hideScrollbar: true,
        dragToSeek: true,

        plugins: [
            @if(Auth::user()->parameters()->where('parameter_id', 33)->exists())
            regions, // Initialize Regions plugin
            @endif

        ]
    });

    document.getElementById('playPauseBtn').addEventListener('click', () => {
        wavesurfer.playPause();
    });

    wavesurfer.on('pause', () => {
        document.getElementById('playPauseBtn').textContent = '▶';
    });

    wavesurfer.on('play', () => {
        document.getElementById('playPauseBtn').textContent = '⏸';
    });




    wavesurfer.on('timeupdate', (currentTime) => {


        console.log(currentTime);

     goToTransc(currentTime);
        globalcurrentTime = currentTime;
        const percentage = (currentTime / totalduration) * 100;
        progress.style.width = `${percentage}%`;

        const minutes = Math.floor(currentTime / 60).toString().padStart(2, "0");
        const seconds = Math.floor(currentTime % 60).toString().padStart(2, "0");
        currentTimeDuration.textContent = `${minutes}:${seconds}`;

    });







    rewindButton.addEventListener("click", () => {
    const rewindTime = Math.floor(totalduration * 0.1); // 10% of total duration
    const newTime = Math.max(0, globalcurrentTime - rewindTime); // Ensure it doesn't go below 0

    // Normalize newTime to a value between 0 and 1
    const normalizedTime = newTime / totalduration;
    wavesurfer.seekTo(normalizedTime);

    console.log(`Rewinded to: ${newTime}s`);
});

// Forward by 10% of the total duration
forwardButton.addEventListener("click", () => {
    const forwardTime = Math.floor(totalduration * 0.1); // 10% of total duration
    const newTime = Math.min(totalduration, globalcurrentTime + forwardTime); // Ensure it doesn't exceed total duration

    // Normalize newTime to a value between 0 and 1
    const normalizedTime = newTime / totalduration;
    wavesurfer.seekTo(normalizedTime);

    console.log(`Forwarded to: ${newTime}s`);
});
    // Add regions when the waveform is ready

    wavesurfer.on('ready', (duration) => {
        // Add a thumbs-up emoji at 5 seconds
        // Create the main SVG element
        const duration10Percent = Math.floor(duration * 0.1); // 10% of total duration
        rewindDurationDisplay.textContent = `${duration10Percent}s`;
        forwardDurationDisplay.textContent = `${duration10Percent}s`;
        totalduration = duration;


        // Append the SVG to the container
        // Add the required classes for Font Awesome and Bootstrap

        @foreach ($transcription as $singleRow)
            @if (strtolower($singleRow->classification) == 'positive')
            console.log('{{$singleRow->content}}');

                regions.addRegion({
                    start: {{ $singleRow->duration_from }},
                    end: {{ $singleRow->duration_to + 2 }},
                    content: createThumbsUpSVG( '{{$singleRow->content}}'), // Create new thumbs-up SVG
                    color: 'transparent',
                    minLength: 1,
                    maxLength: 20,
                    drag: false,
                    resize: false,
                });
            @elseif (strtolower($singleRow->classification) == 'negative')
                   console.log('{{$singleRow->content}}');
                regions.addRegion({
                    start: {{ $singleRow->duration_from }},
                    end: {{ $singleRow->duration_to }},
                    content: createThumbsDownSVG('{{$singleRow->content}}'), // Create new thumbs-down SVG
                    color: 'transparent',
                    minLength: 1,
                    maxLength: 10,
                    drag: false,
                    resize: false,
                });
            @endif
        @endforeach
        const elementsWithPart = document.querySelectorAll('[part]');

        // Log or manipulate the elements
        elementsWithPart.forEach(element => {
            console.log(element);
            // Example: Add a class to each element
            element.classList.add('highlight');
        });

        // Add another thumbs-up emoji at 10 seconds

    });


    function createThumbsUpSVG(content) {
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '15px');
        svg.setAttribute('height', '15px');
        svg.setAttribute('viewBox', '0 0 45 45');
        svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        svg.setAttribute('fill', '#03a34d');
        svg.setAttribute('stroke', '#03a34d');
        svg.setAttribute('style', 'margin-top:0 !important');
        svg.style.marginTop = '0'; // Explicitly reset margin

        const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        title.textContent = content; // Add the tooltip text
        svg.appendChild(title);
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d',
            'M38,17H31l.4-3.3C32,8.8,31,4.9,27.8,4h-.3A2,2,0,0,0,26,5.2s-5.7,12-9,14.4V40h1.3a1.6,1.6,0,0,1,1.2.4c1.4,1,6.1,3.6,8.5,3.6h5c5.9,0,11-4,11.5-11.9h0l.5-8A6.7,6.7,0,0,0,38,17ZM3,22V38a2,2,0,0,0,2,2h8V20H5A2,2,0,0,0,3,22Z'
        );
        svg.appendChild(path);
        return svg;
    }

    // Function to create a new thumbs-down SVG
    function createThumbsDownSVG(content) {
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '15px');
        svg.setAttribute('height', '15px');
        svg.setAttribute('viewBox', '0 0 45 45');
        svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        svg.setAttribute('fill', '#ff4747');
        svg.setAttribute('stroke', '#ff4747');
        svg.setAttribute('transform', 'rotate(180)');
        svg.setAttribute('style', 'margin-top:0 !important; color:red');
        svg.style.marginTop = '0'; // Explicitly reset margin

        const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        title.textContent = content; // Add the tooltip text
        svg.appendChild(title);
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d',
            'M38,17H31l.4-3.3C32,8.8,31,4.9,27.8,4h-.3A2,2,0,0,0,26,5.2s-5.7,12-9,14.4V40h1.3a1.6,1.6,0,0,1,1.2.4c1.4,1,6.1,3.6,8.5,3.6h5c5.9,0,11-4,11.5-11.9h0l.5-8A6.7,6.7,0,0,0,38,17ZM3,22V38a2,2,0,0,0,2,2h8V20H5A2,2,0,0,0,3,22Z'
        );
        svg.appendChild(path);
        return svg;
    }
    let prevD = null; // Store the previous highlighted element

    function goToTransc(time_id)

    {
        //let allDs = document.querySelectorAll("[data-from-time]");


        let transcD = document.querySelector(`[data-from-time='${parseInt(time_id)}']`);
        if (!transcD) return; // Exit if no matching element found
        if (prevD) {
        prevD.style.border = 'none';
    }

            transcD.style.border = '3px solid gray';
            transcD.scrollIntoView({
                    behavior: 'smooth', // Smooth scrolling animation
                    block: 'center', // Scroll to the center of the viewport
                });


        prevD = transcD;

    }
</script>


