<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('evaluation_submission_answers', function (Blueprint $table) {

            $table->id();
            $table->timestamps();
            $table->foreignId('evaluation_submission_id');
            $table->foreignId('evaluation_question_id');
            $table->longText('comment')->nullable();;
            $table->string('mark');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('evaluation_submission_answers');
    }
};
