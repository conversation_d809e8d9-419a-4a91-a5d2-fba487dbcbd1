<?php

namespace App\Livewire\Reports;

use App\Models\QualityCheck;
use Livewire\Component;
use Livewire\WithPagination;

class InteractionQuality extends Component
{
    use WithPagination;

    public $callIdFilter = '';
    public $perPage = 15;
    protected $paginationTheme = 'bootstrap';

    public function perPage($value)
    {
        $this->perPage = (int) $value;
        $this->resetPage();
    }
    public function recordsPerPage($value)
    {
        $this->perPage = (int) $value;
        $this->resetPage();
    }

    public function filterByCallId()
    {
        $this->resetPage();
    }

    public function clearFilter()
    {
        $this->callIdFilter = '';
        $this->resetPage();
    }

    public function render()
    {
        $query = QualityCheck::query();
        if ($this->callIdFilter) {
            $query->where('call_id', 'like', "%{$this->callIdFilter}%");
        }
        $qualityChecks = $query->paginate($this->perPage);
        return view('livewire.reports.interaction-quality', [
            'qualityChecks' => $qualityChecks,
        ]);
    }
}
