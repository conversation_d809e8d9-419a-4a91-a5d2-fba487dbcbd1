<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;

class EvaluationForms extends Component
{
    use LivewireAlert, WithPagination;

    // search supervisor (modal edit user group)
    public $searchSupervisors = '';
    public $supers = [];

    // Users modal
    public $activeTab = 'general';

    // public $users;
    public $selectedUserId;
    public $selectedUserName;
    public $selectedUserRole;

    public $theIdToDelete;

    public $email;
    public $name;
    public $password;
    public $role;

    protected $paginationTheme = 'bootstrap';

    public function render()
    {
        return view('livewire.evaluation-forms', [
            'users' => User::orderBy('agent_id')->paginate(5)
        ]);
    }
}
