@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Evaluation Reports')

{{-- Style Section --}}
@section('style')

    <style>

         /* .d-block {
            width: 60px;
            height: 59px;
        } */
        .fa-magnifying-glass{
            position: absolute;
            left: 2%;
            top: 25%;
        }
        .p-10 {
            padding: 10px;
        }
        .b-radius-10 {
            border-radius: 10px;
        }
        .header-field{
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .b-radius-10 {
            border-radius: 10px;
        }

        :focus-visible {
            outline: none;
        }
        .div-search {
            padding-left: 0px;
        }
        .filters-block {
            display: inline-flex;
            width: 36%;
            justify-content: end;
            float: right;
            position: absolute;
            right: 30px;
            padding-top: 4px;
        }

        .mt-10 {
            margin-right: 12px;
        }

        .btn-color {
            height: 43px;
            background-color: #00a34e;
            border-color: #00a34e;
            width: 100%;
        }

        .btn-dark {
            height: 43px;
        }
        .modal-button-search {
            position: relative;
            /* Higher z-index value to bring the button to the front */
        }

        .modal-content-search {
            position: absolute !important;
            top: 125% !important;
            left: -9% !important;
            background-color: #fff !important;
            box-shadow: 0 0.125rem 7.25rem #00000013 !important;
            width: 500px;
            border-radius: 10px !important;
            z-index: 1 !important;
            display: none;
        }

        .style-btn-search {
            margin: 5px;
            padding: 0px;
            width: 100%;
            background-color: #00a34e;
            border-color: #00a34e;
        }

        .input-search {
            width: 100%;
            border: var(--bs-border-width) var(--bs-border-style) #ffffff !important;
            vertical-align: sub;
            margin-top: 5px;
            padding: 0px;
        }

        .search-icon {
            margin-top: 5px;
        }
        .style-border{
            border-radius: 10px;
            border: 1px solid #dddddd;
        }
        .from-to-date{
            border: 1px solid #d7d7d7;
            height: 100%;
            margin: 0px;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            background-color: #eceff7;
            cursor: pointer;
        }
        .date-field{
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 0.0rem .5rem #00000026!important;
            height: 100%;
        }

        .parent-sections {
            height: 200px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-top: 3%;
            margin-bottom: 2%;
        }

        @media (max-width: 576px) {
            .parent-sections {
                height: 200px;
            }
        }
        @media (min-width: 768px) {
            .parent-sections {
                height: 250px;
            }
        }
        @media (min-width: 992px) {
            .parent-sections {
                height: 350px;
            }
        }
        @media (min-width: 1200px) {
            .parent-sections {
                height: 450px;

            }
        }
        @media (min-width: 2000px) {
            .parent-sections {
                height: 700px;

            }
        }
        .section-one{
            width: 67%;
        }
        .section-tow{
            width: 28%;
            height: 100%;

        }
        .div-table{
            /* border: 1px solid #d0caca; */
            border-radius: 0px;
            width: 100%;
            height: 100%;
            overflow: auto;
        }
        .table{
            margin-bottom: 0;
            text-align: center;
        }
        th{
            border-bottom-color: currentColor;
            vertical-align: middle;
        }
        .thead{
            /* border-bottom: 2.3px solid #d0caca; */
            height: 50px;
            vertical-align: middle;
        }
        .table tbody tr td {
            background-color:white !important;
            vertical-align: middle;
            font-size: 12px;
        }
        .section-tow{
            background-color: white;
            border-radius: 10px;
            border: 1px solid white;
        }
        .shadow{
            box-shadow: 0 0.1rem .5rem #00000026!important;
        }
        .filter-header{
            height: 10%;
            width: 100%;
            padding: 4%;
        }
        hr{
            color: #00a34e;
            margin: 0;
        }
        .filter-body{
            padding: 5% 7%;
            height: 70%;
            overflow: auto;
        }
        .category{
            width: 35% !important;
            border: 1px solid #e3e3e3;
            border-radius: 40px;
            padding: 1%;
            display: flex;
            flex-direction: row;
            margin-right: 5%;
            margin-bottom: 4%;
            cursor: pointer;
            background-color: white;
            display: flex;
            flex-direction: row;
        }
        .spanText{
            margin-left: 10% !important;
        }
        .category-exc1{
            width: 34% !important;
        }
        .category-exc2{
            width: 50% !important;
        }

        .text-box{
            margin-left: 20px;
        }
        .main-filter{
            display: flex;
            flex-direction: row;
        }
        .main-filter{
            margin-bottom: 7%;
        }
        .custom-select select {

            border-radius: 0px;
            cursor: pointer;
        }

        .arrow-icon {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            pointer-events: none;
            fill: #00a34e;
            width: 36px;
            height: 32px;
        }
        .field-type{
            padding: 10% 8%;
        }
        .filter-footer{
            padding: 0% 20%;
        }
        .from-date{
            margin-right: 3%;
        }

        .card-with-motion{
    opacity: 0; /* Start hidden */
    transform: translateY(20px); /* Start slightly shifted */
    animation: fadeIn 0.5s ease-out forwards;
    max-width: 19rem;
         }
         .card-with-motion:hover{
            background: #f8f8f8 !important;
            transform: scale(1.05) !important; /* Make the card slightly larger */
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3); /* Add a larger shadow */
         }

         .card-with-motion:nth-child(1) {
    animation-delay: 0.2s; /* Delayed by 0.2s */
}

.card-with-motion:nth-child(2) {
    animation-delay: 0.4s;
}

.card-with-motion:nth-child(3) {
    animation-delay: 0.6s;
}

.card-with-motion:nth-child(4) {
    animation-delay: 0.8s;
}

.card-with-motion:nth-child(5) {
    animation-delay: 1s;
}
@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px); /* Offset to animate into position */
    }
    100% {
        opacity: 1;
        transform: translateY(0); /* End in the original position */
    }
}
    </style>




@endsection

@section('content')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
    <div class="container-fluid mt-3 px-4">

        <div class="row mx-3 ps-5 d-flex flex-wrap justify-content-start" style="gap:3rem; padding-left: 5% !important;">
            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('evaluation.report', ['reportType' => 'evaluation_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#fff0d4 !important">
                        <i class="fas fa-clipboard-list" style="font-size: 30px; color: #ffc560 !important;"></i>
                    </div>
                    <div>
                        <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Evaluation Report</h6>
                        <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                            Overview of agent performance and scores.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('evaluation.report', ['reportType' => 'evaluation_parameteres_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#eed5fc !important">
                        <i class="fas fa-cogs" style="font-size: 30px; color: #cb75fe !important;"></i>
                    </div>
                    <div>
                        <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Parameters Report</h6>
                        <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                            Evaluation criteria and performance parameters.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('evaluation.report', ['reportType' => 'evaluation_avg_score_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#fff0d4 !important">
                        <i class="fas fa-chart-bar" style="font-size: 30px; color: #ffc262 !important;"></i>
                    </div>
                    <div>
                        <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Avg. Score Report</h6>
                        <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                            Average evaluation score per interaction.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('evaluation.report', ['reportType' => 'agent_wise_defect_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#fed8d9 !important">
                        <i class="fas fa-user-times" style="font-size: 30px; color: #ff7575 !important;"></i>
                    </div>
                    <div>
                        <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Agent Defect Report</h6>
                        <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                            Report highlighting agent-specific defects.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('evaluation.report', ['reportType' => 'evaluation_agent_avg_score_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#f2d5ff !important">
                        <i class="fas fa-users" style="font-size: 30px; color: #c27af5 !important;"></i>
                    </div>
                    <div>
                        <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Agent Avg. Score</h6>
                        <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                            Average score across all agents.
                        </p>
                    </div>
                </a>
            </div>

            @if (in_array(Auth::user()->role, ['2', '5']))
                <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                    <a href="{{ route('evaluation.report', ['reportType' => 'account_wise_defect_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#d7e5ff !important">
                            <i class="fas fa-building" style="font-size: 30px; color: #94b4f8 !important;"></i>
                        </div>
                        <div>
                            <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Account Defect Report</h6>
                            <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                                Defects reported per account.
                            </p>
                        </div>
                    </a>
                </div>

                <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                    <a href="{{ route('evaluation.report', ['reportType' => 'qa_occupancy_report']) }}" class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color:#fed7da !important">
                            <i class="fas fa-users-cog" style="font-size: 30px; color: #ff7879 !important;"></i>
                        </div>
                        <div>
                            <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">QA Occupancy Report</h6>
                            <p class="text-muted" style="font-size: 14px;color: #8f9b9e;">
                                QA occupancy and monitoring report.
                            </p>
                        </div>
                    </a>
                </div>
            @endif

            {{-- <div class="ps-0">
                <div class="col-6">
                    <a href="{{ route('admin.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                        <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;"> <span style="font-size: 17px;color:white">Previous</span>
                    </a>
                </div>
            </div> --}}
        </div>










    </div>
    </div>
@endsection




