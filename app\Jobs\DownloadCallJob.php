<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DownloadCallJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    /**
     * Max time (in seconds) the job should run.
     */
    public $timeout = 1440000; // 10 minutes

    /**
     * Max attempts before failing.
     */
    public $tries = 3;

    protected $fileNames;

    public function __construct(array $fileNames)
    {
        $this->fileNames = $fileNames;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

            $fileName = $this->fileNames;

            try {
                // Fetch the file content
                $response = Http::withoutVerifying()->timeout(300*3)->get($fileName);

                if ($response->successful()) {
                    // Get the file content
                    $content = $response->body();

                    // Generate the file path in the public directory
                    $path = 'calls/' . basename($fileName);

                    // Store the file in the "public" disk
                    Storage::disk('calls_volume')->put($path, $content);

                    $call_id = str_replace('_final', '', pathinfo(parse_url($fileName, PHP_URL_PATH), PATHINFO_FILENAME));

                    Log::info("Call ID set to downloaded: $call_id");
                    Log::info("File stored successfully in public directory: $path");
                } else {
                    Log::error("Failed to download file: $fileName (HTTP {$response->status()})");
                }
            } catch (\Exception $e) {
                // Log error and continue with the next file
                Log::error("Failed to process file: $fileName", [
                    'error' => $e->getMessage(),
                ]);
            }

    }
}
