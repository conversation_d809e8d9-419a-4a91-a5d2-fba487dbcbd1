@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Groups Page')

{{-- Style Section --}}
@section('style')

    <style>


        .but{
            padding-left: 20px;
            padding-right: 20px;
            border-radius: 40px;
            border-color: #e0f4ea;
            background-color: #e0f4ea;
            border: 2px;
            padding-top: 5px;
            padding-bottom: 5px;
        }
        .row-tow-table{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            height: 70vh !important;
        }
        .table2 , .table1{
            /* height: 24rem !important; */
            height: 70vh !important;
            overflow-y: auto ;
        }
        .dropdown-container , .dropdown-container2{
            width: 11% !important;
            /*right: 17%;*/
            --bs-dropdown-zindex: 1000;
            --bs-dropdown-min-width: 10rem;
            --bs-dropdown-padding-x: 0;
            --bs-dropdown-padding-y: .5rem;
            --bs-dropdown-spacer: .125rem;
            --bs-dropdown-font-size: .9rem;
            --bs-dropdown-color: #212529;
            --bs-dropdown-bg: #fff;
            --bs-dropdown-border-color: var(--bs-border-color-translucent);
            --bs-dropdown-border-radius: .375rem;
            --bs-dropdown-border-width: 1px;
            --bs-dropdown-inner-border-radius: calc(.375rem - 1px);
            --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
            --bs-dropdown-divider-margin-y: .5rem;
            --bs-dropdown-box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
            --bs-dropdown-link-color: #212529;
            --bs-dropdown-link-hover-color: #1e2125;
            --bs-dropdown-link-hover-bg: #e9ecef;
            --bs-dropdown-link-active-color: #fff;
            --bs-dropdown-link-active-bg: #0d6efd;
            --bs-dropdown-link-disabled-color: #adb5bd;
            --bs-dropdown-item-padding-x: 1rem;
            --bs-dropdown-item-padding-y: .25rem;
            --bs-dropdown-header-color: #6c757d;
            --bs-dropdown-header-padding-x: 1rem;
            --bs-dropdown-header-padding-y: .5rem;
            position: absolute;
            z-index: var(--bs-dropdown-zindex);
            min-width: var(--bs-dropdown-min-width);
            padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
            margin: 0;
            font-size: var(--bs-dropdown-font-size);
            color: var(--bs-dropdown-color);

            list-style: none;
            background-color: var(--bs-dropdown-bg);
            background-clip: padding-box;
            border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
            border-radius: var(--bs-dropdown-border-radius);
        }

        /*
        new styles
        */
            .switche{
                font-size: 150%;
                cursor: pointer;
            }
            .table{
                text-align: center;
                vertical-align: middle
            }
            .span_icon{
                vertical-align: sub;
                float: inline-end;
                cursor: pointer;
            }
            td{
                vertical-align: middle;
                font-size: 12px;
            }
            thead{
                height: 50px;
                vertical-align: middle;
                
            }
            thead tr th{
                vertical-align: middle;
                background-color: #40798c !important;
                color:white !important;
                font-size: 15px;
            }
            .parent-sections {
                height: 70vh;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-top: 1.5%;
                margin-bottom: 3%;
            }

            .section-one{
                width: 100%;
                height: 100%;
            }
            .div-table{
                /* border: 1px solid #d0caca; */
                border-radius: 0px;
                width: 100%;
                height: 100%;
                overflow: auto;
            }
            .form-control , .form-select , .dropdown-toggle-style{
                background-color: #eff3f4 !important;
                border: none !important;
                height: 40px;
            }
            label{
                color : #40798c !important;
                font-size: 17px;
                /* font-weight: 700 !important; */
            }
            .previous{
                margin-bottom: 5px;
            }
        /*
        end new styles
        */
        /*
        pagination styles
        */
                #searchInput {
                    height: 2.8rem !important;
                    width: 100% !important;
                    /* Increase the height for a larger input */
                    padding-left: 2.5rem !important;
                    /* Increase padding for better spacing */
                    border: none !important;
                    /* Slightly darker border */
                    border-radius: 0.5rem;
                    /* Rounded corners */
                    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
                    /* Subtle shadow */
                    transition: box-shadow 0.3s ease, border-color 0.3s ease;
                    /* Smooth transition */
                    font-size: 1.2rem;
                    /* Slightly larger text size */
                    background-position: left 0.5rem center;
                    /* Icon positioning */
                }

                /* Focus styles */
                #searchInput:focus {
                    outline: none;
                    /* Remove default outline */
                    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
                    /* Larger shadow on focus */
                    border-color: rgba(0, 0, 0, 0.3);
                    /* Slightly darker border on focus */
                }

                /* Placeholder styling */
                #searchInput::placeholder {
                    font-family: inherit;
                    /* Use inherited font style */
                    color: #01A44F;
                    /* Green placeholder text */
                    font-size: 1.2rem;
                    /* Match placeholder size with input text */
                }

                .main-buttons-container button {
                    height: 2.9rem;
                    font-size: 15px;
                }

                .main-buttons-container button:hover {
                    background-color: #018F3E !important;
                }

                /* pagination  */
                ul.pagination {
                    gap: 0.3rem;
                }

                ul.pagination li button,
                ul.pagination li span {
                    padding: 0.9rem;
                    padding-top: 0.5rem;
                    padding-bottom: 0.5rem;
                }

                ul.pagination li button:hover {
                    background-color: rgb(196, 183, 183) !important;
                }

                ul.pagination>li>button,
                ul.pagination>li>span {
                    color: black !important;
                    font-weight: 600 !important;
                    background-color: white;
                }

                .page-item span,
                .page-item button {
                    border-radius: 0.7rem !important;
                }

                .page-item.active span,
                .page-item.active button {
                    border-radius: 0.5rem !important;
                }

                .page-item.active>span {
                    background-color: #00a34e !important;
                    color: white !important;
                }

                div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
                    font-size: 0.9rem;
                }

                div.tab-pane {
                    font-weight: 600 !important;
                }

                div.tab-pane hr {
                    display: none;
                }
        /*
        end pagination styles
        */
    </style>




@endsection

{{-- Content Section --}}
@section('content')
    <div class="container-fluid">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
        <livewire:evaluation.create-group :id="$id" />
    </div>
@endsection

{{-- Script Section --}}

    <script type="module">

document.addEventListener('DOMContentLoaded', function() {
    var searchButton = document.getElementById('search_button');
    var search = document.getElementById('search');

    searchButton.addEventListener('click', function() {
        if (search.style.display === 'none') {
            search.style.display = 'block';
        } else {
            search.style.display = 'none';
        }
    });
});
        window.addEventListener('close-modal', event => {
            document.getElementById('closeModal').click()
        });
        window.addEventListener('open-modal', event => {
            document.querySelector('#open_modal').click()
        });
        window.addEventListener('load', () => {
            document.querySelector('#search').style.display = 'none';
        });
        window.addEventListener('close-modal-search', event => {
            document.querySelector('#search_button').click()
        });
        window.addEventListener('reset-tabel', event => {
            document.querySelectorAll('tr').forEach(tr => {
                    tr.style.border = '';
                    tr.style.boxShadow = '';
                tr.querySelectorAll('i').forEach(span => {
                    span.style.visibility = 'hidden';
                });
            });
        });
        window.addEventListener('show-edit', (event) => {
            var id = event.detail[0].id;
            // Remove styling from all other tr elements and i tag
            document.querySelectorAll('tr').forEach(tr => {
                    tr.style.border = '';
                    tr.style.boxShadow = '';
                tr.querySelectorAll('i').forEach(span => {
                    span.style.visibility = 'hidden';
                });
            });

            var rowElement = document.querySelector('#row' + id); // Use '+' for string concatenation
            if (rowElement) {
                // alert(rowElement)
                rowElement.style.border = '3px solid #40e690'; // Setting border style
                rowElement.style.boxShadow = '-2px -1px 3px 0px rgb(0 163 78)'; // Adding box-shadow
            }

            var icon = document.querySelector('#pencel_icon' + id);
            var icon2 = document.querySelector('#pencel_icon2' + id);
            if (icon) {
                icon.style.visibility = 'visible';
            }
            if (icon2) {
                icon2.style.visibility = 'visible';
            }
        });
        document.addEventListener('DOMContentLoaded', function () {
            var searchButton = document.getElementById('searchButton');

            if (searchButton) {
                searchButton.addEventListener('click', function (event) {
                    event.stopPropagation(); // Stop the event propagation
                });
            }
        });





        window.addEventListener('open-modal-edit', (event) => {
            var id = event.detail[0].id;
            var rowElement = document.querySelector('#td' + id); // Use '+' for string concatenation
            var dropdownContainer = document.getElementById('dropdownContainer');

            if (rowElement && dropdownContainer) {
                // Calculate the position relative to the rowElement
                var rect = rowElement.getBoundingClientRect();
                var containerRect = dropdownContainer.getBoundingClientRect();
                var leftPosition = rect.left + window.pageXOffset;
                var topPosition = rect.bottom + window.pageYOffset;

                // Adjust the left position to align the center of the container with the center of the rowElement
                leftPosition += (rect.width - containerRect.width) / 2;

                // Set the position of the dropdown container
                dropdownContainer.style.position = 'absolute';
                dropdownContainer.style.left = leftPosition + 'px';
                dropdownContainer.style.top = topPosition + 'px';
                dropdownContainer.style.display = 'block';
            }

        });
        window.addEventListener('close-modal-weight', event => {
            var dropdownContainer = document.getElementById('dropdownContainer');
            dropdownContainer.style.display = 'none';

        });

        window.addEventListener('open-modal-edit2', (event) => {
            var id = event.detail[0].id;
            var rowElement = document.querySelector('#td2' + id); // Use '+' for string concatenation
            var dropdownContainer = document.getElementById('dropdownContainer2');

            if (rowElement && dropdownContainer) {
                // Calculate the position relative to the rowElement
                var rect = rowElement.getBoundingClientRect();
                var containerRect = dropdownContainer.getBoundingClientRect();
                var leftPosition = rect.left + window.pageXOffset;
                var topPosition = rect.bottom + window.pageYOffset;

                // Adjust the left position to align the center of the container with the center of the rowElement
                leftPosition += (rect.width - containerRect.width) / 2;

                // Set the position of the dropdown container
                dropdownContainer.style.position = 'absolute';
                dropdownContainer.style.left = leftPosition + 'px';
                dropdownContainer.style.top = topPosition + 'px';
                dropdownContainer.style.display = 'block';
            }
        });


        window.addEventListener('close-modal-group-name', event => {
            var dropdownContainer = document.getElementById('dropdownContainer2');
            dropdownContainer.style.display = 'none';

        });


        document.addEventListener('click', function(event) {
            var dropdown1 = document.getElementById('dropdownContainer');
            var dropdown2 = document.getElementById('dropdownContainer2');

            var targetElement = event.target;

            // Check if the clicked element is NOT within dropdownContainer or dropdownContainer2
            if (targetElement != dropdown1 && !dropdown1.contains(targetElement) &&
                targetElement != dropdown2 && !dropdown2.contains(targetElement)) {
                // Hide both dropdowns if click occurred outside
                dropdown1.style.display = 'none';
                dropdown2.style.display = 'none';
            }
        });
    </script>

