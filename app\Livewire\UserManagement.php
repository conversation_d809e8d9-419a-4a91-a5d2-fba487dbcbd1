<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use App\Models\Parameter;
use App\Models\UserGroup;
use App\Models\Evaluation;
use App\Models\Permission;
use App\Exports\UserExport;
use App\Imports\UsersImport;
use App\Models\Organization;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use App\Models\SupervisorGroup;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class UserManagement extends Component
{

    use LivewireAlert, WithPagination, withFileUploads;

    public $perPage = 15;

    public $filter_name = null;
    public $filter_agent_id = null;
    public $filter_user_group = null;
    public $filter_role = null;
    public $filter_terminated = null;
    public $filter_enabled = null;

    // add multi users 
    public $importFile;

    public $createModal = false;

    // Users modal
    public $activeTab = 'general';

    // edit modal 
    public $selectedUserId;
    public $N;
    public $selectedUserRole;
    public $selectedFullName;
    public $selectedUserEmail;
    public $selectedUserName;
    public $selectedUserAgentId;
    public $selectedUserOrganizationId;
    public $selectedUserOrganizationName;
    public $selectedUserUserGroups;
    public $selectedUserUserGroupsIds;
    public $selectedUserDirectSupervisorId;
    public $selectedUserDirectSupervisor;
    public $selectedUserTerminated;
    public $selectedUserPasswordPolicy;
    public $selectedUserPasswordPolicyPeriod;
    public $selectedUserParams = [];
    public $selectedUserPermissions = [];
    public $selectedSupervisorOrgs = [];



    public $selectedUserResetNextLogin;

    // after changing the org - edit modal
    public $possibleUserGroups_edit = [];

    public $selectedSupervisorUserGroups = [];
    public $selectedSupervisorUserGroups_ids = [];

    public $selectedUserEvaluationForms = [];

    public $searchUserGroups = '';
    public $selectedUserGroups = [];

    public $searchOrganizations = '';
    public $selectedOrganizations = [];

    public $theIdToDelete;

    public $email;
    public $name;
    public $password;
    public $role;

    // live search 
    public $searchUser = '';
    public $selectedUser = [];
    public $sortBy = 'full_name';
    public $sortDir = 'ASC';


    public $multiUserGroups;

    // parameters 
    // public $param_duration = false;

    // add user modal 
    public $added_role = 'Agent';
    public $added_id;
    public $added_role_id = 4;
    public $added_fullName;
    public $added_username;
    public $added_password;
    public $added_email;
    public $added_organization_id;
    public $added_organization;
    public $supervisorsToAdd = [];
    public $added_directSupervisor_id;
    public $added_directSupervisor;

    public $addAgentuserGroups = [];
    public $added_agent_userGroup_id;
    public $added_agent_userGroup;

    public $addSupervisorOrganizations_ids = [];
    public $addSupervisorOrganizations = [];
    public $addSupervisorUserGroups = [];

    public $added_SupervisorUserGroups = [];
    public $added_SupervisorUserGroups_ids = [];

    public $selectedEvaluationForms = [];
    public $searchEvaluationForms = '';


    // public $added_supervisor_userGroup_id;
    // public $added_supervisor_userGroup;


    protected $paginationTheme = 'bootstrap';


    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }

    public function getListeners()
    {
        return [
            'confirmed'
        ];
    }

    public function mount()
    {
        $this->selectedUserParams = [
            'Duration',
            'Called ID',
            'Caller ID',
            'Hold Duration',
            'Evaluation Score',
            'Is Evaluated',
            'Agent Name',
            'Comment',
        ];

        $this->selectedUserPermissions = [
            'Allow Access Telephony',
            'Play Recorded Interactions',
            'Show Evaluated Interactions Only',
            'View Other Users Interactions Comments',
            'View Interactions Evaluations',
            'View Evaluation Reports'
        ];
    }

    public function selectRole($role)
    {
        // return 
        $this->selectedUserRole = $role;
        $this->role = $role;
    }

    public function selectUser($id)
    {
        $this->selectedUserId = $id;
        $user = User::find($id);

        // dd($user->parameters()->where('name', 'someparameter')->exists());

        $this->N = $user->username;
        $this->selectedFullName = $user->full_name;
        $this->selectedUserRole = $user->role;
        $this->selectedUserEmail = $user->email;
        $this->selectedUserName = $user->username;
        $this->selectedUserAgentId = $user->agent_id;
        $this->selectedUserTerminated = (bool) $user->terminated;
        $this->selectedUserPasswordPolicy = $user->password_policy != null ? true : false;
        $this->selectedUserPasswordPolicyPeriod = $user->password_policy;
        $this->selectedUserResetNextLogin = (bool) $user->reset_pass_next_login;

        $this->selectedUserParams = [];
        // params
        foreach ($user->parameters as $param) {
            $this->selectedUserParams[] = $param->name;
        }


        $this->selectedUserPermissions = [];
        // permissions 
        foreach ($user->permissions as $permission) {
            $this->selectedUserPermissions[] = $permission->name;
        }

        // if the user in an agent, he has one org 
        if ($this->selectedUserRole == 4 || $this->selectedUserRole == 6) {
            $this->selectedUserOrganizationId = $user->organization_id;
            $this->selectedUserOrganizationName = Organization::find($this->selectedUserOrganizationId)?->name;
        } else {
            $this->selectedSupervisorOrgs = $user->supervisorOrganizations()->pluck('name', 'organizations.id')->toArray();
        }

        // if the user is agent, he has one user group 
        if ($this->selectedUserRole == 4) {
            $this->selectedUserUserGroupsIds = $user?->user_group_id;
            $this->selectedUserUserGroups = $user?->userGroup?->name;
        } else {
            $this->selectedSupervisorUserGroups = $user->supervisorGroups()->pluck('name', 'user_groups.id')->toArray();
            $this->selectedSupervisorUserGroups_ids = $user->supervisorGroups()->pluck('user_groups.id')->toArray();

            // the primary possible user groups (accoring to the supervisor previously selected orgs )
            $this->possibleUserGroups_edit = UserGroup::whereIn('organization_id', array_keys($this->selectedSupervisorOrgs))->orderBy('name', 'ASC')->get();
        }

        // if the user is supervisor or quality, he has evaluation forms 
        if (in_array($this->selectedUserRole, [2, 5])) {
            $this->selectedUserEvaluationForms = $user->evaluations()->pluck('evaluation_name', 'evaluations.id')->toArray();
            $this->selectedEvaluationForms = $user->evaluations()->pluck('evaluation_name', 'evaluations.id')->toArray();
        }
    }

    public function toggleSelectedUserParams($param)
    {
        // dd($this->selectedUserParams);
        // if (in_array($param, $this->selectedUserParams)) {
        //     // Remove the parameter
        //     $this->selectedUserParams = array_diff($this->selectedUserParams, [$param]);
        // } else {
        //     // Add the parameter
        //     $this->selectedUserParams[] = $param;
        // }
    }

    public function toggleSelectedUserPermissions($permission)
    {
        // dd($this->selectedUserPermissions);
    }

    public function clear()
    {
        $this->resetValidation();
        $this->role =
            $this->selectedUserId =
            $this->selectedUserName =
            $this->selectedUserRole =
            $this->email =
            $this->name =
            $this->password = null;

        $this->activeTab = 'general';

        $this->createModal = false;
        $this->added_role_id = 4;
        $this->added_role = 'Agent';
        $this->added_id =
            $this->added_fullName =
            $this->added_username =
            $this->added_password =
            $this->added_email =
            $this->added_organization_id =
            $this->added_organization =
            $this->supervisorsToAdd =
            $this->added_directSupervisor_id =
            $this->added_directSupervisor =

            $this->addAgentuserGroups =
            $this->added_agent_userGroup_id =
            $this->added_agent_userGroup =

            $this->addSupervisorUserGroups =

            $this->added_SupervisorUserGroups =
            $this->added_SupervisorUserGroups_ids = null;
        $this->addSupervisorOrganizations_ids = [];
        $this->addSupervisorOrganizations = [];


        $this->selectedUserId = null;
        $this->N = null;
        $this->selectedUserRole = null;
        $this->selectedFullName = null;
        $this->selectedUserEmail = null;
        $this->selectedUserName = null;
        $this->selectedUserAgentId = null;
        $this->selectedUserOrganizationId = null;
        $this->selectedUserOrganizationName = null;
        $this->selectedUserUserGroups = null;
        $this->selectedUserUserGroupsIds = null;
        $this->selectedUserDirectSupervisorId = null;
        $this->selectedUserDirectSupervisor = null;
        $this->selectedUserTerminated = null;
        $this->selectedUserPasswordPolicy = null;
        $this->selectedUserPasswordPolicyPeriod = null;
        $this->selectedUserParams = [];
        $this->selectedUserPermissions = [];
        $this->selectedSupervisorOrgs = [];
        $this->selectedUserResetNextLogin = null;
        $this->possibleUserGroups_edit = [];
        $this->selectedSupervisorUserGroups = [];
        $this->selectedSupervisorUserGroups_ids = [];
        $this->selectedUserEvaluationForms = [];
        $this->searchUserGroups = '';
        $this->selectedUserGroups = [];
        $this->searchOrganizations = '';
        $this->selectedOrganizations = [];

        $this->selectedSupervisorUserGroups = [];
        $this->selectedSupervisorUserGroups_ids = [];
        $this->selectedEvaluationForms = [];
        $this->searchEvaluationForms = '';

        $this->theIdToDelete = null;
    }

    public function closeModal()
    {
        $this->activeTab = 'general';
        $this->resetValidation();
        $this->dispatch('closeModal');
    }

    public function createUser()
    {
        $this->validate();

        $name = $this->name;
        $email = $this->email;
        $password = $this->password;
        $role = $this->role;

        if (User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'role' => $role
        ])) {
            $this->alert("success", "User Created Successfully");
            $this->mount();
            $this->closeModal();
        };
    }

    public function editUser()
    {
        if (User::find($this->selectedUserId)->update([
            'name' => $this->selectedUserName,
            'role' => $this->selectedUserRole,
        ])) {
            $this->alert("success", "User Updated Successfully");
            $this->closeModal();
            $this->mount();
        };
    }

    public function confirmed()
    {
        if (User::find($this->theIdToDelete)->delete()) {
            $this->alert("success", "User Deleted Successfully");
            $this->mount();
        };
    }

    public function enable($id)
    {
        if (User::whereId($id)->first()->update(['enabled' => 1, 'is_locked' => 0])) {
            $this->alert("success", "User Enabled Successfully");
            $this->mount();
        };
    }

    public function disable($id)
    {
        if (User::whereId($id)->first()->update(['enabled' => 0])) {
            $this->alert("success", "User Disabled Successfully");
            $this->mount();
        };
    }

    public function showDeleteAlert($id)
    {
        $this->theIdToDelete = $id;
        $this->alert('warning', 'Are you sure you want to delete this user?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    // for agent 
    public function changeOrg($id)
    {
        $this->selectedUserOrganizationId = $id;
        $this->selectedUserOrganizationName = Organization::find($id)->name;

        // clear the user group and direct supervisor 
        $this->selectedUserUserGroupsIds = $this->selectedUserUserGroups = $this->selectedUserDirectSupervisorId = $this->selectedUserDirectSupervisor = null;
    }

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    // for agent - edit modal
    public function changeUserGroup($id, $name)
    {
        $this->selectedUserUserGroupsIds = $id;
        $this->selectedUserUserGroups = $name;
    }

    // for non agents - edit modal
    public function selectUserGroup($id)
    {
        $userGroup = UserGroup::find($id);


        if ($userGroup && !in_array($userGroup->id, $this->selectedSupervisorUserGroups_ids)) {
            $this->selectedSupervisorUserGroups[] = $userGroup->name;
            $this->selectedSupervisorUserGroups_ids[] = $userGroup->id;
        }

        $this->searchUserGroups = '';
    }

    public function selectUserGroup_add($id)
    {
        $userGroup = UserGroup::find($id);

        if ($userGroup && !in_array($userGroup->name, $this->selectedSupervisorUserGroups)) {
            $this->selectedSupervisorUserGroups[$id] = $userGroup->name;
        }

        $this->searchUserGroups = '';
    }

    public function selectEvaluationForm_edit($id)
    {
        $evaluationForm = Evaluation::find($id);

        if ($evaluationForm && !in_array($evaluationForm->name, $this->selectedUserEvaluationForms)) {
            $this->selectedUserEvaluationForms[$id] = $evaluationForm->evaluation_name;
            $this->selectedEvaluationForms[$id] = $evaluationForm->evaluation_name;
        }

        $this->searchEvaluationForms = '';
    }

    public function removeEvaluationForm_edit($id)
    {
        unset($this->selectedUserEvaluationForms[$id]);
        unset($this->selectedEvaluationForms[$id]);
    }

    public function selectEvaluationForm_add($id)
    {
        $evaluationForm = Evaluation::find($id);

        if ($evaluationForm && !in_array($evaluationForm->name, $this->selectedEvaluationForms)) {
            $this->selectedEvaluationForms[$id] = $evaluationForm->evaluation_name;
        }

        $this->searchEvaluationForms = '';
    }

    public function removeEvaluationForm_add($id)
    {
        unset($this->selectedEvaluationForms[$id]);
    }



    // for non agents - add modal 
    public function selectOrg($id)
    {
        $org = Organization::find($id);

        if ($org && !in_array($org->name, $this->addSupervisorOrganizations)) {
            $this->addSupervisorOrganizations[$id] = Organization::find($id)->name;
        }


        // clear search result
        $this->searchOrganizations = '';

        // add the corresponding user groups to the possible list
        $this->addSupervisorUserGroups = UserGroup::whereIn('organization_id', array_keys($this->addSupervisorOrganizations))->orderBy('name', 'ASC')->get();

        // dd($this->selectedSupervisorOrgs, $this->addSupervisorOrganizations_ids);
    }

    // for non agents - edit modal 
    public function selectOrg_edit($id)
    {
        $org = Organization::find($id);

        if ($org && !in_array($org->name, $this->selectedSupervisorOrgs)) {
            $this->selectedSupervisorOrgs[$id] = Organization::find($id)->name;
        }

        // clear search result
        $this->searchOrganizations = '';

        // possible user groups according to the orgs
        $this->possibleUserGroups_edit = UserGroup::whereIn('organization_id', array_keys($this->selectedSupervisorOrgs))->orderBy('name', 'ASC')->get();


        // $this->selectedSupervisorUserGroups = UserGroup::whereIn('organization_id', array_keys($this->selectedSupervisorOrgs))->orderBy('name', 'ASC')->get();
        $this->selectedSupervisorUserGroups = [];

        // dd($this->selectedSupervisorOrgs, $this->addSupervisorOrganizations_ids);
    }

    // fot non-agents - add modal
    public function removeOrg($index)
    {

        unset($this->addSupervisorOrganizations[$index]);

        // clear the already selected user groups (add supervisor modal)
        $this->selectedSupervisorUserGroups = [];
        $this->selectedSupervisorUserGroups_ids = [];

        // for add non agents, remove the possible user groups of the removed org.
        $this->addSupervisorUserGroups = [];
        // $this->addSupervisorOrganizations_ids = [];
        $this->addSupervisorUserGroups = UserGroup::whereIn('organization_id', array_keys($this->addSupervisorOrganizations))->orderBy('name', 'ASC')->get();
    }
    // fot non-agents - edit modal
    public function removeOrg_edit($index)
    {

        unset($this->selectedSupervisorOrgs[$index]);

        // clear the already selected user groups (add supervisor modal)
        $this->selectedSupervisorUserGroups = [];
        $this->selectedSupervisorUserGroups_ids = [];

        $this->possibleUserGroups_edit = UserGroup::whereIn('organization_id', array_keys($this->selectedSupervisorOrgs))->orderBy('name', 'ASC')->get();
    }

    public function changeDirectSupervisor($id, $fullName)
    {
        $this->selectedUserDirectSupervisorId = $id;
        $this->selectedUserDirectSupervisor = $fullName;
    }

    public function removeUserGroup($index,  $loop_index)
    {
        unset($this->selectedSupervisorUserGroups_ids[$loop_index]);
        unset($this->selectedSupervisorUserGroups[$index]);

        $this->selectedSupervisorUserGroups = array_values($this->selectedSupervisorUserGroups);
        $this->selectedSupervisorUserGroups_ids = array_values($this->selectedSupervisorUserGroups_ids);

        // for add modal - non agents 
        // unset($this->selectedSupervisorUserGroups[$index]);
    }

    public function openAddUserModal()
    {
        $this->createModal =  true;
        $this->selectedUserParams = [
            'Duration',
            'Called ID',
            'Caller ID',
            'Hold Duration',
            'Evaluation Score',
            'Is Evaluated',
            'Agent Name',
            'Comment',
        ];

        $this->selectedUserPermissions = [
            'Allow Access Telephony',
            'Play Recorded Interactions',
            'Show Evaluated Interactions Only',
            'View Other Users Interactions Comments',
            'View Interactions Evaluations',
            'View Evaluation Reports'
        ];
    }

    // for add user modal 
    public function addRole($id)
    {


        $this->added_role_id = $id;
        $this->added_role = match ((int) $id) {
            1 => 'Admin',
            2 => 'Supervisor',
            3 => 'IT',
            4 => 'Agent',
            5 => 'Quality',
            6 => 'Client',
            7 => 'Development Supervisor',
            default => 'Unknown Role',
        };

        // clear the already selected params and permissions to avoid problems
        $this->selectedUserParams = $this->selectedUserPermissions = [];

        // default params
        switch ($id) {
                // admin
            case 1:
                $this->selectedUserParams = [
                    'Call ID',
                    'Duration',
                    'Interaction Number',
                    'Called ID',
                    'Interaction Ender',
                    'Caller ID',
                    'Hold Duration',
                    'Call Type',
                    'Digits Count',
                    'Extension',
                    'Hold Count',
                    'Pause Duration',
                    'Ring Duration',
                    'Screen Capture',
                    'Server Name',
                    'Transferred From',
                    'Transferred To',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Includes Fatal Error',
                    'Is Assigned',
                    'Is Flagged',
                    'Penalties',
                    'Agent Name',
                    'Comment',
                    'Interaction Importance',
                    'Custom Flag',
                    'Group',
                    'Played',
                    'Organization',
                    'Skill Group',
                ];
                break;
                // supevisor
            case 2:
                $this->selectedUserParams = [
                    'Call ID',
                    'Duration',
                    'Interaction Number',
                    'Called ID',
                    'Interaction Ender',
                    'Caller ID',
                    'Hold Duration',
                    'Call Type',
                    'Digits Count',
                    'Extension',
                    'Hold Count',
                    'Pause Duration',
                    'Ring Duration',
                    'Screen Capture',
                    'Server Name',
                    'Transferred From',
                    'Transferred To',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Includes Fatal Error',
                    'Is Assigned',
                    'Is Flagged',
                    'Penalties',
                    'Agent Name',
                    'Comment',
                    'Interaction Importance',
                    'Custom Flag',
                    'Group',
                    'Played',
                    'Organization',
                    'Skill Group',
                ];
                break;
                // it
            case 3:
                $this->selectedUserParams = [
                    'Call ID',
                    'Duration',
                    'Interaction Number',
                    'Called ID',
                    'Interaction Ender',
                    'Caller ID',
                    'Hold Duration',
                    'Call Type',
                    'Digits Count',
                    'Extension',
                    'Hold Count',
                    'Pause Duration',
                    'Ring Duration',
                    'Screen Capture',
                    'Server Name',
                    'Transferred From',
                    'Transferred To',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Includes Fatal Error',
                    'Is Assigned',
                    'Is Flagged',
                    'Penalties',
                    'Agent Name',
                    'Comment',
                    'Interaction Importance',
                    'Custom Flag',
                    'Group',
                    'Played',
                    'Organization',
                    'Skill Group',
                ];
                break;
                // agent
            case 4:
                $this->selectedUserParams = [
                    'Duration',
                    'Called ID',
                    'Caller ID',
                    'Hold Duration',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Agent Name',
                    'Comment',
                ];
                break;
                // quality
            case 5:
                $this->selectedUserParams = [
                    'Call ID',
                    'Duration',
                    'Interaction Number',
                    'Called ID',
                    'Interaction Ender',
                    'Caller ID',
                    'Hold Duration',
                    'Call Type',
                    'Digits Count',
                    'Extension',
                    'Hold Count',
                    'Pause Duration',
                    'Ring Duration',
                    'Screen Capture',
                    'Server Name',
                    'Transferred From',
                    'Transferred To',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Includes Fatal Error',
                    'Is Assigned',
                    'Is Flagged',
                    'Penalties',
                    'Agent Name',
                    'Comment',
                    'Interaction Importance',
                    'Custom Flag',
                    'Group',
                    'Played',
                    'Organization',
                    'Skill Group',
                ];
                break;
            case 6:
                $this->selectedUserParams = [
                    'Call ID',
                    'Duration',
                    'Interaction Number',
                    'Called ID',
                    'Interaction Ender',
                    'Caller ID',
                    'Hold Duration',
                    'Call Type',
                    'Digits Count',
                    'Extension',
                    'Hold Count',
                    'Pause Duration',
                    'Ring Duration',
                    'Screen Capture',
                    'Server Name',
                    'Transferred From',
                    'Transferred To',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Includes Fatal Error',
                    'Is Assigned',
                    'Is Flagged',
                    'Penalties',
                    'Agent Name',
                    'Comment',
                    'Interaction Importance',
                    'Custom Flag',
                    'Group',
                    'Played',
                    'Organization',
                    'Skill Group',
                ];
                break;
                // development supervisor
            case 7:
                $this->selectedUserParams = [
                    'Call ID',
                    'Duration',
                    'Interaction Number',
                    'Called ID',
                    'Interaction Ender',
                    'Caller ID',
                    'Hold Duration',
                    'Call Type',
                    'Digits Count',
                    'Extension',
                    'Hold Count',
                    'Pause Duration',
                    'Ring Duration',
                    'Screen Capture',
                    'Server Name',
                    'Transferred From',
                    'Transferred To',
                    'Evaluation Score',
                    'Is Evaluated',
                    'Includes Fatal Error',
                    'Is Assigned',
                    'Is Flagged',
                    'Penalties',
                    'Agent Name',
                    'Comment',
                    'Interaction Importance',
                    'Custom Flag',
                    'Group',
                    'Played',
                    'Organization',
                    'Skill Group',
                    'AI Flags',
                ];
                break;

            default:
                # code...
                break;
        }

        // default permissions
        switch ($id) {
                // admin
            case 1:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Evaluation Reports',
                    'View Interactions Evaluations',
                    'Create Interaction Evaluations',
                ];
                break;
                // supevisor
            case 2:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Evaluation Reports',
                    'View Interactions Evaluations',
                    'Create Interaction Evaluations',
                ];
                break;
                // it
            case 3:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Evaluation Reports',
                    'View Interactions Evaluations',
                    'Create Interaction Evaluations',
                ];
                break;
                // agent
            case 4:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Interactions Evaluations',
                    'View Evaluation Reports'
                ];
                break;
                // quality
            case 5:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Evaluation Reports',
                    'View Interactions Evaluations',
                    'Create Interaction Evaluations',
                ];
                break;
            case 6:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Evaluation Reports',
                    'View Interactions Evaluations',
                    // 'Create Interaction Evaluations',
                ];
                break;
            case 7:
                $this->selectedUserPermissions = [
                    'Allow Access Telephony',
                    'Play Recorded Interactions',
                    'Show Evaluated Interactions Only',
                    'View Other Users Interactions Comments',
                    'View Evaluation Reports',
                    'View Interactions Evaluations',
                    'Create Interaction Evaluations',
                ];
                break;

            default:
                # code...
                break;
        }
    }

    public function addOrg($id, $name)
    {
        // if user group already selected, remove it, also direct supervisor  
        $this->added_agent_userGroup_id = $this->added_agent_userGroup = null;
        $this->added_directSupervisor_id = $this->added_directSupervisor = null;


        $this->added_organization_id = $id;
        $this->added_organization = $name;

        $this->addAgentuserGroups = UserGroup::where('organization_id', $this->added_organization_id)
            ->orderBy('name', 'ASC')
            ->get();
    }

    public function addAgentUserGroup($id, $name)
    {

        // if a direct supervisor already selected, remove it when changing the user group
        $this->added_directSupervisor_id = $this->added_directSupervisor = null;


        $this->added_agent_userGroup_id = $id;
        $this->added_agent_userGroup = $name;


        // possible supervisors (that has the same selected user group) 
        $supervisors_ids = SupervisorGroup::where('user_group_id', $id)->pluck('user_id');
        $this->supervisorsToAdd = User::whereIn('id', $supervisors_ids)->orderBy('full_name', 'ASC')->get();
    }

    public function addDirectSupervisor($id, $name)
    {
        $this->added_directSupervisor_id = $id;
        $this->added_directSupervisor = $name;
    }

    public function rules()
    {
        $general_rules = [];

        if (!$this->selectedUserId) {
            $general_rules = [
                'added_role_id' => ['required', 'in:1,2,3,4,5,6,7'],
                'added_password' => ['required', 'string', 'min:8'],
                'added_fullName' => ['required', 'string', 'max:255'],
                'added_username' => ['required', 'string', 'max:255'],
                'added_id' => ['required', 'numeric', 'not_in:0', 'unique:users,agent_id'],
                'added_email' => ['required', 'email', 'unique:users,email'],
            ];
        }
        $edit_modal_rules = [];

        if ($this->selectedUserId) {
            $edit_modal_rules = [
                'selectedFullName' => ['required', 'string', 'max:255'],
                'selectedUserName' => ['required', 'string', 'max:255'],
                'selectedUserEmail' => ['required', 'email', Rule::unique('users', 'email')->ignore($this->selectedUserId)],
                'selectedUserAgentId' => ['required', 'numeric', 'not_in:0'],
            ];
        }

        // if edit modal, 
        return array_merge($general_rules, $edit_modal_rules);
    }

    public function messages()
    {
        return [
            'added_password.regex' => 'The password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.',
        ];
    }

    public function submitEditUser()
    {
        // validation 
        $this->validate();

        $commonData = [
            'role' => $this->selectedUserRole,
            'full_name' => $this->selectedFullName,
            'username' => $this->selectedUserName,
            // 'password' => Hash::make($this->added_password),
            'email' => $this->selectedUserEmail,
            'agent_id' => $this->selectedUserAgentId,
            'terminated' => (bool) $this->selectedUserTerminated,
            'password_policy' => $this->selectedUserPasswordPolicy == null ? null : $this->selectedUserPasswordPolicyPeriod,
            'reset_pass_next_login' => (bool) $this->selectedUserResetNextLogin,
        ];

        // if agent
        if ($this->selectedUserRole == 4 || $this->selectedUserRole == 6) {

            User::find($this->selectedUserId)->update(array_merge($commonData, [
                'organization_id' => $this->selectedUserOrganizationId,
                'user_group_id' => $this->selectedUserUserGroupsIds,
            ]));

            // save the parameters 
            $parameterIds = Parameter::whereIn('name', $this->selectedUserParams)->pluck('id');
            User::find($this->selectedUserId)->parameters()->sync($parameterIds);

            // save the permissions 
            $permissionIds = Permission::whereIn('name', $this->selectedUserPermissions)->pluck('id');
            User::find($this->selectedUserId)->permissions()->sync($permissionIds);

            $this->alert('success', "User Updated Successfully", [
                'timerProgressBar' => true,
                'timer' => '4000',
            ]);
        } else {
            // save common data 
            User::find($this->selectedUserId)->update($commonData);

            // save the orgs
            $selectedOrgs = Organization::whereIn('name', $this->selectedSupervisorOrgs)->pluck('id')->toArray();
            User::find($this->selectedUserId)->supervisorOrganizations()->sync($selectedOrgs);

            //save the user groups
            User::find($this->selectedUserId)->supervisorGroups()->sync($this->selectedSupervisorUserGroups_ids);
            // User::find($this->selectedUserId)->supervisorGroups()->sync(array_keys($this->selectedSupervisorUserGroups));

            // save the parameters 
            $parameterIds = Parameter::whereIn('name', $this->selectedUserParams)->pluck('id');

            User::find($this->selectedUserId)->parameters()->sync($parameterIds);

            // save the permissions 
            $permissionIds = Permission::whereIn('name', $this->selectedUserPermissions)->pluck('id');
            User::find($this->selectedUserId)->permissions()->sync($permissionIds);

            // if supervisor or quality or dev supervisor
            if (in_array($this->selectedUserRole, [2, 5, 7])) {
                // save the assigned evaluation forms 
                User::find($this->selectedUserId)->evaluations()->sync(array_keys($this->selectedEvaluationForms));
            }

            $this->alert('success', "User Updated Successfully", [
                'timerProgressBar' => true,
                'timer' => '4000',
            ]);
        }


        $this->closeModal();
    }

    public function submitAddUser()
    {

        // validation 
        $this->validate();

        $commonData = [
            'role' => $this->added_role_id,
            'full_name' => $this->added_fullName,
            'username' => $this->added_username,
            'password' => Hash::make($this->added_password),
            'email' => $this->added_email,
            'agent_id' => $this->added_id,
            'terminated' => (bool) $this->selectedUserTerminated,
            'password_policy' => $this->selectedUserPasswordPolicyPeriod,
            'reset_pass_next_login' => (bool) $this->selectedUserResetNextLogin,
        ];


        // if agent
        if ($this->added_role_id == "4") {
            $agent = User::create(array_merge($commonData, [
                'organization_id' => $this->selectedUserOrganizationId,
                'user_group_id' => $this->selectedUserUserGroupsIds,
            ]));

            // save the parameters 
            $parameterIds = Parameter::whereIn('name', $this->selectedUserParams)->pluck('id');
            User::find($agent->id)->parameters()->sync($parameterIds);

            // save the permissions 
            $permissionIds = Permission::whereIn('name', $this->selectedUserPermissions)->pluck('id');
            User::find($agent->id)->permissions()->sync($permissionIds);
        } else if ($this->added_role_id == "1" || $this->added_role_id == "3") { //if admin or IT
            // add user to database 
            $user = User::create($commonData);

            // save the parameters 
            $parameterIds = Parameter::whereIn('name', $this->selectedUserParams)->pluck('id');
            User::find($user->id)->parameters()->sync($parameterIds);

            // save the permissions 
            $permissionIds = Permission::whereIn('name', $this->selectedUserPermissions)->pluck('id');
            User::find($user->id)->permissions()->sync($permissionIds);

            // save the orgs
            User::find($user->id)->supervisorOrganizations()->sync(array_keys($this->addSupervisorOrganizations));

            // save the groups
            User::find($user->id)->supervisorGroups()->sync($this->addSupervisorUserGroups ?? $this->addSupervisorUserGroups?->pluck('id')->toArray());
        }
        // if supervisor or quality or development supervisor
        else if ($this->added_role_id == "2" || $this->added_role_id == "5" || $this->added_role_id == "7") {
            $supervisor = User::create($commonData);

            // save the parameters 
            $parameterIds = Parameter::whereIn('name', $this->selectedUserParams)->pluck('id');
            User::find($supervisor->id)->parameters()->sync($parameterIds);

            // save the permissions 
            $permissionIds = Permission::whereIn('name', $this->selectedUserPermissions)->pluck('id');
            User::find($supervisor->id)->permissions()->sync($permissionIds);

            // save the orgs
            User::find($supervisor->id)->supervisorOrganizations()->sync(array_keys($this->addSupervisorOrganizations));

            // save the groups
            User::find($supervisor->id)->supervisorGroups()->sync($this->addSupervisorUserGroups ??  $this->addSupervisorUserGroups?->pluck('id')->toArray());

            // save the assigned evaluation forms 
            User::find($supervisor->id)->evaluations()->sync(array_keys($this->selectedEvaluationForms));
        }
        // if client
        else if ($this->added_role_id == "6") {

            $client = User::create(array_merge($commonData, [
                'organization_id' => $this->selectedUserOrganizationId,
                // 'user_group_id' => $this->selectedUserUserGroupsIds,
            ]));

            // save the parameters 
            $parameterIds = Parameter::whereIn('name', $this->selectedUserParams)->pluck('id');
            User::find($client->id)->parameters()->sync($parameterIds);

            // save the permissions 
            $permissionIds = Permission::whereIn('name', $this->selectedUserPermissions)->pluck('id');
            User::find($client->id)->permissions()->sync($permissionIds);
        }

        $this->alert('success', "User Created Successfully", [
            'timerProgressBar' => true,
            'timer' => '4000',
        ]);

        $this->closeModal();
    }

    public function submitAddMultiUsers()
    {
        $this->validate([
            'importFile' => 'required|mimes:csv,xlsx,xls|max:2048',
        ]);


        $filePath = $this->importFile->getRealPath();

        // if agents
        if ($this->added_role_id == 4 || $this->added_role_id == 6) {
            $additionalData = [
                'organization_id' => $this->selectedUserOrganizationId,
                'user_group_id' => $this->selectedUserUserGroupsIds,
                'role' => $this->added_role_id,
                'password_policy' => $this->selectedUserPasswordPolicy == null ? null : $this->selectedUserPasswordPolicyPeriod,
                'reset_pass_next_login' => (bool) $this->selectedUserResetNextLogin,
            ];
        } else {
            $additionalData = [
                'role' => $this->added_role_id,
                'password_policy' => $this->selectedUserPasswordPolicy == null ? null : $this->selectedUserPasswordPolicyPeriod,
                'reset_pass_next_login' => (bool) $this->selectedUserResetNextLogin,
            ];
        }


        // orgs IDs for non agents
        $orgs = Organization::whereIn('name', $this->addSupervisorOrganizations)->pluck('id')->toArray();

        // user groups IDs for non agents 
        $userGroups = $this->selectedSupervisorUserGroups_ids;


        // permissions and params are mutual for all roles 
        $permissions = $this->selectedUserPermissions;
        $params = $this->selectedUserParams;

        try {
            // Create an instance of UserImport and call its import method
            Excel::import(new UsersImport($additionalData, $permissions, $params, $orgs, $userGroups), $filePath);

            // If import is successful, show success message
            session()->flash('message', 'Users imported successfully!');
            Log::info('Users imported successfully!');

            $this->alert("success", 'Users Saved Successfully', [
                'timerProgressBar' => true,
                'timer' => 4000,
            ]);

            $this->closeModal();
        } catch (\Exception $e) {
            Log::error('Error occurred during import: ' . $e);

            // If there's an error during import, show error message
            // $this->alert("error", 'Error occurred during import: ' . $e->getMessage(), [
            $this->alert("error", 'An Error Occurred During The Import', [
                'timerProgressBar' => true,
                'timer' => 4000,
            ]);
            // session()->flash('error', 'Error occurred during import: ' . $e->getMessage());
        }
    }


    public function export()
    {
        $export = new UserExport($this->filter_name, $this->filter_agent_id, $this->filter_user_group, $this->filter_role, $this->filter_terminated, $this->filter_enabled);

        // Return the export response
        return Excel::download($export, 'users.xlsx');
    }


    public function render()
    {
        return view('livewire.user-management', [
            'users' => User::search($this->searchUser)
                ->orderBy($this->sortBy, $this->sortDir)
                ->paginate($this->perPage),


            // orgs, work for both agents (without search) and non agents, with search     
            'organizations' => Organization::where('name', 'like', "%$this->searchOrganizations%")->orderBy('name', 'ASC')->get(),

            // the user groups that belong to the selected org and also depend on the user search - edit modal
            'userGroups' => UserGroup::where('name', 'like', "%$this->searchUserGroups%")->where('organization_id', $this->selectedUserOrganizationId)->orderBy('name', 'ASC')->get(),

            // the user groups that correspond to multi orgs selected (in case of non-agents)
            'multiUserGroups' => UserGroup::where('name', 'like', "%$this->searchUserGroups%")->where('organization_id', $this->selectedSupervisorOrgs)->orderBy('name', 'ASC')->get(),

            // eval forms 
            'evaluationForms' => Evaluation::where('evaluation_name', 'like', "%$this->searchEvaluationForms%")->orderBy('evaluation_name', 'ASC')->get(),

            // for agent - edit modal 
            // 'directSupervisors' => User::where('role', 2)
            //     ->where('organization_id', $this->selectedUserOrganizationId)
            //     ->orderBy('full_name', 'ASC')
            //     ->get(),
        ]);
    }
}
