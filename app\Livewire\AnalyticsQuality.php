<?php

namespace App\Livewire;

use App\Models\AnalyticsParametersActivition;
use App\Models\Organization;
use App\Models\User;
use App\Models\UserGroup;
use Livewire\Component;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class AnalyticsQuality extends Component
{
    public $accountIDFilter;
    public $accountNameFilter;
    public $dateFrom;
    public $dateTo;
    public $dateFromFilter;
    public $dateToFilter;
    public $dateType;
    public $groupsAccount;
    public $dataPage;
    public $searchGroup;
    public $groupSelected;
    public $editFlag;
    public $userSettings;
    public $cardSelected;
    public $role;

    public function mount(){
        $this->getDate('Last 24 Hours');
        $this->getUserSettings();
        $this->editFlag = false;
        $this->role = auth()->user()->role;
    }
    public function remove($type)
    {
        // Add a new record with the given parameter_name, user_id, and is_active set to 0
        AnalyticsParametersActivition::create([
            'parameter_name' => $type,
            'user_id' => auth()->id(),
            'is_active' => 0,
        ]);
        $this->getUserSettings();
        $this->render();
    }
    
    public function add($type)
    {
        // Remove the record where parameter_name equals $type and user_id is the current authenticated user
        AnalyticsParametersActivition::where('parameter_name', $type)
            ->where('user_id', auth()->id())
            ->delete();
            $this->getUserSettings();
            $this->render();
    }
    
    public function getUserSettings(){

        $this->userSettings = AnalyticsParametersActivition::where('user_id', auth()->user()->id)
        ->where('is_active', 0)
        ->pluck('parameter_name')
        ->toArray();
        // dd($this->userSettings);
    }
    public function getAccounts(){
        if($this->role == 4){
            return Organization::query()->where('id',auth()->user()->organization_id)->get();
        }elseif($this->role == 2 || $this->role == 5){
            $supervisorOrgs = Auth::user()->supervisorOrganizations->pluck('id');
            return Organization::query()->whereIN('id',$supervisorOrgs)->get();
        }else{
            return Organization::query()->get();
        }
    }
    public function setAccountFilter($id, $name)
    {
        if($id){
            $this->accountIDFilter = $id;
            $this->accountNameFilter = $name;
            $arrays = [];
                if($this->role == 4){
                    $data = UserGroup::where('id', auth()->user()->user_group_id)
                    ->when(!empty($this->searchGroup), function($query) {
                        $query->where('name', 'like', '%' . $this->searchGroup . '%');
                    })
                    ->get();
                }else{
                    $data = UserGroup::where('organization_id', $this->accountIDFilter)
                    ->when(!empty($this->searchGroup), function($query) {
                        $query->where('name', 'like', '%' . $this->searchGroup . '%');
                    })
                    ->get();
                }
            

                foreach ($data as $array) {
                    // Filter and process call durations
                    $filteredCallDurations = $array->callInteraction
                        ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                    if ($this->role == 4) {
                        $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                    }
                    $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                        return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    });
                
                    $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds;
                    });
                
                    $callInteractionCount = $filteredCallDurations->count();
                    $avgCallDurationInSeconds = $callInteractionCount > 0 ? $totalCallDurationInSeconds / $callInteractionCount : 0;
                    $avgCallDurationFormatted = sprintf(
                        "%02d:%02d:%02d",
                        floor($avgCallDurationInSeconds / 3600),
                        floor(($avgCallDurationInSeconds % 3600) / 60),
                        $avgCallDurationInSeconds % 60
                    );
                
                    // Filter and process hold durations
                    $filteredHoldTimes = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                        ->filter(function ($interaction) {
                            return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                        });
                
                    $totalHoldTimeInSeconds = $filteredHoldTimes->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds;
                    });
                
                    $holdInteractionCount = $filteredHoldTimes->count();
                    $avgHoldTimeInSeconds = $holdInteractionCount > 0 ? $totalHoldTimeInSeconds / $holdInteractionCount : 0;
                    $avgHoldTimeFormatted = sprintf(
                        "%02d:%02d:%02d",
                        floor($avgHoldTimeInSeconds / 3600),
                        floor(($avgHoldTimeInSeconds % 3600) / 60),
                        $avgHoldTimeInSeconds % 60
                    );
                
                    // Count interactions based on conditions
                    $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                        $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                        return $durationInSeconds < 120; // < 2 minutes
                    })->count();
                
                    $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                        $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                        return $durationInSeconds > 480; // > 8 minutes
                    })->count();
                
                    $longHoldDurationCount = $filteredHoldTimes->filter(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                        return $durationInSeconds > 120; // > 2 minutes
                    })->count();
                
                    // Calculate total outbound and inbound calls
                    $totalOutbound = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                        ->filter(function ($interaction) {
                            return $interaction->call_type === 'Outbound';
                        })->count();
                
                    $totalInbound = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                        ->filter(function ($interaction) {
                            return $interaction->call_type === 'Inbound';
                        })->count();
                
                    // Total call duration in formatted time
                    $totalCallDurationFormatted = sprintf(
                        "%02d:%02d:%02d",
                        floor($totalCallDurationInSeconds / 3600),
                        floor(($totalCallDurationInSeconds % 3600) / 60),
                        $totalCallDurationInSeconds % 60
                    );
                
                    $arrays[] = [
                        'group_id' => $array->id,
                        'group_name' => $array->name,
                        'avg_interactions_duration' => $avgCallDurationFormatted,
                        'avg_hold_time' => $avgHoldTimeFormatted,
                        'total_interactions' => $callInteractionCount,
                        'short_call_duration_count' => $shortCallDurationCount,
                        'long_call_duration_count' => $longCallDurationCount,
                        'long_hold_duration_count' => $longHoldDurationCount,
                        'total_call_duration' => $totalCallDurationFormatted, // New: Total Call Duration
                        'total_outbound' => $totalOutbound,                 // New: Total Outbound Calls
                        'total_inbound' => $totalInbound,                   // New: Total Inbound Calls
                    ];
                }
                

            $this->groupsAccount = $arrays;
            // dd($this->groupsAccount);
            if($this->role == 4){
                $this->getData(auth()->user()->user_group_id);
            }else{
                $this->getData('All');
            }
            
        }

    }
    public function getData($groupId)
    {
        $arrays = [];

        $this->groupSelected = $groupId;
        $this->dispatch('style-row', ['groupId' => $this->groupSelected]);
        if($groupId == 'All'){
            $array = Organization::where('id', $this->accountIDFilter)
                ->first();
                $countEvaluation = 0;
                foreach ($array->evaluationForm as $evaluationForm) {
                    // dd($this->dateFrom,$this->dateTo);
                    $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->count();
                     // This will give the count for each evaluationForm
                }
                
            // Apply date range filter to the callInteraction relation
            $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });
        
            // Total Call Duration in seconds
            $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });
        
            // Other duration-related calculations
            $avgCallDurationInSeconds = $filteredCallDurations->avg(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });
            // Formatting average duration
            if($avgCallDurationInSeconds){
                $avgCallDurationFormatted = gmdate("H:i:s", $avgCallDurationInSeconds);
            }else{
                $avgCallDurationFormatted = '00:00:00';
            }
            // Calculate other call metrics
            $callInteractionCount = $filteredCallDurations->count();
            $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                return strtotime($interaction->call_duration) < strtotime('00:01:00');
            })->count();
        
            $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                return strtotime($interaction->call_duration) > strtotime('00:10:00');
            })->count();
        
            $longHoldDurationCount = $filteredCallDurations->filter(function ($interaction) {
                return strtotime($interaction->hold_duration) > strtotime('00:05:00');
            })->count();
        
            // Total Hold Time in seconds
            $totalHoldTimeInSeconds = $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });
        
            if($totalCallDurationInSeconds){
                $totalCallDurationFormatted = gmdate("H:i:s", $totalCallDurationInSeconds);
            }else{
                $totalCallDurationFormatted = '00:00:00';
            }
            if($totalHoldTimeInSeconds){
                $totalHoldTimeFormatted = gmdate("H:i:s", $totalHoldTimeInSeconds);
            }else{
                $totalHoldTimeFormatted = '00:00:00';
            }
        
            // Calculating Inbound and Outbound totals
            $totalOutbound = $filteredCallDurations->where('call_type', 'Outbound')->count();
            $totalInbound = $filteredCallDurations->where('call_type', 'Inbound')->count();

            $arrays = [
                'group_id' => $array->id,
                'group_name' => $array->name,
                'avg_interactions_duration' => $avgCallDurationFormatted,
                'avg_hold_time' => $totalHoldTimeFormatted,
                'total_interactions' => $callInteractionCount,
                'short_call_duration_count' => $shortCallDurationCount,
                'long_call_duration_count' => $longCallDurationCount,
                'long_hold_duration_count' => $longHoldDurationCount,
                'total_call_duration' => $totalCallDurationFormatted,
                'total_outbound' => $totalOutbound,
                'total_inbound' => $totalInbound,
                'countEvaluation' => $countEvaluation,
            ];
        
            $this->dataPage = $arrays;
        
        }elseif($groupId){
            $array = UserGroup::where('id', $groupId)
                ->first();
                
                $countEvaluation = 0;
                if ($this->role == 4) {
                    foreach ($array->organization->evaluationForm as $evaluationForm) {
                        $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('user_id',auth()->user()->id)->count();
                        // This will give the count for each evaluationForm
                    }
                }else{
                    foreach ($array->organization->evaluationForm as $evaluationForm) {
                        $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->count();
                        // This will give the count for each evaluationForm
                    }
                }
                // dd($countEvaluation);
            // Apply date range filter to the callInteraction relation
            $filteredCallDurations = $array->callInteraction
                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
            if ($this->role == 4) {
                $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
            }
            $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
            });
        
            // Total Call Duration in seconds
            $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });
        
            // Other duration-related calculations
            $avgCallDurationInSeconds = $filteredCallDurations->avg(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });
        
            // Formatting average duration
            if($avgCallDurationInSeconds){
                $avgCallDurationFormatted = gmdate("H:i:s", $avgCallDurationInSeconds);
            }else{
                $avgCallDurationFormatted = '00:00:00';
            }
        
            // Calculate other call metrics
            $callInteractionCount = $filteredCallDurations->count();
            $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                return strtotime($interaction->call_duration) < strtotime('00:01:00');
            })->count();
        
            $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                return strtotime($interaction->call_duration) > strtotime('00:10:00');
            })->count();
        
            $longHoldDurationCount = $filteredCallDurations->filter(function ($interaction) {
                return strtotime($interaction->hold_duration) > strtotime('00:05:00');
            })->count();
        
            // Total Hold Time in seconds
            $totalHoldTimeInSeconds = $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });
        
            // Formatting total call duration and hold time
            if($totalCallDurationInSeconds){
                $totalCallDurationFormatted = gmdate("H:i:s", $totalCallDurationInSeconds);
            }else{
                $totalCallDurationFormatted = '00:00:00';
            }
            if($totalHoldTimeInSeconds){
                $totalHoldTimeFormatted = gmdate("H:i:s", $totalHoldTimeInSeconds);
            }else{
                $totalHoldTimeFormatted = '00:00:00';
            }
        
            // Calculating Inbound and Outbound totals
            $totalOutbound = $filteredCallDurations->where('call_type', 'Outbound')->count();
            $totalInbound = $filteredCallDurations->where('call_type', 'Inbound')->count();
        
            $arrays = [
                'group_id' => $array->id,
                'group_name' => $array->name,
                'avg_interactions_duration' => $avgCallDurationFormatted,
                'avg_hold_time' => $totalHoldTimeFormatted,
                'total_interactions' => $callInteractionCount,
                'short_call_duration_count' => $shortCallDurationCount,
                'long_call_duration_count' => $longCallDurationCount,
                'long_hold_duration_count' => $longHoldDurationCount,
                'total_call_duration' => $totalCallDurationFormatted,
                'total_outbound' => $totalOutbound,
                'total_inbound' => $totalInbound,
                'countEvaluation' => $countEvaluation,
            ];
        
            $this->dataPage = $arrays;
        
        }else{
            $this->dataPage = [
                'group_id' => 0,
                'group_name' => 'null',
                'avg_interactions_duration' => '00:00:00',
                'avg_hold_time' => '00:00:00',
                'total_interactions' => '0',
                'short_call_duration_count' => '0',
                'long_call_duration_count' => '0',
                'long_hold_duration_count' => '0',
                'total_call_duration' => '00:00:00',
                'total_outbound' => '0',
                'total_inbound' => '0',
            ];
        }
        
            // Chart 2
            $totalOutbound = $this->dataPage['total_outbound'];
            $totalInbound = $this->dataPage['total_inbound'];

            $count = $totalOutbound + $totalInbound;

            $averageOutbound = $count > 0 ? $totalOutbound / $count : 0;
            $averageInbound = $count > 0 ? $totalInbound / $count : 0;

            $this->dispatch('update-chart', [
                'chartTitle' => 'Call',
                'percentage_inbound' => round($averageInbound * 100),
                'percentage_outbound' => round($averageOutbound * 100),
            ]);
    }
    public function getGroupFilter()
    {
        if ($this->searchGroup && $this->accountIDFilter) {
            $query = UserGroup::query();
    
            if (!empty($this->accountIDFilter)) {
                $query->where('organization_id', $this->accountIDFilter);
            }
            $query->where('name', 'like', '%' . $this->searchGroup . '%');
            $this->groupsAccount = $query->get();
        }
    }
    public function getChartData($type){
        $this->cardSelected = $type;
        if($type == 'Avg Call Duration Card'){
                if ($this->groupSelected == 'All') {
                    // Get the organization by account ID
                    $array = Organization::where('id', $this->accountIDFilter)
                        ->first();
                
                    // Apply date range filter to the callInteraction relation
                    $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                        ->filter(function ($interaction) {
                            return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                        });
                
                    // Group by date (day)
                    $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                        return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                    });
                
                    // Calculate daily average durations (in minutes)
                    $dailyAvgDurationsInMinutes = $groupedByDate->map(function ($interactions) {
                        $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                            list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                            return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                        });
                
                        $avgDurationInSeconds = $totalDurationInSeconds / $interactions->count();
                        return $avgDurationInSeconds / 60; // Convert average duration from seconds to minutes
                    });
                
                    // Prepare data for the chart (dates and corresponding average durations in minutes)
                    $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes
                
                    // Dispatch data to the chart (adjusting as per chart's requirements)
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Avg. Call Duration (minutes)',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $avgDurations, // Pass the average durations in minutes
                        'isDuration' => true
                    ]);
                }elseif($this->groupSelected){

                    $array = UserGroup::where('id', $this->groupSelected)->first();
                
                    // Apply date range filter to the callInteraction relation
                    $filteredCallDurations = $array->callInteraction
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);

                            if ($this->role == 4) {
                                $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                            }

                            $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                                return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                            });

                
                    // Group by date (day)
                    $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                        return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                    });
                
                    // Calculate daily average durations (in minutes)
                    $dailyAvgDurationsInMinutes = $groupedByDate->map(function ($interactions) {
                        $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                            list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                            return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                        });
                
                        $avgDurationInSeconds = $totalDurationInSeconds / $interactions->count();
                        return $avgDurationInSeconds / 60; // Convert average duration from seconds to minutes
                    });
                
                    // Prepare data for the chart (dates and corresponding average durations in minutes)
                    $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes
                
                    // Dispatch data to the chart (adjusting as per chart's requirements)
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Avg. Call Duration (minutes)',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $avgDurations, // Pass the average durations in minutes
                        'isDuration' => true
                    ]);
                }else{
                    $this->dispatch('update-chart2', [
                        'chartTitle' => '',
                        'dates' => [],
                        'avgDurations' => [],
                        'isDuration' => true
                    ]);
                }
        }elseif($type == 'Avg Hold Duration Card'){
            if ($this->groupSelected == 'All') {
                // Get the organization by account ID
                $array = Organization::where('id', $this->accountIDFilter)
                    ->first();
            
                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });
            
                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });
            
                // Calculate daily average durations (in minutes)
                $dailyAvgDurationsInMinutes = $groupedByDate->map(function ($interactions) {
                    $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                    });
            
                    $avgDurationInSeconds = $totalDurationInSeconds / $interactions->count();
                    return $avgDurationInSeconds / 60; // Convert average duration from seconds to minutes
                });
            
                // Prepare data for the chart (dates and corresponding average durations in minutes)
                $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes
            
                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Avg. Hold Duration (minutes)',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $avgDurations, // Pass the average durations in minutes
                    'isDuration' => true
                ]);
            }elseif($this->groupSelected){

                $array = UserGroup::where('id', $this->groupSelected)->first();
            
                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });
            
                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });
            
                // Calculate daily average durations (in minutes)
                $dailyAvgDurationsInMinutes = $groupedByDate->map(function ($interactions) {
                    $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                    });
            
                    $avgDurationInSeconds = $totalDurationInSeconds / $interactions->count();
                    return $avgDurationInSeconds / 60; // Convert average duration from seconds to minutes
                });
            
                // Prepare data for the chart (dates and corresponding average durations in minutes)
                $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes
            
                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Avg. Hold Duration (minutes)',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $avgDurations, // Pass the average durations in minutes
                    'isDuration' => true
                ]);
            }else{
                $this->dispatch('update-chart2', [
                    'chartTitle' => '',
                    'dates' => [],
                    'avgDurations' => [],
                    'isDuration' => true
                ]);
            }
        }elseif($type == 'Total Evaluation'){
            
            if ($this->groupSelected == 'All') {
               // Get the organization by account ID
                $array = Organization::where('id', $this->accountIDFilter)
                ->first();

                if (!$array) {
                return; // Or handle accordingly
                }

                $countEvaluation = 0;
                $dailyCount = []; // Initialize an array to store the grouped counts

                if ($this->role == 4) {
                foreach ($array->evaluationForm as $evaluationForm) {
                    // Get the count of submissions per day, filtered by date range and user
                    $submissions = $evaluationForm?->evaluationSubmissions()
                        ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                        ->where('user_id', auth()->user()->id)
                        ->selectRaw('DATE(created_at) as date, count(*) as count')
                        ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                        ->get();

                    // Add to the daily count array
                    foreach ($submissions as $submission) {
                        $dailyCount[$submission->date] = $submission->count;
                    }
                }
                } else {
                foreach ($array->evaluationForm as $evaluationForm) {
                // Get the count of submissions per day, filtered by date range
                    $submissions = $evaluationForm?->evaluationSubmissions()
                        ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                        ->selectRaw('DATE(created_at) as date, count(*) as count')
                        ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                        ->get();

                    // Add to the daily count array
                    foreach ($submissions as $submission) {
                        $dailyCount[$submission->date] = $submission->count;
                    }
                }
                }

                // Prepare data for the chart (dates and corresponding counts)
                $dates = array_keys($dailyCount); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $counts = array_values($dailyCount); // Corresponding counts of evaluation submissions

                // Dispatch data to the chart
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Evaluation Submissions by Date',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $counts, // Pass the count of evaluation submissions per date
                'isDuration' => false
                ]);


            }elseif($this->groupSelected){

               // Get the organization by account ID

               $array = UserGroup::where('id', $this->groupSelected)->first();

               if (!$array) {
               return; // Or handle accordingly
               }

               $countEvaluation = 0;
               $dailyCount = []; // Initialize an array to store the grouped counts

               if ($this->role == 4) {
               foreach ($array->organization->evaluationForm as $evaluationForm) {
                   // Get the count of submissions per day, filtered by date range and user
                   $submissions = $evaluationForm?->evaluationSubmissions()
                       ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                       ->where('user_id', auth()->user()->id)
                       ->selectRaw('DATE(created_at) as date, count(*) as count')
                       ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                       ->get();

                   // Add to the daily count array
                   foreach ($submissions as $submission) {
                       $dailyCount[$submission->date] = $submission->count;
                   }
               }
               } else {
               foreach ($array->organization->evaluationForm as $evaluationForm) {
               // Get the count of submissions per day, filtered by date range
                   $submissions = $evaluationForm?->evaluationSubmissions()
                       ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                       ->selectRaw('DATE(created_at) as date, count(*) as count')
                       ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                       ->get();

                   // Add to the daily count array
                   foreach ($submissions as $submission) {
                       $dailyCount[$submission->date] = $submission->count;
                   }
               }
               }

               // Prepare data for the chart (dates and corresponding counts)
               $dates = array_keys($dailyCount); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
               $counts = array_values($dailyCount); // Corresponding counts of evaluation submissions

               // Dispatch data to the chart
               $this->dispatch('update-chart2', [
               'chartTitle' => 'Evaluation Submissions by Date',
               'dates' => $dates, // Pass the dates to the chart
               'avgDurations' => $counts, // Pass the count of evaluation submissions per date
               'isDuration' => false
               ]);
            }else{
                $this->dispatch('update-chart2', [
                    'chartTitle' => '',
                    'dates' => [],
                    'avgDurations' => [],
                    'isDuration' => true
                ]);
            }
        }elseif($type == 'Duration < 2 Minutes'){
            if ($this->groupSelected == 'All') {
               
                $array = Organization::where('id', $this->accountIDFilter)
                ->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                return $interactions->filter(function ($interaction) {
                    // Check if the call duration is less than 2 minutes
                    return strtotime($interaction->call_duration) < strtotime('00:02:00');
                })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Calls < 2 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $count, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false
                ]);


            }elseif($this->groupSelected){

                $array = UserGroup::where('id', $this->groupSelected)->first();
            
                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });


                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                return $interactions->filter(function ($interaction) {
                    // Check if the call duration is less than 2 minutes
                    return strtotime($interaction->call_duration) < strtotime('00:02:00');
                })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Calls < 2 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $count, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false
                ]);
            }else{
                $this->dispatch('update-chart2', [
                    'chartTitle' => '',
                    'dates' => [],
                    'avgDurations' => [],
                    'isDuration' => false
                ]);
            }
        }elseif($type == 'Duration > 8 Minutes'){
            if ($this->groupSelected == 'All') {
               
                $array = Organization::where('id', $this->accountIDFilter)
                ->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                return $interactions->filter(function ($interaction) {
                    // Check if the call duration is less than 2 minutes
                    return strtotime($interaction->call_duration) > strtotime('00:08:00');
                })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Calls > 8 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $count, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false
                ]);


            }elseif($this->groupSelected){

                $array = UserGroup::where('id', $this->groupSelected)->first();
            
                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });


                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                return $interactions->filter(function ($interaction) {
                    // Check if the call duration is less than 2 minutes
                    return strtotime($interaction->call_duration) > strtotime('00:08:00');
                })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Calls > 8 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $count, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false
                ]);
            }else{
                $this->dispatch('update-chart2', [
                    'chartTitle' => '',
                    'dates' => [],
                    'avgDurations' => [],
                    'isDuration' => false
                ]);
            }
        }elseif($type == 'Hold Duration > 2 Minutes'){
            if ($this->groupSelected == 'All') {
               
                $array = Organization::where('id', $this->accountIDFilter)
                ->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });


                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                return $interactions->filter(function ($interaction) {
                    // Check if the call duration is less than 2 minutes
                    return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Hold Duration > 2 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $count, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false
                ]);


            }elseif($this->groupSelected){

                $array = UserGroup::where('id', $this->groupSelected)->first();
            
                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                return $interaction->created_at->format('Y-m-d'); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                return $interactions->filter(function ($interaction) {
                    // Check if the call duration is less than 2 minutes
                    return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // Dispatch data to the chart (adjusting as per chart's requirements)
                $this->dispatch('update-chart2', [
                'chartTitle' => 'Hold Duration > 2 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $count, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false
                ]);
            }else{
                $this->dispatch('update-chart2', [
                    'chartTitle' => '',
                    'dates' => [],
                    'avgDurations' => [],
                    'isDuration' => false
                ]);
            }
        }
    }
    public function getDate($type)
    {
        $this->dateTo = Carbon::now();
        $this->dateType = $type;

        switch ($type) {
            case 'Last 24 Hours':
                $this->dateFrom = Carbon::now()->subHours(24);
                break;

            case 'Last 7 Days':
                $this->dateFrom = Carbon::today()->subDays(6);
                break;

            case 'Last 30 Days':
                $this->dateFrom = Carbon::today()->subDays(29);
                break;

            case 'Last 60 Days':
                $this->dateFrom = Carbon::today()->subDays(59);
                break;

            default:
                $this->dateFrom = null;
                $this->dateTo = null;
                break;
        }
        $this->setAccountFilter($this->accountIDFilter,$this->accountNameFilter);
        $this->getData($this->groupSelected);
        $this->getChartData($this->cardSelected);
    }
    public function apply_custom_date(){
        $this->validate();
        $this->dateType = null;
        $this->dateFrom = $this->dateFromFilter .' 00:00:00';
        $this->dateTo = $this->dateToFilter .' 23:59:59';
        $this->setAccountFilter($this->accountIDFilter,$this->accountNameFilter);
        $this->getData($this->groupSelected);
        $this->getChartData($this->cardSelected);
        $this->dispatch('close-modal');
    }
    public function rules()
    {
        return [
            'dateFromFilter' => 'required',
            'dateToFilter' => 'required|after:dateFromFilter'
        ];
    }
    public function render()
    {
        return view('livewire.analytics-quality',['accounts' => $this->getAccounts()]);
    }
}
