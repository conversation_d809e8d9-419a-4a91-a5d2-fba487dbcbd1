@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Configuration')

{{-- Style Section --}}
@section('style')
    <style>
        .filter-dialog {
            transition: 0.8s;
        }

        .filter-dialog:hover {
            color: green !important;
            font-weight: 900 !important;
            box-shadow: 3px 4px 11px -4px green !important;
        }

        .filter-dialog:hover .card-body i {
            filter: blur(0.3px);
        }

        .filter-dialog.active {
            color: green !important;
            font-weight: 900 !important;

        }

        thead th {
            background-color: #40798C !important;
            color: #FFFFFF !important;
            /* font-size: medium; */
            font-size: small;
            height: 0rem;
        }

        tbody td {
            /* font-size: medium !important; */
            font-size: small !important;
            height: 0rem;
            font-weight: 600;
            border-bottom: none;
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 1rem !important;
        }

        .table-responsive table {
            text-align: center;
            vertical-align: middle;
        }

        form select {
            cursor: pointer;
        }

        select {
            -webkit-appearance: none;
            /* Remove default styling in Safari/Chrome */
            -moz-appearance: none;
            /* Remove default styling in Firefox */
            appearance: none;
            /* Standard */
            padding-right: 2.5rem;
            /* Add space for the custom arrow */
            background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="black" viewBox="0 0 24 24"%3E%3Cpath d="M7 10l5 5 5-5z"/%3E%3C/svg%3E');
            /* Custom arrow */
            background-repeat: no-repeat;
            background-position: right 1rem center;
            /* Position arrow */
            background-size: 1.5rem;
            /* Make arrow bigger */
        }

        select:disabled {
            background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="gray" viewBox="0 0 24 24"%3E%3Cpath d="M7 10l5 5 5-5z"/%3E%3C/svg%3E');
            /* Gray arrow for disabled dropdown */
        }
        .thead {
            height: 50px;
            vertical-align: middle;
        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        tr th {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        th {
            text-wrap: nowrap !important;
        }

        td {
            text-wrap: nowrap !important;
        }

        table td {
            border-bottom: none !important;
        }

        table {
            position: relative;
        }

        thead {
            height: 1rem !important;
        }
        tbody {
            height: 1rem !important;
        }
    </style>
@endsection


{{-- Content Section --}}
@section('content')
    <div class="container-fluid ms-5" style="width: 93%;">
{{--        @include('admin.configuration.filter.filter')--}}
        <div class="row">
            <div class="col-12">
                <livewire:admin.configuration.script-index />
            </div>
        </div>
    </div>
@endsection

{{-- Script Section --}}
@section('scripts')
    <script>
        window.addEventListener('close-modal', event => {
            console.log('test')
            document.getElementById('closeModal').click();
        });
    </script>
@endsection
