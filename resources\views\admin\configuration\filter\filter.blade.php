@php
    $current_page = \Route::currentRouteName();
@endphp
<div class="row mt-3 px-5 ">

    {{-- <div class="col-lg-2 col-md-6 col-sm-6 col-12">
        <div class="card mb-3 shadow   rounded" style="width: 100%;">
            <a href="{{ route('admin.config.encryption') }}" class="text-decoration-none text-dark filter-dialog @if (Illuminate\Support\Str::contains($current_page, 'admin.config.encryption')) active @endif">
                <div class="card-body">
                    <h2 class="card-title text-center" style="font-size: 55px"><i class="fa fa-lock"></i></h2>
                    <p class="card-text text-center mt-2 ">Encryption</p>
                </div>
            </a>
        </div>
    </div> --}}
    <div class="card col-md-2 col-12 lower-card bg-white shadow-sm ms-5 mb-5  card-with-motion" style="border-radius: 12px; height: 300px; border-color: #f2f2f2;">
        <a href="{{ route('admin.config.bad_words') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #d7e5ff !important;">
                <i class="fas fa-circle-pause" style="font-size: 30px; color: #83aeff !important;"></i>
            </div>
            <div class="mt-5">
                <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Keywords</h6>
                <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                    Keywords ensures the detection of inappropriate or offensive words in call interactions..
                </p>
            </div>
        </a>
    </div>

    <div class="card col-md-2 col-12 lower-card bg-white shadow-sm ms-5 mb-5  card-with-motion" style="border-radius: 12px; height: 300px; border-color: #f2f2f2;">
        <a href="{{ route('admin.config.scripts') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #d7e5ff !important;">
                <i class="fas fa-id-card-alt" style="font-size: 30px; color: #83aeff !important;"></i>
            </div>
            <div class="mt-5">
                <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Scripts</h6>
                <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                    Scripts ensures the detection of inappropriate or offensive words in call interactions..
                </p>
            </div>
        </a>
    </div>

    <div class="card col-md-2 col-12 lower-card bg-white shadow-sm ms-5 mb-5 card-with-motion" style="border-radius: 12px; height: 300px; border-color: #f2f2f2;">
        <a href="{{ route('admin.config.encryption') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #d7e5ff !important;">
                <i class="fa fa-lock" style="font-size: 30px; color: #83aeff !important;"></i>
            </div>
            <div class="mt-5">
                <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Encryption</h6>
                <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                    Encryption ensures the secure storage and transfer of call recordings, unauthorized access.
                </p>
            </div>
        </a>
    </div>




</div>
