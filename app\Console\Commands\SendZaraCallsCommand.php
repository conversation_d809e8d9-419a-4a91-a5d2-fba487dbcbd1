<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use GuzzleHttp\Client;
use App\Mail\ZaraFilesMail;
use App\Models\Interaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class SendZaraCallsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'zara:sendCalls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get yesterday's date in 'Y-m-d' format
        $yesterday = Carbon::yesterday()->toDateString();

        // Get the organization ID you want to filter by
        $organizationId = 48; // Change this to the organization ID you want to filter by

        // Count the number of interactions for yesterday and filter by organization_id
        $interactionCount = Interaction::join('users', 'interactions.user_id', '=', 'users.id')
            ->where('users.organization_id', $organizationId)
            ->whereDate('interactions.created_at', $yesterday)
            ->count();

        // Calculate 40% of the interaction count and convert it to an integer
        $interactionPercentage = floor($interactionCount * 0.4);

        // Select 4% of the recorded interactions randomly
        $randomInteractions = Interaction::join('users', 'interactions.user_id', '=', 'users.id')
            ->where('users.organization_id', $organizationId)
            ->whereDate('interactions.created_at', $yesterday)
            ->whereNotNull('interactions.caller_id') // Ensure caller_id is not null
            ->whereNotNull('interactions.language') // Ensure language is not null
            ->inRandomOrder()
            ->take($interactionPercentage)
            ->get(['interactions.call_id', 'interactions.caller_id', 'users.agent_id', 'interactions.language', 'interactions.arrival_time']);

        $formattedInteractions = $randomInteractions->map(function ($interaction) {
            $formattedArrivalTime = Carbon::parse($interaction->arrival_time)->format('Ymd');
            $languageAbbreviation = ($interaction->language == 'english') ? 'EN' : 'AR';

            return [
                'call_id' => $interaction->call_id,
                'new_name' => $interaction->caller_id . '_' . $interaction->agent_id . '_SA_' . $languageAbbreviation . '_' . $formattedArrivalTime
            ];
        })->toArray();

        // // Debug the formatted interactions
        // dd($formattedInteractions);

        // Create a Guzzle HTTP client instance
        $client = new Client();

        // Send the data to the external API
        $response = Http::withoutVerifying()->timeout(120)->post('https://oms.extwebonline.com/Extensya_APIs/recording/featch_recording_zara.php', [
            'json' => $formattedInteractions
        ]);


        // Check the response status
        if ($response->getStatusCode() == 200) {
            $responseBody = json_decode($response->getBody(), true);
            Log::info('Zara: ' . $response->body()); // Display the response for debugging

            // send email notification 
            Mail::to(['<EMAIL>', '<EMAIL>', '<EMAIL>'])->send(new ZaraFilesMail('emails.zara_files_uploaded'));
        } else {
            Log::info('Zara Error: ' . $response->getStatusCode()); // Display error code for debugging

            // send email notification 
            Mail::to(['<EMAIL>', '<EMAIL>', '<EMAIL>'])->send(new ZaraFilesMail('emails.zara_files_failed'));
        }
    }
}
