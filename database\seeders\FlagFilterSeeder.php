<?php

namespace Database\Seeders;

use App\Models\FlagFilter;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FlagFilterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filter_names = [
            'Agent ID',
            'Agent Name',
            'Silent Duration',
            'Interaction Ender',
            'Hold Duration',
            'Hold Count',
            'Language',
        ];

        foreach ($filter_names as $name) {
            FlagFilter::create([
                'name' => $name,
                'qa_flag_id' => 1,
                'condition' => '=',
                'first_data' => 1,
        ]);
        }
    }
}
