<?php

namespace Database\Seeders;

use App\Models\QaFlag;
use App\Models\Evaluation;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class QaFlagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $flag = QaFlag::create([
            'organization_id' => 1,
            'name' => 'System_Default_Flag',
            'start_date' => now(),
            'end_date' => now()->addYears(100),
            'interactions_number' => 1,
            'per' => 1,
            'time_interval' => 'Day',
            'distribution_level' => 'Agent',
            // 'screen_capture' => $this->screen_capture_add,
            'call_type' => 'All',
            'interaction_days' => json_encode([1,2,3,4,5,6,7]),
            'interaction_time_from' => null,
            'interaction_time_to' => null,
            'duration_start' => null,
            'duration_end' => null,
            'duration_condition' => '=',
            'enabled' => 1,
            'active' => 1,
        ]);

        $flag->evaluationForms()->sync(Evaluation::all()->pluck('id')->toArray());
    }
}
