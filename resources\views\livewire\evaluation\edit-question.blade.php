<div class="container-fluid mt-3 px-4">




<div class="shadow content row">
    <div class="header-div" style="height: 15%;"></div>
    <div class="row" style="height: 70%;">
    <div class="col-4 coustome-style-div">
        <div class="row">
            <div class="">
                <div class="mb-3">
                    <label for="title" class="col-form-label ">Header Name:</label>
                    <div class="custom-select" style="position: relative">
                        <select class="form-control" wire:model.defer="header_name" id="header_name" style="font-weight: 300;">
                            <option value="">Select Header...</option>

                            @forelse($headers ?? [] as $header)

                                <option value="{{$header->id}}">{{$header->header_name}}</option>
                            @empty
                            @endforelse
                        </select>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z"/>
                        </svg>
                    </div>
                    <div class="new-header-style" wire:click="createNewHeader">Create new header</div>
                </div>
            </div>
        </div>
        @if($create_new_header)
            <div class="row">
                <div class="">
                    <div class="mb-3">
                        <label for="title" class="col-form-label ">New Header:</label>

                        <input type="text" class="form-control" wire:model.defer="new_header" id="new_header" style="resize: none;" />

                    </div>
                </div>
            </div>
        @endif
        <div class="row">
            <div class="">
                <div class="mb-3">
                    <label for="title" class="col-form-label ">Question Name:</label>

                    <textarea class="form-control" wire:model.defer="question_name" id="question_name" style="resize: none;" placeholder="write question name here..."></textarea>
{{--                    @error('question_name')<small class="text-danger"> {{ $message }} </small> @enderror--}}

                </div>
            </div>
        </div>
    </div>
    <div class="col-4 coustome-style-div">
        <div class="row">
            <div class="">
                <div class="mb-3">
                    <label for="title" class="col-form-label ">Add:</label>
                    <div class="checkbox-parent">
                        <div class="form-group" style="margin-right: 3%;width: 25%;" >
                            <input type="checkbox" id="Fatal" wire:click="getFatalType">
                            <label for="Fatal" class="text-muted">Fatal</label>
                        </div>
                        <div class="form-group" style="width: 20%;">
                            <input type="checkbox" id="NA" wire:click="getFatalNA"  />
                            <label for="NA" class="text-muted">NA</label>
                        </div>
                    </div>

                    @if($fatalNA && !$flag3)<script>document.getElementById('NA').click();</script>@endif
                    @if($fatal && !$flag)<script>document.getElementById('Fatal').click();</script>@endif
                    @if($fatal)

                        <hr style="width: 50%;color: #00a34e;">
                        <div class="radio-parent">
                            <div class="form-group-raido" style="margin-right: 3%;width: 45%;border-radius: 20px">

                                <input type="radio" class="form-check-input" name="fatal_type" value="Fatal Per Group" wire:model="fatal_type" >
                                <label for="Fatal Per Group" class="text-muted">Fatal Per Group</label>
                            </div>
                            <div class="form-group-raido" style="margin-right: 5%;width: 40%;border-radius: 20px">
                                <input type="radio" class="form-check-input" name="fatal_type" value="Fatal Critical" wire:model="fatal_type">
                                <label for="Fatal Critical" class="text-muted">Fatal Critical</label>
                            </div>
                        </div>
                        @error('fatal_type')<small class="text-danger"> {{ $message }} </small> @enderror
                    @endif
                </div>
            </div>
        </div>
        <div class="row">
            <div class="">
                <div class="mb-3">
                    <label for="title" class="col-form-label ">Mark Type:</label>
                    <div class="custom-select" style="position: relative">
                        <div class="form-control" wire:click="showDropdown"  style="font-weight: 300;cursor: pointer">
                            @if($selected_mark_type=='Emojy')
                                <span style="margin-left: 5px;">&#128513;</span>
                            @elseif($selected_mark_type=='Text')
                                <img  src="{{ asset('assets/images/evaluation/textIcon.PNG') }}" alt="Text Icon" width="30" height="30">
                            @elseif($selected_mark_type=='Number')
                                <span style="margin-left: 4px;"><img  src="{{ asset('assets/images/evaluation/numberIcon.PNG') }}" alt="Text Icon" width="23" height="30"></span>
                            @endif
                            {{$selected_mark_type}}
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z"/>
                        </svg>
                    </div>
                    @if($dropdownIsOpen)
                        <div class="shadow dropdown-style" >
                            <div class="dropdown-item" wire:click="getMarkType('Emojy')"><span style="margin-left: 5px;">&#128513;</span> <span style="margin-left: 5px;">Emoji</span></div>
                            <div class="dropdown-item" wire:click="getMarkType('Text')">
                                <img  src="{{ asset('assets/images/evaluation/textIcon.PNG') }}" alt="Text Icon" width="30" height="30">
                                Text</div>
                            <div class="dropdown-item" wire:click="getMarkType('Number')">
                                <span style="margin-left: 4px;"><img  src="{{ asset('assets/images/evaluation/numberIcon.PNG') }}" alt="Text Icon" width="23" height="30"></span>
                                <span style="margin-left: 5px;">Number</span>
                            </div>
                        </div>
                    @endif
                    @error('mark_type')<small class="text-danger"> {{ $message }} </small> @enderror
                </div>
            </div>
        </div>

    </div>
    <div class="col-4 coustome-style-div">
        @if($mark_type)

            <div class="row js-copy">
                @if($selected_mark_type=='Emojy')
                    <div class="mb-3 col-6 mrk">
                        <label for="title" class="col-form-label ">Mark:</label>
                        <div class="custom-select" style="position: relative">
                            <select class="form-control"  wire:model="arrayMarkAndWeight.0.mark" style="font-weight: 200;">
                                <option value="">Select Mark...</option>
                                <option value="&#129321; Amazed">&#129321; Amazed</option>
                                <option value="&#128526; Great">&#128526; Great</option>
                                <option value="&#128513; Happy">&#128513; Happy</option>
                                <option value="&#128533; Confused">&#128533; Confused</option>
                                <option value="&#128577; Disappointed">&#128577; Disappointed</option>
                                <option value="&#128532; Sad">&#128532; Sad</option>
                                <option value="&#x1F44D; Good">&#x1F44D; Good</option>
                                <option value="&#x1F44E; Bad">&#x1F44E; Bad</option>
                            </select>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                <path d="M7 10l5 5 5-5z"/>
                            </svg>
                        </div>
                        @error('arrayMarkAndWeight.0.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                    </div>
                @elseif($selected_mark_type=='Text')
                    <div class="mb-3 col-6 mrk">
                        <label for="title" class="col-form-label  mark-lable">Mark:</label>
                        <div class="custom-select" style="position: relative">
                            <select class="form-control" wire:model="arrayMarkAndWeight.0.mark" style="font-weight: 200;">
                                <option value="">Select Mark...</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                                <option value="Pass">Pass</option>
                                <option value="Fail">Fail</option>
                            </select>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                <path d="M7 10l5 5 5-5z"/>
                            </svg>
                        </div>
                        @error('arrayMarkAndWeight.0.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                    </div>
                @elseif($selected_mark_type=='Number')

                    <div class="mb-3 col-6 mrk">
                        <label for="title" class="col-form-label  mark-lable">Mark:</label>
                        <div class="custom-select" style="position: relative">
                        <select class="form-control" wire:model="arrayMarkAndWeight.0.mark" style="font-weight: 200;">
                            <option value="">Select Mark...</option>
                            <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="15">15</option>
                                                    <option value="20">20</option>
                                                    <option value="25">25</option>
                                                    <option value="30">30</option>
                                                    <option value="35">35</option>
                                                    <option value="40">40</option>
                                                    <option value="45">45</option>
                                                    <option value="50">50</option>
                                                    <option value="55">55</option>
                                                    <option value="60">60</option>
                                                    <option value="65">65</option>
                                                    <option value="70">70</option>
                                                    <option value="75">75</option>
                                                    <option value="80">80</option>
                                                    <option value="85">85</option>
                                                    <option value="90">90</option>
                                                    <option value="95">95</option>
                                                    <option value="100">100</option>
                        </select>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                            <path d="M7 10l5 5 5-5z"/>
                        </svg>
                         </div>
                        @error('arrayMarkAndWeight.0.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                    </div>
                @endif
                <div class="mb-3 col-5 weight">
                    <label for="title" class="col-form-label  weight-lable">Weight:</label>
                    <input type="number" class="form-control" wire:model="arrayMarkAndWeight.0.weight" style="font-weight: 300;" placeholder="Write Number..." />
                    @error('arrayMarkAndWeight.0.weight')<small class="text-danger"> {{ $message }} </small> @enderror
                </div>
            </div>



            @foreach($inputs as $key => $value)
                <div class="row js-copy">
                    @if($selected_mark_type=='Emojy')
                        <div class="mb-3 col-6">
                            <div class="custom-select" style="position: relative">
                                <select class="form-control"  wire:model="arrayMarkAndWeight.{{$value}}.mark" style="font-weight: 200;">
                                    <option value="">Select Mark...</option>
                                    <option value="&#129321; Amazed">&#129321; Amazed</option>
                                    <option value="&#128526; Great">&#128526; Great</option>
                                    <option value="&#128513; Happy">&#128513; Happy</option>
                                    <option value="&#128533; Confused">&#128533; Confused</option>
                                    <option value="&#128577; Disappointed">&#128577; Disappointed</option>
                                    <option value="&#128532; Sad">&#128532; Sad</option>
                                    <option value="&#x1F44D; Good">&#x1F44D; Good</option>
                                    <option value="&#x1F44E; Bad">&#x1F44E; Bad</option>
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                    <path d="M7 10l5 5 5-5z"/>
                                </svg>
                            </div>
                            @error('arrayMarkAndWeight.' .$value. '.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                        </div>
                    @elseif($selected_mark_type=='Text')
                        <div class="mb-3 col-6 mrk">
                            <div class="custom-select" style="position: relative">
                                <select class="form-control" wire:model="arrayMarkAndWeight.{{$value}}.mark" style="font-weight: 200;">
                                    <option value="">Select Mark...</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                    <option value="Pass">Pass</option>
                                    <option value="Fail">Fail</option>
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                    <path d="M7 10l5 5 5-5z"/>
                                </svg>
                            </div>
                            @error('arrayMarkAndWeight.' .$value. '.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                        </div>
                    @elseif($selected_mark_type=='Number')

                        <div class="mb-3 col-6">
                            <div class="custom-select" style="position: relative">
                                <select class="form-control" wire:model="arrayMarkAndWeight.{{$value}}.mark" style="font-weight: 200;">
                                    <option value="">Select Mark...</option>
                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="15">15</option>
                                                    <option value="20">20</option>
                                                    <option value="25">25</option>
                                                    <option value="30">30</option>
                                                    <option value="35">35</option>
                                                    <option value="40">40</option>
                                                    <option value="45">45</option>
                                                    <option value="50">50</option>
                                                    <option value="55">55</option>
                                                    <option value="60">60</option>
                                                    <option value="65">65</option>
                                                    <option value="70">70</option>
                                                    <option value="75">75</option>
                                                    <option value="80">80</option>
                                                    <option value="85">85</option>
                                                    <option value="90">90</option>
                                                    <option value="95">95</option>
                                                    <option value="100">100</option>

                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                    <path d="M7 10l5 5 5-5z"/>
                                </svg>
                            </div>
                            @error('arrayMarkAndWeight.' .$value. '.mark')<small class="text-danger"> {{ $message }} </small> @enderror
                        </div>
                    @endif
                    <div class="mb-3 col-5 weight">
                        <input type="number" class="form-control" name="weight[]" wire:model="arrayMarkAndWeight.{{$value}}.weight" style="font-weight: 300;" placeholder="Write Number..." />
                        @error('arrayMarkAndWeight.' .$value. '.weight')<small class="text-danger"> {{ $message }} </small> @enderror
                    </div>
                    <div class="mb-2 col-1 delete-btn" style="padding-top: 1.8%;">
                        <i class="fa fa-trash-alt" style="font-size:20px;cursor: pointer" wire:click="removeElement({{$key}})"></i>
                    </div>
                </div>

            @endforeach
            <div class="">
                <div class="circle">
                    <i class="fa fa-plus fa-style" style="font-size: 19px;color: white" wire:click="addNewElement({{$counter}})"></i>
                </div>
            </div>
        @endif
    </div>
    </div>
    <div class="footer-btn" style="height: 15%;">

        
            <a 
                type="button" 
                class="btn btn-secondary rounded-3 px-4" 
                style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" 
                href="{{ route('evaluation.createQuestion', ['evaluation_id' => $evaluation_id , 'group_id' => $group_id]) }}">
                Cancel
    </a>
            @if(!$modalIdShow)
                <button 
                    class="btn btn-success rounded-3 px-4" 
                    style="height: 40px; border-color: #01a44f; background: #01a44f;" 
                    wire:click="{{ $modalId ? 'update' : 'store' }}" >
                    Update
                </button>
            @endif
       
    </div>
</div>




</div>

