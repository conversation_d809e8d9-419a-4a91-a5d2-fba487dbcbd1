@props(['transcription'])
@props(['callId'])
@props(['badWords'])
@props(['summary'])
@props(['reason'])
@props(['sub_reason'])
@props(['calllanguage'])
@php

    $lastLeft = 'start';
    $lastRight = 'start';
@endphp
<div>
    <div class="d-flex p-3 w-full transcript-header rounded-3 justify-content-between"
        style='background-color:rgb(239 243 244) !important'>
        <div id="search-wrapper">



            <input type="text" style="width: 300px;" id="search" onkeydown="search(event, this.value)"
                placeholder="Search">
            <i class="search-icon fas fa-search"></i>
            <button type="button" class="btn btn-light rounded-circle" onclick="downloadCSV()" title="Export CSV"
                style="width: 42px; height: 42px;">
                <i class="fas fa-file-export text-success"></i>
            </button>

        </div>


        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#whisperInfoModal">
            What affects transcription?
        </button>

    </div>
    <div class="row">
        <div class="col-6 transcription-container pt-3">
            @foreach ($transcription as $singleRow)
                @if (
                    ($singleRow->source == 'left' && $lastLeft == $singleRow->content) ||
                        ($singleRow->source == 'right' && $lastRight == $singleRow->content))
                    @continue
                @endif
                @php
                    $lastLeft = $singleRow->source == 'left' ? $singleRow->content : $lastLeft;
                    $lastRight = $singleRow->source == 'right' ? $singleRow->content : $lastRight;
                @endphp
                <x-chunks.conv :time="$singleRow->duration_from" :time_to="$singleRow->duration_to" :source="$singleRow->source" :content="$singleRow->content"
                    :classificationRow="$singleRow" />
            @endforeach
        </div>
        <div class="col-6 transcription-container pt-3"
            style="display: flex
;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    align-content: stretch;
    overflow:hidden;">
            <div
                style='border-bottom:1px solid gray; border-bottom: 1px solid gray;
    display: flex
;
    width: 100%;
    justify-content:  space-around;    flex-wrap: wrap;

    flex-direction: row; '>


                @php
                    $type = $reason;
                    $badgeColor = match ($type) {
                        'Complaint', 'شكوى' => 'rgb(255, 99, 132)',
                        'Escalation', 'تصعيد' => 'rgb(255, 99, 132)',
                        'Inquiry', 'استفسار' => '#96c0cf',
                        'Feedback', 'ملاحظات' => '#96c0cf',
                        default => '#96c0cf	',
                    };
                @endphp

                @php
                    if ($calllanguage == 'English') {
                        $callReasonText = 'Call Reason';
                        $callSubReasonText = 'Call Sub Reason';
                        $direction = 'ltr';
                        $summaryHeadr = 'Summary';
                    } else {
                        $callReasonText = 'سبب المكالمة';
                        $callSubReasonText = 'السبب الفرعي للمكالمة';
                        $direction = 'rtl';
                        $summaryHeadr = 'ملخص المكالمة';
                    }
                @endphp

                <h5 class="text-muted fw-bold text-center w-100">{{ $summaryHeadr}}</h5>
                <div class="d-flex flex-column pt-1 pb-1 align-items-center " style="min-width: 25%;">
                    <h5 class="text-muted pb-2 fw-bold">{{ $callReasonText}}</h5>
                    <span class="badge d-flex align-items-center justify-content-center rounded-pill p-2 w-100"
                        style="font-size: 0.9rem; background-color: {{ $badgeColor }}; color: white;">
                        <span class="pt-1">

                        {{ __($type) }}
                                                </span>

                    </span>
                </div>
                <div class="d-flex flex-column pt-1 pb-1 align-items-center" style="min-width: 25%;">
                    <h5 class="text-muted pb-2 fw-bold">{{ $callSubReasonText }}</h5>
                    <span class="badge d-flex align-items-center justify-content-center rounded-pill p-2 w-100"
                        style="font-size: 0.9rem;background-color: #198754; color: white;">
                        {{-- Sub Reason --}}
                        <span class="pt-1">
                        {{ __($sub_reason) }}
                        </span>
                    </span>
                </div>
            </div>

            @if ($summary)
                <div class=" pt-2 col-11 conv-text-container conv-text-container-customer" style="overflow: auto;
    max-height: 60%;">
                    <p style="font-size: 1.125rem;
    white-space: normal; direction:{{ $direction }}"
                        class='  conv-text px-2 rounded-4{{-- py-1 --}}   custom-tooltip'>

                        {{ $summary->summary }}
                    </p>
                </div>
            @else
                <p style="font-size: 1.125rem;
    white-space: normal;"
                    class='  conv-text px-2 rounded-4{{-- py-1 --}}   custom-tooltip'>
                    No summary available.
                </p>
            @endif
        </div>

    </div>
</div>


<div class="modal modal-lg fade" id="whisperInfoModal" tabindex="-1" wire:ignore.self>
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            {{-- Modal Header --}}
            <div class="modal-header" style="border: none;">
                <div class="d-flex ">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3"
                        style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                        <i class="fas fa-brain" style="font-size: 30px; color: #01a44f !important;"></i>
                    </div>

                    <h4 class="modal-title d-flex justify-content-center align-items-center" style="font-size: 24px;">
                        Transcription Info
                    </h4>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="border-0 bg-transparent" onclick="closeWhisperInfoModal()"
                        aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
            </div>

            {{-- Modal Body --}}
            <div class="modal-body">
                <div class="section-one">
                    <div class="px-2">

                        <h6 class="mt-3 text-success">🧠 How It Works</h6>
                        <ul>
                            <li>We use a powerful speech recognition AI model.</li>
                            <li>It listens to the audio, breaks it into parts, and types what it hears.</li>
                            <li>It supports many languages and handles noisy environments (within limits).</li>
                            <li><strong>Neural Processing:</strong> An encoder--decoder Transformer trained on vast
                                multilingual speech–text pairs maps each frame sequence to probabilities over text
                                tokens..</li>
                            <li><strong>Contextual Decoding:</strong> It uses beam-search decoding to propose complete
                                word sequences, scoring each against prior and following context to choose the most
                                coherent transcription.</li>
                            <li><strong>Post-Processing:</strong> Finally, raw tokens are detokenized, punctuated, and
                                formatted into readable text.</li>
                        </ul>

                        <h6 class="mt-4 text-success">🎯 What Affects Accuracy</h6>
                        <ul>
                            <li><strong>Audio Quality:</strong> Clear sound helps; background noise and low bitrate
                                audio reduce accuracy. Telephony codecs (e.g., G.711 at 8 kHz) cut off higher
                                frequencies, making consonants less clear.</li>
                            <li><strong>Speaker Clarity:</strong> Fast talking, mumbling, or strong accents may confuse
                                the model.</li>
                            <li><strong>Background Noise:</strong> Music, traffic, or other voices in the background can
                                distort results.</li>
                            <li><strong>Overlapping Speech:</strong> When two or more people talk at the same time, the
                                model struggles to distinguish words.</li>
                            <li><strong>Dropouts & Jitter:</strong> Network hiccups or timing shifts skip or scramble
                                chunks of speech.</li>
                            <li><strong>Long Silences:</strong> Extended pauses and holds may confuse the model or cause
                                it to insert incorrect or missing words.</li>
                            <li><strong>Microphone Distance:</strong> Distant or muffled voices are harder to capture
                                correctly.</li>
                            <li><strong>Volume Swings:</strong> Moving the mic or changing gain can push audio into
                                clipping or make it too soft.</li>
                            <li><strong>Echo:</strong> Incomplete echo cancellation leaves faint repetitions that blur
                                word boundaries.</li>
                            <li><strong>Unfamiliar Terms:</strong>Rare names, product codes, or slang not seen in
                                training may be miswritten.</li>
                        </ul>

                        <h6 class="mt-4 text-success">⚠️ Limitations</h6>
                        <ul>
                            <li>Doesn't always correctly detect speaker changes.</li>
                            <li>Uncommon names or technical words might be misspelled.</li>
                            <li>Heavy accents or mixed languages can impact results.</li>
                            <li>The model doesn’t “understand” meanings — it only transcribes what it hears.</li>
                        </ul>

                        <h6 class="mt-4 text-success">🚀 Always Improving</h6>
                        <ul>
                            <li>We're working on better handling for background noise and speaker separation.</li>
                            <li>Thank you for using our transcription feature and helping us improve!</li>
                        </ul>

                    </div>
                </div>
            </div>

            {{-- Modal Footer --}}
            <div class="modal-footer" style="border: none;">
                <button type="button" onclick="closeWhisperInfoModal()" class="btn btn-secondary rounded-3 px-4"
                    style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">
                    Close
                </button>
            </div>

        </div>
    </div>
</div>

<script>
    function closeWhisperInfoModal() {
        $('#whisperInfoModal').modal('hide');

        // Remove lingering backdrop and modal-open class (Bootstrap 4 fix)
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('padding-right', '');
    }
</script>

<script>
    // Global variables
    let currentMatchIndex = 0;
    let previousSearchValue = "";
function resetHighlights(divs) {
    divs.forEach(div => {
        const spans = div.querySelectorAll('span[data-highlight]');
        spans.forEach(span => {
            const textNode = document.createTextNode(span.textContent);
            span.replaceWith(textNode);
        });
        div.normalize();
    });
}
    function search(event, value) {
        let divs = document.querySelectorAll('.conv-row');
        let matchingDivs = []; // Array to store matching divs

        // Reset the highlights if the search value changes
        if (value !== previousSearchValue) {
            resetHighlights(divs); // Remove highlights
            previousSearchValue = value; // Update the previous search value
            currentMatchIndex = 0; // Reset match index
        }


        if (event.key === 'Enter' && value !== "") {
            // Collect all matching divs
            divs.forEach((div) => {
                if (div.textContent.includes(value)) {
                    matchingDivs.push(div);
                }
            });

            // If there are matches
            if (matchingDivs.length > 0) {
                // Reset index if it exceeds available matches
                if (currentMatchIndex >= matchingDivs.length) {
                    currentMatchIndex = 0;
                }

                // Get the current matching div
                const currentDiv = matchingDivs[currentMatchIndex];

                // Scroll the div into view
                currentDiv.scrollIntoView({
                    behavior: 'smooth', // Smooth scrolling animation
                    block: 'center', // Scroll to the center of the viewport
                });

                // Highlight the search term in the current div
                highlightText(currentDiv, value);

                console.log(`Scrolled to match ${currentMatchIndex + 1}:`, currentDiv.textContent);

                // Move to the next match on the next Enter press
                currentMatchIndex++;
            } else {
                console.log('No matches found.');
            }
        }
    }

    // Function to highlight the search term
/*     function highlightText(div, value, color = 'yellow') {
        const regex = new RegExp(`(${value})`, 'gi'); // Regex to match the value
        console.log(regex);

        div.innerHTML = div.innerHTML.replace(
            regex,
            `<span style="font-weight: bold; color: ${color}; ">$1</span>`
        );
    }
 */

 function highlightText(div, value, color = '#ffe5b4') {
    const regex = new RegExp(`(${escapeRegExp(value)})`, 'gi');

    const walker = document.createTreeWalker(div, NodeFilter.SHOW_TEXT, null, false);
    let node;
    const nodesToProcess = [];

    while ((node = walker.nextNode())) {
        if (regex.test(node.textContent)) {
            nodesToProcess.push(node);
        }
    }

    nodesToProcess.forEach(textNode => {
        const parent = textNode.parentNode;
        const originalText = textNode.textContent;

        const tempContainer = document.createElement('span');
        tempContainer.innerHTML = originalText.replace(
            regex,
            `<span data-highlight style="font-weight: bold; background-color: ${color};">$1</span>`
        );

        while (tempContainer.firstChild) {
            parent.insertBefore(tempContainer.firstChild, textNode);
        }

        parent.removeChild(textNode);
    });
}

function escapeRegExp(text) {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
    function escapeRegex(word) {
        return word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    function highlightTextInNode(node, words, color = 'red') {
        const escapedWords = words
            .filter(w => w.trim() !== '')
            .map(w => escapeRegex(w));

        if (escapedWords.length === 0) return;

        //const regex = new RegExp(`(${escapedWords.join('|')})`, 'gi');
        const regex = new RegExp(`(?<!\\S)(${escapedWords.join('|')})(?!\\S)`, 'gi');

        const walker = document.createTreeWalker(
            node,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let textNode;
        const textNodes = [];

        while ((textNode = walker.nextNode())) {
            textNodes.push(textNode);
        }

        textNodes.forEach(textNode => {
            const originalText = textNode.nodeValue;
            const highlighted = originalText.replace(regex, match => {
                return `<span style="font-weight:bold; color:${color};">${match}</span>`;
            });

            if (highlighted !== originalText) {
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlighted;
                textNode.parentNode.replaceChild(wrapper, textNode);
            }
        });
    }

    // Example extractWords and usage
    function extractWords(jsonString) {
        try {
            const obj = JSON.parse(jsonString);
            return Object.values(obj).flat();
        } catch {
            return [];
        }
    }

    const data = @json($badWords);
    const extractedWords = [...new Set(data.flatMap(item => extractWords(item.words_detected)).map(w => w
    .toLowerCase()))];

    const divs = document.querySelectorAll('.conv-row');

    divs.forEach(div => {
        highlightTextInNode(div, extractedWords, 'red');
        highlightConsecutiveRepeatedWords(div, 'orange'); // new: orange for repeated-in-same-row


    });
    /*

    function highlightRepeatedWordsInSameDiv(div, color = 'orange') {
        const text = div.textContent.toLowerCase();

        // Extract and clean words
        const words = text
            .split(/\s+/)
            .map(w => w.replace(/[^\wء-ي]+/g, '').trim())
            .filter(w => w.length > 1);

            console.log(words);

        // Count frequencies within the same div
        const freq = {};
        words.forEach(word => {
            freq[word] = (freq[word] || 0) + 1;
        });

        // Select words repeated more than once
        const repeatedWords = Object.keys(freq).filter(word => freq[word] > 1);

        if (repeatedWords.length === 0) return;

        highlightTextInNode(div, repeatedWords, color);
    } */
    function highlightConsecutiveRepeatedWords(div, color = 'orange') {
        const walker = document.createTreeWalker(div, NodeFilter.SHOW_TEXT, null, false);

        function wrap(word) {
            return `<span style="font-weight:bold; color:${color};">${word}</span>`;
        }

        let textNode;
        while ((textNode = walker.nextNode())) {
            const originalText = textNode.nodeValue;
            const tokens = originalText.match(/\S+|\s+/g); // words and spaces
            if (!tokens) continue;

            const result = [];
            let lastClean = '';
            let lastIndex = -1;

            for (let i = 0; i < tokens.length; i++) {
                const token = tokens[i];
                const cleaned = token.toLowerCase().replace(/[^\wء-ي]+/g, '');

                if (cleaned && cleaned === lastClean) {
                    result[lastIndex] = wrap(tokens[lastIndex]); // safely wrap previous
                    result[i] = wrap(token); // wrap current
                } else {
                    result[i] = token;
                }

                // Only track if it's an actual word
                if (cleaned) {
                    lastClean = cleaned;
                    lastIndex = i;
                }
            }

            const final = result.join('');
            if (final !== originalText) {
                const span = document.createElement('span');
                span.innerHTML = final;
                textNode.parentNode.replaceChild(span, textNode);
            }
        }
    }
</script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        window.downloadCSV = function() {
            const rows = [
                ["Source", "Content", "Start Time", "End Time"]
            ];
            const elements = document.querySelectorAll('.conv-text');

            elements.forEach(el => {
                let source = el.dataset.source || '';
                source = source === 'left' ? 'Customer' : source === 'right' ? 'Agent' : source;
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = el.dataset.content || '';
                const cleanText = tempDiv.textContent || tempDiv.innerText || '';
                const content = cleanText.replace(/\n/g, ' ').trim();

                const timeFrom = el.dataset.fromTime || '';
                const timeTo = el.dataset.fromTo || '';
                rows.push([source, content, timeFrom, timeTo]);
            });

            if (rows.length <= 1) {
                alert("No transcription data found.");
                return;
            }

            let csvContent = rows.map(row =>
                row.map(field => `"${field.replace(/"/g, '""')}"`).join(',')
            ).join('\n');

            // ✨ Add BOM prefix for UTF-8
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], {
                type: 'text/csv;charset=utf-8;'
            });
            const url = URL.createObjectURL(blob);

            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "transcription.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };
    });
</script>
