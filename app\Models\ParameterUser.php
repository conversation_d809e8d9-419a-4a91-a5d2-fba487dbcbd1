<?php

namespace App\Models;

use App\Models\User;
use App\Models\Parameter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ParameterUser extends Model
{
    use HasFactory;
    protected $table = 'parameter_user';

    // Define the relationship with the User model
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Define the relationship with the Parameter model
    public function parameter()
    {
        return $this->belongsTo(Parameter::class);
    }
}
