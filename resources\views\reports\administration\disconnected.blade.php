@extends('layouts.app')

@section('style')
    {{-- <style>
        input {
            border: solid 1px #b6b6b6 !important;
            border-radius: 0.6rem !important;
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        } --}}
    {{-- Tilte Section --}}
@section('title', 'Disconnected Calls Report')
<style>
    /* hide the scrollbar for lists  */
    .dropdown-menu::-webkit-scrollbar {
        display: none
    }

    .table-responsive {
        box-shadow: none !important;
        border-radius: 0 !important;
    }

    input {
        border: solid 1px #b6b6b6 !important;
        /* border-radius: 0.6rem !important; */
        background-color: white !important;
        padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
    }

    .list-group-item:hover {
        color: white;
        background-color: #00a34e;
    }

    th {
        text-wrap: nowrap;
    }

    .dropdown-menu::-webkit-scrollbar {
        display: none
    }

    #dropDownList {
        border-radius: 7px !important;
    }

    .dropdown-toggle::after {
        vertical-align: top !important;
    }

    input::placeholder {
        font-size: 0.85rem;
    }

    .header-button {
        transition: box-shadow 0.3s !important;
    }

    .header-button:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    }

    .dropdown-menu.w-100.show {
        transform: translate3d(0px, 39.2px, 0px) !important;
    }

    /*
new styles
*/
    .thead {
        height: 50px;
        vertical-align: middle;

    }

    .thead tr th {
        background-color: #40798c !important;
        color: white;
        font-size: initial;
    }

    .parent-sections {
        height: 70vh;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-top: 1.5%;
        margin-bottom: 3%;
    }

    .section-one {
        width: 100%;
        height: 100%;
    }

    .div-table {
        /* border: 1px solid #d0caca; */
        border-radius: 0px;
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    .form-control,
    .form-select,
    .dropdown-toggle-style {
        background-color: #eff3f4 !important;
        border: none !important;
        height: 40px;
    }

    label {
        color: #40798c;
        font-size: 17px;
    }

    .previous {
        margin-bottom: 5px;
    }

    /*
end new styles
*/
    /*
            pagination styles
                    */
    #searchInput {
        height: 2.8rem !important;
        width: 100% !important;
        /* Increase the height for a larger input */
        padding-left: 2.5rem !important;
        /* Increase padding for better spacing */
        border: none !important;
        /* Slightly darker border */
        border-radius: 0.5rem;
        /* Rounded corners */
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
        /* Subtle shadow */
        transition: box-shadow 0.3s ease, border-color 0.3s ease;
        /* Smooth transition */
        font-size: 1.2rem;
        /* Slightly larger text size */
        background-position: left 0.5rem center;
        /* Icon positioning */
    }

    /* Focus styles */
    #searchInput:focus {
        outline: none;
        /* Remove default outline */
        box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
        /* Larger shadow on focus */
        border-color: rgba(0, 0, 0, 0.3);
        /* Slightly darker border on focus */
    }

    /* Placeholder styling */
    #searchInput::placeholder {
        font-family: inherit;
        /* Use inherited font style */
        color: #01A44F;
        /* Green placeholder text */
        font-size: 1.2rem;
        /* Match placeholder size with input text */
    }

    .main-buttons-container button {
        height: 2.9rem;
        font-size: 15px;
    }

    .main-buttons-container button:hover {
        background-color: #018F3E !important;
    }

    /* pagination  */
    ul.pagination {
        gap: 0;
    }

    ul.pagination li button,
    ul.pagination li span {
        padding: 0.7rem;
        padding-top: 0.4rem;
        padding-bottom: 0.4rem;
    }

    ul.pagination li button:hover {
        background-color: rgb(196, 183, 183) !important;
    }

    ul.pagination>li>button,
    ul.pagination>li>span {
        color: black !important;
        font-weight: 600 !important;
        background-color: white;
    }

    .page-item span,
    .page-item button {
        border-radius: 0.7rem !important;
    }

    .page-item.active span,
    .page-item.active button {
        border-radius: 0.5rem !important;
    }

    .page-item.active>span {
        background-color: #00a34e !important;
        color: white !important;
    }

    div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
        font-size: 0.9rem;
    }

    div.tab-pane label {
        /* font-weight: 600 !important; */
    }

    div.tab-pane hr {
        display: none;
    }

    .page-link[aria-label="« Previous"],
    .page-link[aria-label="Next »"] {
        padding: 0.8rem !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    .table tr th {
        font-size: small !important;
        font-weight: 600;
    }

    table td {
        font-size: small !important;
        border-bottom: none;
    }

    /*
        end pagination styles
        */
</style>
{{-- </style> --}}
@endsection


@section('content')


<div class="container-fluid">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">

    @livewire('reports.disconnected')

</div>
@endsection

<script type="module">
    window.addEventListener('close-modal', event => {
        document.getElementById('closeModal').click()
    });
</script>
<script>
    window.addEventListener('selectAllLangs', () => {
        document.querySelectorAll('#dropdownMenu2323sd input[type="checkbox"]').forEach((checkbox) => {
            checkbox.checked = true;
        });
    });

    window.addEventListener('checkOrUncheckLangs', (event) => {
        // Get the state from the event
        const shouldCheck = event.detail.shouldCheck;

        // Select all checkbox elements within the dropdown menu
        document.querySelectorAll('#dropdownMenu2323sd input[type="checkbox"]').forEach((checkbox) => {

            checkbox.checked = event.detail[0].shouldCheck;
        });
    });

    window.addEventListener('closeModal', () => {
        document.querySelector('#closeAddComment').click();
        document.querySelector('#closeViewComments').click();
    });

    window.addEventListener('closeCustomDateModal', () => {
        document.querySelector('#closeCustomDate').click();
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Call the function when the page loads

        document.getElementById('evaluationSelect').value;

        // Add event listener to the select element

    });
    document.getElementById('evaluationSelect').addEventListener('change', function() {
        // Call the function when the select element's value changes
        updateEvaluationLink();
    });

    function updateEvaluationLink() {
        var selectedValue = document.getElementById('evaluationSelect').value;

        if (selectedValue == 0) {
            document.getElementById('evaluationLink').style.display = 'none';
            document.getElementById('btnDisabled').style.display = '';
        } else {
            document.getElementById('evaluationLink').style.display = '';
            document.getElementById('btnDisabled').style.display = 'none';
        }
    }
</script>
