<?php

namespace App\Livewire\Evaluation;

use App\Models\Evaluation;
use App\Models\EvaluationFields;
use App\Models\EvaluationGroup;
use App\Models\EvaluationSubmission;
use App\Models\EvaluationSubmissionAnswer;
use App\Models\Interaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redirect;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class SubmitEvaluationForm extends Component
{
    use WithPagination, LivewireAlert;

    public $order_by = null;
    public $sort_by = null;
    public $limit = null;

    public $flagField = true;
    public $todayDate;
    public $userid;
    public $evaluation_id;
    public $form_name;
    public $name;
    public $user_id;
    public $year;
    public $month;
    public $referenceID;
    public $source;
    public $week;
    public $voiceFileName = null;
    public $commentEvaluation;
    public $arrayFields;
    public $arrayNameFields;
    public $arrayFieldsRequired;
    public $callIdToServe;
    public $arrayAnswers = [];
    public $arrayQuestion = [];
    public $arrayAnswersStatic = [];
    public $totalWeightPoints;
    public $totalPossibleWeightPoints;
    public $totalPossibleWeightPointsPerGroup = [];
    public $totalPossibleScore;
    public $totalScore;
    public $interaction;
    public $pathVoice;
    public $qualityPercentage = 100;
    public $arrayQuestionsWeight = [];
    public $totalPossibleWeightPointsStatic;
    public $chartColor = '#08a54f';
    public $arrayComments = [];
    public $agentFlaq = false;
    public $elapsedTime = '00:00:00';

    public $array = [];

    protected $paginationTheme = 'bootstrap';
    protected $listeners = ['callFunctionFromJS' => 'renderChartAfterLodded', 'updateElapsedTime' => 'updateElapsedTime'];

    public function mount($evaluation_id, $callIdToServe)
    {
        if (null !== request()->query('chat_id')) {
            $this->interaction = request()->query('chat_id');
            $this->getVoiceRecord();
        }
        $this->todayDate = Carbon::now()->toDateString();
        $this->month = Carbon::now()->format('F');
        $this->userid = auth()->id();
        $this->evaluation_id = $evaluation_id;
        $this->callIdToServe = $callIdToServe;
        $this->getEvaluationName();
        $this->getEvaluationFields();
        $this->getWeek();
        $this->getTableData();
        $this->renderChartAfterLodded();

    }
    public function updateElapsedTime($time)
    {

        $this->elapsedTime = $time;
    }
    public function getVoiceRecord()
    {

        $voiceRecord = Interaction::query()->where('id', $this->interaction)->first();
        $this->referenceID = $voiceRecord->id;
        $this->pathVoice = $voiceRecord->call_id;
        $this->voiceFileName = $voiceRecord->file_name ?? '';
        $this->agentFlaq = true;
        $this->user_id = $voiceRecord->user_id;
        $this->year = $voiceRecord->created_at->format('Y');
        $this->source = 'Calls';
        // CheckAudioExists::dispatch($this->referenceID)->onQueue($this->referenceID);
        // return Redirect::route('audio-check', ['call_id' => $this->referenceID ]);
    }
    public function getEvaluationName()
    {

        $evaluation = Evaluation::query()
            ->where('id', $this->evaluation_id)->first();
        $this->form_name = $evaluation->evaluation_name;

    }
    public function getEvaluationFields()
    {

        $fields = EvaluationFields::query()
            ->where('evaluation_id', $this->evaluation_id)
            ->get();

        if ($this->flagField) {
            foreach ($fields as $index => $data) {
                if ($data['type'] != 'Week') {
                    $this->arrayFields[$data['id']] = '';
                    $this->arrayFieldsRequired[$data['id']] = $data['required'];
                    $this->arrayNameFields[$data['id']] = $data['type'];
                }
            }
            $this->flagField = false;
        }

        return $fields;
    }
    public function getWeek()
    {

        $evaluationFieldsArray = $this->getEvaluationFields()->toArray();
        $countTextFields = array_filter($evaluationFieldsArray, function ($field) {
            return $field['type'] == 'Week';
        });
        if (!empty($countTextFields)) {

            $valueWeek = json_decode(reset($countTextFields)['value'])[0];
            if ($valueWeek == '48 Weeks') {
                $this->getWeek48();
            } elseif ($valueWeek == '52 Weeks') {
                $this->getWeek52();
            }

        } else {

            $this->getWeek48();
        }
    }
    public function getWeek48()
    {
        $day = date("d");
        $weekNo = $day / 7;
        $number = intval(round($weekNo));
        if ($number == 0) {$number = 1;}
        $this->week = 'Week ' . $number;
    }

    public function getWeek52()
    {
        $this->week = 'Week ' . intval(date("W"));
    }
    public function getTableData()
    {

        $evaluationData = EvaluationGroup::with(['questions.answers', 'questions.header'])
            ->whereHas('questions.header')
            ->whereHas('questions.answers')
            ->where('evaluation_id', $this->evaluation_id)
            ->where('status', 1)
            ->whereHas('questions', function ($query) {
                $query->where('status', 1); // Filter based on the 'status' column in the 'questions' table
            })
            ->get();

        //Sort Array To BE => (Group -> Header -> Questions -> Answer)
        foreach ($evaluationData as $index => $group) {

            $this->totalPossibleScore += $group['group_weight'];
            $this->totalScore += $group['group_weight'];

            foreach ($group['questions'] as $index1 => $dataQuestion) {
                if ($dataQuestion['status'] == 0) {
                    continue;
                }
                if (!isset($this->array[$group['group_name']][$group['group_weight']][$dataQuestion['header']['header_name']])) {
                    $this->array[$group['group_name']][$group['group_weight']][$dataQuestion['header']['header_name']] = [];
                }

                //Get Possible Weight Points Per All Groups
                $data = $dataQuestion['answers'][0]['mark_and_weight'];
                $data = json_decode($data, true);
                $maxMarkItem = array_reduce($data, function ($carry, $item) {
                    return ($carry['weight'] ?? 0) > $item['weight'] ? $carry : $item;
                });

                $this->totalPossibleWeightPoints += $maxMarkItem['weight'];
                $this->totalWeightPoints += $maxMarkItem['weight'];
                $this->totalPossibleWeightPointsStatic += $maxMarkItem['weight'];

                //Get Possible Weight Points Per Group
                if (!isset($this->totalPossibleWeightPointsPerGroup[$group['group_name']])) {
                    $this->totalPossibleWeightPointsPerGroup[$group['group_name']] = 0;
                }
                $this->totalPossibleWeightPointsPerGroup[$group['group_name']] += $maxMarkItem['weight'];

                //Get arrayAnswers values -> big value per every answer
                $this->arrayQuestionsWeight[$group['group_name']][$dataQuestion['header']['header_name']][] = $maxMarkItem['weight'];

                //Sort Array To BE => (Group -> Header -> Questions -> Answer)
                $this->array[$group['group_name']][$group['group_weight']][$dataQuestion['header']['header_name']][] = $dataQuestion;

                //Get Qusetion Id
                $this->arrayQuestion[] = $dataQuestion['id'];
            }

            ////Delete Header Name Array And Sort array And Get arrayAnswers values -> big value per every answer
            $result = [];

            foreach ($this->arrayQuestionsWeight[$group['group_name']] as $values) {
                if (is_array($values)) {
                    $result = array_merge($result, $values);

                }
                $this->arrayComments[$group['group_name']][] = null;
            }
            $this->arrayAnswersStatic[$group['group_name']] = $result;
            $this->arrayAnswers[$group['group_name']] = $result;

        }

    }
    public function getPointsAndScores()
    {

        $this->totalWeightPoints = 0;
        $this->totalPossibleWeightPoints = $this->totalPossibleWeightPointsStatic;
        $this->totalScore = $this->totalPossibleScore;
        $this->totalScore = 0;

        foreach ($this->arrayAnswers as $groupName => $arr) {

            foreach ($this->array[$groupName] as $groupWeight => $arr2) {

                $groupWeightScore = $groupWeight;
            }

            //Get Total Weight Points After Change Values
            $index = 0;
            $sum = 0;
            $fatalCriticalFlag = false;
            $avarage = 0;
            $sumNA = 0;
            $flagAllAnswersNA = false;
            foreach ($this->arrayAnswers[$groupName] as $answers) {

                if ($answers == 'NA') {

                    $this->totalPossibleWeightPoints -= $this->arrayAnswersStatic[$groupName][$index];
                    $sumNA += $this->arrayAnswersStatic[$groupName][$index];
                    $answers = 0;
                    $sum += $answers;
                    $flagAllAnswersNA = true;
                } elseif ($answers == 'Fatal Per Group') {

                    $sum = 0;
                    $answers = 0;
                    break;

                } elseif ($answers == 'Fatal Critical') {

                    $fatalCriticalFlag = true;
                    break;

                } else {

                    $sum += $answers;
                    $flagAllAnswersNA = false;
                }

                $index += 1;
            }

            if ($fatalCriticalFlag) {
                $this->totalWeightPoints = 0;
                $this->totalScore = 0;
                break;
            } else {
                $this->totalWeightPoints += $sum;
            }

            if ($flagAllAnswersNA) {

                $avarage = $groupWeightScore;

            } else {
                if ($this->totalPossibleWeightPointsPerGroup[$groupName] - $sumNA == 0) {
                    $avarage = 0;
                } else {
                    $avarage = round(($sum / ($this->totalPossibleWeightPointsPerGroup[$groupName] - $sumNA)) * $groupWeightScore);

                }
            }
            $this->totalScore += $avarage;

        }
        $this->qualityPercentage = round(($this->totalScore / $this->totalPossibleScore) * 100);
        $this->getChartColor();
    }
    public function getChartColor()
    {
        if ($this->qualityPercentage >= 80) {
            $this->chartColor = '#08a54f';
        } elseif ($this->qualityPercentage >= 60) {
            $this->chartColor = '#bbcf34';
        } elseif ($this->qualityPercentage >= 40) {
            $this->chartColor = '#e8cf4d';
        } elseif ($this->qualityPercentage >= 20) {
            $this->chartColor = '#cf810b';
        } elseif ($this->qualityPercentage >= 0) {
            $this->chartColor = '#a82020';
        }

        $this->dispatch('chart', ['qualityPercentage' => $this->qualityPercentage, 'chartColor' => $this->chartColor]);

    }
    public function renderChartAfterLodded()
    {

        $this->dispatch('chart', ['qualityPercentage' => $this->qualityPercentage, 'chartColor' => $this->chartColor]);
    }

    public function modelData()
    {

        $model = [
            'evaluation_id' => $this->evaluation_id,
            'created_by' => $this->userid,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'year' => $this->year,
            'source' => $this->source,
            'month' => $this->month,
            'referenceID' => $this->referenceID,
            'week' => $this->week,
            'commentEvaluation' => $this->commentEvaluation,
            'total_weight_points' => $this->totalWeightPoints,
            'total_possible_weighted_points' => $this->totalPossibleWeightPoints,
            'total_score' => $this->totalScore,
            'total_possible_score' => $this->totalPossibleScore,
            'quality_percentage' => $this->qualityPercentage,
            'date' => $this->todayDate,
            'evaluation_duration' => $this->elapsedTime,
        ];
        $index = 0;
        $field = null;
        if (!empty($this->arrayFields)) {
            foreach ($this->arrayFields as $fields => $value) {

                if ($index == 0) {
                    $field = 'extra_field_one';
                } elseif ($index == 1) {
                    $field = 'extra_field_tow';
                } elseif ($index == 2) {
                    $field = 'extra_field_three';
                }

                $model[$field] = json_encode([
                    'label' => $this->arrayNameFields[$fields],
                    'value' => $value,
                ]);

                $index++;
            }
        }
        return $model;
    }
    public function rules()
    {
        $rules = [
            'user_id' => ['required'],
            'year' => ['required'],
            'month' => ['required', 'string'],
            'referenceID' => ['required'],
            'week' => ['required', 'string'],
            'source' => ['required', 'string'],
            'todayDate' => ['required'],
        ];
        if (!empty($this->arrayFields)) {
            foreach ($this->arrayFields as $fields => $rr) {

                if ($this->arrayFieldsRequired[$fields] == 1) {

                    $rules['arrayFields.' . $fields] = 'required';
                }
            }
        }

        return $rules;
    }

    public function messages()
    {
        $messages = [];
        $messages['user_id.required'] = 'The field is required.';
        $messages['year.required'] = 'The field is required.';
        $messages['month.required'] = 'The field is required.';
        $messages['referenceID.required'] = 'The field is required.';
        $messages['week.required'] = 'The field is required.';
        $messages['todayDate.required'] = 'The field is required.';
        $messages['source.required'] = 'The field is required222.';
        if (!empty($this->arrayFields)) {
            foreach ($this->arrayFields as $fields => $rr) {

                if ($this->arrayFieldsRequired[$fields] == 1) {

                    $messages['arrayFields.' . $fields . '.required'] = 'The field is required.';
                }
            }
        }

        return $messages;
    }

    public function store()
    {

        $this->renderChartAfterLodded();

        if ($this->messages()) {

            $this->dispatch('scroll-up');
        }
        $this->validate();

        try {
            $agent = User::query()->where('id', $this->user_id)->first();
            $this->name = $agent['full_name'];
            $Evaluation_submission = EvaluationSubmission::create($this->modelData());
            $indexArray = 0;
            foreach ($this->arrayAnswers as $arr => $answer) {

                $indexArray2 = 0;
                foreach ($answer as $ans) {

                    if (isset($this->arrayComments[$arr][$indexArray2])) {
                        EvaluationSubmissionAnswer::create([
                            'evaluation_submission_id' => $Evaluation_submission->id,
                            'evaluation_question_id' => $this->arrayQuestion[$indexArray],
                            'comment' => $this->arrayComments[$arr][$indexArray2],
                            'mark' => $ans,
                        ]);
                    } else {
                        EvaluationSubmissionAnswer::create([
                            'evaluation_submission_id' => $Evaluation_submission->id,
                            'evaluation_question_id' => $this->arrayQuestion[$indexArray],
                            'mark' => $ans,
                        ]);
                    }

                    $indexArray++;
                    $indexArray2++;
                }

            }
            Interaction::find($this->referenceID)->qaFlags()->detach();

            $this->alert('success', 'Successfully Updated!', [
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            return redirect('recordings');
            $this->modelFormReset();

        } catch (\Exception $e) {
dd($e->getMessage());
            $this->alert('error', $e, [
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();

        }

    }

    public function getAgents()
    {
        try
        {
            return User::query()
            // ->with('agentRoles')
            // ->whereHas('roles', function ($q) {
            //     $q->where('allowed_route', 'agent');
            // })
                ->when($this->agentFlaq, function ($query) {
                    return $query->where('id', $this->user_id);
                })
                ->orderBy($this->order_by != null ? $this->order_by : 'id', $this->sort_by != null ? $this->sort_by : 'asc')
                ->get();
        } catch (\Exception $e) {
            $this->alert('error', 'Something was wrong !', [
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }
    public function modelFormReset()
    {

        $this->flagField = true;
        $this->name = '';
        $this->user_id = '';
        $this->year = '';
        $this->referenceID = '';
        $this->week = '';
        $this->source = '';
        $this->commentEvaluation = '';
        $this->arrayFields = [];
        $this->arrayFieldsRequired = [];
        $this->arrayAnswers = [];
        $this->arrayQuestion = [];
        $this->arrayAnswersStatic = [];
        $this->totalWeightPoints = 0;
        $this->totalPossibleWeightPoints = 0;
        $this->totalPossibleWeightPointsPerGroup = [];
        $this->totalPossibleScore = 0;
        $this->totalScore = 0;
        $this->qualityPercentage = 100;
        $this->arrayQuestionsWeight = [];
        $this->totalPossibleWeightPointsStatic = 0;
        $this->chartColor = '#08a54f';
        $this->arrayComments = [];
        $this->array = [];

        $this->mount($this->evaluation_id);

        $this->renderChartAfterLodded();

    }
    public function render()
    {

        return view('livewire.evaluation.submit-evaluation-form', ['Evaluation_fields' => $this->getEvaluationFields(), 'Agent' => $this->getAgents()]);
    }
}
