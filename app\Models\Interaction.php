<?php

namespace App\Models;

use App\Models\User;
use App\Models\QaFlag;
use App\Models\Comment;
use App\Models\Organization;
use App\Models\UserGroup;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Interaction extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'arrival_time' => 'datetime'
    ];


    public function qaFlags()
    {
        return $this->belongsToMany(QaFlag::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }
    public function agent()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function callEvaluation()
    {
        return $this->hasOne(EvaluationSubmission::class, 'referenceID'); //agent has role 2
    }

    public function listeners()
    {
        return $this->belongsToMany(User::class, 'interaction_listener', 'interaction_id', 'user_id')->withPivot('created_at');
    }

    public function scopeSearch($q, $value)
    {
        $q->where('call_type', 'like', "%$value%")
            ->orWhere('id', $value)
            ->orWhere('Genesys_CallUUID', $value)
            ->orWhere('agent_extension', 'like', "%$value%")
            ->orWhere('caller_id', 'like', "%$value%")
            ->orWhere('called_id', 'like', "%$value%")
            ->orWhere('call_ender', 'like', "%$value%")
            ->orWhere('language', 'like', "%$value%")
            ->orWhereHas('organization', function ($q1) use ($value) {
                $q1->where('name', 'like', "%$value%");
            })
            ->orWhereHas('agent', function ($q1) use ($value) {
                $q1->where('full_name', 'like', "%$value%")
                    ->orWhere('agent_id', 'like', "%$value%")
                    ->orWhere('username', 'like', "%$value%");
            });
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function userGroup()
    {
        return $this->belongsTo(userGroup::class);
    }
    public function interactionDetails()
    {
        return $this->hasOne(CallDetails::class, 'call_id', 'call_id');
    }
}
