<style>
    #video-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: 1px solid #ddd;
        border-radius: 50%;
        background-color: #03a34d;
        cursor: pointer;
        transition: background-color 0.3s ease;
        text-align: center;
    }

    #video-btn:hover {
        background-color: #eaeaea;
    }

    #video-btn .fa-video {
        font-size: 20px;
        color: #555;
    }
</style>

<!-- Modal -->
<!-- Button trigger modal -->
{{-- <button class="video-btn play-pause-button" id="video-btn" data-bs-toggle="modal" data-bs-src="https://www.youtube.com/embed/JJUo8Fe3_JY" data-bs-target="#videoModal">📹</button> --}}
<i class="fa-solid fa-video video-btn play-pause-button" id="video-btn" data-bs-toggle="modal" data-bs-src="https://www.youtube.com/embed/JJUo8Fe3_JY" data-bs-target="#videoModal">
</i>

<!-- Modal -->
<div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
    <div class="modal-dialog video-modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalLabel">Video Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Video content will be dynamically added here -->
                <video id="record-video" class="w-100" controls preload="metadata" controlsList="nodownload" oncontextmenu="return false;">
                    <source src="" type="video/mp4" />
                </video>
            </div>
            <div class="modal-footer">
                {{--                 <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
 --}}
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const videoModal = document.getElementById('videoModal');

        if (videoModal) {
            videoModal.addEventListener('hidden.bs.modal', function() {
                console.log('Modal is now closed.');
                document.getElementById('record-video').pause();
                // Add your custom logic here
            });
        }
    });
</script>

<style>
    .video-modal-dialog {
        max-width: 800px;
        margin: 30px auto;
        max-width: 50%;
    }



    .video-modal-body {
        position: relative;
        padding: 0px;
    }

    .video-close {
        position: absolute;
        right: -30px;
        top: 0;
        z-index: 999;
        font-size: 2rem;
        font-weight: normal;
        color: #fff;
        opacity: 1;
    }

    .video-frame {
        width: 100%;
        height: 100vh;
    }
</style>
