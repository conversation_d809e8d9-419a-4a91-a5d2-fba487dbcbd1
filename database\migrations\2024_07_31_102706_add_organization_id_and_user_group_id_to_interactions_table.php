<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('interactions', function (Blueprint $table) {
            $table->unsignedBigInteger('organization_id')->nullable()->after('id');
            $table->unsignedBigInteger('user_group_id')->nullable()->after('organization_id');

            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            $table->foreign('user_group_id')->references('id')->on('user_groups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('interactions', function (Blueprint $table) {
            $table->dropForeign(['organization_id']);
            $table->dropForeign(['user_group_id']);

            $table->dropColumn('organization_id');
            $table->dropColumn('user_group_id');
        });
    }
};
