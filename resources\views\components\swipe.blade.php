<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

<script type="module">
    function initializeSwiper() {
        const swiperContainer = document.querySelector('.swiper');
        const slides = swiperContainer.querySelectorAll('.swiper-slide');
        const paginationContainer = document.querySelector('.swiper-pagination');
        const visibleSlides = 3; // Adjust based on the desired number of visible slides
        const slideSpacing = 90; // Space between slides in pixels

        // Set up slide styles dynamically
        const slideWidth = (swiperContainer.offsetWidth - (visibleSlides - 1) * slideSpacing) / visibleSlides;
        slides.forEach(slide => {
            slide.style.width = `${slideWidth}px`;
            slide.style.marginRight = `${slideSpacing}px`;
        });

        // Pagination setup
        const totalSlides = slides.length;
        const totalPages = Math.ceil(totalSlides / visibleSlides);
        let currentIndex = 0;

        function createPagination() {
            for (let i = 0; i < totalPages; i++) {
                const dot = document.createElement('span');
                dot.classList.add('pagination-dot');
                if (i === 0) dot.classList.add('active');
                dot.dataset.index = i;
                dot.addEventListener('click', () => {
                    currentIndex = parseInt(dot.dataset.index, 12);
                    updateSlides();
                });
                paginationContainer.appendChild(dot);
            }
        }

        function updateSlides() {
            const offset = currentIndex * (slideWidth + slideSpacing) * visibleSlides;

            swiperContainer.querySelector('.swiper-wrapper').style.transform = `translateX(-${offset}px)`;

            // Update pagination
            const dots = paginationContainer.querySelectorAll('.pagination-dot');
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentIndex);
            });
        }

        createPagination();
    }

    document.addEventListener('DOMContentLoaded', initializeSwiper);
</script>

{{-- <script type="module">
    document.addEventListener('DOMContentLoaded', () => {
        const swiperContainer = document.querySelector('.swiper');
        const swiperWrapper = swiperContainer.querySelector('.swiper-wrapper');
        const slides = swiperWrapper.querySelectorAll('.swiper-slide');
        const paginationContainer = document.querySelector('.swiper-pagination');
        const visibleSlides = 4; // Number of visible slides
        const slideSpacing = 16; // Space between slides in pixels

        // Set up slide styles dynamically
        const calculateSlideWidth = () => {
            const slideWidth = (swiperContainer.offsetWidth - (visibleSlides - 1) * slideSpacing) / visibleSlides;
            slides.forEach(slide => {
                slide.style.width = `${slideWidth}px`;
                slide.style.marginRight = `${slideSpacing}px`;
            });
            return slideWidth;
        };

        const slideWidth = calculateSlideWidth();

        // Pagination setup
        const totalSlides = slides.length;
        const totalPages = Math.ceil(totalSlides / visibleSlides);
        let currentIndex = 0;

        const createPagination = () => {
            paginationContainer.innerHTML = ''; // Clear existing dots
            for (let i = 0; i < totalPages; i++) {
                const dot = document.createElement('span');
                dot.classList.add('pagination-dot');
                if (i === 0) dot.classList.add('active');
                dot.dataset.index = i;
                dot.addEventListener('click', () => {
                    currentIndex = parseInt(dot.dataset.index, 10);
                    updateSlides();
                });
                paginationContainer.appendChild(dot);
            }
        };

        const updateSlides = () => {
            const offset = currentIndex * (slideWidth + slideSpacing) * visibleSlides;
            swiperWrapper.style.transform = `translateX(-${offset}px)`;

            // Update pagination dots
            const dots = paginationContainer.querySelectorAll('.pagination-dot');
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentIndex);
            });
        };

        createPagination();
        window.addEventListener('resize', () => {
            calculateSlideWidth();
            updateSlides();
        });
    });
</script> --}}

<div class="swiper" wire:ignore.self>
    <div class="swiper-wrapper" wire:ignore.self>

        {{ $slot }}

    </div>
    <div class="swiper-pagination" wire:ignore></div>
</div>




{{-- new --------------  --}}
 <style>
    .swiper-pagination {
        width: 100%;
    }

    .swiper-wrapper {
        zoom: 0.9;
    }

    .swiper {
        padding: 3%;
        /* padding-left: 0 !important; */
        /* padding-left: 1vw !important; */
        padding-left: 1.5vw !important;
        padding-right: 1.5vw !important;
    }

    .swiper {
        overflow: hidden;
        position: relative;
    }

    .swiper-wrapper {
        display: flex;
        transition: transform 0.3s ease;
        max-width: 80%;
        /* padding-left: 1rem; */
    }

    .swiper-slide {
        margin-right: 10%;
        width: 33.3% !important;
    }

    .swiper-pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .pagination-dot {
        width: 10px;
        height: 10px;
        background-color: #ddd;
        border-radius: 50%;
        margin: 0 5px;
        cursor: pointer;
    }

    .pagination-dot.active {
        background-color: #333;
    }
</style>




{{-- new with media queries ---------- --}}
{{-- <style>
    .swiper-pagination {
        width: 100%;
    }

    .swiper-wrapper {
        zoom: 0.9;
    }

    .swiper {
        padding: 3%;
        padding-left: 1.5vw !important;
        padding-right: 1.5vw !important;
        overflow: hidden;
        position: relative;
    }

    .swiper-wrapper {
        display: flex;
        transition: transform 0.3s ease;
        max-width: 49rem;
    }

    .swiper-slide {
        margin-right: 4%;
        width: 25%;
        /* Default: 4 cards per view */
    }

    .swiper-pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .pagination-dot {
        width: 10px;
        height: 10px;
        background-color: #ddd;
        border-radius: 50%;
        margin: 0 5px;
        cursor: pointer;
    }

    .pagination-dot.active {
        background-color: #333;
    }

    /* Media Queries */

    /* Large Screens (Desktops) */
    @media (min-width: 1024px) {
        .swiper-slide {
            width: 25%;
            /* 4 cards per view */
            margin-right: 4%;
        }
    }

    /* Medium Screens (Tablets) */
    @media (min-width: 768px) and (max-width: 1023px) {
        .swiper-slide {
            width: 33.333%;
            /* 3 cards per view */
            margin-right: 3%;
        }

        .swiper {
            padding-left: 2vw !important;
            padding-right: 2vw !important;
        }
    }

    /* Small Screens (Mobile) */
    @media (max-width: 767px) {
        .swiper-slide {
            width: 50%;
            /* 2 cards per view */
            margin-right: 2%;
        }

        .swiper {
            padding-left: 3vw !important;
            padding-right: 3vw !important;
        }
    }

    /* Extra Small Screens (Phones in Portrait Mode) */
    @media (max-width: 480px) {
        .swiper-slide {
            width: 100%;
            /* 1 card per view */
            margin-right: 0;
        }

        .swiper-wrapper {
            max-width: 100%;
            /* Full width for small screens */
        }
    }
</style> --}}







{{-- old ---------------  --}}
{{-- <style>

.swiper {
    padding: 3%;
    overflow: hidden;
    position: relative;
    width: 100%; /* Ensure swiper takes the full width */
}

.swiper-wrapper {
    display: flex;
    transition: transform 0.3s ease; /* Smooth transitions for slides */
    zoom: 0.9; /* Optional scaling for the entire swiper */
}

.swiper-slide {
    flex-shrink: 0;
    margin-right: 16px; /* Space between slides */
    border-radius: 8px; /* Optional rounded corners */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); /* Optional shadow for better visuals */
}

.swiper-pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    width: 100%; /* Ensures the pagination takes full width */
}

.pagination-dot {
    width: 10px;
    height: 10px;
    background-color: #ddd;
    border-radius: 50%;
    margin: 0 5px;
    cursor: pointer;
}

.pagination-dot.active {
    background-color: #333; /* Highlight active dot */
}

</style> --}}
