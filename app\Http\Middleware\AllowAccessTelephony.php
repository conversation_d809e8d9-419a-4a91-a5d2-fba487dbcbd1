<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;

class AllowAccessTelephony
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if the authenticated user has the specified permission
        if (!Auth::user()->permissions()->where('permission_id', 1)->exists() || Auth::user()->role == 1) {
            // Return a 403 Forbidden response if the permission does not exist
            abort(403);
        }

        return $next($request);
    }
}
