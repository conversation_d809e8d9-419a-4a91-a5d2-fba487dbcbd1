<div>
    <div class="row">
        <div class="col-12 mb-4 mt-3 ms-5">
            <button class="btn btn-md rounded-1 me-5" style="float:right;background-color: #00a34e; color:white" data-bs-toggle="modal" data-bs-target="#departmentModal"><i class="fa-solid fa-plus"></i><span> Add New Script  </span></button>
            {{--
            <button class="btn btn-md rounded-1 me-2" style="background-color: #00a34e; color:white" wire:click="changePage('variables')"><span> Variables</span></button>
            <button class="btn btn-md rounded-1 me-2" style="background-color: #00a34e; color:white" wire:click="changePage('values')"><span> Values</span></button> --}}
        </div>
    </div>

    <div class="table-responsive mb-2 ms-5 rounded-3">
        <table class="table table-striped table-bordered">
            <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 0; z-index: 4;height: 1rem !important;">
                <tr>
                    <th scope="col" style="width: 20%;font-size: 1.1rem !important;">Organization</th>
                    <th scope="col" style="width: 15%;font-size: 1.1rem !important;">LOB</th>
                    <th scope="col" style="width: 25%;font-size: 1.1rem !important;">Script</th>
                    <th scope="col" style="width: 15%;font-size: 1.0rem !important;">Script Type</th>
                    <th scope="col" style="width: 10%;font-size: 1.0rem !important;">Language</th> {{-- Added Language Column Header --}}
                    {{-- <th scope="col" style="width: 20%;font-size: 1.1rem !important;">From Side</th>--}}
                    {{-- <th scope="col" style="width: 20%;font-size: 1.1rem !important;">Type</th>--}}
                    <th scope="col" class="text-center" style="width: 15%">Action</th>
                </tr>
            </thead>
            <tbody>
                @forelse($transcriptionBadWords as $transcriptionBadWord)
                    <tr>
                        <td style="font-size: 1.1rem !important;color:#424242 !important">{{ \App\Models\Organization::where('id',$transcriptionBadWord->organization_id)->first()->name ?? "-" }}</td>
                        <td style="font-size: 1.1rem !important;color:#424242 !important">{{ \App\Models\UserGroup::where('id',$transcriptionBadWord->user_group_id)->first()->name ?? "-"}}</td>
                        <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->key }}</td>
                        <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->script_type ?? "-" }}</td>
                        <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->language ?? "-" }}</td> {{-- Added Language Column Data --}}
                        {{-- <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->side_type }}</td>--}}
                        {{-- <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->type }}</td>--}}
                        <td>
                            <div class="btn-group btn-group-sm mx-3" role="group" aria-label="Basic mixed styles example">
                                <i wire:click="showUpdateModal({{ $transcriptionBadWord->id }})" class="fa-solid fa-pen" id="pencel_icon23" style="font-size: 20px;color: #00a34e; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#departmentModal"></i>
                                {{-- <img wire:click="showUpdateModal({{ $transcriptionBadWord->id }})" src="{{ asset('assets/SVG/assets-v2/union-1.svg') }}" alt="Edit" style="width: 1.5rem; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#departmentModal" />--}}
                            </div>
                            <i class="fa fa-trash fa-xl" aria-hidden="true" style="cursor: pointer; color:tomato" wire:click="showDeleteAlert('{{ $transcriptionBadWord->id }}')" title="Delete User"></i>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="text-center" style="font-size: 1.1rem !important;color:#424242 !important">No scripts found.</td> {{-- Adjusted colspan --}}
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <div wire:ignore.self class="modal fade" id="departmentModal" tabindex="-1" role="dialog" aria-labelledby="departmentModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white; border-radius: 12px;">
                <div class="modal-header" style="border: none; padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color: #eff3f4 !important;">
                            <i class="fa-solid fa-lock" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h5 class="modal-title fw-bold" id="departmentModalLabel" style="font-size: 24px; color: #40798c;">
                            @if ($modalIdShow != 'on')
                                {{ $modalId ? 'Update Script' : 'New Script' }}
                            @else
                                {{ 'View Script' }}
                            @endif
                        </h5>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id="closeModal" data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>

                <div class="modal-body" style="padding: 20px; border: none;">
                    <form>
                        <div class="mb-3">
                            <label for="status" style="color: #40798c;">Script:</label>
                            @if ($modalIdShow != 'on')
                                <textarea class="form-control" wire:model.defer="key"></textarea>
                                @error('key')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <textarea class="form-control" wire:model.defer="key" disabled></textarea>
                                @error('key')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="status" style="color: #40798c;">Organization:</label>
                            @if ($modalIdShow != 'on')
                                <select class="form-control" wire:model="organization_id" wire:change="storeGet">
                                    <option class="fw-bold" value="">--- Select Organization ---</option>
                                    @foreach($organizations as $organization)
                                        <option class="fw-bold" value="{{$organization->id}}">{{$organization->name}}</option>
                                    @endforeach
                                </select>
                                @error('organization_id')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select class="form-control" wire:model="organization_id" disabled>
                                    <option class="fw-bold" value="">--- Select Organization ---</option>
                                    @foreach($organizations as $organization)
                                        <option class="fw-bold" value="{{$organization->id}}">{{$organization->name}}</option>
                                    @endforeach
                                </select>
                                @error('organization_id')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="status" style="color: #40798c;">LOB:</label>
                            @if ($modalIdShow != 'on')
                                <select class="form-control" wire:model.defer="user_group_id">
                                    <option class="fw-bold" value="">--- Select LOB ---</option>
                                    @foreach($getUserGroup as $userGroup)
                                        <option class="fw-bold" value="{{$userGroup->id}}">{{$userGroup->name}}</option>
                                    @endforeach
                                </select>
                                @error('user_group_id')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select class="form-control" wire:model.defer="user_group_id" disabled>
                                     <option class="fw-bold" value="">--- Select LOB ---</option>
                                    @foreach($getUserGroup as $userGroup)
                                        <option class="fw-bold" value="{{$userGroup->id}}">{{$userGroup->name}}</option>
                                    @endforeach
                                </select>
                                @error('user_group_id')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="script_type" style="color: #40798c;">Script Type:</label>
                            @if ($modalIdShow != 'on')
                                <select class="form-control" wire:model.defer="script_type">
                                    <option class="fw-bold" value="">--- Select Script Type ---</option>
                                    <option class="fw-bold" value="Greeting">Greeting</option>
                                    <option class="fw-bold" value="Closure">Closure</option>
                                </select>
                                @error('script_type')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select class="form-control" wire:model.defer="script_type" disabled>
                                    <option class="fw-bold" value="">--- Select Script Type ---</option>
                                    <option class="fw-bold" value="Greeting">Greeting</option>
                                    <option class="fw-bold" value="Closure">Closure</option>
                                </select>
                                @error('script_type')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>

                        {{-- Added Language Dropdown --}}
                        <div class="mb-3">
                            <label for="language" style="color: #40798c;">Language:</label>
                            @if ($modalIdShow != 'on')
                                <select class="form-control" wire:model.defer="language">
                                    <option class="fw-bold" value="">--- Select Language ---</option>
                                    <option class="fw-bold" value="English">English</option>
                                    <option class="fw-bold" value="Arabic">Arabic</option>
                                    <option class="fw-bold" value="Others">Others</option>
                                </select>
                                @error('language')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select class="form-control" wire:model.defer="language" disabled>
                                    <option class="fw-bold" value="">--- Select Language ---</option>
                                    <option class="fw-bold" value="English">English</option>
                                    <option class="fw-bold" value="Arabic">Arabic</option>
                                    <option class="fw-bold" value="Others">Others</option>
                                </select>
                                @error('language')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>
                        {{-- End of Language Dropdown --}}

                        {{-- Other commented out fields can be added similarly if needed --}}
                        {{-- <div class="mb-3">--}}
                        {{-- <label for="status" style="color: #40798c;">From Side:</label>--}}
                        {{-- @if ($modalIdShow != 'on')--}}
                        {{-- <select class="form-control" wire:model.defer="side_type">--}}
                        {{-- <option class="fw-bold" value="">---</option>--}}
                        {{-- <option class="fw-bold" value="Agent">Agent</option>--}}
                        {{-- <option class="fw-bold" value="Customer">Customer</option>--}}
                        {{-- </select>--}}
                        {{-- @error('side_type')--}}
                        {{-- <small class="text-danger"> {{ $message }} </small>--}}
                        {{-- @enderror--}}
                        {{-- @else--}}
                        {{-- <select class="form-control" wire:model.defer="side_type" disabled>--}}
                        {{-- <option class="fw-bold" value="">---</option>--}}
                        {{-- <option class="fw-bold" value="Agent">Agent</option>--}}
                        {{-- <option class="fw-bold" value="Customer">Customer</option>--}}
                        {{-- </select>--}}
                        {{-- @error('side_type')--}}
                        {{-- <small class="text-danger"> {{ $message }} </small>--}}
                        {{-- @enderror--}}
                        {{-- @endif--}}
                        {{-- </div>--}}

                        {{-- <div class="mb-3">--}}
                        {{-- <label for="status" style="color: #40798c;">Type:</label>--}}
                        {{-- @if ($modalIdShow != 'on')--}}
                        {{-- <select class="form-control" wire:model.defer="type">--}}
                        {{-- <option class="fw-bold" value="">---</option>--}}
                        {{-- <option class="fw-bold" value="Out of Script">Out of Script</option>--}}
                        {{-- <option class="fw-bold" value="In Script">In Script</option>--}}
                        {{-- </select>--}}
                        {{-- @error('type')--}}
                        {{-- <small class="text-danger"> {{ $message }} </small>--}}
                        {{-- @enderror--}}
                        {{-- @else--}}
                        {{-- <select class="form-control" wire:model.defer="type" disabled>--}}
                        {{-- <option class="fw-bold" value="">---</option>--}}
                        {{-- <option class="fw-bold" value="Out of Script">Out of Script</option>--}}
                        {{-- <option class="fw-bold" value="In Script">In Script</option>--}}
                        {{-- </select>--}}
                        {{-- @error('type')--}}
                        {{-- <small class="text-danger"> {{ $message }} </small>--}}
                        {{-- @enderror--}}
                        {{-- @endif--}}
                        {{-- </div>--}}
                    </form>
                </div>

                <div class="modal-footer" style="border: none; padding: 20px;">
                    <button type="button" class="btn btn-outline-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" data-bs-dismiss="modal" wire:click="closeModal">
                        Close
                    </button>
                    @if ($modalIdShow != 'on')
                        <button class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="{{ $modalId ? 'update' : 'store' }}">
                            {{ $modalId ? 'Update' : 'Save' }}
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>