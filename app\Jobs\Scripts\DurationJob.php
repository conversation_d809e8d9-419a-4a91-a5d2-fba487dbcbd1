<?php

namespace App\Jobs\Scripts;

use App\Models\CallDetails;
use App\Models\CallsTranscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class DurationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    /**
     * Create a new job instance.
     */
    public $path;
    public $path2;
    public $call_id;
    public $duration;
    public $timeout = 7200;

    public function __construct($call_id, $path, $path2, $duration)
    {
        $this->path = $path;
        $this->path2 = $path2;
        $this->call_id = $call_id;
        $this->duration = $duration;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //

        $exePath = storage_path('app/scripts/duration_calculator.sh');
        /* Log::channel('duration')->info($textData->content); */
        $process = new Process([$exePath, $this->path]);
        Log::channel('duration')->info("Running script with path: {$this->path} {$this->path2}");

        $process->setTimeout(360);
        $process->run();
        if (!$process->isSuccessful()) {
            Log::channel('duration')->info($process->getErrorOutput());
        }

        Log::channel('duration')->info($process->getOutput());
        $data = json_decode($process->getOutput(), true);
        if ($data === null) {
            echo "Failed to decode JSON: " . json_last_error_msg();
        } else {
            Log::channel('duration')->info("This Duration =$this->duration");
            Log::channel('duration')->info("This total_talk_duration_combined = " . $data['total_talk_duration_combined'] . "");
            Log::channel('duration')->info("This silence_duration =" . $data['silence_duration'] . "");

            $otherDuration = $this->duration - ($data['total_talk_duration_combined'] + $data['silence_duration']);

            /******* Overlap  duration logic *****/
            $calculateOverlap = $this->calculateOverlap($this->call_id);


            /******* Overlap duration logic *****/
            CallDetails::create([
                'call_id' => $this->call_id,
                'left_channel_duration' => $data["talk_durations"]["left_channel"],
                'right_channel_duration' => $data["talk_durations"]["right_channel"],
                'silence_duration' => $data["silence_duration"],
//                'overtalk_duration' => $data["overlap_duration"],
                'overtalk_duration' => $calculateOverlap,
                'engage_duration' => $this->duration,
                'other_duration' => $otherDuration,
            ]);
        }
    }


    public function calculateOverlap($call_id)
    {
        // Fetch all transcription data for the given call_id, ordered by duration_from
        $callData = CallsTranscription::orderBy('duration_from', 'asc')
            ->where('call_id', $call_id)
            ->get();

        $overlapDuration = 0; // Initialize the overlap duration
        $lastAgentEndTime = null; // Track the end time of the last agent segment
        $ddd =[];
        // Loop through all segments and compare them
        foreach ($callData as $segment) {
            // If it's an agent segment (left)
            if ($segment->source == 'left') {
                // Echo details for the left (Agent) segment
//            echo "left " . $segment->duration_from . " to " . $segment->duration_to . "\n";

                // If this is the first agent segment, update lastAgentEndTime
                $lastAgentEndTime = $segment->duration_to;
            }
            // If it's a customer segment (right), check if there's an overlap with the previous agent (left)
            elseif ($segment->source == 'right' && $lastAgentEndTime !== null) {
                // Echo details for the right (Customer) segment
//            echo "right " . $segment->duration_from . " to " . $segment->duration_to . "\n";

                // Calculate the overlap between the agent's segment and the customer segment
                if ($lastAgentEndTime > $segment->duration_from) {
                    // Calculate the start and end of the overlap
                    $overlapStart = $segment->duration_from;
                    $overlapEnd = min($segment->duration_to, $lastAgentEndTime);

                    // If the overlapStart is less than overlapEnd, there is an actual overlap
                    if ($overlapStart < $overlapEnd) {
                        $overlapDuration += ($overlapEnd - $overlapStart); // Add the overlap duration
                        $ddd[]= "Overlap is " . ($overlapEnd - $overlapStart) . " from " . $overlapStart . " to " . $overlapEnd . "\n";
                    }
                }
                // Update the last agent's end time to the current customer's end time
                $lastAgentEndTime = max($lastAgentEndTime, $segment->duration_to);
            }
        }

        // Return the total overlap duration as JSON
        return round($overlapDuration, 2);
    }

}
