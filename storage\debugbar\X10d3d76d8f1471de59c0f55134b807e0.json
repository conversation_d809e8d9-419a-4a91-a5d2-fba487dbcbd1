{"__meta": {"id": "X10d3d76d8f1471de59c0f55134b807e0", "datetime": "2025-07-22 15:51:13", "utime": **********.967175, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[15:51:13] LOG.emergency: Unable to create configured logger. Using emergency logger. {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "emergency", "time": **********.87048, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:13] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php on line 720", "message_html": null, "is_string": false, "label": "warning", "time": **********.871319, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.061877, "end": **********.967231, "duration": 0.9053540229797363, "duration_str": "905ms", "measures": [{"label": "Booting", "start": **********.061877, "relative_start": 0, "end": **********.615326, "relative_end": **********.615326, "duration": 0.5534489154815674, "duration_str": "553ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.615348, "relative_start": 0.5534710884094238, "end": **********.967234, "relative_end": 2.86102294921875e-06, "duration": 0.3518857955932617, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29169696, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.analytics-new", "param_count": null, "params": [], "start": **********.951092, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/analytics-new.blade.phplivewire.analytics-new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Fanalytics-new.blade.php&line=1", "ajax": false, "filename": "analytics-new.blade.php", "line": "?"}}, {"name": "components.swipe", "param_count": null, "params": [], "start": **********.958781, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/components/swipe.blade.phpcomponents.swipe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Fcomponents%2Fswipe.blade.php&line=1", "ajax": false, "filename": "swipe.blade.php", "line": "?"}}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Livewire\\AnalyticsNew@getData", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=388\" onclick=\"\">app/Livewire/AnalyticsNew.php:388-1131</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.12624000000000002, "accumulated_duration_str": "126ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.675465, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "ilog", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 36 limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.680275, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ilog", "start_percent": 0, "width_percent": 2.891}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 397}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.733241, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:397", "source": "app/Livewire/AnalyticsNew.php:397", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=397", "ajax": false, "filename": "AnalyticsNew.php", "line": "397"}, "connection": "ilog", "start_percent": 2.891, "width_percent": 1.402}, {"sql": "SELECT AVG(quality_percentage) AS avg_quality, COUNT(a.id) AS total_qa_submissions\nFROM evaluation_submissions a\nINNER JOIN evaluations b ON a.evaluation_id = b.id\nWHERE\na.created_at BETWEEN '2025-07-21 15:51:08' AND '2025-07-22 15:51:08'\nAND\nb.organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 631}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.746409, "duration": 0.03264, "duration_str": "32.64ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:631", "source": "app/Livewire/AnalyticsNew.php:631", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=631", "ajax": false, "filename": "AnalyticsNew.php", "line": "631"}, "connection": "ilog", "start_percent": 4.293, "width_percent": 25.856}, {"sql": "SELECT COUNT(call_id) AS totalCalls,call_type FROM interactions   WHERE arrival_time BETWEEN '2025-07-21 15:51:08' AND '2025-07-22 15:51:08'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50) GROUP BY call_type", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.790002, "duration": 0.0074800000000000005, "duration_str": "7.48ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:641", "source": "app/Livewire/AnalyticsNew.php:641", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=641", "ajax": false, "filename": "AnalyticsNew.php", "line": "641"}, "connection": "ilog", "start_percent": 30.149, "width_percent": 5.925}, {"sql": "SELECT\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,\nAVG(ring) AS avg_ring,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,\nSUM(call_duration) AS total_ring,\nCOUNT(call_id) AS total_calls\nFROM interactions\nWHERE arrival_time BETWEEN '2025-07-21 15:51:08' AND '2025-07-22 15:51:08'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 648}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8062758, "duration": 0.01025, "duration_str": "10.25ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:648", "source": "app/Livewire/AnalyticsNew.php:648", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=648", "ajax": false, "filename": "AnalyticsNew.php", "line": "648"}, "connection": "ilog", "start_percent": 36.074, "width_percent": 8.119}, {"sql": "SELECT\nCOUNT(CASE WHEN call_duration < '00:02:00' THEN 1 END) AS less_than_2_minutes,\nCOUNT(CASE WHEN call_duration >= '00:08:00' THEN 1 END) AS greater_or_equal_8_minutes,\nCOUNT(CASE WHEN hold_duration >= '00:02:00' THEN 1 END) AS greater_or_equal_2_minutes_hold\nFROM interactions\nWHERE arrival_time BETWEEN '2025-07-21 15:51:08' AND '2025-07-22 15:51:08'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 666}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.824728, "duration": 0.0062, "duration_str": "6.2ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:666", "source": "app/Livewire/AnalyticsNew.php:666", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=666", "ajax": false, "filename": "AnalyticsNew.php", "line": "666"}, "connection": "ilog", "start_percent": 44.194, "width_percent": 4.911}, {"sql": "SELECT\nCOUNT(a.interaction_id) AS totalFlags\nFROM interaction_qa_flag a INNER JOIN interactions b\nON a.interaction_id = b.id AND\narrival_time BETWEEN '2025-07-21 15:51:08' AND '2025-07-22 15:51:08'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 681}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.838156, "duration": 0.00931, "duration_str": "9.31ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:681", "source": "app/Livewire/AnalyticsNew.php:681", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=681", "ajax": false, "filename": "AnalyticsNew.php", "line": "681"}, "connection": "ilog", "start_percent": 49.105, "width_percent": 7.375}, {"sql": "SELECT\nCOUNT(*) AS totalaiFlags\nFROM interactions WHERE\narrival_time BETWEEN '2025-07-21 15:51:08' AND '2025-07-22 15:51:08'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 692}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.855972, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:692", "source": "app/Livewire/AnalyticsNew.php:692", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=692", "ajax": false, "filename": "AnalyticsNew.php", "line": "692"}, "connection": "ilog", "start_percent": 56.48, "width_percent": 4.412}, {"sql": "select\nCOUNT(*) as total_calls,\nSUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,\nSUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,\nSUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections\nfrom `interactions` where `arrival_time` between '2025-07-21 15:51:08' and '2025-07-22 15:51:08' limit 1", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:08", "2025-07-22 15:51:08"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3372}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.874061, "duration": 0.03591, "duration_str": "35.91ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:3372", "source": "app/Livewire/AnalyticsNew.php:3372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=3372", "ajax": false, "filename": "AnalyticsNew.php", "line": "3372"}, "connection": "ilog", "start_percent": 60.892, "width_percent": 28.446}, {"sql": "select * from `organizations` where `id` in (4, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50)", "type": "query", "params": [], "bindings": ["4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 124}, {"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3397}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.916747, "duration": 0.013460000000000001, "duration_str": "13.46ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:124", "source": "app/Livewire/AnalyticsNew.php:124", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=124", "ajax": false, "filename": "AnalyticsNew.php", "line": "124"}, "connection": "ilog", "start_percent": 89.338, "width_percent": 10.662}]}, "models": {"data": {"App\\Models\\Organization": {"value": 101, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Interaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FInteraction.php&line=1", "ajax": false, "filename": "Interaction.php", "line": "?"}}}, "count": 103, "is_counter": true}, "livewire": {"data": {"analytics-new #2X4bS4KFv3GO8B74KnSD": "array:4 [\n  \"data\" => array:23 [\n    \"totalCallDurationAllGroups\" => null\n    \"selected_group\" => null\n    \"disconnected_by_customer\" => 0\n    \"disconnected_by_agent\" => 0\n    \"disconnected_by_system\" => 0\n    \"accountIDFilter\" => null\n    \"accountNameFilter\" => null\n    \"dateFrom\" => Carbon\\Carbon @********** {#758\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000002f60000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-21 15:51:08.0 +03:00\n    }\n    \"dateTo\" => Carbon\\Carbon @********** {#762\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000002fa0000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-22 15:51:08.0 +03:00\n    }\n    \"dateFromFilter\" => null\n    \"dateToFilter\" => null\n    \"dateType\" => \"Last 24 Hours\"\n    \"groupsAccount\" => null\n    \"dataPage\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => null\n      \"avg_hold_time\" => null\n      \"total_hold_time\" => null\n      \"total_interactions\" => 0\n      \"short_call_duration_count\" => 0\n      \"long_call_duration_count\" => 0\n      \"long_hold_duration_count\" => 0\n      \"total_call_duration\" => null\n      \"total_outbound\" => 0\n      \"total_inbound\" => 0\n      \"countEvaluation\" => 0\n      \"avgEvaluationScore\" => null\n      \"totalRing\" => null\n      \"averageRing\" => 0.0\n      \"totalHandledCalls\" => 0\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 0\n    ]\n    \"dataPage2\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => null\n      \"avg_hold_time\" => null\n      \"total_hold_time\" => null\n      \"total_interactions\" => 0\n      \"short_call_duration_count\" => 0\n      \"long_call_duration_count\" => 0\n      \"long_hold_duration_count\" => 0\n      \"total_call_duration\" => null\n      \"total_outbound\" => 0\n      \"total_inbound\" => 0\n      \"countEvaluation\" => 0\n      \"avgEvaluationScore\" => null\n      \"totalRing\" => null\n      \"averageRing\" => 0.0\n      \"totalHandledCalls\" => 0\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 0\n    ]\n    \"searchGroup\" => null\n    \"groupSelected\" => null\n    \"editFlag\" => false\n    \"userSettings\" => []\n    \"cardSelected\" => \"Avg Call Duration Card\"\n    \"role\" => 2\n    \"page\" => \"pageOne\"\n    \"queryGrouFormat\" => \"%H:00\"\n  ]\n  \"name\" => \"analytics-new\"\n  \"component\" => \"App\\Livewire\\AnalyticsNew\"\n  \"id\" => \"2X4bS4KFv3GO8B74KnSD\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/analytics-new\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "36", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753187629\n]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f739ad0-5425-438e-ada8-d6eb3d799444\" target=\"_blank\">View in Telescope</a>", "path_info": "/livewire/update", "status_code": "<pre class=sf-dump id=sf-dump-430485623 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-430485623\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-949356668 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-949356668\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-264951852 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1745 characters\">{&quot;data&quot;:{&quot;totalCallDurationAllGroups&quot;:null,&quot;selected_group&quot;:null,&quot;disconnected_by_customer&quot;:0,&quot;disconnected_by_agent&quot;:0,&quot;disconnected_by_system&quot;:0,&quot;accountIDFilter&quot;:null,&quot;accountNameFilter&quot;:null,&quot;dateFrom&quot;:[&quot;2025-07-21T15:51:08+03:00&quot;,{&quot;type&quot;:&quot;carbon&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;dateTo&quot;:[&quot;2025-07-22T15:51:08+03:00&quot;,{&quot;type&quot;:&quot;carbon&quot;,&quot;s&quot;:&quot;cbn&quot;}],&quot;dateFromFilter&quot;:null,&quot;dateToFilter&quot;:null,&quot;dateType&quot;:&quot;Last 24 Hours&quot;,&quot;groupsAccount&quot;:null,&quot;dataPage&quot;:[{&quot;group_id&quot;:&quot;All&quot;,&quot;group_name&quot;:&quot;All&quot;,&quot;avg_interactions_duration&quot;:null,&quot;avg_hold_time&quot;:null,&quot;total_hold_time&quot;:null,&quot;total_interactions&quot;:0,&quot;short_call_duration_count&quot;:0,&quot;long_call_duration_count&quot;:0,&quot;long_hold_duration_count&quot;:0,&quot;total_call_duration&quot;:null,&quot;total_outbound&quot;:0,&quot;total_inbound&quot;:0,&quot;countEvaluation&quot;:0,&quot;avgEvaluationScore&quot;:null,&quot;totalRing&quot;:null,&quot;averageRing&quot;:0,&quot;totalHandledCalls&quot;:0,&quot;qaFlagsCount&quot;:0,&quot;aiFlagsCount&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],&quot;dataPage2&quot;:[{&quot;group_id&quot;:&quot;All&quot;,&quot;group_name&quot;:&quot;All&quot;,&quot;avg_interactions_duration&quot;:null,&quot;avg_hold_time&quot;:null,&quot;total_hold_time&quot;:null,&quot;total_interactions&quot;:0,&quot;short_call_duration_count&quot;:0,&quot;long_call_duration_count&quot;:0,&quot;long_hold_duration_count&quot;:0,&quot;total_call_duration&quot;:null,&quot;total_outbound&quot;:0,&quot;total_inbound&quot;:0,&quot;countEvaluation&quot;:0,&quot;avgEvaluationScore&quot;:null,&quot;totalRing&quot;:null,&quot;averageRing&quot;:0,&quot;totalHandledCalls&quot;:0,&quot;qaFlagsCount&quot;:0,&quot;aiFlagsCount&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchGroup&quot;:null,&quot;groupSelected&quot;:null,&quot;editFlag&quot;:false,&quot;userSettings&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;cardSelected&quot;:&quot;Avg Call Duration Card&quot;,&quot;role&quot;:2,&quot;page&quot;:&quot;pageOne&quot;,&quot;queryGrouFormat&quot;:&quot;%H:00&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;2X4bS4KFv3GO8B74KnSD&quot;,&quot;name&quot;:&quot;analytics-new&quot;,&quot;path&quot;:&quot;analytics-new&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;fff9fd0a18e55f82d2985e9b21d757f8942f27814238eefa1ae9bf8c13eb437a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">getData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">All</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264951852\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1297769732 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2106</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1253 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndORVZmZm1CQWx1OVVtcUxhcW9Wbnc9PSIsInZhbHVlIjoiNEJpVnZuTkZPUmgxdk1ndVNLeXltN3hiL3lRMkpmckV4Y3ZFczI4Qm0xaXM0eldYak1IU1NYQitGUlhyVUFsODNvTWVWN3NxV2p2bEpkbEc0R1NVWDF2Q0d5dTgrQTgyQWZTNFBMQTFqY25YU1F2UldyNDhxaUNFZ1FiSSthUWpaakExbUJPM0h2cDlyMHI3YU1BaC82d09tYlRzS29EbFpwRTNYVU5GbGY0clN1bTFBTlQzekk0Wi96RUR5aUdwZWtIRFdGaENSYjBDQjlxNnlNOFdKeFg5dXZWQ0twbGNSMkRGcVFGREJ6QT0iLCJtYWMiOiJjYWYxMjVmN2NiZGM3YzJlMDVmYWQ1NjI3YzdjZGQxZmE0MmYyMTVmZDQ5MGY2NTA2NDU3ODc1NThiNTAwYWFmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkpDUWZQYmVUT0xJYlFIWEF6OG5wRkE9PSIsInZhbHVlIjoiMUZhSkhueDF0bjJNVjVYVHJDYldreDlUNTV1MHBvYXU2bXZ4bmN1R2tJN1Y1RDRYYUJXcS9xK3JmSldiZVpTZmtuRVA2ZStBU3dvYVVKbkdaUDdnamZFWHEveDA4UlVyRmNUT2VKeTRjellCQUI2aFNDRmYrMlpDZVVpTmEwRFIiLCJtYWMiOiI0NTYzYTFlODYyMGRmMWNlN2ExOTQ0NzlkYTY0OTI4YmYwZWU0ZThlZDc3N2U3NzJhYjYwYmM4YjBiNWMxYmRkIiwidGFnIjoiIn0%3D; i_log_session=eyJpdiI6ImJEamxGVWJlRVhKUGNhdVlDcDM1TUE9PSIsInZhbHVlIjoiOERZL3drMm1WQjh5Q2k1Wk9aQUJrcjNRUThRVmVCZVl0SmFaTUxlU0NsWUJpTlJmb3VFK1dnM1kwcVJ6RXVPVHU0Vk9VNHpXS1Q0bzlSbFkzR0lqeGl4WDIxSmN1dzVUK250bnRhTnhFZmtzZlhpWUVXVnVOdkpQUm1sVEl4NTkiLCJtYWMiOiI4MTAzZDIzMjcyYjUyOGNiMWFhMzBjNmFhY2NmMzZmYzNmYTgwNTk3NDE0NGI5ZmMyYmMxYzY0YTM0ZDY4NzM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297769732\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-613245638 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">36|5ib4AQhtLHzATEGHqeW0FyHXea5XYBhkXcIWMbiaY723twUGMepP4FnyoU3L|$2y$12$sKmCpsW.aT14AEFPyOgHeO2lhV7BRWjK7d1ywUnOncv84uKprV4di</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>i_log_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3Bna7bWbMH5plZLa1koljiLCJBixIU0hg7L8yoS8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613245638\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-562128273 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 12:51:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6IkJLTit5WStQQWtZK281TXB0ZWVneVE9PSIsInZhbHVlIjoiVzIwVi8zY0hKN0l3emtsend4b0VhV1BqK3JyVDlNNTJZQ2s4QysxZkI3cVFOSkNHM21hTlNzbmdGWlloelBKQnhLRm5OVG9HcExWSmgwRDFQc0FMVDNJbENTUEU0MUZSYVBXN1V4OFU0d21nNHJpOG5IbWp2RDIzSHhxOU1pOTIiLCJtYWMiOiI0MGM5ODg5ZGZjYzEzNDEyZGU1OTAwNmUxMzZlMzk1NmJiMGRiODY3MWNjY2VmOWE2YjliNzU0MjQ1YTBmYzRlIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:51:13 GMT; Max-Age=21600; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">i_log_session=eyJpdiI6Im9SSm94Q3VBSUVGS245NU1wNTJPQnc9PSIsInZhbHVlIjoic3ZFVWdBbzNoNElqejZGMitUbFl2TnhxRzVpbkFMVmtncVV0cGFMbjNsazBySG5NaUJ1aEljd0FLQmRoQnZIemoxaG5xR1VjZk5lT1NmYjIvcFcyeUJYVmRSN1lHV1l1RUtJa2VINTlLZElNd0xKVzJ3ZnRsaWZTUVJma1Y3VlIiLCJtYWMiOiJiOTY5NzBkOGVhN2NmZmM4NDhkMWUxMDc5OTU3YTgyYTg4ZWY2NmY2NmQ1MmE3MjQ2N2Y5ODk5ZDIwZTllOGEyIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:51:13 GMT; Max-Age=21600; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJLTit5WStQQWtZK281TXB0ZWVneVE9PSIsInZhbHVlIjoiVzIwVi8zY0hKN0l3emtsend4b0VhV1BqK3JyVDlNNTJZQ2s4QysxZkI3cVFOSkNHM21hTlNzbmdGWlloelBKQnhLRm5OVG9HcExWSmgwRDFQc0FMVDNJbENTUEU0MUZSYVBXN1V4OFU0d21nNHJpOG5IbWp2RDIzSHhxOU1pOTIiLCJtYWMiOiI0MGM5ODg5ZGZjYzEzNDEyZGU1OTAwNmUxMzZlMzk1NmJiMGRiODY3MWNjY2VmOWE2YjliNzU0MjQ1YTBmYzRlIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:51:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">i_log_session=eyJpdiI6Im9SSm94Q3VBSUVGS245NU1wNTJPQnc9PSIsInZhbHVlIjoic3ZFVWdBbzNoNElqejZGMitUbFl2TnhxRzVpbkFMVmtncVV0cGFMbjNsazBySG5NaUJ1aEljd0FLQmRoQnZIemoxaG5xR1VjZk5lT1NmYjIvcFcyeUJYVmRSN1lHV1l1RUtJa2VINTlLZElNd0xKVzJ3ZnRsaWZTUVJma1Y3VlIiLCJtYWMiOiJiOTY5NzBkOGVhN2NmZmM4NDhkMWUxMDc5OTU3YTgyYTg4ZWY2NmY2NmQ1MmE3MjQ2N2Y5ODk5ZDIwZTllOGEyIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:51:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562128273\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-209692786 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>36</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753187629</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209692786\", {\"maxDepth\":0})</script>\n"}}