<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="row ps-1 d-flex justify-content-center">
        <!-- Search Section -->
        <div class="col-2 mb-4 ms-5">
            <div class="d-flex mb-1">
                {{-- <input id="searchInput" type="text" class="form-control mb-1 text-muted p-1" placeholder="Search..." style="width: 15rem; background: url('{{ asset('assets/SVG/assets-v2/88.svg') }}') no-repeat left 0.5rem center; background-size: 1rem; padding-left: 2rem;" wire:model.live.debounce.300ms="searchUser" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';"> --}}
                <div class="d-flex align-items-center ps-2 rounded-2 bg-color w-100 w-lg-auto">
                    <i class="fas fa-search me-2 color"></i>
                    <input type="text" class="rounded-2 form-control border-0 color shadow-none text-secondary" wire:model.live.debounce.300ms="searchUser" placeholder='Search...' onfocus="this.placeholder='';" onblur="this.placeholder='Search...';">
                </div>
            </div>
        </div>


        <!-- Button Section -->
        <div class="col-9 d-flex justify-content-end main-buttons-container" style="margin-left: 1rem">
            <button wire:target="export" wire:click="export" class="btn btn-md rounded-3 d-flex align-items-center justify-content-between" style="background-color: #01A44F; color: white;">
                <span>Extract Excel</span>
                <img src="{{ asset('assets/SVG/assets-v2/84.svg') }}" alt="icon" style="width: 1.9rem; height: 1.3rem; margin-left: 0.5rem;">
            </button>
            <button class="btn btn-md rounded-3 ms-2" style="background-color: #01A44F; color: white;" data-bs-toggle="modal" data-bs-target="#add-user" wire:click="openAddUserModal">
                <span>Add User</span>
                <img src="{{ asset('assets/SVG/assets-v2/86.svg') }}" alt="icon" style="width: 1.9rem; height: 1.3rem; margin-left: 0.5rem;">
            </button>
            <button class="btn btn-md rounded-3 ms-2" style="background-color: #01A44F; color: white;" data-bs-toggle="modal" data-bs-target="#add-multi">
                <span>Add Multiple Users</span>
                <img src="{{ asset('assets/SVG/assets-v2/87.svg') }}" alt="icon" style="width: 2.5rem; height: 1.3rem; margin-left: 0.5rem;">
            </button>
        </div>
    </div>



    {{-- bottom row  --}}
    <div class="row mx-4 d-flex ps-5">
        <div class="table-responsive px-0 rounded-2" style="height: fit-content; max-height:90vh">
            <table class="table table-hover table-striped overflow-auto mb-0 rounded-2" id="table" style="width:100%">
                <thead id="thead" class="text-muted" style="font-size: 0.7rem;">
                    <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                        {{-- <th scope="col" class="text-center align-middle">ID</th> --}}
                        <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('full_name')">
                            Full Name
                            @if ($sortBy !== 'full_name')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th>
                        <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('role')">
                            Role
                            @if ($sortBy !== 'role')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th>

                        <th scope="col" class="text-center align-middle">OPS ID</th>
                        {{-- <th scope="col" class="text-center align-middle" style="cursor: pointer" wire:click="setSortBy('enabled')">
                            Access
                            @if ($sortBy !== 'enabled')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th> --}}
                        {{-- <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('username')">
                            User Name
                            @if ($sortBy !== 'username')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th> --}}
                        <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('organization_id')">
                            Organization
                            @if ($sortBy !== 'organization_id')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th>
                        <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('user_group_id')">
                            Group
                            @if ($sortBy !== 'user_group_id')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th>
                        {{-- <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('terminated')">
                            Terminated
                            @if ($sortBy !== 'terminated')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th> --}}
                        <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                            Created At
                            @if ($sortBy !== 'created_at')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#FFFFFF"></i>
                            @endif
                        </th>
                        {{-- <th colspan="col" scope="col" class=" text-center align-middle">ACTIONS</th> --}}
                        <th colspan="col" scope="col" class=" text-center align-middle"></th>
                    </tr>
                </thead>
                <tbody class="" style="font-size:0.8rem" id="tbody">

                    @forelse($users as $user)
                        <tr class="align-middle">
                            {{-- <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td> --}}
                            <td class="text-muted text-center py-3 align-middle text-nowrap d-flex justify-content-start">
                                @if ($user->role == 4)
                                    <img src="{{ asset('assets/SVG/assets-v2/82.svg') }}" alt="User Icon" style="width: 3rem; height: 3rem; margin-right:3rem; margin-left:2rem;">
                                @elseif ($user->role == 1)
                                    <img src="{{ asset('assets/SVG/assets-v2/83.svg') }}" alt="User Icon" style="width: 3rem; height: 3rem; margin-right:3rem; margin-left:2rem;">
                                @elseif ($user->role == 2)
                                    <img src="{{ asset('assets/SVG/assets-v2/81.svg') }}" alt="User Icon" style="width: 3rem; height: 3rem; margin-right:3rem; margin-left:2rem;">
                                @elseif ($user->role == 5)
                                    <img src="{{ asset('assets/SVG/assets-v2/80.svg') }}" alt="User Icon" style="width: 3rem; height: 3rem; margin-right:3rem; margin-left:2rem;">
                                @elseif ($user->role == 6)
                                    <img src="{{ asset('assets/images/avatar.png') }}" alt="User Icon" style="width: 3rem; height: 3rem; margin-right:3rem; margin-left:2rem;">
                                @elseif ($user->role == 7)
                                    <img src="{{ asset('assets/SVG/assets-v2/83.svg') }}" alt="User Icon" style="width: 3rem; height: 3rem; margin-right:3rem; margin-left:2rem;">
                                @endif
                                <span style="padding-top:0.8rem">{{ $user->full_name }}</span>
                            </td>
                            {{-- <td class="text-muted text-center py-3 align-middle"> {{ $user->email }} </td> --}}
                            <td class="text-muted text-center py-3 text-nowrap align-middle">
                                @if ($user->role == 1)
                                    Admin
                                @elseif ($user->role == 2)
                                    Supervisor
                                @elseif ($user->role == 3)
                                    Admin
                                @elseif ($user->role == 5)
                                    Quality
                                @elseif ($user->role == 6)
                                    Client
                                @else
                                    Agent
                                @endif
                            </td>
                            <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $user->agent_id }} </td>
                            {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">
                                @if ($user->enabled)
                                    <i class="fa-solid fa-user-check fa-lg" aria-hidden="true" title="Active" style="color: #01A44F"></i>
                                @else
                                    <i class="fa-solid fa-user-xmark fa-lg " aria-hidden="true" title="Disabled" style="color: tomato"></i>
                                @endif
                            </td> --}}
                            {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">{{ $user->username }}</td> --}}
                            <td class="text-muted text-center py-3 text-nowrap align-middle">
                                @if ($user->role == 4 || $user->role == 6)
                                    {{ $user->organization->name ?? '-' }}
                                @elseif($user->role != 4 && $user->supervisorOrganizations()->count() == 0)
                                    -
                                @else
                                    {{ $user->supervisorOrganizations()->count() > 1 ? 'Mutli' : $user->supervisorOrganizations()?->first()?->name }}
                                @endif
                            </td>
                            <td class="text-muted text-center py-3 text-nowrap align-middle">
                                @if ($user->role == 4)
                                    {{ $user->userGroup->name ?? '-' }}
                                @else
                                    {{ $user->supervisorGroups()->exists() ? 'Mutli' : '-' }}
                                @endif
                            </td>
                            {{-- <td @class([
                                'text-danger fw-bold' => $user->terminated,
                                'text-muted' => !$user->terminated,
                                'text-center py-3 text-nowrap align-middle',
                            ])> {{ $user->terminated == 1 ? 'Yes' : 'No' }} </td> --}}

                            <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $user->created_at ? Carbon::parse($user->created_at)->format('Y-m-d h:i A') : '-' }}</td>
                            <td class="text-muted text-center py-3 text-nowrap align-middle">
                                @if (in_array(Auth::user()->role, [1, 3, 2, 5]))
                                    <div class="d-flex justify-content-center">
                                        <img src="{{ asset('assets/SVG/assets-v2/union-1.svg') }}" alt="" style="width: 1.5rem; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#edit-user" wire:click="selectUser('{{ $user->id }}')" />

                                        <!-- Custom Styled Toggle Switch -->
                                        <div class="d-flex align-items-center gap-2 justify-content-center align-items-center ms-3 pt-2">
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="customSwitch{{ $user->id }}" @checked($user->enabled) wire:change="{{ $user->enabled ? 'disable(' . $user->id . ')' : 'enable(' . $user->id . ')' }}" />
                                                <label for="customSwitch{{ $user->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $user->enabled ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="
                                                            width: 18px;
                                                            height: 18px;
                                                            background-color: {{ $user->enabled ? '#ffffff' : '#FF5E60' }};
                                                            border-radius: 50%;
                                                            top: 3px;
                                                            left: {{ $user->enabled ? '22px' : '3px' }};
                                                            transition: left 0.3s, background-color 0.3s;">
                                                        @if ($user->enabled)
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path
                                                                    d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                    fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <!-- End of Custom Styled Toggle Switch -->

                                    </div>
                                    {{-- @if ($user->enabled)
                                        <i class="fa-solid fa-user-xmark fa-xl me-1" aria-hidden="true" style="cursor: pointer;color:darkgray" title="Disable" wire:click="disable('{{ $user->id }}')"></i>
                                    @else
                                        <i class="fa-solid fa-user-check fa-xl text-success me-1" aria-hidden="true" style="cursor: pointer" title="Enable" wire:click="enable('{{ $user->id }}')"></i>
                                    @endif

                                    <i class="fa-solid fa-user-pen fa-xl me-1" aria-hidden="true" style="cursor: pointer;color:rgb(235, 179, 76)" data-bs-toggle="modal" data-bs-target="#edit-user" wire:click="selectUser('{{ $user->id }}')" title="Edit User"></i>
                                    <i class="fa fa-trash fa-xl" aria-hidden="true" style="cursor: pointer; color:tomato" wire:click="showDeleteAlert('{{ $user->id }}')" title="Delete User"></i> --}}
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="18" class="text-muted text-center bg-white"> No users found</td>
                        </tr>
                    @endforelse

                </tbody>
            </table>
        </div>


        <div class="d-flex justify-content-between mt-3">
            <!-- Dropdown for Number of Items per Page -->
            <div>
                <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                    <option value="10">10</option>
                    <option value="15" selected>15</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>Results Per Page</span>
            </div>

            <!-- Pagination Links -->
            <div>
                {{ $users->links(data: ['scrollTo' => false]) }}
            </div>
        </div>

        {{-- <div class="mt-2 pe-0">
            {{ $users->links(data: ['scrollTo' => false]) }}
        </div> --}}


        <!-- Edit Modal -->
        <div class="modal fade" id="edit-user" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header text-white" style="background-color: #01A44F;">
                        <div class="d-flex align-items-center">
                            <div class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef; display: flex; justify-content: center; align-items: center;">
                                <img src="{{ asset('assets/SVG/assets-v2/union-1 - green.svg') }}" alt="Edit Icon" style="width: 100%; height: 100%; object-fit: contain;">
                            </div>
                            <h5 class="modal-title mb-0" id="staticBackdropLabel">Edit User</h5>
                        </div>
                        <i class="fas fa-times text-success" style="font-size: 24px; cursor:pointer" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></i>
                        {{-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button> --}}
                    </div>



                    {{-- <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'general') active @endif" wire:click="$set('activeTab', 'general')" style="cursor: pointer">General</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'parameters') active @endif" wire:click="$set('activeTab', 'parameters')" style="cursor: pointer">Parameters</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'permissions') active @endif" wire:click="$set('activeTab', 'permissions')" style="cursor: pointer">Permissions</a>
                        </li>
                    </ul> --}}

                    <div class="d-flex gap-3 ms-3" style="background-color: transparent; border: none; padding: 10px;">
                        <button class="btn btn-outline-success @if ($activeTab === 'general') active @endif tab-button" wire:click="$set('activeTab', 'general')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            General
                        </button>
                        <button class="btn btn-outline-success @if ($activeTab === 'parameters') active @endif tab-button" wire:click="$set('activeTab', 'parameters')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            Parameters
                        </button>
                        <button class="btn btn-outline-success @if ($activeTab === 'permissions') active @endif tab-button" wire:click="$set('activeTab', 'permissions')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            Permissions
                        </button>
                    </div>


                    <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">

                        <div class="tab-content">
                            {{-- GENERAL TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'general') show active @endif">
                                <form wire:submit.prevent>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Role:</label>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                @if ($selectedUserRole == 1)
                                                    Admin
                                                @elseif ($selectedUserRole == 2)
                                                    Supervisor
                                                @elseif ($selectedUserRole == 3)
                                                    Admin
                                                @elseif ($selectedUserRole == 5)
                                                    Quality
                                                @elseif ($selectedUserRole == 6)
                                                    Client/TL
                                                @else
                                                    Agent
                                                @endif
                                            </button>
                                            <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('5')">Quality</a></li>
                                                <hr class="m-0">
                                                {{-- <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                                <hr class="m-0"> --}}
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('6')">Client/TL</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Full Name:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="selectedFullName" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                        @error('selectedFullName')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Username:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                        @error('selectedUserName')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    {{-- <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label">Password:</label>
                                        <input type="password" class="form-control" id="recipient-name" wire:model="selectedUserName">
                                    </div>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label">Confirm Password:</label>
                                        <input type="password" class="form-control" id="recipient-name" wire:model="selectedUserName">
                                    </div> --}}
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Email:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserEmail" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                        @error('selectedUserEmail')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    {{-- <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label">Phone Number:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                                    </div> --}}
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">ID:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserAgentId" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                        @error('selectedUserAgentId')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div class="mb-3">

                                        {{-- the agent and client can have only one org  --}}
                                        @if ($selectedUserRole == 4 || $selectedUserRole == 6)
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    {{ $selectedUserOrganizationName == '' ? '--' : $selectedUserOrganizationName }}
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;">
                                                    @forelse ($organizations as $org)
                                                        <li><a class="dropdown-item" wire:click="changeOrg('{{ $org->id }}')">{{ $org->name }}</a></li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li><a class="dropdown-item">No Organizations Found</a></li>
                                                    @endforelse
                                                </ul>
                                            </div>
                                            {{-- for non agents - multi orgs  --}}
                                        @else
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organizations:</label>

                                            @php
                                                $selectedSupervisorOrgs = $selectedSupervisorOrgs ?? [];
                                            @endphp
                                            @forelse ($selectedSupervisorOrgs as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeOrg_edit({{ $index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                                <span id="searchIcon" class="input-group-text border-0" style="width: 2.8rem; background-color:#eff3f4 !important;">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control border-0" wire:model.live.debounce.300ms="searchOrganizations" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0; background-color:#eff3f4 !important;" aria-describedby="searchIcon">

                                                @if ($searchOrganizations != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @foreach ($organizations as $org)
                                                            <li class="list-group-item" wire:click="selectOrg_edit('{{ $org->id }}')" style="cursor: pointer">{{ $org->name }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        @endif

                                    </div>

                                    {{-- <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label">Skill Groups:</label>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                @if ($role)
                                                    @if ($role == 1)
                                                        Admin
                                                    @elseif ($role == 2)
                                                        Supervisor
                                                    @else
                                                        IT
                                                    @endif
                                                @else
                                                    {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                                @endif
                                            </button>
                                            <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                            </ul>
                                        </div>
                                    </div> --}}

                                    <div class="mb-3">

                                        {{-- the agent can have only one user group  --}}
                                        @if ($selectedUserRole == 4)
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Group:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    {{ $selectedUserUserGroups == '' ? '--' : $selectedUserUserGroups }}
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;z-index:999999">
                                                    @forelse ($userGroups as $group)
                                                        <li><a class="dropdown-item" wire:click='changeUserGroup("{{ $group->id }}", "{{ $group->name }}")'>{{ $group->name }}</a></li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li><a class="dropdown-item">No User Groups Found</a></li>
                                                    @endforelse
                                                </ul>
                                            </div>
                                        @elseif ($selectedUserRole == 6)
                                        @else
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Groups:</label>

                                            @php
                                                $selectedSupervisorUserGroups = $selectedSupervisorUserGroups ?? [];
                                            @endphp
                                            @forelse ($selectedSupervisorUserGroups as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeUserGroup({{ $index }}, {{ $loop->index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                                <span id="searchIcon" class="input-group-text border-0" style="width: 2.8rem; background-color:#eff3f4 !important;">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control border-0" wire:model.live.debounce.300ms="searchUserGroups" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0; background-color:#eff3f4 !important;" aria-describedby="searchIcon">

                                                @if ($searchUserGroups != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @foreach ($possibleUserGroups_edit as $userGroup)
                                                            <li class="list-group-item" wire:click="selectUserGroup({{ $userGroup->id }})" style="cursor: pointer">{{ $userGroup->name }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        @endif

                                    </div>

                                    {{-- Direct supervisor is only for agents  --}}
                                    {{-- @if ($selectedUserRole == 4)
                                        <div class="mb-3">
                                            <label for="recipient-name" class="col-form-label">Direct Supervisor:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    {{ $selectedUserDirectSupervisor == '' ? '--' : $selectedUserDirectSupervisor }}
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;">
                                                    @forelse ($directSupervisors as $user)
                                                        <li><a class="dropdown-item" wire:click="changeDirectSupervisor('{{ $user->id }}', '{{ $user->full_name }}')">{{ $user->full_name }}</a></li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li><a class="dropdown-item">No Supervisors Found</a></li>
                                                    @endforelse
                                                </ul>
                                            </div>
                                        </div>
                                    @endif --}}

                                    {{-- evaluation forms are for supervisors only --}}
                                    {{-- @if (in_array($selectedUserRole, [2, 5]))
                                        <div class="mb-3">
                                            <label for="recipient-name" class="col-form-label">Evaluation Forms:</label>
                                            @php
                                                $selectedUserEvaluationForms = $selectedUserEvaluationForms ?? [];
                                            @endphp
                                            @forelse ($selectedUserEvaluationForms as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeEvaluationForm_edit({{ $index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative">
                                                <span id="searchIcon" class="input-group-text">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control" wire:model.live.debounce.300ms="searchEvaluationForms" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                                @if ($searchEvaluationForms != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @foreach ($evaluationForms as $form)
                                                            <li class="list-group-item" wire:click="selectEvaluationForm_edit('{{ $form->id }}')" style="cursor: pointer">{{ $form->evaluation_name }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        </div>
                                    @endif --}}

                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1s" wire:model.live="selectedUserTerminated">
                                        <label class="form-check-label" for="exampleCheck1">Terminated</label>
                                    </div> --}}
                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="terminatedToggle">Terminated</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="terminatedToggle" wire:model.live="selectedUserTerminated">
                                            <label for="terminatedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserTerminated ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserTerminated ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserTerminated ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserTerminated)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>


                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1s" wire:model.live="selectedUserResetNextLogin">
                                        <label class="form-check-label" for="exampleCheck1">Password Reset At Next Login</label>
                                    </div> --}}

                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="resetNextLoginToggle">Password Reset At Next Login</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="resetNextLoginToggle" wire:model.live="selectedUserResetNextLogin">
                                            <label for="resetNextLoginToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserResetNextLogin ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserResetNextLogin ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserResetNextLogin ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserResetNextLogin)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>


                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="selectedUserPasswordPolicy">
                                        <label class="form-check-label" for="exampleCheck1">Password Policy</label>
                                    </div>


                                    @if ($selectedUserPasswordPolicy == true)
                                        <label class="form-check-label" for="exampleCheck1ss">Enforce Password Reset After: </label>
                                        <input type="number" class="form-control d-inline" min="1" style="width: 4rem; height:1.5rem" wire:model.live.debounce.200ms="selectedUserPasswordPolicyPeriod">
                                        <span class="fs-6">Day(s)</span>
                                    @endif --}}

                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="passwordPolicyToggle">Password Policy</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="passwordPolicyToggle" wire:model.live="selectedUserPasswordPolicy">
                                            <label for="passwordPolicyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserPasswordPolicy ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserPasswordPolicy ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserPasswordPolicy ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserPasswordPolicy)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    @if ($selectedUserPasswordPolicy == true)
                                        <label class="form-check-label mt-2" for="enforceResetAfter">Enforce Password Reset After:</label>
                                        <input type="number" class="form-control d-inline" min="1" style="width: 4rem; height: 1.5rem;" wire:model.live.debounce.200ms="selectedUserPasswordPolicyPeriod">
                                        <span class="fs-6">Day(s)</span>
                                    @endif



                                </form>
                            </div>


                            {{-- PARAMETERS TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'parameters') show active @endif">
                                <form wire:submit.prevent>
                                    <fieldset class="row">
                                        <legend class="col-form-label">General</legend>
                                        {{-- @if ($selectedUserRole != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Call ID</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Call ID', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Call ID" wire:click="toggleSelectedUserParams('Call ID')">
                                            </div>
                                        @endif --}}
                                        @if ($selectedUserRole != 4)
                                            <div class="mb-2 d-flex align-items-center gap-2 mt-1 justify-content-between">
                                                <label class="form-check-label" for="callIdToggle">Call ID</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="callIdToggle" wire:model="selectedUserParams" value="Call ID" wire:click="toggleSelectedUserParams('Call ID')">
                                                    <label for="callIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Call ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Call ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Call ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Call ID', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif

                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Duration</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Duration', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Duration" wire:click="toggleSelectedUserParams('Duration')">
                                        </div>


                                        @if ($selectedUserRole != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Number</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Interaction Number', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                            </div>
                                        @endif --}}
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="durationToggle">Duration</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="durationToggle" wire:model="selectedUserParams" value="Duration" wire:click="toggleSelectedUserParams('Duration')">
                                                <label for="durationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Duration', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        {{--
                                        @if ($selectedUserRole != 4)
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionNumberToggle">Interaction Number</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionNumberToggle" wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                                    <label for="interactionNumberToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Number', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Number', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif --}}

                                        @if ($selectedUserRole != 4)
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionNumberToggle">Interaction Number</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionNumberToggle" wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                                    <label for="interactionNumberToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Number', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Number', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif


                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label">Telephony</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Called ID</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Called ID', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Called ID" wire:click="toggleSelectedUserParams('Called ID')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Hold Duration</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Hold Duration', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Hold Duration" wire:click="toggleSelectedUserParams('Hold Duration')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Caller ID</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Caller ID', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Caller ID" wire:click="toggleSelectedUserParams('Caller ID')">
                                        </div> --}}
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="calledIdToggle">Called ID</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="calledIdToggle" wire:model="selectedUserParams" value="Called ID" wire:click="toggleSelectedUserParams('Called ID')">
                                                <label for="calledIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Called ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Called ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Called ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Called ID', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="holdDurationToggle">Hold Duration</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="holdDurationToggle" wire:model="selectedUserParams" value="Hold Duration" wire:click="toggleSelectedUserParams('Hold Duration')">
                                                <label for="holdDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Hold Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Hold Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Hold Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Hold Duration', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="callerIdToggle">Caller ID</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="callerIdToggle" wire:model="selectedUserParams" value="Caller ID" wire:click="toggleSelectedUserParams('Caller ID')">
                                                <label for="callerIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Caller ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Caller ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Caller ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Caller ID', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($selectedUserRole != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Ender</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Interaction Ender', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Interaction Ender" wire:click="toggleSelectedUserParams('Interaction Ender')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Call Type</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Call Type', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Call Type" wire:click="toggleSelectedUserParams('Call Type')">
                                            </div> --}}
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionEnderToggle">Interaction Ender</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionEnderToggle" wire:model="selectedUserParams" value="Interaction Ender" wire:click="toggleSelectedUserParams('Interaction Ender')">
                                                    <label for="interactionEnderToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Ender', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Ender', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Ender', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Ender', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="callTypeToggle">Call Type</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="callTypeToggle" wire:model="selectedUserParams" value="Call Type" wire:click="toggleSelectedUserParams('Call Type')">
                                                    <label for="callTypeToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Call Type', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Call Type', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Call Type', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Call Type', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Digits Count</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Digits Count', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Digits Count" wire:click="toggleSelectedUserParams('Digits Count')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Extension</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Extension', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Extension" wire:click="toggleSelectedUserParams('Extension')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Hold Count</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Hold Count', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Hold Count" wire:click="toggleSelectedUserParams('Hold Count')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Pause Duration</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Pause Duration', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Pause Duration" wire:click="toggleSelectedUserParams('Pause Duration')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Ring Duration</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Ring Duration', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Ring Duration" wire:click="toggleSelectedUserParams('Ring Duration')">
                                            </div> --}}

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="extensionToggle">Extension</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="extensionToggle" wire:model="selectedUserParams" value="Extension" wire:click="toggleSelectedUserParams('Extension')">
                                                    <label for="extensionToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Extension', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Extension', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Extension', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Extension', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="holdCountToggle">Hold Count</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="holdCountToggle" wire:model="selectedUserParams" value="Hold Count" wire:click="toggleSelectedUserParams('Hold Count')">
                                                    <label for="holdCountToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Hold Count', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Hold Count', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Hold Count', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Hold Count', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="pauseDurationToggle">Pause Duration</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="pauseDurationToggle" wire:model="selectedUserParams" value="Pause Duration" wire:click="toggleSelectedUserParams('Pause Duration')">
                                                    <label for="pauseDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Pause Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Pause Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Pause Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Pause Duration', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="ringDurationToggle">Ring Duration</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="ringDurationToggle" wire:model="selectedUserParams" value="Ring Duration" wire:click="toggleSelectedUserParams('Ring Duration')">
                                                    <label for="ringDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Ring Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Ring Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Ring Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Ring Duration', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>






                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Screen Capture</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Screen Capture', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Screen Capture" wire:click="toggleSelectedUserParams('Screen Capture')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Server Name</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Server Name', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Server Name" wire:click="toggleSelectedUserParams('Server Name')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Transferred From</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Transferred From', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Transferred From" wire:click="toggleSelectedUserParams('Transferred From')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Transferred To</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Transferred To', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Transferred To" wire:click="toggleSelectedUserParams('Transferred To')">
                                            </div> --}}
                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label">Quality</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Evaluation Score</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Evaluation Score', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Evaluation Score" wire:click="toggleSelectedUserParams('Evaluation Score')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Is Evaluated</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Is Evaluated', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Is Evaluated" wire:click="toggleSelectedUserParams('Is Evaluated')">
                                        </div> --}}
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="evaluationScoreToggle">Evaluation Score</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="evaluationScoreToggle" wire:model="selectedUserParams" value="Evaluation Score" wire:click="toggleSelectedUserParams('Evaluation Score')">
                                                <label for="evaluationScoreToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Evaluation Score', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Evaluation Score', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Evaluation Score', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Evaluation Score', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="isEvaluatedToggle">Is Evaluated</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="isEvaluatedToggle" wire:model="selectedUserParams" value="Is Evaluated" wire:click="toggleSelectedUserParams('Is Evaluated')">
                                                <label for="isEvaluatedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Is Evaluated', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Is Evaluated', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Is Evaluated', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Is Evaluated', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>


                                        @if ($selectedUserRole != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Includes Fatal Error</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Includes Fatal Error', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Includes Fatal Error" wire:click="toggleSelectedUserParams('Includes Fatal Error')">
                                            </div> --}}

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="includesFatalErrorToggle">Includes Fatal Error</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="includesFatalErrorToggle" wire:model="selectedUserParams" value="Includes Fatal Error" wire:click="toggleSelectedUserParams('Includes Fatal Error')">
                                                    <label for="includesFatalErrorToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Includes Fatal Error', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Is Assigned</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Is Assigned', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Is Assigned" wire:click="toggleSelectedUserParams('Is Assigned')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Is Flagged</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Is Flagged', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Is Flagged" wire:click="toggleSelectedUserParams('Is Flagged')">
                                            </div> --}}

                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="isFlaggedToggle">Is Flagged</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="isFlaggedToggle" wire:model="selectedUserParams" value="Is Flagged" wire:click="toggleSelectedUserParams('Is Flagged')">
                                                    <label for="isFlaggedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Is Flagged', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Is Flagged', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Is Flagged', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Is Flagged', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>



                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Penalties</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Penalties', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Penalties" wire:click="toggleSelectedUserParams('Penalties')">
                                            </div> --}}
                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label">Users & Actions</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Agent Name</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Agent Name', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Agent Name" wire:click="toggleSelectedUserParams('Agent Name')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Comment</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Comment', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Comment" wire:click="toggleSelectedUserParams('Comment')">
                                        </div> --}}

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="agentNameToggle">Agent Name</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="agentNameToggle" wire:model="selectedUserParams" value="Agent Name" wire:click="toggleSelectedUserParams('Agent Name')">
                                                <label for="agentNameToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Agent Name', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Agent Name', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Agent Name', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Agent Name', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="commentToggle">Comment</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="commentToggle" wire:model="selectedUserParams" value="Comment" wire:click="toggleSelectedUserParams('Comment')">
                                                <label for="commentToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Comment', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Comment', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Comment', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Comment', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($selectedUserRole != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Importance</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Interaction Importance', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Interaction Importance" wire:click="toggleSelectedUserParams('Interaction Importance')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Custom Flag</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Custom Flag', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Custom Flag" wire:click="toggleSelectedUserParams('Custom Flag')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Group</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Group', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Group" wire:click="toggleSelectedUserParams('Group')">
                                            </div> --}}
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="groupToggle">Group</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="groupToggle" wire:model="selectedUserParams" value="Group" wire:click="toggleSelectedUserParams('Group')">
                                                    <label for="groupToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Group', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Group', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Group', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Group', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Played</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Played', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Played" wire:click="toggleSelectedUserParams('Played')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Organization</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Organization', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Organization" wire:click="toggleSelectedUserParams('Organization')">
                                            </div> --}}
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="organizationToggle">Organization</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="organizationToggle" wire:model="selectedUserParams" value="Organization" wire:click="toggleSelectedUserParams('Organization')">
                                                    <label for="organizationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Organization', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Organization', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Organization', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Organization', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Skill Group</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Skill Group', $selectedUserParams) ? 'checked' : '' }} wire:model="selectedUserParams" value="Skill Group" wire:click="toggleSelectedUserParams('Skill Group')">
                                            </div> --}}
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="skillGroupToggle">Skill Group</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="skillGroupToggle" wire:model="selectedUserParams" value="Skill Group" wire:click="toggleSelectedUserParams('Skill Group')">
                                                    <label for="skillGroupToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Skill Group', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Skill Group', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Skill Group', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Skill Group', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    {{-- <fieldset class="row w-75"> --}}
                                    {{-- <legend class="col-form-label text-success">Telephony Custom Attributes</legend> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                    <label class="form-check-label" for="exampleCheck1">Agent Name</label>
                                    <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                </div> --}}
                                    {{-- </fieldset> --}}

                                </form>
                            </div>

                            {{-- PERMISSIONS TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'permissions') show active @endif">
                                <form>
                                    <fieldset class="row">
                                        <legend class="col-form-label">Telephony</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Access Telephony</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Allow Access Telephony" wire:click="toggleSelectedUserPermissions('Allow Access Telephony')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Play Recorded Interactions</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Play Recorded Interactions" wire:click="toggleSelectedUserPermissions('Play Recorded Interactions')">

                                        </div> --}}
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="allowAccessTelephonyToggle">Allow Access Telephony</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="allowAccessTelephonyToggle" wire:model="selectedUserPermissions" value="Allow Access Telephony" wire:click="toggleSelectedUserPermissions('Allow Access Telephony')">
                                                <label for="allowAccessTelephonyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Allow Access Telephony', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="playRecordedInteractionsToggle">Play Recorded Interactions</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="playRecordedInteractionsToggle" wire:model="selectedUserPermissions" value="Play Recorded Interactions" wire:click="toggleSelectedUserPermissions('Play Recorded Interactions')">
                                                <label for="playRecordedInteractionsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Play Recorded Interactions', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>



                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Download Audio Files</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Download Audio Files', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Download Audio Files" wire:click="toggleSelectedUserPermissions('Download Audio Files')">

                                        </div> --}}
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Show Evaluated Interactions Only</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Show Evaluated Interactions Only" wire:click="toggleSelectedUserPermissions('Show Evaluated Interactions Only')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Other Users Interactions Comments</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="View Other Users Interactions Comments" wire:click="toggleSelectedUserPermissions('View Other Users Interactions Comments')">

                                        </div> --}}

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="showEvaluatedInteractionsToggle">Show Evaluated Interactions Only</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="showEvaluatedInteractionsToggle" wire:model="selectedUserPermissions" value="Show Evaluated Interactions Only" wire:click="toggleSelectedUserPermissions('Show Evaluated Interactions Only')">
                                                <label for="showEvaluatedInteractionsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Show Evaluated Interactions Only', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewOtherUsersCommentsToggle">View Other Users Interactions Comments</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewOtherUsersCommentsToggle" wire:model="selectedUserPermissions" value="View Other Users Interactions Comments" wire:click="toggleSelectedUserPermissions('View Other Users Interactions Comments')">
                                                <label for="viewOtherUsersCommentsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Other Users Interactions Comments', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>




                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Search By Deleted Interactions</label>
                                                                                        <input type="checkbox" class="form-check-input"  {{ in_array('Skill Group', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Skill Group" wire:click="toggleSelectedUserPermissions('Skill Group')">

                                        </div> --}}
                                    </fieldset>


                                    {{-- @if ($selectedUserRole != 4)
                                    <hr class="m-0">
                                    <fieldset class="row w-75">
                                        <legend class="col-form-label">General Quality</legend>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Search By Evaluation Form</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Allow Search By Evaluation Form', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Allow Search By Evaluation Form" wire:click="toggleSelectedUserPermissions('Allow Search By Evaluation Form')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Listen Own Session Recordings</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('Listen Own Session Recordings', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Listen Own Session Recordings" wire:click="toggleSelectedUserPermissions('Listen Own Session Recordings')">

                                        </div>
                                    </fieldset>
                                    @endif --}}

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label">Telephony Quality</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Interactions Evaluations</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="View Interactions Evaluations" wire:click="toggleSelectedUserPermissions('View Interactions Evaluations')">

                                        </div>
                                        @if ($selectedUserRole != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Create Interaction Evaluations</label>
                                                <input type="checkbox" class="form-check-input" {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Create Interaction Evaluations" wire:click="toggleSelectedUserPermissions('Create Interaction Evaluations')">
                                            </div>
                                        @endif --}}
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewInteractionsEvaluationsToggle">View Interactions Evaluations</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewInteractionsEvaluationsToggle" wire:model="selectedUserPermissions" value="View Interactions Evaluations" wire:click="toggleSelectedUserPermissions('View Interactions Evaluations')">
                                                <label for="viewInteractionsEvaluationsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Interactions Evaluations', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($selectedUserRole != 4)
                                            <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="createInteractionEvaluationsToggle">Create Interaction Evaluations</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="createInteractionEvaluationsToggle" wire:model="selectedUserPermissions" value="Create Interaction Evaluations" wire:click="toggleSelectedUserPermissions('Create Interaction Evaluations')">
                                                    <label for="createInteractionEvaluationsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Create Interaction Evaluations', $selectedUserPermissions))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif


                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label">Reports</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Evaluation Reports</label>
                                            <input type="checkbox" class="form-check-input" {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="View Evaluation Reports" wire:click="toggleSelectedUserPermissions('View Evaluation Reports')">

                                        </div> --}}
                                        <div class="mb-2 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewEvaluationReportsToggle">View Evaluation Reports</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewEvaluationReportsToggle" wire:model="selectedUserPermissions" value="View Evaluation Reports" wire:click="toggleSelectedUserPermissions('View Evaluation Reports')">
                                                <label for="viewEvaluationReportsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Evaluation Reports', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                    </fieldset>

                                </form>
                            </div>

                        </div>
                    </div>


                    <div class="modal-footer bg-white">
                        <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="closeEdit">Close</button>
                        <button type="button" class="btn btn-md btn-success modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="submitEditUser">Apply</button>
                    </div>
                </div>
            </div>
        </div>

        {{-- Add user Modal  --}}
        <div class="modal fade" id="add-user" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header text-white">
                        <div class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef; display: flex; justify-content: center; align-items: center;">
                            <img src="{{ asset('assets/SVG/assets-v2/86 - green.svg') }}" alt="" style="width: 80%; height: 80%; object-fit: contain;">
                        </div>
                        <h5 class="modal-title" id="staticBackdropLabel">Add User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                    </div>


                    {{-- <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'general') active @endif" wire:click="$set('activeTab', 'general')" style="cursor: pointer">General</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'parameters') active @endif" wire:click="$set('activeTab', 'parameters')" style="cursor: pointer">Parameters</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'permissions') active @endif" wire:click="$set('activeTab', 'permissions')" style="cursor: pointer">Permissions</a>
                        </li>
                    </ul> --}}

                    <div class="d-flex gap-3 ms-3" style="background-color: transparent; border: none; padding: 10px;">
                        <button class="btn btn-outline-success @if ($activeTab === 'general') active @endif tab-button" wire:click="$set('activeTab', 'general')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            General
                        </button>
                        <button class="btn btn-outline-success @if ($activeTab === 'parameters') active @endif tab-button" wire:click="$set('activeTab', 'parameters')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            Parameters
                        </button>
                        <button class="btn btn-outline-success @if ($activeTab === 'permissions') active @endif tab-button" wire:click="$set('activeTab', 'permissions')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            Permissions
                        </button>
                    </div>

                    <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">

                        <div class="tab-content">
                            {{-- GENERAL TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'general') show active @endif">
                                <form wire:submit.prevent>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Role:</label>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                {{ $added_role }}
                                            </button>
                                            <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                <li><a class="dropdown-item" wire:click="addRole('1')">Admin</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('2')">Supervisor</a></li>
                                                <hr class="m-0">
                                                {{-- <li><a class="dropdown-item" wire:click="addRole('3')">IT</a></li>
                                                <hr class="m-0"> --}}
                                                <li><a class="dropdown-item" wire:click="addRole('5')">Quality</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('4')">Agent</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('6')">Client/TL</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('7')">Development Supervisor</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Full Name:</label>
                                        <input type="text" class="form-control" id="recipient-name" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;" wire:model="added_fullName" name="name" placeholder="Full Name">
                                    </div>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Username:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="added_username" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;" name="user" placeholder="User Name">
                                    </div>
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Password:</label>
                                        <input type="password" class="form-control" id="recipient-name" wire:model="added_password" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;" placeholder="Password">
                                        @error('added_password')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    {{-- <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label">Confirm Password:</label>
                                        <input type="password" class="form-control" id="recipient-name" wire:model="selectedUserName">
                                    </div> --}}
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Email:</label>
                                        <input type="email" class="form-control" id="recipient-name" wire:model="added_email" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;" name="added_email" placeholder="Email">
                                        @error('added_email')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    {{-- <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label">Phone Number:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                                    </div> --}}
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Ops ID:</label>
                                        <input type="text" class="form-control" id="recipient-name" wire:model="added_id" placeholder="Ops ID" style="color:black; font-weight:600; background-color:#eff3f4 !important; border:none !important; height:3rem;">
                                        @error('added_id')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>


                                    <div class="mb-3">

                                        {{-- the agent and client can have only one org  --}}
                                        @if ($added_role_id == 4 || $added_role_id == 6)
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    {{ $selectedUserOrganizationName == '' ? '--' : $selectedUserOrganizationName }}
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;">
                                                    @forelse ($organizations as $org)
                                                        <li><a class="dropdown-item" wire:click="changeOrg('{{ $org->id }}')">{{ $org->name }}</a></li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li><a class="dropdown-item">No Organizations Found</a></li>
                                                    @endforelse
                                                </ul>
                                            </div>
                                            {{-- for non agents - multi orgs  --}}
                                        @else
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organizations:</label>

                                            @php
                                                $addSupervisorOrganizations = $addSupervisorOrganizations ?? [];
                                            @endphp
                                            @forelse ($addSupervisorOrganizations as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeOrg({{ $index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative">
                                                <span id="searchIcon" class="input-group-text">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control" wire:model.live.debounce.300ms="searchOrganizations" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">


                                                @if ($searchOrganizations != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @foreach ($organizations as $org)
                                                            <li class="list-group-item" wire:click="selectOrg('{{ $org->id }}')" style="cursor: pointer">{{ $org->name }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        @endif

                                    </div>

                                    <div class="mb-3">

                                        {{-- the agent or client can have only one user group  --}}
                                        @if ($added_role_id == 4)
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Group:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    {{ $selectedUserUserGroups == '' ? '--' : $selectedUserUserGroups }}
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;z-index:999999">
                                                    @forelse ($userGroups as $group)
                                                        <li><a class="dropdown-item" wire:click='changeUserGroup("{{ $group->id }}", "{{ $group->name }}")'>{{ $group->name }}</a></li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li><a class="dropdown-item">No User Groups Found</a></li>
                                                    @endforelse
                                                </ul>
                                            </div>
                                        @elseif($added_role_id == 6)
                                        @else
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Groups:</label>

                                            @php
                                                $selectedSupervisorUserGroups = $selectedSupervisorUserGroups ?? [];
                                            @endphp
                                            @forelse ($selectedSupervisorUserGroups as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeUserGroup({{ $index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative">
                                                <span id="searchIcon" class="input-group-text">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control" wire:model.live.debounce.300ms="searchUserGroups" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                                @if ($searchUserGroups != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @foreach ($addSupervisorUserGroups as $userGroup)
                                                            <li class="list-group-item" wire:click="selectUserGroup_add('{{ $userGroup->id }}')" style="cursor: pointer">{{ $userGroup->name }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        @endif

                                    </div>


                                    {{-- evaluation forms are for supervisors and quality only --}}
                                    {{-- @if (in_array($added_role_id, [2, 5]))
                                        <ul>
                                            @foreach ($errors as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                        <div class="mb-3">
                                            <label for="recipient-name" class="col-form-label">Evaluation Forms:</label>
                                            @php
                                                $selectedEvaluationForms = $selectedEvaluationForms ?? [];
                                            @endphp
                                            @forelse ($selectedEvaluationForms as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeEvaluationForm_add({{ $index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative">
                                                <span id="searchIcon" class="input-group-text">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control" wire:model.live.debounce.300ms="searchEvaluationForms" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                                @if ($searchEvaluationForms != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @forelse ($evaluationForms as $form)
                                                            <li class="list-group-item" wire:click="selectEvaluationForm_add('{{ $form->id }}')" style="cursor: pointer">{{ $form->evaluation_name }}</li>
                                                        @empty
                                                            <li class="list-group-item" style="cursor: pointer">No Evaluation Forms found</li>
                                                        @endforelse
                                                    </ul>
                                                @endif
                                            </div>
                                        </div>
                                    @endif --}}



                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1s" wire:model.live="selectedUserResetNextLogin">
                                        <label class="form-check-label" for="exampleCheck1">Password Reset At Next Login</label>
                                    </div> --}}


                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="selectedUserPasswordPolicy">
                                        <label class="form-check-label" for="exampleCheck1">Password Policy</label>
                                    </div>


                                    @if ($selectedUserPasswordPolicy == true)
                                        <label class="form-check-label" for="exampleCheck1ss">Enforce Password Reset After: </label>
                                        <input type="number" class="form-control d-inline" min="1" style="width: 4rem; height:1.5rem" wire:model.live.debounce.200ms="selectedUserPasswordPolicyPeriod">
                                        <span class="fs-6">Day(s)</span>
                                    @endif --}}

                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="resetNextLoginToggle">Password Reset At Next Login</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="resetNextLoginToggle" wire:model.live="selectedUserResetNextLogin">
                                            <label for="resetNextLoginToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserResetNextLogin ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserResetNextLogin ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserResetNextLogin ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserResetNextLogin)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="passwordPolicyToggle">Password Policy</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="passwordPolicyToggle" wire:model.live="selectedUserPasswordPolicy">
                                            <label for="passwordPolicyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserPasswordPolicy ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserPasswordPolicy ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserPasswordPolicy ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserPasswordPolicy)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    @if ($selectedUserPasswordPolicy)
                                        <label class="form-check-label mt-2" for="passwordPolicyPeriodInput">Enforce Password Reset After: </label>
                                        <input type="number" class="form-control d-inline" min="1" style="width: 4rem; height:1.5rem" id="passwordPolicyPeriodInput" wire:model.live.debounce.200ms="selectedUserPasswordPolicyPeriod">
                                        <span class="fs-6">Day(s)</span>
                                    @endif


                                </form>
                            </div>


                            {{-- PARAMETERS TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'parameters') show active @endif">
                                <form wire:submit.prevent>
                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">General</legend>
                                        {{-- @if ($added_role_id != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Call ID</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Call ID" wire:click="toggleSelectedUserParams('Call ID')">
                                            </div>
                                        @endif --}}
                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="callIdToggle">Call ID</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="callIdToggle" wire:model="selectedUserParams" value="Call ID" wire:click="toggleSelectedUserParams('Call ID')">
                                                    <label for="callIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Call ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Call ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Call ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Call ID', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                        @endif


                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Duration</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Duration" wire:click="toggleSelectedUserParams('Duration')">
                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="durationToggle">Duration</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="durationToggle" wire:model="selectedUserParams" value="Duration" wire:click="toggleSelectedUserParams('Duration')">
                                                <label for="durationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Duration', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>




                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Number</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                            </div> --}}
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionNumberToggle">Interaction Number</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionNumberToggle" wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                                    <label for="interactionNumberToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Number', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Number', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Telephony</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Called ID</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Called ID" wire:click="toggleSelectedUserParams('Called ID')">
                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="calledIdToggle">Called ID</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="calledIdToggle" wire:model="selectedUserParams" value="Called ID" wire:click="toggleSelectedUserParams('Called ID')">
                                                <label for="calledIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Called ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Called ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Called ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Called ID', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>


                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Ender</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Interaction Ender" wire:click="toggleSelectedUserParams('Interaction Ender')">
                                            </div> --}}
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionEnderToggle">Interaction Ender</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionEnderToggle" wire:model="selectedUserParams" value="Interaction Ender" wire:click="toggleSelectedUserParams('Interaction Ender')">
                                                    <label for="interactionEnderToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Ender', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Ender', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Ender', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Ender', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                        @endif

                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Caller ID</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Caller ID" wire:click="toggleSelectedUserParams('Caller ID')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Hold Duration</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Hold Duration" wire:click="toggleSelectedUserParams('Hold Duration')">
                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="callerIdToggle">Caller ID</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="callerIdToggle" wire:model="selectedUserParams" value="Caller ID" wire:click="toggleSelectedUserParams('Caller ID')">
                                                <label for="callerIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Caller ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Caller ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Caller ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Caller ID', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="holdDurationToggle">Hold Duration</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="holdDurationToggle" wire:model="selectedUserParams" value="Hold Duration" wire:click="toggleSelectedUserParams('Hold Duration')">
                                                <label for="holdDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Hold Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Hold Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Hold Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Hold Duration', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>






                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Call Type</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Call Type" wire:click="toggleSelectedUserParams('Call Type')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="callTypeToggle">Call Type</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="callTypeToggle" wire:model="selectedUserParams" value="Call Type" wire:click="toggleSelectedUserParams('Call Type')">
                                                    <label for="callTypeToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Call Type', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Call Type', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Call Type', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Call Type', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Digits Count</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Digits Count" wire:click="toggleSelectedUserParams('Digits Count')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="extensionToggle">Extension</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="extensionToggle" wire:model="selectedUserParams" value="Extension" wire:click="toggleSelectedUserParams('Extension')">
                                                    <label for="extensionToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Extension', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Extension', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Extension', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Extension', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>



                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Extension</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Extension" wire:click="toggleSelectedUserParams('Extension')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Hold Count</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Hold Count" wire:click="toggleSelectedUserParams('Hold Count')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="holdCountToggle">Hold Count</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="holdCountToggle" wire:model="selectedUserParams" value="Hold Count" wire:click="toggleSelectedUserParams('Hold Count')">
                                                    <label for="holdCountToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Hold Count', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Hold Count', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Hold Count', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Hold Count', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>



                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Pause Duration</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Pause Duration" wire:click="toggleSelectedUserParams('Pause Duration')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Ring Duration</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Ring Duration" wire:click="toggleSelectedUserParams('Ring Duration')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="pauseDurationToggle">Pause Duration</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="pauseDurationToggle" wire:model="selectedUserParams" value="Pause Duration" wire:click="toggleSelectedUserParams('Pause Duration')">
                                                    <label for="pauseDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Pause Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Pause Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Pause Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Pause Duration', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="ringDurationToggle">Ring Duration</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="ringDurationToggle" wire:model="selectedUserParams" value="Ring Duration" wire:click="toggleSelectedUserParams('Ring Duration')">
                                                    <label for="ringDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Ring Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Ring Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Ring Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Ring Duration', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>




                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Screen Capture</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Screen Capture" wire:click="toggleSelectedUserParams('Screen Capture')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Server Name</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Server Name" wire:click="toggleSelectedUserParams('Server Name')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Transferred From</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Transferred From" wire:click="toggleSelectedUserParams('Transferred From')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Transferred To</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Transferred To" wire:click="toggleSelectedUserParams('Transferred To')">
                                            </div> --}}
                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Quality</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Evaluation Score</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Evaluation Score" wire:click="toggleSelectedUserParams('Evaluation Score')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Is Evaluated</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Is Evaluated" wire:click="toggleSelectedUserParams('Is Evaluated')">
                                        </div> --}}

                                        {{-- ONLY FOR DEVELOPMENT SUPERVISOR --}}
                                        @if ($added_role_id == 7)
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="evaluationScoreToggle">AI Flags</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="evaluationScoreToggle" wire:model="selectedUserParams" value="AI Flags" wire:click="toggleSelectedUserParams('AI Flags')">
                                                    <label for="evaluationScoreToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('AI Flags', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('AI Flags', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('AI Flags', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('AI Flags', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="evaluationScoreToggle">Evaluation Score</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="evaluationScoreToggle" wire:model="selectedUserParams" value="Evaluation Score" wire:click="toggleSelectedUserParams('Evaluation Score')">
                                                <label for="evaluationScoreToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Evaluation Score', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Evaluation Score', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Evaluation Score', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Evaluation Score', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="isEvaluatedToggle">Is Evaluated</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="isEvaluatedToggle" wire:model="selectedUserParams" value="Is Evaluated" wire:click="toggleSelectedUserParams('Is Evaluated')">
                                                <label for="isEvaluatedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Is Evaluated', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Is Evaluated', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Is Evaluated', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Is Evaluated', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>



                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Includes Fatal Error</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Includes Fatal Error" wire:click="toggleSelectedUserParams('Includes Fatal Error')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="includesFatalErrorToggle">Includes Fatal Error</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="includesFatalErrorToggle" wire:model="selectedUserParams" value="Includes Fatal Error" wire:click="toggleSelectedUserParams('Includes Fatal Error')">
                                                    <label for="includesFatalErrorToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Includes Fatal Error', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Is Assigned</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Is Assigned" wire:click="toggleSelectedUserParams('Is Assigned')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Is Flagged</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Is Flagged" wire:click="toggleSelectedUserParams('Is Flagged')">
                                            </div> --}}
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="isFlaggedToggle">Is Flagged</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="isFlaggedToggle" wire:model="selectedUserParams" value="Is Flagged" wire:click="toggleSelectedUserParams('Is Flagged')">
                                                    <label for="isFlaggedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Is Flagged', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Is Flagged', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Is Flagged', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Is Flagged', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>




                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Penalties</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Penalties" wire:click="toggleSelectedUserParams('Penalties')">
                                            </div> --}}
                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Users & Actions</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Agent Name</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Agent Name" wire:click="toggleSelectedUserParams('Agent Name')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Comment</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Comment" wire:click="toggleSelectedUserParams('Comment')">
                                        </div> --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="agentNameToggle">Agent Name</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="agentNameToggle" wire:model="selectedUserParams" value="Agent Name" wire:click="toggleSelectedUserParams('Agent Name')">
                                                <label for="agentNameToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Agent Name', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Agent Name', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Agent Name', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Agent Name', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="commentToggle">Comment</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="commentToggle" wire:model="selectedUserParams" value="Comment" wire:click="toggleSelectedUserParams('Comment')">
                                                <label for="commentToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Comment', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Comment', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Comment', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Comment', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Importance</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Interaction Importance" wire:click="toggleSelectedUserParams('Interaction Importance')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Custom Flag</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Custom Flag" wire:click="toggleSelectedUserParams('Custom Flag')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Group</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Group" wire:click="toggleSelectedUserParams('Group')">
                                            </div> --}}
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="groupToggle">Group</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="groupToggle" wire:model="selectedUserParams" value="Group" wire:click="toggleSelectedUserParams('Group')">
                                                    <label for="groupToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Group', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Group', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Group', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Group', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Played</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Played" wire:click="toggleSelectedUserParams('Played')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Organization</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Organization" wire:click="toggleSelectedUserParams('Organization')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Skill Group</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Skill Group" wire:click="toggleSelectedUserParams('Skill Group')">
                                            </div> --}}
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="organizationToggle">Organization</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="organizationToggle" wire:model="selectedUserParams" value="Organization" wire:click="toggleSelectedUserParams('Organization')">
                                                    <label for="organizationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Organization', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Organization', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Organization', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Organization', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="skillGroupToggle">Skill Group</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="skillGroupToggle" wire:model="selectedUserParams" value="Skill Group" wire:click="toggleSelectedUserParams('Skill Group')">
                                                    <label for="skillGroupToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Skill Group', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Skill Group', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Skill Group', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Skill Group', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>



                                        @endif
                                    </fieldset>

                                    {{-- <hr class="m-0"> --}}

                                    {{-- <fieldset class="row w-75"> --}}
                                    {{-- <legend class="col-form-label text-success">Telephony Custom Attributes</legend> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                    <label class="form-check-label" for="exampleCheck1">Agent Name</label>
                                    <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                </div> --}}
                                    {{-- </fieldset> --}}

                                </form>
                            </div>

                            {{-- PERMISSIONS TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'permissions') show active @endif">
                                <form>
                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Telephony</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Access Telephony</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Allow Access Telephony" wire:click="toggleSelectedUserPermissions('Allow Access Telephony')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Play Recorded Interactions</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Play Recorded Interactions" wire:click="toggleSelectedUserPermissions('Play Recorded Interactions')">
                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="allowAccessTelephonyToggle">Allow Access Telephony</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="allowAccessTelephonyToggle" wire:model="selectedUserPermissions" value="Allow Access Telephony" wire:click="toggleSelectedUserPermissions('Allow Access Telephony')">
                                                <label for="allowAccessTelephonyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Allow Access Telephony', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="playRecordedInteractionsToggle">Play Recorded Interactions</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="playRecordedInteractionsToggle" wire:model="selectedUserPermissions" value="Play Recorded Interactions" wire:click="toggleSelectedUserPermissions('Play Recorded Interactions')">
                                                <label for="playRecordedInteractionsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Play Recorded Interactions', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>




                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Download Audio Files</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Download Audio Files" wire:click="toggleSelectedUserPermissions('Download Audio Files')">

                                            </div> --}}
                                        @endif
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Show Evaluated Interactions Only</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Show Evaluated Interactions Only" wire:click="toggleSelectedUserPermissions('Show Evaluated Interactions Only')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Other Users Interactions Comments</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="View Other Users Interactions Comments" wire:click="toggleSelectedUserPermissions('View Other Users Interactions Comments')">

                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="showEvaluatedInteractionsOnlyToggle">Show Evaluated Interactions Only</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="showEvaluatedInteractionsOnlyToggle" wire:model="selectedUserPermissions" value="Show Evaluated Interactions Only" wire:click="toggleSelectedUserPermissions('Show Evaluated Interactions Only')">
                                                <label for="showEvaluatedInteractionsOnlyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Show Evaluated Interactions Only', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewOtherUsersInteractionsCommentsToggle">View Other Users Interactions Comments</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewOtherUsersInteractionsCommentsToggle" wire:model="selectedUserPermissions" value="View Other Users Interactions Comments" wire:click="toggleSelectedUserPermissions('View Other Users Interactions Comments')">
                                                <label for="viewOtherUsersInteractionsCommentsToggle" class="custom-switch-label position-relative d-inline-block"
                                                    style="width: 44px; height: 24px; background-color: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Other Users Interactions Comments', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>




                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Search By Deleted Interactions</label>
                                                                                        <input type="checkbox" class="form-check-input"  {{ in_array('Skill Group', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Skill Group" wire:click="toggleSelectedUserPermissions('Skill Group')">

                                        </div> --}}
                                    </fieldset>

                                    <hr class="m-0">

                                    @if ($added_role_id != 4)
                                        {{-- <fieldset class="row w-75">
                                        <legend class="col-form-label text-success">General Quality</legend>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Search By Evaluation Form</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Allow Search By Evaluation Form" wire:click="toggleSelectedUserPermissions('Allow Search By Evaluation Form')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Listen Own Session Recordings</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Listen Own Session Recordings" wire:click="toggleSelectedUserPermissions('Listen Own Session Recordings')">

                                        </div>
                                    </fieldset>
                                    <hr class="m-0"> --}}
                                    @endif

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Telephony Quality</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Interactions Evaluations</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="View Interactions Evaluations" wire:click="toggleSelectedUserPermissions('View Interactions Evaluations')">

                                        </div> --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewInteractionsEvaluationsToggle">View Interactions Evaluations</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewInteractionsEvaluationsToggle" wire:model="selectedUserPermissions" value="View Interactions Evaluations" wire:click="toggleSelectedUserPermissions('View Interactions Evaluations')">
                                                <label for="viewInteractionsEvaluationsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Interactions Evaluations', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($added_role_id != 4 && $added_role_id != 6)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Create Interaction Evaluations</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Create Interaction Evaluations" wire:click="toggleSelectedUserPermissions('Create Interaction Evaluations')">

                                            </div> --}}
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="createInteractionEvaluationsToggle">Create Interaction Evaluations</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="createInteractionEvaluationsToggle" wire:model="selectedUserPermissions" value="Create Interaction Evaluations" wire:click="toggleSelectedUserPermissions('Create Interaction Evaluations')">
                                                    <label for="createInteractionEvaluationsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Create Interaction Evaluations', $selectedUserPermissions))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row ">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Reports</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Evaluation Reports</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="View Evaluation Reports" wire:click="toggleSelectedUserPermissions('View Evaluation Reports')">
                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewEvaluationReportsToggle">View Evaluation Reports</label>

                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewEvaluationReportsToggle" wire:model="selectedUserPermissions" value="View Evaluation Reports" wire:click="toggleSelectedUserPermissions('View Evaluation Reports')">
                                                <label for="viewEvaluationReportsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Evaluation Reports', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                    </fieldset>

                                </form>
                            </div>

                        </div>
                    </div>


                    <div class="modal-footer bg-white">
                        <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="closeAdd">Close</button>
                        <button type="button" class="btn btn-md btn-success modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="submitAddUser">Apply</button>
                    </div>
                </div>
            </div>
        </div>

        {{-- Add multiple users Modal --}}
        <div class="modal fade" id="add-multi" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header text-white border-0" style="background-color: #01A44F">
                        <img src="{{ asset('assets/SVG/assets-v2/87 - green.svg') }}" alt="" class="rounded rounded-5 p-3 me-2" style="width:4rem; aspect-ratio:1/1; background-color: #e9ecef">
                        <h5 class="modal-title" id="staticBackdropLabel">Add Multiple Users</h5>
                        <button type="button" class="btn-close btn-success" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                    </div>

                    {{-- <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'general') active @endif" wire:click="$set('activeTab', 'general')" style="cursor: pointer">General</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'parameters') active @endif" wire:click="$set('activeTab', 'parameters')" style="cursor: pointer">Parameters</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-success @if ($activeTab === 'permissions') active @endif" wire:click="$set('activeTab', 'permissions')" style="cursor: pointer">Permissions</a>
                        </li>
                    </ul> --}}

                    <div class="d-flex gap-3 ms-3" style="background-color: transparent; border: none; padding: 10px;">
                        <button class="btn btn-outline-success @if ($activeTab === 'general') active @endif tab-button" wire:click="$set('activeTab', 'general')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            General
                        </button>
                        <button class="btn btn-outline-success @if ($activeTab === 'parameters') active @endif tab-button" wire:click="$set('activeTab', 'parameters')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            Parameters
                        </button>
                        <button class="btn btn-outline-success @if ($activeTab === 'permissions') active @endif tab-button" wire:click="$set('activeTab', 'permissions')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                            Permissions
                        </button>
                    </div>


                    <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">

                        <div class="tab-content">
                            {{-- GENERAL TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'general') show active @endif">
                                <form wire:submit.prevent enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Role:</label>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                {{ $added_role }}
                                            </button>
                                            <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                <li><a class="dropdown-item" wire:click="addRole('1')">Admin</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('2')">Supervisor</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('5')">Quality</a></li>
                                                <hr class="m-0">
                                                {{-- <li><a class="dropdown-item" wire:click="addRole('3')">IT</a></li>
                                                <hr class="m-0"> --}}
                                                <li><a class="dropdown-item" wire:click="addRole('4')">Agent</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="addRole('6')">Client/TL</a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="mb-3">

                                        {{-- the agent can have only one org  --}}
                                        @if ($added_role_id == 4 || $added_role_id == 6)
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organization:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    {{ $selectedUserOrganizationName == '' ? '--' : $selectedUserOrganizationName }}
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;">
                                                    @forelse ($organizations as $org)
                                                        <li><a class="dropdown-item" wire:click="changeOrg('{{ $org->id }}')">{{ $org->name }}</a></li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li><a class="dropdown-item">No Organizations Found</a></li>
                                                    @endforelse
                                                </ul>
                                            </div>
                                            {{-- for non agents - multi orgs  --}}
                                        @else
                                            <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Organizations:</label>

                                            @php
                                                $selectedSupervisorOrgs = $selectedSupervisorOrgs ?? [];
                                            @endphp
                                            {{-- @forelse ($selectedSupervisorOrgs as $index => $item) --}}
                                            @forelse ($addSupervisorOrganizations as $index => $item)
                                                <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                    {{ $item }}
                                                    <button class="btn-close btn-close-white" wire:click="removeOrg({{ $index }})"></button>
                                                </span>
                                            @empty
                                            @endforelse

                                            <div class="input-group relative">
                                                <span id="searchIcon" class="input-group-text">
                                                    <i class="fa-solid fa-magnifying-glass"></i>
                                                </span>
                                                <input type="text" class="form-control" wire:model.live.debounce.300ms="searchOrganizations" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                                @if ($searchOrganizations != '')
                                                    <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                        @foreach ($organizations as $org)
                                                            <li class="list-group-item" wire:click="selectOrg('{{ $org->id }}')" style="cursor: pointer">{{ $org->name }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        @endif

                                    </div>




                                    @if ($added_role_id != 6)
                                        <div class="mb-3">
                                            {{-- the agent can have only one user group  --}}
                                            @if ($added_role_id == 4)
                                                <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Group:</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $selectedUserUserGroups == '' ? '--' : $selectedUserUserGroups }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; position: absolute; overflow:auto;z-index:999999">
                                                        @forelse ($userGroups as $group)
                                                            <li><a class="dropdown-item" wire:click='changeUserGroup("{{ $group->id }}", "{{ $group->name }}")'>{{ $group->name }}</a></li>
                                                            @if (!$loop->last)
                                                                <hr class="m-0">
                                                            @endif
                                                        @empty
                                                            <li><a class="dropdown-item">No User Groups Found</a></li>
                                                        @endforelse
                                                    </ul>
                                                </div>
                                            @else
                                                <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">User Groups:</label>

                                                @php
                                                    $selectedSupervisorUserGroups = $selectedSupervisorUserGroups ?? [];
                                                @endphp
                                                @forelse ($selectedSupervisorUserGroups as $index => $item)
                                                    <span class="badge me-2 mb-1" style="background-color: #01A44F">
                                                        {{ $item }}
                                                        <button class="btn-close btn-close-white" wire:click="removeUserGroup({{ $index }})"></button>
                                                    </span>
                                                @empty
                                                @endforelse

                                                <div class="input-group relative">
                                                    <span id="searchIcon" class="input-group-text">
                                                        <i class="fa-solid fa-magnifying-glass"></i>
                                                    </span>
                                                    <input type="text" class="form-control" wire:model.live.debounce.300ms="searchUserGroups" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                                    @if ($searchUserGroups != '')
                                                        <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                            @foreach ($addSupervisorUserGroups as $userGroup)
                                                                <li class="list-group-item" wire:click="selectUserGroup('{{ $userGroup->id }}')" style="cursor: pointer">{{ $userGroup->name }}</li>
                                                            @endforeach
                                                        </ul>
                                                    @endif
                                                </div>
                                            @endif

                                        </div>

                                    @endif


                                    {{-- evaluation forms are for supervisors only --}}
                                    {{-- @if ($added_role_id == 2)
                                        <div class="mb-3">
                                            <label for="recipient-name" class="col-form-label">Evaluation Form:</label>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                    @if ($role)
                                                        @if ($role == 1)
                                                            Admin
                                                        @elseif ($role == 2)
                                                            Supervisor
                                                        @endif
                                                    @else
                                                        {{ $added_role_id == 1 ? 'Admin' : 'Supervisor' }}
                                                    @endif
                                                </button>
                                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                                    <hr class="m-0">
                                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    @endif --}}

                                    {{-- <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Upload Users:</label>
                                        <a href="{{ asset('test.xlsx') }}" download="{{ asset('test.xlsx') }}" style="color:inherit;text-decoration: none;">
                                            <i class="fa-solid fa-file-excel fs-5" style="cursor: pointer;color:#01A44F" title="Download Example"></i>
                                        </a>
                                        <input type="file" class="form-control" id="exampleCheck1s" wire:model="importFile">
                                        @error('importFile')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div> --}}
                                    <div class="mb-3">
                                        <label for="recipient-name" class="col-form-label" style="color: #40798C; font-weight:300 !important;">Upload Users:</label>
                                        <a href="{{ asset('test.xlsx') }}" download="{{ asset('test.xlsx') }}" style="color:inherit;text-decoration: none;">
                                            <i class="fa-solid fa-file-excel fs-5" style="cursor: pointer;color:#01A44F" title="Download Example"></i>
                                        </a>
                                        <div class="custom-file-input-wrapper">
                                            <input type="file" class="custom-file-input" id="exampleCheck1s" wire:model="importFile">
                                            <label for="exampleCheck1s" class="custom-file-label">Choose File</label>
                                        </div>
                                        @error('importFile')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>



                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1s" wire:model.live="selectedUserTerminated">
                                        <label class="form-check-label" for="exampleCheck1">Terminated</label>
                                    </div> --}}

                                    {{-- <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1s" wire:model.live="selectedUserResetNextLogin">
                                        <label class="form-check-label" for="exampleCheck1">Password Reset At Next Login</label>
                                    </div>

                                    <div class="mb-3 form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="selectedUserPasswordPolicy">
                                        <label class="form-check-label" for="exampleCheck1">Password Policy</label>
                                    </div>


                                    @if ($selectedUserPasswordPolicy == true)
                                        <label class="form-check-label" for="exampleCheck1ss">Enforce Password Reset After: </label>
                                        <input type="number" class="form-control d-inline" min="1" style="width: 4rem; height:1.5rem" wire:model.live.debounce.200ms="selectedUserPasswordPolicyPeriod">
                                        <span class="fs-6">Day(s)</span>
                                    @endif --}}
                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="resetNextLoginToggle">Password Reset At Next Login</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="resetNextLoginToggle" wire:model.live="selectedUserResetNextLogin">
                                            <label for="resetNextLoginToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserResetNextLogin ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserResetNextLogin ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserResetNextLogin ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserResetNextLogin)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3 d-flex align-items-center gap-2 mt-4 justify-content-between">
                                        <label class="form-check-label" for="passwordPolicyToggle">Password Policy</label>

                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="passwordPolicyToggle" wire:model.live="selectedUserPasswordPolicy">
                                            <label for="passwordPolicyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selectedUserPasswordPolicy ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ $selectedUserPasswordPolicy ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ $selectedUserPasswordPolicy ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                    @if ($selectedUserPasswordPolicy)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                            <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    @if ($selectedUserPasswordPolicy)
                                        <label class="form-check-label" for="passwordPolicyPeriod">Enforce Password Reset After: </label>
                                        <input type="number" class="form-control d-inline" min="1" style="width: 4rem; height:1.5rem" wire:model.live.debounce.200ms="selectedUserPasswordPolicyPeriod">
                                        <span class="fs-6">Day(s)</span>
                                    @endif


                                </form>
                            </div>


                            {{-- PARAMETERS TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'parameters') show active @endif">
                                <form wire:submit.prevent>
                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">General</legend>
                                        {{-- @if ($added_role_id != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Call ID</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Call ID" wire:click="toggleSelectedUserParams('Call ID')">
                                            </div>
                                        @endif --}}

                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="callIdToggle">Call ID</label>

                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="callIdToggle" wire:model="selectedUserParams" value="Call ID" wire:click="toggleSelectedUserParams('Call ID')">
                                                    <label for="callIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Call ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Call ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Call ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Call ID', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif

                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Duration</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Duration" wire:click="toggleSelectedUserParams('Duration')">

                                        </div>


                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Number</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                            </div>
                                        @endif --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="durationToggle">Duration</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="durationToggle" wire:model="selectedUserParams" value="Duration" wire:click="toggleSelectedUserParams('Duration')">
                                                <label for="durationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Duration', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionNumberToggle">Interaction Number</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionNumberToggle" wire:model="selectedUserParams" value="Interaction Number" wire:click="toggleSelectedUserParams('Interaction Number')">
                                                    <label for="interactionNumberToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Number', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Number', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Number', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif

                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Telephony</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Called ID</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Called ID" wire:click="toggleSelectedUserParams('Called ID')">
                                        </div>
                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Ender</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Interaction Ender" wire:click="toggleSelectedUserParams('Interaction Ender')">
                                            </div>
                                        @endif --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="calledIdToggle">Called ID</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="calledIdToggle" wire:model="selectedUserParams" value="Called ID" wire:click="toggleSelectedUserParams('Called ID')">
                                                <label for="calledIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Called ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Called ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Called ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Called ID', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="interactionEnderToggle">Interaction Ender</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="interactionEnderToggle" wire:model="selectedUserParams" value="Interaction Ender" wire:click="toggleSelectedUserParams('Interaction Ender')">
                                                    <label for="interactionEnderToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Interaction Ender', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Interaction Ender', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Interaction Ender', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Interaction Ender', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif

                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Caller ID</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Caller ID" wire:click="toggleSelectedUserParams('Caller ID')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Hold Duration</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Hold Duration" wire:click="toggleSelectedUserParams('Hold Duration')">
                                        </div> --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="callerIdToggle">Caller ID</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="callerIdToggle" wire:model="selectedUserParams" value="Caller ID" wire:click="toggleSelectedUserParams('Caller ID')">
                                                <label for="callerIdToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Caller ID', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Caller ID', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Caller ID', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Caller ID', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="holdDurationToggle">Hold Duration</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="holdDurationToggle" wire:model="selectedUserParams" value="Hold Duration" wire:click="toggleSelectedUserParams('Hold Duration')">
                                                <label for="holdDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Hold Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Hold Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Hold Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Hold Duration', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        {{-- /////////////////////// --}}
                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Call Type</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Call Type" wire:click="toggleSelectedUserParams('Call Type')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="callTypeToggle">Call Type</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="callTypeToggle" wire:model="selectedUserParams" value="Call Type" wire:click="toggleSelectedUserParams('Call Type')">
                                                    <label for="callTypeToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Call Type', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Call Type', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Call Type', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Call Type', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Digits Count</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Digits Count" wire:click="toggleSelectedUserParams('Digits Count')">
                                            </div> --}}



                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Extension</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Extension" wire:click="toggleSelectedUserParams('Extension')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Hold Count</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Hold Count" wire:click="toggleSelectedUserParams('Hold Count')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Pause Duration</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Pause Duration" wire:click="toggleSelectedUserParams('Pause Duration')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Ring Duration</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Ring Duration" wire:click="toggleSelectedUserParams('Ring Duration')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="extensionToggle">Extension</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="extensionToggle" wire:model="selectedUserParams" value="Extension" wire:click="toggleSelectedUserParams('Extension')">
                                                    <label for="extensionToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Extension', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Extension', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Extension', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Extension', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="holdCountToggle">Hold Count</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="holdCountToggle" wire:model="selectedUserParams" value="Hold Count" wire:click="toggleSelectedUserParams('Hold Count')">
                                                    <label for="holdCountToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Hold Count', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Hold Count', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Hold Count', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Hold Count', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="pauseDurationToggle">Pause Duration</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="pauseDurationToggle" wire:model="selectedUserParams" value="Pause Duration" wire:click="toggleSelectedUserParams('Pause Duration')">
                                                    <label for="pauseDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Pause Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Pause Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Pause Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Pause Duration', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="ringDurationToggle">Ring Duration</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="ringDurationToggle" wire:model="selectedUserParams" value="Ring Duration" wire:click="toggleSelectedUserParams('Ring Duration')">
                                                    <label for="ringDurationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Ring Duration', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Ring Duration', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Ring Duration', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Ring Duration', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Screen Capture</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Screen Capture" wire:click="toggleSelectedUserParams('Screen Capture')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Server Name</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Server Name" wire:click="toggleSelectedUserParams('Server Name')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Transferred From</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Transferred From" wire:click="toggleSelectedUserParams('Transferred From')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Transferred To</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Transferred To" wire:click="toggleSelectedUserParams('Transferred To')">
                                            </div> --}}
                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Quality</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Evaluation Score</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Evaluation Score" wire:click="toggleSelectedUserParams('Evaluation Score')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Is Evaluated</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Is Evaluated" wire:click="toggleSelectedUserParams('Is Evaluated')">
                                        </div> --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="evaluationScoreToggle">Evaluation Score</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="evaluationScoreToggle" wire:model="selectedUserParams" value="Evaluation Score" wire:click="toggleSelectedUserParams('Evaluation Score')">
                                                <label for="evaluationScoreToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Evaluation Score', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Evaluation Score', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Evaluation Score', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Evaluation Score', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="isEvaluatedToggle">Is Evaluated</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="isEvaluatedToggle" wire:model="selectedUserParams" value="Is Evaluated" wire:click="toggleSelectedUserParams('Is Evaluated')">
                                                <label for="isEvaluatedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Is Evaluated', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Is Evaluated', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Is Evaluated', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Is Evaluated', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>


                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Includes Fatal Error</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Includes Fatal Error" wire:click="toggleSelectedUserParams('Includes Fatal Error')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="includesFatalErrorToggle">Includes Fatal Error</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="includesFatalErrorToggle" wire:model="selectedUserParams" value="Includes Fatal Error" wire:click="toggleSelectedUserParams('Includes Fatal Error')">
                                                    <label for="includesFatalErrorToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Includes Fatal Error', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Includes Fatal Error', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>




                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Is Assigned</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Is Assigned" wire:click="toggleSelectedUserParams('Is Assigned')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Is Flagged</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Is Flagged" wire:click="toggleSelectedUserParams('Is Flagged')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="isFlaggedToggle">Is Flagged</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="isFlaggedToggle" wire:model="selectedUserParams" value="Is Flagged" wire:click="toggleSelectedUserParams('Is Flagged')">
                                                    <label for="isFlaggedToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Is Flagged', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Is Flagged', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Is Flagged', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Is Flagged', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>



                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Penalties</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Penalties" wire:click="toggleSelectedUserParams('Penalties')">
                                            </div> --}}
                                        @endif
                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label text-success" style="color: #40798C !important; font-weight:300 !important;">Users & Actions</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Agent Name</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Agent Name" wire:click="toggleSelectedUserParams('Agent Name')">
                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Comment</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Comment" wire:click="toggleSelectedUserParams('Comment')">
                                        </div> --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="agentNameToggle">Agent Name</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="agentNameToggle" wire:model="selectedUserParams" value="Agent Name" wire:click="toggleSelectedUserParams('Agent Name')">
                                                <label for="agentNameToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Agent Name', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Agent Name', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Agent Name', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Agent Name', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="commentToggle">Comment</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="commentToggle" wire:model="selectedUserParams" value="Comment" wire:click="toggleSelectedUserParams('Comment')">
                                                <label for="commentToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Comment', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Comment', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Comment', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Comment', $selectedUserParams))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>


                                        @if ($added_role_id != 4)
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Interaction Importance</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Interaction Importance" wire:click="toggleSelectedUserParams('Interaction Importance')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Custom Flag</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Custom Flag" wire:click="toggleSelectedUserParams('Custom Flag')">
                                            </div> --}}
                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Group</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Group" wire:click="toggleSelectedUserParams('Group')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="groupToggle">Group</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="groupToggle" wire:model="selectedUserParams" value="Group" wire:click="toggleSelectedUserParams('Group')">
                                                    <label for="groupToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Group', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Group', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Group', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Group', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>


                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Played</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Played" wire:click="toggleSelectedUserParams('Played')">
                                            </div> --}}

                                            {{-- <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Organization</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Organization" wire:click="toggleSelectedUserParams('Organization')">
                                            </div>
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Skill Group</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserParams" value="Skill Group" wire:click="toggleSelectedUserParams('Skill Group')">
                                            </div> --}}

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="organizationToggle">Organization</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="organizationToggle" wire:model="selectedUserParams" value="Organization" wire:click="toggleSelectedUserParams('Organization')">
                                                    <label for="organizationToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Organization', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Organization', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Organization', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Organization', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="skillGroupToggle">Skill Group</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="skillGroupToggle" wire:model="selectedUserParams" value="Skill Group" wire:click="toggleSelectedUserParams('Skill Group')">
                                                    <label for="skillGroupToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Skill Group', $selectedUserParams) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute" style="width: 18px; height: 18px; background-color: {{ in_array('Skill Group', $selectedUserParams) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Skill Group', $selectedUserParams) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Skill Group', $selectedUserParams))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>

                                        @endif
                                    </fieldset>

                                    {{-- <hr class="m-0"> --}}

                                    {{-- <fieldset class="row w-75"> --}}
                                    {{-- <legend class="col-form-label text-success">Telephony Custom Attributes</legend> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                    <label class="form-check-label" for="exampleCheck1">Agent Name</label>
                                    <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                </div> --}}
                                    {{-- </fieldset> --}}

                                </form>
                            </div>

                            {{-- PERMISSIONS TAB  --}}
                            <div class="tab-pane @if ($activeTab === 'permissions') show active @endif">
                                <form>
                                    <fieldset class="row">
                                        <legend class="col-form-label">Telephony</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Access Telephony</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Allow Access Telephony" wire:click="toggleSelectedUserPermissions('Allow Access Telephony')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Play Recorded Interactions</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Play Recorded Interactions" wire:click="toggleSelectedUserPermissions('Play Recorded Interactions')">

                                        </div> --}}

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="allowAccessTelephonyToggle">Allow Access Telephony</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="allowAccessTelephonyToggle" wire:model="selectedUserPermissions" value="Allow Access Telephony" wire:click="toggleSelectedUserPermissions('Allow Access Telephony')">
                                                <label for="allowAccessTelephonyToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Allow Access Telephony', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Allow Access Telephony', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="playRecordedInteractionsToggle">Play Recorded Interactions</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="playRecordedInteractionsToggle" wire:model="selectedUserPermissions" value="Play Recorded Interactions" wire:click="toggleSelectedUserPermissions('Play Recorded Interactions')">
                                                <label for="playRecordedInteractionsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Play Recorded Interactions', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Play Recorded Interactions', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Download Audio Files</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Download Audio Files" wire:click="toggleSelectedUserPermissions('Download Audio Files')">

                                        </div> --}}
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Show Evaluated Interactions Only</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Show Evaluated Interactions Only" wire:click="toggleSelectedUserPermissions('Show Evaluated Interactions Only')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Other Users Interactions Comments</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="View Other Users Interactions Comments" wire:click="toggleSelectedUserPermissions('View Other Users Interactions Comments')">

                                        </div> --}}


                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="showEvaluatedInteractionsToggle">Show Evaluated Interactions Only</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="showEvaluatedInteractionsToggle" wire:model="selectedUserPermissions" value="Show Evaluated Interactions Only" wire:click="toggleSelectedUserPermissions('Show Evaluated Interactions Only')">
                                                <label for="showEvaluatedInteractionsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Show Evaluated Interactions Only', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('Show Evaluated Interactions Only', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewOtherUsersCommentsToggle">View Other Users Interactions Comments</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewOtherUsersCommentsToggle" wire:model="selectedUserPermissions" value="View Other Users Interactions Comments" wire:click="toggleSelectedUserPermissions('View Other Users Interactions Comments')">
                                                <label for="viewOtherUsersCommentsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Other Users Interactions Comments', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Other Users Interactions Comments', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>


                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Search By Deleted Interactions</label>
                                                                                        <input type="checkbox" class="form-check-input"  {{ in_array('Skill Group', $selectedUserPermissions) ? 'checked' : '' }} wire:model="selectedUserPermissions" value="Skill Group" wire:click="toggleSelectedUserPermissions('Skill Group')">

                                        </div> --}}
                                    </fieldset>

                                    <hr class="m-0">

                                    {{-- <fieldset class="row w-75">
                                        <legend class="col-form-label">General Quality</legend>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Allow Search By Evaluation Form</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Allow Search By Evaluation Form" wire:click="toggleSelectedUserPermissions('Allow Search By Evaluation Form')">

                                        </div>
                                        <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">Listen Own Session Recordings</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Listen Own Session Recordings" wire:click="toggleSelectedUserPermissions('Listen Own Session Recordings')">

                                        </div>
                                    </fieldset>

                                    <hr class="m-0"> --}}

                                    <fieldset class="row">
                                        <legend class="col-form-label">Telephony Quality</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Interactions Evaluations</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="View Interactions Evaluations" wire:click="toggleSelectedUserPermissions('View Interactions Evaluations')">

                                        </div>
                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-3 form-check">
                                                <label class="form-check-label" for="exampleCheck1">Create Interaction Evaluations</label>
                                                <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="Create Interaction Evaluations" wire:click="toggleSelectedUserPermissions('Create Interaction Evaluations')">

                                            </div>
                                        @endif --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewInteractionsEvaluationsToggle">View Interactions Evaluations</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewInteractionsEvaluationsToggle" wire:model="selectedUserPermissions" value="View Interactions Evaluations" wire:click="toggleSelectedUserPermissions('View Interactions Evaluations')">
                                                <label for="viewInteractionsEvaluationsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Interactions Evaluations', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Interactions Evaluations', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @if ($added_role_id != 4)
                                            <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                                <label class="form-check-label" for="createInteractionEvaluationsToggle">Create Interaction Evaluations</label>
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="createInteractionEvaluationsToggle" wire:model="selectedUserPermissions" value="Create Interaction Evaluations" wire:click="toggleSelectedUserPermissions('Create Interaction Evaluations')">
                                                    <label for="createInteractionEvaluationsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="width: 18px; height: 18px; background-color: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('Create Interaction Evaluations', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                            @if (in_array('Create Interaction Evaluations', $selectedUserPermissions))
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif


                                    </fieldset>

                                    <hr class="m-0">

                                    <fieldset class="row">
                                        <legend class="col-form-label">Reports</legend>
                                        {{-- <div class="mb-3 ms-3 form-check">
                                            <label class="form-check-label" for="exampleCheck1">View Evaluation Reports</label>
                                            <input type="checkbox" class="form-check-input" wire:model="selectedUserPermissions" value="View Evaluation Reports" wire:click="toggleSelectedUserPermissions('View Evaluation Reports')">

                                        </div> --}}
                                        <div class="mb-3 ms-0 d-flex align-items-center gap-2 justify-content-between">
                                            <label class="form-check-label" for="viewEvaluationReportsToggle">View Evaluation Reports</label>
                                            <div class="position-relative">
                                                <input type="checkbox" class="d-none" id="viewEvaluationReportsToggle" wire:model="selectedUserPermissions" value="View Evaluation Reports" wire:click="toggleSelectedUserPermissions('View Evaluation Reports')">
                                                <label for="viewEvaluationReportsToggle" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                    <div class="switch-handle position-absolute"
                                                        style="width: 18px; height: 18px; background-color: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '#ffffff' : '#FF5E60' }}; border-radius: 50%; top: 3px; left: {{ in_array('View Evaluation Reports', $selectedUserPermissions) ? '22px' : '3px' }}; transition: left 0.3s, background-color 0.3s;">
                                                        @if (in_array('View Evaluation Reports', $selectedUserPermissions))
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M10.0915 0.951972L9.42927 0.939309L4.16201 6.22962L1.58507 3.63469L0.932892 4.29224L3.58046 6.95832L4.71406 6.9584L10.0468 1.60234L10.0915 0.951972Z" fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                            </svg>
                                                        @else
                                                            <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        @endif
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                    </fieldset>

                                </form>
                            </div>

                        </div>
                    </div>


                    <div class="modal-footer bg-white">
                        <button type="button" class="btn btn-outline-secondary modal-apply-close-btns modal-close" data-bs-dismiss="modal" wire:click="clear" id="closeAddMulti">Close</button>
                        <button type="button" class="btn btn-md btn-success modal-apply-close-btns modal-apply" style="background-color: #01A44F" wire:click="submitAddMultiUsers">Apply</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
