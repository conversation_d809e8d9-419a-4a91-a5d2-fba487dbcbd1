<?php

namespace Database\Seeders;

use App\Models\Parameter;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ParameterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            'Call ID',
            'Duration',
            'Interaction Number',
            'Called ID',
            'Interaction Ender',
            'Caller ID',
            'Hold Duration',
            'Call Type',
            'Digits Count',
            'Extension',
            'Hold Count',
            'Pause Duration',
            'Ring Duration',
            'Screen Capture',
            'Server Name',
            'Transferred From',
            'Transferred To',
            'Quality',
            'Evaluation Score',
            'Includes Fatal Error',
            'Is Assigned',
            'Is Evaluated',
            'Is Flagged',
            'Penalties',
            'Agent Name',
            'Interaction Importance',
            'Comment',
            'Custom Flag',
            'Group',
            'Played',
            'Organization',
            'Skill Group',
            'AI Flags',
        ];

        foreach ($permissions as $permission) {
            Parameter::create(['name' => $permission]);
        }
    }
}
