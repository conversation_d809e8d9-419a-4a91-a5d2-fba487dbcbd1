<?php
namespace App\Http\Controllers;

use App\Jobs\ProcessIteractionsJob;
use App\Jobs\Scripts\ClassificationJob;
use App\Jobs\Scripts\CorrectorJob;
use App\Jobs\Scripts\DetectJob;
use App\Jobs\Scripts\DetectScriptJob;
use App\Jobs\Scripts\TopicsJob;
use App\Mail\ScriptsMail;
use App\Models\CallSummary;
use App\Models\Interaction;
use App\Models\TranscriptionBadWord;
use App\Models\TranscriptionClassifications;
use App\Models\TranscriptionScriptsWord;
use App\Services\OpenAISummarizationService;
use Illuminate\Bus\Batch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ScriptController extends Controller
{

    public function index($call_id)
    {

        try {
            //$callConversation = DB::table('calls_transcription')->orderBy('duration_from', 'asc')->where('call_id', $call_id)->get();
            $callConversation = DB::table('calls_transcription')
                ->where('call_id', $call_id)
                ->where('content', 'not like', '%القناة%')
                ->where('content', 'not like', '%ترجمة نانسي قنقر%')
                ->where(function ($query) {
                    $query->where('avg_logprob', '>=', 0)
                        ->orWhere('no_speech_prob', '<=', 0.13)
                        ->orWhere('compression_ratio', '<=', 8.055);
                })
                ->orderBy('duration_from', 'asc')
                ->orderBy('duration_to', 'asc')
                ->get();

            $filtered = new Collection();
            foreach ($callConversation as $row) {
                $key = $row->source . '|' . trim($row->content);

                if (! isset($seenKeys[$key])) {
                    $filtered->push($row);  // Keep the first occurrence
                    $seenKeys[$key] = true; // Mark as seen
                }
                // else → skip all repeats
            }

            $Interaction = Interaction::where('call_id', 'like', '%' . $call_id . '%')->first();
            //$Interaction = Interaction::where('call_id', $call_id)->first();

            $call_type       = $Interaction ? $Interaction->call_type : null;
            $preferredSource = $call_type === 'Inbound' ? 'right' : ($call_type === 'Outbound' ? 'left' : null);
            $lang          = $Interaction ? $Interaction->language : 'Arabic';
            $filtered2 = new Collection();
            $seenKeys2 = [];

            foreach ($callConversation as $row) {
                if ($preferredSource !== null && $row->source !== $preferredSource) {
                    continue;
                }

                $key = $row->source . '|' . trim($row->content);

                if (! isset($seenKeys2[$key])) {
                    $filtered2->push($row);
                    $seenKeys2[$key] = true;
                }
            }

            $total = $callConversation->count();

            $firstFive = new Collection();
            $lastFive  = new Collection();

            if ($filtered2->isNotEmpty()) {
                $firstFive = $filtered2->take(3);
                $lastFive  = $filtered2->slice(-3);
            }
            // $Interaction = Interaction::where('call_id','like','%'.$this->callConversation[0]->call_id.'%')->first();

            try {
                //code...
                TranscriptionBadWord::where('call_id', 'like', '%' . $call_id . '%')->delete();
            } catch (\Throwable $th) {
                //throw $th;.
                Log::channel('detect')->critical("TranscriptionBadWord  " . $e->getMessage());

            }
            try {
                TranscriptionScriptsWord::where('call_id', 'like', '%' . $call_id . '%')->delete();
            } catch (\Throwable $th) {
                //throw $th;.
                Log::channel('detect')->critical("TranscriptionScriptsWord  " . $e->getMessage());

            }
            try {
                TranscriptionClassifications::where('call_id', 'like', '%' . $call_id . '%')->delete();
            } catch (\Throwable $th) {
                //throw $th;.
                Log::channel('detect')->critical("TranscriptionClassifications  " . $e->getMessage());

            }
        } catch (\Throwable $e) {
            Log::channel('detect')->critical("💥 Job crashed for call_id 123{$call_id}: " . $e->getMessage());

        }
        $Interaction = Interaction::where('call_id', 'like', '%' . $call_id . '%')->first();

        if ($Interaction) {
            $Interaction->update(['topics_flag' => 0, 'ai_flag' => 0]);
        }

        if ($callConversation->count()) {
            try {
                //code...

                $this->detectScript($firstFive, $call_id, 'Greeting');
                $this->detectScript($lastFive, $call_id, 'Closure');
                $this->detect($callConversation, $call_id);

                $this->topics($callConversation, $call_id, $lang);
                $this->classification($filtered, $call_id);
            } catch (\Throwable $th) {
                //throw $th;
                Log::channel('interactions')->critical("💥 Job crashed for call_id " . $th->getMessage());

            }

            try {
                ProcessIteractionsJob::dispatch($call_id);
            } catch (\Throwable $e) {
                Log::channel('interactions')->critical("💥 Job crashed for call_id {$call_id}: " . $e->getMessage());

            }
            try {
                //code...
                $lan        = $Interaction->language == 'English' ? 'en' : 'ar';
                $summaryRes = app(OpenAISummarizationService::class)->summarizeFromCallId($call_id, $lan);

                $lines   = preg_split("/\r\n|\n|\r/", $summaryRes);
                $type    = 'غير معروف';
                $subtype = 'غير معروف';

                foreach (array_reverse($lines) as $line) {
                    if (
                        stripos($line, 'Type:') === 0 || stripos($line, 'النوع:') === 0 ||
                        stripos($line, 'التصنيف الفرعي:') === 0 || stripos($line, 'Subtype:') === 0
                    ) {
                        Log::debug("Matched line: " . $line);

                        $cleanLine = trim($line);
                        $cleanLine = rtrim($cleanLine, " .،،٫؛:");
                        $cleanLine = preg_replace('/[–—ـ−‑‒﹘﹣－]/u', '-', $cleanLine);

                        Log::debug("Normalized line: " . $cleanLine);

                        // Arabic full pattern: النوع: ... - التصنيف الفرعي: ...
                        if (preg_match('/النوع:\s*(.*?)\s*-\s*التصنيف الفرعي:\s*(.*)/u', $cleanLine, $matches)) {
                            $type    = trim($matches[1]);
                            $subtype = trim($matches[2]);
                            Log::debug("Extracted Arabic type: $type, subtype: $subtype");

                            // English full pattern: Type: ... - Subtype: ...
                        } elseif (preg_match('/Type:\s*(.*?)\s*-\s*Subtype:\s*(.*)/i', $cleanLine, $matches)) {
                            $type    = trim($matches[1]);
                            $subtype = trim($matches[2]);
                            Log::debug("Extracted English type: $type, subtype: $subtype");

                            // Fallback: separate lines like "Type: Inquiry" or "Subtype: Order cancellation"
                        } elseif (stripos($cleanLine, 'Type:') === 0) {
                            $type = trim(substr($cleanLine, 5));
                            Log::debug("Fallback: Extracted English type only: $type");

                        } elseif (stripos($cleanLine, 'Subtype:') === 0) {
                            $subtype = trim(substr($cleanLine, 8));
                            Log::debug("Fallback: Extracted English subtype only: $subtype");

                        } elseif (stripos($cleanLine, 'النوع:') === 0) {
                            $type = trim(substr($cleanLine, 6));
                            Log::debug("Fallback: Extracted Arabic type only: $type");

                        } elseif (stripos($cleanLine, 'التصنيف الفرعي:') === 0) {
                            $subtype = trim(substr($cleanLine, 17));
                            Log::debug("Fallback: Extracted Arabic subtype only: $subtype");
                        }

                        break;
                    }
                }

// Remove classification line(s) from summary content
                $summary = implode("\n", array_filter($lines, fn($l) =>
                    stripos($l, 'Type:') !== 0 &&
                    stripos($l, 'Subtype:') !== 0 &&
                    stripos($l, 'النوع:') !== 0 &&
                    stripos($l, 'التصنيف الفرعي:') !== 0
                ));

                CallSummary::create([
                    'call_id'   => $call_id,
                    'summary'   => $summary,
                    'call_type' => $type,
                    'sub_type'  => $subtype,
                ]);
            } catch (\Throwable $th) {
                //throw $th;
                Log::channel('interactions')->critical("💥 summary crashed for call_id {$call_id}:  " . $th->getMessage());

            }
            // ProcessIteractionsJob::dispatch($call_id);
            //            $this->duration($callConversation, $call_id);
            //$this->corrector($callConversation, $call_id);

            return response()->json(['call_id' => $call_id, 'status' => true]);
        } else {
            return response()->json(['call_id' => $call_id, 'status' => false]);
        }
    }

//    protected function duration($callConversation, $call_id)
//    {
//        Bus::batch([
//            new DurationJob($callConversation, $call_id)
//        ])->before(function () use ($call_id) {
//            Log::channel('detect')->info('Batch Before Hook: Starting Detect AI Tools.', ['call_id' => $call_id]);
//        })->progress(function () use ($call_id) {
//            Log::channel('detect')->info('Batch Progress Hook: Processing Detect AI Tools.', ['call_id' => $call_id]);
//        })->catch(function () use ($call_id){
//            Log::channel('detect')->info($call_id);
//            Mail::to('<EMAIL>')->send(new ScriptsMail('Detect AI Tools On Call ID: '. $call_id, 'error'));
//        })->finally(function () use ($call_id) {
//
//            Log::channel('detect')->info('Batch Finally Hook: Detect AI Tools completed.', ['call_id' => $call_id]);
//        })->onQueue('detects')->dispatch();
//
//    }

    protected function detect($callConversation, $call_id)
    {
        Bus::batch([
            new DetectJob($callConversation, $call_id),
        ])->before(function () use ($call_id) {
            Log::channel('detect')->info('Batch Before Hook: Starting Detect AI Tools.', ['call_id' => $call_id]);
        })->progress(function () use ($call_id) {
            Log::channel('detect')->info('Batch Progress Hook: Processing Detect AI Tools.', ['call_id' => $call_id]);
        })->catch(function () use ($call_id) {
            Log::channel('detect')->info($call_id);
            Mail::to('<EMAIL>')->send(new ScriptsMail('Detect AI Tools On Call ID: ' . $call_id, 'error'));
        })->finally(function () use ($call_id) {

            Log::channel('detect')->info('Batch Finally Hook: Detect AI Tools completed.', ['call_id' => $call_id]);
        })->onQueue('detects')->dispatch();

    }

    protected function detectScript($callConversation, $call_id, $lookingFor)
    {
        Bus::batch([
            new DetectScriptJob($callConversation, $call_id, $lookingFor),
        ])->before(function () use ($call_id) {
            Log::channel('corrector')->info('Batch Before Hook: Starting Detect Script AI Tools.', ['call_id' => $call_id]);
        })->progress(function () use ($call_id) {
            Log::channel('corrector')->info('Batch Progress Hook: Processing Detect Script AI Tools.', ['call_id' => $call_id]);
        })->catch(function () use ($call_id) {
            Log::channel('corrector')->info($call_id);
            Mail::to('<EMAIL>')->send(new ScriptsMail('Detect Script AI Tools On Call ID: ' . $call_id, 'error'));
        })->finally(function () use ($call_id) {

            Log::channel('corrector')->info('Batch Finally Hook: Detect Script AI Tools completed.', ['call_id' => $call_id]);
        })->onQueue('corrector')->dispatch();

    }

    protected function topics($callConversation, $call_id, $lang)
    {
        Bus::batch([
            new TopicsJob($callConversation, $call_id, $lang),
        ])->before(function () use ($call_id) {
            Log::channel('topics')->info('Batch Before Hook: Starting Topics AI Tools.', ['call_id' => $call_id]);
        })->progress(function () use ($call_id) {
            Log::channel('topics')->info('Batch Progress Hook: Processing Topics AI Tools.', ['call_id' => $call_id]);
        })->catch(function (Batch $batch, \Exception $e) use ($call_id) {
            Log::channel('topics')->info($e);
            Mail::to('<EMAIL>')->send(new ScriptsMail('Topics AI Tools On Call ID: ' . $call_id, $e));
        })->finally(function () use ($call_id) {
            Log::channel('topics')->info('Batch Finally Hook: Topics AI Tools completed.', ['call_id' => $call_id]);
        })->onQueue('topics')->dispatch();
    }

    protected function classification($callConversation, $call_id)
    {
        Bus::batch([
            new ClassificationJob($callConversation, $call_id),
        ])->before(function () use ($call_id) {
            Log::channel('classification')->info('Batch Before Hook: Starting Classification AI Tools.', ['call_id' => $call_id]);
        })->progress(function () use ($call_id) {
            Log::channel('classification')->info('Batch Progress Hook: Processing Classification AI Tools.', ['call_id' => $call_id]);
        })->catch(function () use ($call_id) {
            Log::channel('classification')->info($call_id);
//            Mail::to('<EMAIL>')->send(new ScriptsMail('Detect AI Tools On Call ID: '. $call_id, 'error'));
        })->finally(function () use ($call_id) {

            Log::channel('classification')->info('Batch Finally Hook: Classification AI Tools completed.', ['call_id' => $call_id]);
        })->onQueue('classification')->dispatch();
    }

    protected function corrector($callConversation, $call_id)
    {
        Bus::batch([
            new CorrectorJob($callConversation, $call_id),
        ])->before(function () use ($call_id) {
            Log::channel('corrector')->info('Batch Before Hook: Starting Corrector AI Tools.', ['call_id' => $call_id]);
        })->progress(function () use ($call_id) {
            Log::channel('corrector')->info('Batch Progress Hook: Processing Corrector AI Tools.', ['call_id' => $call_id]);
        })->catch(function (Batch $batch, \Exception $e) use ($call_id) {
            Log::channel('corrector')->info($e);
            Mail::to('<EMAIL>')->send(new ScriptsMail('Corrector AI Tools On Call ID: ' . $call_id, $e));
        })->finally(function () use ($call_id) {
            Log::channel('corrector')->info('Batch Finally Hook: Corrector AI Tools completed.', ['call_id' => $call_id]);
        })->onQueue('corrector')->dispatch();

    }

}
