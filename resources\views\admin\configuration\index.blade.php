@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Configuration')

{{--Style Section--}}
@section('style')
    <style>
        .page-background
        {
            {{--background-image: url("{{global_asset('assets/img/background.png')}}");--}}
            background-position-x: center;
            background-repeat: no-repeat;
            background-position-y: 34px;
            background-size: 812px;
        }

        .center-image{
            margin-top: 80px;
            align-items: center;
            width :35vw;
        }
        .filter-dialog{
            transition:  0.8s;
        }

        .filter-dialog:hover{
            color: green !important;
            font-weight: 900 !important;
            box-shadow: 3px 4px 11px -4px green !important;
        }

        .filter-dialog:hover .card-body i{
            filter: blur(0.3px);
        }

        .filter-dialog.active{
            color: var(--main-color) !important;
            font-weight: 900 !important;

        }

        .card-with-motion{
    opacity: 0; /* Start hidden */
    transform: translateY(20px); /* Start slightly shifted */
    animation: fadeIn 0.5s ease-out forwards;
         }
         .card-with-motion:hover{
            background: #f8f8f8 !important;
            transform: scale(1.05) !important; /* Make the card slightly larger */
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3); /* Add a larger shadow */
         }

         .card-with-motion:nth-child(1) {
    animation-delay: 0.2s; /* Delayed by 0.2s */
}

.card-with-motion:nth-child(2) {
    animation-delay: 0.4s;
}

.card-with-motion:nth-child(3) {
    animation-delay: 0.6s;
}

.card-with-motion:nth-child(4) {
    animation-delay: 0.8s;
}

.card-with-motion:nth-child(5) {
    animation-delay: 1s;
}
@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px); /* Offset to animate into position */
    }
    100% {
        opacity: 1;
        transform: translateY(0); /* End in the original position */
    }
}
    </style>
    </style>
@endsection


{{--Content Section--}}
@section('content')
    <div class="container-fluid ms-5" style="width: 93%;">

        @include('admin.configuration.filter.filter')
{{--        <div class="row">--}}
{{--            <div class="col-md-12 text-center page-background text-center" style="height: 40vh">--}}
{{--                <h2 class="text-secondary py-5" style="margin: 103px;">Please Choose From The <strong>Menu</strong></h2>--}}
{{--            </div>--}}
{{--        </div>--}}
    </div>
@endsection
