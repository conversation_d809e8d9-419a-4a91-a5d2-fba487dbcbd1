<?php

namespace App\Livewire\Evaluation;


use App\Models\Evaluation;
use App\Models\Organization;
// use HTMLPurifier;
// use HTMLPurifier_Config;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{

    use WithPagination,LivewireAlert;

    public $order_by = null;
    public $sort_by = null;
    public $limit = null;

    public $form_name;

    public $search_form_name;

    public $user_id;
    public $modalId;
    public $modalIdShow;
    public $status;
    public $userid;
    public $organization_id;

    protected $paginationTheme = 'bootstrap';

    public function mount()
    {
        $this->userid = auth()->id();
    }

    public function getEvaluationForm(){
        try
        {
            if($this->search_form_name){
                return Evaluation::query()
                    ->where('evaluation_name', 'like', '%' . $this->search_form_name . '%')
                    ->get();
            }else {
                return Evaluation::query()->get();
            }
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }
    public function getOrganizations(){

        return Organization::query()->get();
    }

    public function modelData(){

        // $config = HTMLPurifier_Config::createDefault();
        // $purifier = new HTMLPurifier($config);

        // $this->form_name = $purifier->purify($this->form_name);

        return [
            'evaluation_name'            => $this->form_name,
            'organization_id'            => $this->organization_id,
            'created_by'           => $this->userid,
        ];
    }

    public function rules() {
        return [
            'form_name' => ['required', 'string'],
            'organization_id' => ['required'],
        ];
    }
    
    public function messages() {
        return [
            'organization_id.required' => 'The organization field is required.',
        ];
    }

    public function store(){

        $this->validate();


        try {
            Evaluation::create($this->modelData());

            $this->alert('success', 'Successfully Added !',[
                //'position' => 'bottom-end',
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){

//            dd($e);
            $this->alert('error', $e,[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

        $this->modelFormReset();
        $this->dispatch('close-modal');
    }

    public function update(){


        $this->validate();

        try {

            Evaluation::find($this->modalId)->update($this->modelData());
            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->reloadComponent();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){

            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
            
        }

    }

    public function closeModal()
    {
        $this->modelFormReset();

    }

    public function showModal($id){

        $this->modalId =$id;
        $this->modalIdShow = "on";
    }

    public function statusUpdateModal($id){

        $this->modalId =$id;

        $fb = Evaluation::find($this->modalId);
        $fp = $fb->update(['status' => $fb->status != 0 ? 0 : 1]);
        $this->status = $fb->status;
        $this->alert('success', 'Successfully Updated!',[
            'timerProgressBar' => true,
            'timer' => '6000',
        ]);
    }
    public function showEdit($id){

        $this->dispatch('show-edit', ['id' => $id]);

    }
    public function showUpdateModal($id){
        $this->modalId =$id;
        $fb = Evaluation::find($this->modalId);
        $this->form_name = $fb->evaluation_name;
        $this->status = $fb->status;
        $this->organization_id = $fb->organization_id;
        $this->resetValidation();
        $this->dispatch('open-modal');
    }

    public function showDeleteAlert($id){
        $this->modalId =$id;

    }

    public function modelFormReset(){

        $this->form_name = "";
        $this->organization_id = "";
    }

    public function reloadComponent()
    {
        $this->form_name='';
        $this->organization_id = "";
        $this->modalId='';
        $this->modalIdShow='';
        if($this->search_form_name){
            $this->search_form_name='';
            $this->dispatch('close-modal-search');
        }
    }

    public function render()
    {
        return view('livewire.evaluation.index',['Evaluation_form' => $this->getEvaluationForm(),'organizations' => $this->getOrganizations()]);
    }
}
