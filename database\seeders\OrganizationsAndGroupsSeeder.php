<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class OrganizationsAndGroupsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // Define organizations
        $organizations = [
            'Samsung', 'Sauce Capital', 'SaudiAirline', 'SHEIN', 'Stalar', 'SurePay', 'Swyft',
            'Tiqmo', 'Xnara', 'Zad Fresh Outbound', 'Zajil', 'AExpress', 'Balsam', 'BCI', 'Brandatt',
            'Carrefour', 'Cars24', 'Chicpoint', 'Dajani Consulting', 'Dome', 'fawateercom', 'Geidea',
            'MBC - Gobx', 'Hertz', 'JAF', 'JollyChic', 'Kease', 'Virgin Mobile', 'LC Wakiki', 'Level Shoes',
            'Meddy', 'Mumzworld', 'PACE', 'Patchi', 'Qatar Creates', 'Rantion', 'Rock', 'Etihad Bank',
            'GCC', 'GOBX', 'KAFD', 'MBC', 'Modanisa', 'Wataniya', 'Shahid'
        ];

        // Insert organizations and keep track of their IDs
        $organization_ids = [];
        foreach ($organizations as $name) {
            $id = DB::table('organizations')->insertGetId([
                'name' => $name,
                'description' => $name . ' Account',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
            $organization_ids[$name] = $id;
        }

        // Define user groups
        $user_groups = [
            ['name' => 'Samsung - Inbound', 'organization_id' => $organization_ids['Samsung']],
            ['name' => 'Samsung - Outbound', 'organization_id' => $organization_ids['Samsung']],
            ['name' => 'Sauce Capital - IB', 'organization_id' => $organization_ids['Sauce Capital']],
            ['name' => 'Sauce Capital - Manual', 'organization_id' => $organization_ids['Sauce Capital']],
            ['name' => 'Saudi Airlines', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'Saudi Airlines USA', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'USA New', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'Saudi Airlines+USA/Out', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'Student Station', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'SV-UK New', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'SV Blended', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'GCC', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'GCC New', 'organization_id' => $organization_ids['SaudiAirline']],
            ['name' => 'SHEIN', 'organization_id' => $organization_ids['SHEIN']],
            ['name' => 'SHEIN Outbound', 'organization_id' => $organization_ids['SHEIN']],
            ['name' => 'Stalar', 'organization_id' => $organization_ids['Stalar']],
            ['name' => 'SurePay', 'organization_id' => $organization_ids['SurePay']],
            ['name' => 'Swyft', 'organization_id' => $organization_ids['Swyft']],
            ['name' => 'Swyft Outbound', 'organization_id' => $organization_ids['Swyft']],
            ['name' => 'Swyft Partner Care', 'organization_id' => $organization_ids['Swyft']],
            ['name' => 'Tiqmo - IB', 'organization_id' => $organization_ids['Tiqmo']],
            ['name' => 'Xnara - BL', 'organization_id' => $organization_ids['Xnara']],
            ['name' => 'Zad Fresh Outbound', 'organization_id' => $organization_ids['Zad Fresh Outbound']],
            ['name' => 'Zajil - Jo', 'organization_id' => $organization_ids['Zajil']],
            ['name' => 'Zajil - KSA', 'organization_id' => $organization_ids['Zajil']],
            ['name' => 'AExpress', 'organization_id' => $organization_ids['AExpress']],
            ['name' => 'Balsam - Blended', 'organization_id' => $organization_ids['Balsam']],
            ['name' => 'BCI - IB', 'organization_id' => $organization_ids['BCI']],
            ['name' => 'BCI - Ma', 'organization_id' => $organization_ids['BCI']],
            ['name' => 'Brandatt - Inbound', 'organization_id' => $organization_ids['Brandatt']],
            ['name' => 'Brandatt - Manual', 'organization_id' => $organization_ids['Brandatt']],
            ['name' => 'Carrefour', 'organization_id' => $organization_ids['Carrefour']],
            ['name' => 'Carrefour Dialer', 'organization_id' => $organization_ids['Carrefour']],
            ['name' => 'Cars24-Blended', 'organization_id' => $organization_ids['Cars24']],
            ['name' => 'Cars24-Dialer', 'organization_id' => $organization_ids['Cars24']],
            ['name' => 'Cars24-Manual', 'organization_id' => $organization_ids['Cars24']],
            ['name' => 'Chicpoint', 'organization_id' => $organization_ids['Chicpoint']],
            ['name' => 'Chicpoint - Special', 'organization_id' => $organization_ids['Chicpoint']],
            ['name' => 'Dajani-IB', 'organization_id' => $organization_ids['Dajani Consulting']],
            ['name' => 'Dome Outbound', 'organization_id' => $organization_ids['Dome']],
            ['name' => 'efawateercom', 'organization_id' => $organization_ids['fawateercom']],
            ['name' => 'Geidea', 'organization_id' => $organization_ids['Geidea']],
            ['name' => 'GOBX', 'organization_id' => $organization_ids['GOBX']],
            ['name' => 'GOBX New', 'organization_id' => $organization_ids['GOBX']],
            ['name' => 'Hertz', 'organization_id' => $organization_ids['Hertz']],
            ['name' => 'Hertz New', 'organization_id' => $organization_ids['Hertz']],
            ['name' => 'JAF-OB', 'organization_id' => $organization_ids['JAF']],
            ['name' => 'JollyChic', 'organization_id' => $organization_ids['JollyChic']],
            ['name' => 'Jollychic New', 'organization_id' => $organization_ids['JollyChic']],
            ['name' => 'jollychic.Client', 'organization_id' => $organization_ids['JollyChic']],
            ['name' => 'Kease', 'organization_id' => $organization_ids['Kease']],
            ['name' => 'Kease Manual', 'organization_id' => $organization_ids['Kease']],
            ['name' => 'LC Wakiki - IB', 'organization_id' => $organization_ids['LC Wakiki']],
            ['name' => 'Level Shoes', 'organization_id' => $organization_ids['Level Shoes']],
            ['name' => 'Meddy', 'organization_id' => $organization_ids['Meddy']],
            ['name' => 'Mumzworld', 'organization_id' => $organization_ids['Mumzworld']],
            ['name' => 'PACE', 'organization_id' => $organization_ids['PACE']],
            ['name' => 'PACE Outbound', 'organization_id' => $organization_ids['PACE']],
            ['name' => 'Pace -Ticket', 'organization_id' => $organization_ids['PACE']],
            ['name' => 'Patchi - Blended', 'organization_id' => $organization_ids['Patchi']],
            ['name' => 'Qatar Creates - Di', 'organization_id' => $organization_ids['Qatar Creates']],
            ['name' => 'Rantion', 'organization_id' => $organization_ids['Rantion']],
            ['name' => 'Rantion Jap', 'organization_id' => $organization_ids['Rantion']],
            ['name' => 'Rantion Jap OB', 'organization_id' => $organization_ids['Rantion']],
            ['name' => 'Rock', 'organization_id' => $organization_ids['Rock']],
            ['name' => 'Modanisa', 'organization_id' => $organization_ids['Modanisa']],
            ['name' => 'Modanisa New', 'organization_id' => $organization_ids['Modanisa']],
            ['name' => 'Modanisa Manual', 'organization_id' => $organization_ids['Modanisa']],
            ['name' => 'Shahid', 'organization_id' => $organization_ids['Shahid']],
            ['name' => 'Shahid Cancellation', 'organization_id' => $organization_ids['Shahid']],
            ['name' => 'Shahid New', 'organization_id' => $organization_ids['Shahid']],
            ['name' => 'KSA Pilot', 'organization_id' => $organization_ids['Virgin Mobile']],
        ];


        // Insert user groups
        foreach ($user_groups as $group) {
            DB::table('user_groups')->insert([
                'name' => $group['name'],
                'organization_id' => $group['organization_id'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        }
    }
}
