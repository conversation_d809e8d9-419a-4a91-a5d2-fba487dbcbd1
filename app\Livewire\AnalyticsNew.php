<?php

namespace App\Livewire;

use App\Models\AnalyticsParametersActivition;
use App\Models\Interaction;
use App\Models\Organization;
use App\Models\User;
use App\Models\UserGroup;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class AnalyticsNew extends Component
{
    public $totalCallDurationAllGroups;
    public $selected_group;
    public $disconnected_by_customer;
    public $disconnected_by_agent;
    public $disconnected_by_system;
    public $accountIDFilter;
    public $accountNameFilter;
    public $dateFrom;
    public $dateTo;
    public $dateFromFilter;
    public $dateToFilter;
    public $dateType;
    public $groupsAccount;
    public $dataPage;
    public $dataPage2;
    public $searchGroup;
    public $groupSelected;
    public $editFlag;
    public $userSettings;
    public $cardSelected;
    public $role;
    public $page = 'pageOne';
    public $queryGrouFormat = 'Y-m-d';

    protected $listeners = ['callBackendMethod' => 'callBackendMethod'];

    public function mount()
    {
        $this->getDate('Last 24 Hours');
        $this->getUserSettings();
        $this->editFlag = false;
        $this->role = auth()->user()->role;

        // $this->dispatch('updateSupervisorFlags', $this->dataPage['qaFlagsCount']);

    }
    public function remove($type)
    {
        // Add a new record with the given parameter_name, user_id, and is_active set to 0
        AnalyticsParametersActivition::create([
            'parameter_name' => $type,
            'user_id' => auth()->id(),
            'is_active' => 0,
        ]);
        $this->getUserSettings();
        $this->render();
    }

    public function add($type)
    {
        // Remove the record where parameter_name equals $type and user_id is the current authenticated user
        AnalyticsParametersActivition::where('parameter_name', $type)
            ->where('user_id', auth()->id())
            ->delete();
        $this->getUserSettings();
        $this->render();
    }
    public function swapPages()
    {

        if ($this->page == 'pageOne') {
            $this->page = 'pageTwe';
        } else {
            $this->page = 'pageOne';
        }
        $this->render();
    }
    public function getUserSettings()
    {

        $this->userSettings = AnalyticsParametersActivition::where('user_id', auth()->user()->id)
            ->where('is_active', 0)
            ->pluck('parameter_name')
            ->toArray();

        // dd($this->userSettings);
    }
    //     public function getAccounts()
    // {
    //     // $account = null;

    //     if ($this->role == 4) {
    //         $account = Organization::query()->where('id', auth()->user()->organization_id)->first();
    //         $account1 = Organization::query()->where('id', auth()->user()->organization_id)->get();
    //     } elseif ($this->role == 2 || $this->role == 5) {
    //         $supervisorOrgs = Auth::user()->supervisorOrganizations->pluck('id');
    //         $account = Organization::query()->whereIn('id', $supervisorOrgs)->first();
    //         $account1 = Organization::query()->whereIn('id', $supervisorOrgs)->get();
    //     } else {
    //         $account = Organization::query()->first();
    //         $account1 = Organization::query()->get();
    //     }

    //     if ($account) {
    //         $this->setAccountFilter($account->id, $account->name);
    //     }

    //     return $account1;
    // }
    public function getAccounts()
    {
        $orgs = Organization::query();

        if ($this->role == 4) {
            return $orgs->where('id', auth()->user()->organization_id)->get()->sortBy('name');
        } elseif ($this->role == 2 || $this->role == 5 || $this->role == 7) {
            $supervisorOrgs = Auth::user()->supervisorOrganizations->pluck('id');
            return $orgs->whereIN('id', $supervisorOrgs)->get()->sortBy('name');
        } else {
            return $orgs->get()->sortBy('name');
        }
    }

    public function setAccountFilter($id, $name)
    {

        if ($id) {
            $this->accountIDFilter = $id;
            $this->accountNameFilter = $name;
            $arrays = [];
            if ($this->role == 4) {
                $data = UserGroup::where('id', auth()->user()->user_group_id)
                    ->when(!empty($this->searchGroup), function ($query) {
                        $query->where('name', 'like', '%' . $this->searchGroup . '%');
                    })
                    ->get();
            } else {
                $data = UserGroup::where('organization_id', $this->accountIDFilter)
                    ->when(!empty($this->searchGroup), function ($query) {
                        $query->where('name', 'like', '%' . $this->searchGroup . '%');
                    })
                    ->get();
            }

            foreach ($data as $array) {
                // Filter and process call durations
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });

                $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                $callInteractionCount = $filteredCallDurations->count();
                $avgCallDurationInSeconds = $callInteractionCount > 0 ? $totalCallDurationInSeconds / $callInteractionCount : 0;
                $avgCallDurationFormatted = sprintf(
                    "%02d:%02d:%02d",
                    floor($avgCallDurationInSeconds / 3600),
                    floor(($avgCallDurationInSeconds % 3600) / 60),
                    $avgCallDurationInSeconds % 60
                );

                // Filter and process hold durations
                $filteredHoldTimes = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });

                $totalHoldTimeInSeconds = $filteredHoldTimes->sum(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                $holdInteractionCount = $filteredHoldTimes->count();
                $avgHoldTimeInSeconds = $holdInteractionCount > 0 ? $totalHoldTimeInSeconds / $holdInteractionCount : 0;
                $avgHoldTimeFormatted = sprintf(
                    "%02d:%02d:%02d",
                    floor($avgHoldTimeInSeconds / 3600),
                    floor(($avgHoldTimeInSeconds % 3600) / 60),
                    $avgHoldTimeInSeconds % 60
                );

                // Count interactions based on conditions
                $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                    return $durationInSeconds < 120; // < 2 minutes
                })->count();

                $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                    return $durationInSeconds > 480; // > 8 minutes
                })->count();

                $longHoldDurationCount = $filteredHoldTimes->filter(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                    $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                    return $durationInSeconds > 120; // > 2 minutes
                })->count();

                // Calculate total outbound and inbound calls
                $totalOutbound = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return $interaction->call_type === 'Outbound';
                    })->count();

                $totalInbound = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return $interaction->call_type === 'Inbound';
                    })->count();

                // Total call duration in formatted time
                $totalCallDurationFormatted = sprintf(
                    "%02d:%02d:%02d",
                    floor($totalCallDurationInSeconds / 3600),
                    floor(($totalCallDurationInSeconds % 3600) / 60),
                    $totalCallDurationInSeconds % 60
                );

                $arrays[] = [
                    'group_id' => $array->id,
                    'group_name' => $array->name,
                    'avg_interactions_duration' => $avgCallDurationFormatted,
                    'avg_hold_time' => $avgHoldTimeFormatted,
                    'total_interactions' => $callInteractionCount,
                    'short_call_duration_count' => $shortCallDurationCount,
                    'long_call_duration_count' => $longCallDurationCount,
                    'long_hold_duration_count' => $longHoldDurationCount,
                    'total_call_duration' => $totalCallDurationFormatted, // New: Total Call Duration
                    'total_outbound' => $totalOutbound, // New: Total Outbound Calls
                    'total_inbound' => $totalInbound, // New: Total Inbound Calls
                ];
            }

            $this->groupsAccount = $arrays;
            // dd($this->groupsAccount);
            if ($this->role == 4) {
                $this->getData(auth()->user()->user_group_id);
            } else {
                $this->getData('All');
            }
        }

        // else {
        //     $this->getAllAccountsData();
        //     $this->accountNameFilter = 'All';
        //     $this->dispatch('clickFirstCard');
        // }

        // $this->dispatch('updateSupervisorFlags', $this->dataPage['qaFlagsCount'] ?? []);
    }

    public function setAccountsFilter()
    {

        $this->accountIDFilter = null;
        $this->accountNameFilter = null;
        $arrays = [];
        $data = UserGroup::when(!empty($this->searchGroup), function ($query) {
            $query->where('name', 'like', '%' . $this->searchGroup . '%');
        })
            ->get();

        foreach ($data as $array) {
            // Filter and process call durations
            $filteredCallDurations = $array->callInteraction
                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
            if ($this->role == 4) {
                $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
            }
            $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
            });

            $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });

            $callInteractionCount = $filteredCallDurations->count();
            $avgCallDurationInSeconds = $callInteractionCount > 0 ? $totalCallDurationInSeconds / $callInteractionCount : 0;
            $avgCallDurationFormatted = sprintf(
                "%02d:%02d:%02d",
                floor($avgCallDurationInSeconds / 3600),
                floor(($avgCallDurationInSeconds % 3600) / 60),
                $avgCallDurationInSeconds % 60
            );

            // Filter and process hold durations
            $filteredHoldTimes = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });

            $totalHoldTimeInSeconds = $filteredHoldTimes->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });

            $holdInteractionCount = $filteredHoldTimes->count();
            $avgHoldTimeInSeconds = $holdInteractionCount > 0 ? $totalHoldTimeInSeconds / $holdInteractionCount : 0;
            $avgHoldTimeFormatted = sprintf(
                "%02d:%02d:%02d",
                floor($avgHoldTimeInSeconds / 3600),
                floor(($avgHoldTimeInSeconds % 3600) / 60),
                $avgHoldTimeInSeconds % 60
            );

            // Count interactions based on conditions
            $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                return $durationInSeconds < 120; // < 2 minutes
            })->count();

            $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                return $durationInSeconds > 480; // > 8 minutes
            })->count();

            $longHoldDurationCount = $filteredHoldTimes->filter(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                $durationInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
                return $durationInSeconds > 120; // > 2 minutes
            })->count();

            // Calculate total outbound and inbound calls
            $totalOutbound = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return $interaction->call_type === 'Outbound';
                })->count();

            $totalInbound = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                ->filter(function ($interaction) {
                    return $interaction->call_type === 'Inbound';
                })->count();

            // Total call duration in formatted time
            $totalCallDurationFormatted = sprintf(
                "%02d:%02d:%02d",
                floor($totalCallDurationInSeconds / 3600),
                floor(($totalCallDurationInSeconds % 3600) / 60),
                $totalCallDurationInSeconds % 60
            );

            $arrays[] = [
                'group_id' => $array->id,
                'group_name' => $array->name,
                'avg_interactions_duration' => $avgCallDurationFormatted,
                'avg_hold_time' => $avgHoldTimeFormatted,
                'total_interactions' => $callInteractionCount,
                'short_call_duration_count' => $shortCallDurationCount,
                'long_call_duration_count' => $longCallDurationCount,
                'long_hold_duration_count' => $longHoldDurationCount,
                'total_call_duration' => $totalCallDurationFormatted, // New: Total Call Duration
                'total_outbound' => $totalOutbound, // New: Total Outbound Calls
                'total_inbound' => $totalInbound, // New: Total Inbound Calls
            ];
        }

        $this->groupsAccount = $arrays;
        $this->getData('All');

        // else {
        //     $this->getAllAccountsData();
        //     $this->accountNameFilter = 'All';
        //     $this->dispatch('clickFirstCard');
        // }

        // $this->dispatch('updateSupervisorFlags', $this->dataPage['qaFlagsCount'] ?? []);
    }

    public function getData($groupId)
    {
        try {
            $arrays = [];
            $groupQuery = "";
            // $userOrgz = auth()->user()->supervisorOrganizations->pluck('id')->toArray();
            $userOrgz = [];

            if (in_array(auth()->user()->role, [2, 5, 7])) {
                $userOrgz = auth()->user()->supervisorOrganizations->pluck('id')->toArray();
            } elseif (auth()->user()->role == 1) {
                $userOrgz = Organization::pluck('id')->toArray();
            }

            if (count($userOrgz) === 0) {
                throw new \Exception('No Orgnaizations found for this user');
            }
            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }

            if (!is_null($this->accountIDFilter) && !is_null($groupId)) {

                $this->groupSelected = $groupId;
                if ($groupId != 'All') {
                    $groupQuery = "AND user_group_id = $groupId";
                }
                $totalStatsAccount = DB::select("
                                        SELECT
                                            organization_id,
                                            TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,
                                                TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                                SUM(call_duration) AS total_ring,
                                            COUNT(call_id) AS total_calls
                                        FROM interactions
                                        WHERE arrival_time BETWEEN ? AND ?
                                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                            GROUP BY organization_id

                                        ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                $EvaluationsResults = DB::select("
                                            SELECT AVG(quality_percentage) AS avg_quality, COUNT(a.id) AS total_qa_submissions
                                            FROM evaluation_submissions a
                                            INNER JOIN evaluations b ON a.evaluation_id = b.id
                                            WHERE
                                            a.created_at BETWEEN ? AND ?
                                            AND
                                            b.organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                            ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                $inboundOutbound = DB::select("
                                            SELECT COUNT(call_id) AS totalCalls,call_type FROM interactions   WHERE arrival_time BETWEEN ? AND ? $groupQuery
                                            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ") GROUP BY call_type
                                            ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
                $totalInbound = $inboundOutbound[0]?->totalCalls ?? 0;
                $totalOutbound = $inboundOutbound[1]?->totalCalls ?? 0;

                $totalStats = DB::select("
                                        SELECT
                                            organization_id,
                                            TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,
                                                TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,
                                                            AVG(ring) AS avg_ring,

                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                                SUM(call_duration) AS total_ring,
                                            COUNT(call_id) AS total_calls
                                        FROM interactions
                                        WHERE arrival_time BETWEEN ? AND ?
                                        $groupQuery
                                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                            GROUP BY organization_id

                                        ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                $durationsResult = DB::select("SELECT
                        COUNT(CASE WHEN call_duration < '00:02:00' THEN 1 END) AS less_than_2_minutes,
                        COUNT(CASE WHEN call_duration >= '00:08:00' THEN 1 END) AS greater_or_equal_8_minutes,
                        COUNT(CASE WHEN hold_duration >= '00:02:00' THEN 1 END) AS greater_or_equal_2_minutes_hold
                            FROM interactions
                                                                    WHERE arrival_time BETWEEN ? AND ?
                                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                        $groupQuery", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                $shortCallDurationCount = $durationsResult[0]->less_than_2_minutes;
                $longCallDurationCount = $durationsResult[0]->greater_or_equal_8_minutes;
                $longHoldDurationCount = $durationsResult[0]->greater_or_equal_2_minutes_hold;

                $QAFlags = DB::select("
            SELECT
                COUNT(a.interaction_id) AS totalFlags
            FROM interaction_qa_flag a INNER JOIN interactions b
            ON a.interaction_id = b.id AND
             arrival_time BETWEEN ? AND ?
            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
            ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                $qaFlagsCount = $QAFlags[0]->totalFlags;

                $aiFlags = DB::select("
            SELECT
                COUNT(*) AS totalaiFlags
            FROM interactions WHERE
             arrival_time BETWEEN ? AND ?
            AND ai_flag = '1'
            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
            ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                $aiFlagsCount = $aiFlags[0]->totalaiFlags ?? 0;


                $callInteractionCount = $totalStats[0]->total_calls ?? 0;
                $averageRing = $totalStats[0]->avg_ring ?? 0;
                $arrays = [
                    'group_id' => $totalStats[0]->organization_id ?? 0,
                    'group_name' => 'test',
                    'avg_interactions_duration' => $totalStats[0]->avg_duration ?? 0, // done
                    'avg_hold_time' => $totalStats[0]->avg_hold_duration ?? 0, //done
                    'total_hold_time' => $totalStats[0]->total_hold_time ?? 0, //done
                    'total_interactions' => $totalStats[0]->total_calls ?? 0, //done
                    'short_call_duration_count' => $shortCallDurationCount,
                    'long_call_duration_count' => $longCallDurationCount,
                    'long_hold_duration_count' => $longHoldDurationCount,
                    'total_call_duration' => $totalStats[0]->total_call_duration ?? 0,
                    'total_outbound' => $totalOutbound, //done
                    'total_inbound' => $totalInbound, //done
                    'countEvaluation' => $EvaluationsResults[0]->total_qa_submissions ?? 0, //done
                    'avgEvaluationScore' => $EvaluationsResults[0]->avg_quality ?? 0, //done
                    'totalRing' => $totalStats[0]->total_ring ?? 0, //done
                    'averageRing' => round($averageRing, 1),
                    'totalHandledCalls' => $callInteractionCount, //done
                    'qaFlagsCount' => $qaFlagsCount,
                    'aiFlagsCount' => $aiFlagsCount,

                ];

                $AllAccountArray = [
                    'avg_interactions_duration' => $totalStatsAccount[0]->avg_duration ?? '00:00:00', // done
                    'totalHandledCalls' => $totalStatsAccount[0]->total_calls ?? 0, //done
                    'total_call_duration' => $totalStatsAccount[0]->total_call_duration ?? '00:00:00',

                ];
                $this->dataPage2 = $AllAccountArray;
                $this->groupSelected = $groupId;
                $this->dispatch('style-row', ['groupId' => $this->groupSelected]);
                if ($callInteractionCount > 0) {
                    $this->dispatch('update-qaflags', [
                        'val' => $aiFlagsCount,
                    ]);
                }
                $totalOutbound = $arrays['total_outbound'];
                $totalInbound = $arrays['total_inbound'];

                $count = $totalOutbound + $totalInbound;

                $averageOutbound = $count > 0 ? $totalOutbound / $count : 0;
                $averageInbound = $count > 0 ? $totalInbound / $count : 0;

                $this->getChartData($this->cardSelected);
                $this->dispatch('update-chart', [
                    'chartTitle' => 'Call',
                    'percentage_inbound' => round($averageInbound * 100),
                    'percentage_outbound' => round($averageOutbound * 100),
                ]);
            } else {

                //Agent Dashboard
                if ($this->role == 4) {
                    $EvaluationsResults = DB::select("
                                        SELECT AVG(quality_percentage) AS avg_quality, COUNT(a.id) AS total_qa_submissions
                                        FROM evaluation_submissions a
                                        INNER JOIN evaluations b ON a.evaluation_id = b.id
                                        WHERE
                                        a.created_at BETWEEN ? AND ?
                                        AND a.user_id = ?
                                        AND
                                        b.organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                        ", array_merge([$this->dateFrom, $this->dateTo, auth()->user()->id], $userOrgz));

                    $inboundOutbound = DB::select("
                                        SELECT COUNT(call_id) AS totalCalls,call_type FROM interactions   WHERE arrival_time BETWEEN ? AND ?
                                        AND user_id = ?
                                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ") GROUP BY call_type
                                        ", array_merge([$this->dateFrom, $this->dateTo, auth()->user()->id], $userOrgz));
                    $totalInbound = $inboundOutbound[0]?->totalCalls ?? 0;
                    $totalOutbound = $inboundOutbound[1]?->totalCalls ?? 0;

                    $totalStats = DB::select("
                                        SELECT
                                            TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,
                                                TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,
                                                AVG(ring) AS avg_ring,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                                TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                                SUM(call_duration) AS total_ring,
                                            COUNT(call_id) AS total_calls
                                        FROM interactions
                                        WHERE arrival_time BETWEEN ? AND ?
                                        AND user_id = ?
                                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                        ", array_merge([$this->dateFrom, $this->dateTo, auth()->user()->id], $userOrgz));

                    $shortCallDurationCount = 0;
                    $longCallDurationCount = 0;
                    $longHoldDurationCount = 0;
                    $durationsResult = DB::select("SELECT
                                                COUNT(CASE WHEN call_duration < '00:02:00' THEN 1 END) AS less_than_2_minutes,
                                                COUNT(CASE WHEN call_duration >= '00:08:00' THEN 1 END) AS greater_or_equal_8_minutes,
                                                COUNT(CASE WHEN hold_duration >= '00:02:00' THEN 1 END) AS greater_or_equal_2_minutes_hold
                                                    FROM interactions
                                                                                            WHERE arrival_time BETWEEN ? AND ?
                                                                                            AND user_id = ?
                                                                AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                                                $groupQuery", array_merge([$this->dateFrom, $this->dateTo, auth()->user()->id], $userOrgz));

                    $shortCallDurationCount = $durationsResult[0]->less_than_2_minutes;
                    $longCallDurationCount = $durationsResult[0]->greater_or_equal_8_minutes;
                    $longHoldDurationCount = $durationsResult[0]->greater_or_equal_2_minutes_hold;

                    $averageRing = $totalStats[0]->avg_ring;

                    $QAFlags = DB::select("
                        SELECT
                        COUNT(a.interaction_id) AS totalFlags
                        FROM interaction_qa_flag a INNER JOIN interactions b
                        ON a.interaction_id = b.id AND
                        arrival_time BETWEEN ? AND ?
                        AND user_id = ?
                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                        ", array_merge([$this->dateFrom, $this->dateTo, auth()->user()->id], $userOrgz));

                    $qaFlagsCount = $QAFlags[0]->totalFlags;

                    $aiFlags = DB::select("
                        SELECT
                            COUNT(*) AS totalaiFlags
                        FROM interactions WHERE
                         arrival_time BETWEEN ? AND ?
                        AND ai_flag = '1'
                        AND user_id = ?
                        AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                        ", array_merge([$this->dateFrom, $this->dateTo, auth()->user()->id], $userOrgz));

                    $aiFlagsCount = $aiFlags[0]->totalaiFlags ?? 0;

                    $totalEnderSystem = 0;
                } else {
                    $EvaluationsResults = DB::select("
                                    SELECT AVG(quality_percentage) AS avg_quality, COUNT(a.id) AS total_qa_submissions
                                    FROM evaluation_submissions a
                                    INNER JOIN evaluations b ON a.evaluation_id = b.id
                                    WHERE
                                    a.created_at BETWEEN ? AND ?
                                    AND
                                    b.organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                    ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                    $inboundOutbound = DB::select("
                                    SELECT COUNT(call_id) AS totalCalls,call_type FROM interactions   WHERE arrival_time BETWEEN ? AND ?
                                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ") GROUP BY call_type
                                    ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
                    $totalInbound = $inboundOutbound[0]?->totalCalls ?? 0;
                    $totalOutbound = $inboundOutbound[1]?->totalCalls ?? 0;

                    $totalStats = DB::select("
                                    SELECT
                                        TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,
                                            TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,
                                            AVG(ring) AS avg_ring,
                                            TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,
                                            TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                            TIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,
                                            SUM(call_duration) AS total_ring,
                                        COUNT(call_id) AS total_calls
                                    FROM interactions
                                    WHERE arrival_time BETWEEN ? AND ?
                                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                    ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                    $shortCallDurationCount = 0;
                    $longCallDurationCount = 0;
                    $longHoldDurationCount = 0;
                    $durationsResult = DB::select("SELECT
                                            COUNT(CASE WHEN call_duration < '00:02:00' THEN 1 END) AS less_than_2_minutes,
                                            COUNT(CASE WHEN call_duration >= '00:08:00' THEN 1 END) AS greater_or_equal_8_minutes,
                                            COUNT(CASE WHEN hold_duration >= '00:02:00' THEN 1 END) AS greater_or_equal_2_minutes_hold
                                                FROM interactions
                                                                                        WHERE arrival_time BETWEEN ? AND ?
                                                            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                                                            $groupQuery", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                    $shortCallDurationCount = $durationsResult[0]->less_than_2_minutes;
                    $longCallDurationCount = $durationsResult[0]->greater_or_equal_8_minutes;
                    $longHoldDurationCount = $durationsResult[0]->greater_or_equal_2_minutes_hold;

                    $averageRing = $totalStats[0]->avg_ring;

                    $QAFlags = DB::select("
            SELECT
                COUNT(a.interaction_id) AS totalFlags
            FROM interaction_qa_flag a INNER JOIN interactions b
            ON a.interaction_id = b.id AND
             arrival_time BETWEEN ? AND ?
            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
            ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                    $qaFlagsCount = $QAFlags[0]->totalFlags;

                    $aiFlags = DB::select("
            SELECT
                COUNT(*) AS totalaiFlags
            FROM interactions WHERE
             arrival_time BETWEEN ? AND ?
            AND ai_flag = '1'
            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
            ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));

                    $aiFlagsCount = $aiFlags[0]->totalaiFlags ?? 0;

                    $totalEnderSystem = 0;
                }
                $arrays = [
                    'group_id' => 'All',
                    'group_name' => 'All',
                    'avg_interactions_duration' => $totalStats[0]->avg_duration, // done
                    'avg_hold_time' => $totalStats[0]->avg_hold_duration, //done
                    'total_hold_time' => $totalStats[0]->total_hold_time, //done
                    'total_interactions' => $totalStats[0]->total_calls, //done
                    'short_call_duration_count' => $shortCallDurationCount,
                    'long_call_duration_count' => $longCallDurationCount,
                    'long_hold_duration_count' => $longHoldDurationCount,
                    'total_call_duration' => $totalStats[0]->total_call_duration,
                    'total_outbound' => $totalOutbound, //done
                    'total_inbound' => $totalInbound, //done
                    'countEvaluation' => $EvaluationsResults[0]->total_qa_submissions, //done
                    'avgEvaluationScore' => $EvaluationsResults[0]->avg_quality, //done
                    'totalRing' => $totalStats[0]->total_ring, //done
                    'averageRing' => round($averageRing, 1),
                    'totalHandledCalls' => $totalStats[0]->total_calls, //done
                    'qaFlagsCount' => $qaFlagsCount,
                    'aiFlagsCount' => $aiFlagsCount,

                ];
                $this->dataPage2 = $arrays;
            }

            $this->dataPage = $arrays;
        } catch (\Exception $e) {
            $arrays = [
                'group_id' => 'All',
                'group_name' => 'All',
                'avg_interactions_duration' => 0, // done
                'avg_hold_time' => 0, //done
                'total_hold_time' => 0, //done
                'total_interactions' => 0, //done
                'short_call_duration_count' => 0,
                'long_call_duration_count' => 0,
                'long_hold_duration_count' => 0,
                'total_call_duration' => 0,
                'total_outbound' => 0, //done
                'total_inbound' => 0, //done
                'countEvaluation' => 0, //done
                'avgEvaluationScore' => 0, //done
                'totalRing' => 0, //done
                'averageRing' => 0,
                'totalHandledCalls' => 0, //done
                'qaFlagsCount' => 0,
                'aiFlagsCount' => 0,

            ];
            $this->dataPage2 = $arrays;
            $this->dataPage = $arrays;
            return;
        }
        return;
        if ($this->accountIDFilter) {
            $this->selected_group = $groupId;
            $arrays = [];

            if ($groupId == 'All') {
                $array = Organization::where('id', $this->accountIDFilter)
                    ->first();

                $countEvaluation = 0;
                $averageQualityPercentage = 0;
                $count = 0;
                $totalRing = 0;
                $avgRingDuration = 0;
                $totalHoldTime = '00:00:00';
                if ($this->role == 4) {
                    foreach ($array->evaluationForm as $evaluationForm) {
                        $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('user_id', auth()->user()->id)->count();
                        $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('user_id', auth()->user()->id)
                            ->avg('quality_percentage');
                    }
                } else {
                    if ($this->role == 2) {
                        foreach ($array->evaluationForm as $evaluationForm) {
                            $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->count();
                            // $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->avg('quality_percentage');
                            $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->avg('quality_percentage');
                            $count++;
                            // dump($averageQualityPercentage);
                        }

                        if ($count == 0) {
                            $averageQualityPercentage = 0;
                        } else {

                            $averageQualityPercentage = $averageQualityPercentage / $count;
                        }
                    } elseif ($this->role == 5) {
                        foreach ($array->evaluationForm as $evaluationForm) {
                            $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('created_by', auth()->user()->id)->count();
                            $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('created_by', auth()->user()->id)
                                ->avg('quality_percentage');
                        }
                    }
                }

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);
                // if agent user to get data for him only
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->id);
                }

                /* if the hold duration or call duration is null will be skip on this record */
                /**/
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    /**/
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    /**/
                });
                /**/
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    /**/
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    /**/
                });
                /* if the hold duration or call duration is null will be skip on this record */

                // Total Call Duration in seconds
                $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                // Avg Call Duration in seconds
                $avgCallDurationInSeconds = $filteredCallDurations->avg(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                // Formatting average call duration
                if ($avgCallDurationInSeconds) {
                    $avgCallDurationFormatted = gmdate("H:i:s", $avgCallDurationInSeconds);
                } else {
                    $avgCallDurationFormatted = '00:00:00';
                }

                // Calculate other call metrics where call duration > 2
                $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    return strtotime($interaction->call_duration) < strtotime('00:02:00');
                })->count();

                $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    return strtotime($interaction->call_duration) > strtotime('00:08:00');
                })->count();

                $longHoldDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                })->count();

                // Total Hold Time in seconds
                $totalHoldTimeInSeconds = $filteredCallDurations->sum(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                // Calculate the average hold time in seconds
                $totalCalls = $filteredCallDurations->count();
                $avgHoldTimeInSeconds = $totalCalls > 0 ? $totalHoldTimeInSeconds / $totalCalls : 0;

                // Format the total call time
                if ($totalCallDurationInSeconds) {
                    $totalCallDurationFormatted = gmdate("H:i:s", $totalCallDurationInSeconds);
                } else {
                    $totalCallDurationFormatted = '00:00:00';
                }
                // Format the total hold time
                $totalHoldTimeFormatted = $totalHoldTimeInSeconds
                    ? gmdate("H:i:s", $totalHoldTimeInSeconds)
                    : '00:00:00';

                // Format the average hold time
                $avgHoldTimeFormatted = $avgHoldTimeInSeconds
                    ? gmdate("H:i:s", $avgHoldTimeInSeconds)
                    : '00:00:00';

                // Calculating
                $callInteractionCount = $filteredCallDurations->count();
                $totalOutbound = $filteredCallDurations->where('call_type', 'Outbound')->count();
                $totalInbound = $filteredCallDurations->where('call_type', 'Inbound')->count();
                $totalRing = $filteredCallDurations->sum('ring');
                $averageRing = $filteredCallDurations->avg('ring');
                $totalHandledCalls = $filteredCallDurations->count();
                $totalEnderAgent = $filteredCallDurations->where('call_ender', 'Agent')->count();
                $totalEnderCustomer = $filteredCallDurations->where('call_ender', 'Customer')->count();
                $totalEnderSystem = $filteredCallDurations->where('call_ender', 'System')->count();

                // $totalHandledCalls = $filteredCallDurations->count();
                // foreach
                if ($this->role == 4) {
                    $qaFlagsCount = 0;
                } else {
                    $qaFlagsCount = $filteredCallDurations->filter(function ($rrr) {
                        return $rrr->qaFlags->isNotEmpty();
                    })->count();
                }

                // Count AI flags
                $aiFlagsCount = $filteredCallDurations->filter(function ($interaction) {
                    return $interaction->ai_flag == '1';
                })->count();

                $arrays = [
                    'group_id' => $array->id,
                    'group_name' => $array->name,
                    'avg_interactions_duration' => $avgCallDurationFormatted,
                    'avg_hold_time' => $avgHoldTimeFormatted,
                    'total_hold_time' => $totalHoldTimeFormatted,
                    'total_interactions' => $callInteractionCount,
                    'short_call_duration_count' => $shortCallDurationCount,
                    'long_call_duration_count' => $longCallDurationCount,
                    'long_hold_duration_count' => $longHoldDurationCount,
                    'total_call_duration' => $totalCallDurationFormatted,
                    'total_outbound' => $totalOutbound,
                    'total_inbound' => $totalInbound,
                    'countEvaluation' => $countEvaluation,
                    'avgEvaluationScore' => $averageQualityPercentage,
                    'totalRing' => $totalRing,
                    'averageRing' => round($averageRing, 1),
                    'totalHandledCalls' => $totalHandledCalls,
                    'qaFlagsCount' => $qaFlagsCount,
                    'aiFlagsCount' => $aiFlagsCount,
                    'totalEnderAgent' => $totalEnderAgent,
                    'totalEnderCustomer' => $totalEnderCustomer,
                    'totalEnderSystem' => $totalEnderSystem,

                ];

                $this->dataPage = $arrays;
                $this->dataPage2 = $arrays;
                if ($callInteractionCount > 0) {
                    $this->dispatch('update-qaflags', [
                        'val' => $aiFlagsCount,
                    ]);
                }
            } elseif ($groupId) {
                $array = UserGroup::where('id', $groupId)
                    ->first();

                $countEvaluation = 0;
                $averageQualityPercentage = 0;
                $totalRing = 0;
                $avgRingDuration = 0;
                if ($this->role == 4) {
                    foreach ($array->organization->evaluationForm as $evaluationForm) {
                        $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('user_id', auth()->user()->id)->count();
                        $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('user_id', auth()->user()->id)
                            ->avg('quality_percentage');
                    }
                } else {
                    if ($this->role == 2) {
                        foreach ($array->organization->evaluationForm as $evaluationForm) {
                            $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->count();
                            $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->avg('quality_percentage');
                        }
                    } elseif ($this->role == 5) {
                        foreach ($array->organization->evaluationForm as $evaluationForm) {
                            $countEvaluation += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('created_by', auth()->user()->id)->count();
                            $averageQualityPercentage += $evaluationForm->evaluationSubmissions()->whereBetween('created_at', [$this->dateFrom, $this->dateTo])->where('created_by', auth()->user()->id)
                                ->avg('quality_percentage');
                        }
                    }
                }

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);
                // if agent user to get data for him only
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->id);
                }

                /* if the hold duration or call duration is null will be skip on this record */
                /**/
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    /**/
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    /**/
                });
                /**/
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    /**/
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    /**/
                });
                /* if the hold duration or call duration is null will be skip on this record */

                // Total Call Duration in seconds
                $totalCallDurationInSeconds = $filteredCallDurations->sum(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                // Avg Call Duration in seconds
                $avgCallDurationInSeconds = $filteredCallDurations->avg(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                // Formatting average call duration
                if ($avgCallDurationInSeconds) {
                    $avgCallDurationFormatted = gmdate("H:i:s", $avgCallDurationInSeconds);
                } else {
                    $avgCallDurationFormatted = '00:00:00';
                }

                // Calculate other call metrics where call duration > 2
                $shortCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    return strtotime($interaction->call_duration) < strtotime('00:02:00');
                })->count();

                $longCallDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    return strtotime($interaction->call_duration) > strtotime('00:08:00');
                })->count();

                $longHoldDurationCount = $filteredCallDurations->filter(function ($interaction) {
                    return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                })->count();

                // Total Hold Time in seconds
                $totalHoldTimeInSeconds = $filteredCallDurations->sum(function ($interaction) {
                    list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                    return ($hours * 3600) + ($minutes * 60) + $seconds;
                });

                // Calculate the average hold time in seconds
                $totalCalls = $filteredCallDurations->count();
                $avgHoldTimeInSeconds = $totalCalls > 0 ? $totalHoldTimeInSeconds / $totalCalls : 0;

                // Formatting total call duration
                if ($totalCallDurationInSeconds) {
                    $totalCallDurationFormatted = gmdate("H:i:s", $totalCallDurationInSeconds);
                } else {
                    $totalCallDurationFormatted = '00:00:00';
                }

                // Format the total hold time
                $totalHoldTimeFormatted = $totalHoldTimeInSeconds
                    ? gmdate("H:i:s", $totalHoldTimeInSeconds)
                    : '00:00:00';

                // Format the average hold time
                $avgHoldTimeFormatted = $avgHoldTimeInSeconds
                    ? gmdate("H:i:s", $avgHoldTimeInSeconds)
                    : '00:00:00';

                // Calculating
                $callInteractionCount = $filteredCallDurations->count();
                $totalOutbound = $filteredCallDurations->where('call_type', 'Outbound')->count();
                $totalInbound = $filteredCallDurations->where('call_type', 'Inbound')->count();
                $totalRing = $filteredCallDurations->sum('ring');
                $averageRing = $filteredCallDurations->avg('ring');
                $totalHandledCalls = $filteredCallDurations->count();
                $totalEnderAgent = $filteredCallDurations->where('call_ender', 'Agent')->count();
                $totalEnderCustomer = $filteredCallDurations->where('call_ender', 'Customer')->count();
                $totalEnderSystem = $filteredCallDurations->where('call_ender', 'System')->count();

                if ($this->role == 4) {
                    $qaFlagsCount = 0;
                } else {
                    $qaFlagsCount = $filteredCallDurations->filter(function ($rrr) {
                        return $rrr->qaFlags->isNotEmpty();
                    })->count();
                }

                // Count AI flags
                $aiFlagsCount = $filteredCallDurations->filter(function ($interaction) {
                    return $interaction->ai_flag == '1';
                })->count();

                $arrays = [
                    'group_id' => $array->id,
                    'group_name' => $array->name,
                    'avg_interactions_duration' => $avgCallDurationFormatted,
                    'avg_hold_time' => $avgHoldTimeFormatted,
                    'total_hold_time' => $totalHoldTimeFormatted,
                    'total_interactions' => $callInteractionCount,
                    'short_call_duration_count' => $shortCallDurationCount,
                    'long_call_duration_count' => $longCallDurationCount,
                    'long_hold_duration_count' => $longHoldDurationCount,
                    'total_call_duration' => $totalCallDurationFormatted,
                    'total_outbound' => $totalOutbound,
                    'total_inbound' => $totalInbound,
                    'countEvaluation' => $countEvaluation,
                    'avgEvaluationScore' => $averageQualityPercentage,
                    'totalRing' => $totalRing,
                    'averageRing' => round($averageRing, 1),
                    'totalHandledCalls' => $totalHandledCalls,
                    'qaFlagsCount' => $qaFlagsCount,
                    'aiFlagsCount' => $aiFlagsCount,
                    'totalEnderAgent' => $totalEnderAgent,
                    'totalEnderCustomer' => $totalEnderCustomer,
                    'totalEnderSystem' => $totalEnderSystem,
                ];

                $this->dataPage = $arrays;
                if ($callInteractionCount > 0) {
                    $this->dispatch('update-qaflags', [
                        'val' => $aiFlagsCount,
                    ]);
                }
            } else {
                $this->dataPage = [
                    'group_id' => 0,
                    'group_name' => 'null',
                    'avg_interactions_duration' => '00:00:00',
                    'avg_hold_time' => '00:00:00',
                    'total_interactions' => '0',
                    'short_call_duration_count' => '0',
                    'long_call_duration_count' => '0',
                    'long_hold_duration_count' => '0',
                    'total_call_duration' => '00:00:00',
                    'total_outbound' => '0',
                    'total_inbound' => '0',
                ];
            }

            // Chart 2
            $totalOutbound = $this->dataPage['total_outbound'];
            $totalInbound = $this->dataPage['total_inbound'];

            $count = $totalOutbound + $totalInbound;

            $averageOutbound = $count > 0 ? $totalOutbound / $count : 0;
            $averageInbound = $count > 0 ? $totalInbound / $count : 0;

            $this->getChartData($this->cardSelected);
            $this->dispatch('update-chart', [
                'chartTitle' => 'Call',
                'percentage_inbound' => round($averageInbound * 100),
                'percentage_outbound' => round($averageOutbound * 100),
            ]);
        } else {
            $this->getAllAccountsData();
        }
    }

    public function callBackendMethod()
    {
        // Chart 2
        $totalOutbound = $this->dataPage['total_outbound'];
        $totalInbound = $this->dataPage['total_inbound'];

        $count = $totalOutbound + $totalInbound;

        $averageOutbound = $count > 0 ? $totalOutbound / $count : 0;
        $averageInbound = $count > 0 ? $totalInbound / $count : 0;

        $this->dispatch('update-chart', [
            'chartTitle' => 'Call',
            'percentage_inbound' => round($averageInbound * 100),
            'percentage_outbound' => round($averageOutbound * 100),
        ]);

        $this->dispatch('click_all');
    }
    // public function getAllAccountsData()
    // {
    //     $this->accountIDFilter = false;
    //     $array = Organization::query()->get();

    //     $totalCallDurationInSeconds = 0;
    //     $totalHoldTimeInSeconds = 0;
    //     $totalRing = 0;
    //     $totalInteractions = 0;
    //     $totalOutbound = 0;
    //     $totalInbound = 0;
    //     $totalEvaluationCount = 0;
    //     $totalShortCallDurationCount = 0;
    //     $totalLongCallDurationCount = 0;
    //     $totalLongHoldDurationCount = 0;
    //     $totalEvaluationScore = 0;
    //     $totalQaFlagsCount = 0;

    //     $countEvaluationOrg = 0;
    //     $averageQualityPercentageOrg = 0;
    //     $countOrg = 0;

    //     foreach ($array as $organization) {

    //         foreach ($organization->evaluationForm as $evaluationForm) {
    //             $countEvaluationOrg += $evaluationForm->evaluationSubmissions()
    //                 ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
    //                 ->count();

    //             $averageQualityPercentageOrg += $evaluationForm->evaluationSubmissions()
    //                 ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
    //                 ->avg('quality_percentage');
    //             $countOrg++;
    //         }

    //         $filteredCallDurations = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

    //         $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
    //             return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false &&
    //                 !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
    //         });

    //         $totalCallDurationInSeconds += $filteredCallDurations->sum(function ($interaction) {
    //             list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
    //             return ($hours * 3600) + ($minutes * 60) + $seconds;
    //         });

    //         $totalHoldTimeInSeconds += $filteredCallDurations->sum(function ($interaction) {
    //             list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
    //             return ($hours * 3600) + ($minutes * 60) + $seconds;
    //         });

    //         $totalRing += $filteredCallDurations->sum('ring');
    //         $totalInteractions += $filteredCallDurations->count();
    //         $totalOutbound += $filteredCallDurations->where('call_type', 'Outbound')->count();
    //         $totalInbound += $filteredCallDurations->where('call_type', 'Inbound')->count();
    //         $totalShortCallDurationCount += $filteredCallDurations->filter(fn($i) => strtotime($i->call_duration) < strtotime('00:02:00'))->count();
    //         $totalLongCallDurationCount += $filteredCallDurations->filter(fn($i) => strtotime($i->call_duration) > strtotime('00:08:00'))->count();
    //         $totalLongHoldDurationCount += $filteredCallDurations->filter(fn($i) => strtotime($i->hold_duration) > strtotime('00:02:00'))->count();
    //         $totalEvaluationCount += $countOrg;
    //         $totalEvaluationScore += $averageQualityPercentageOrg;
    //         $totalQaFlagsCount += $filteredCallDurations->filter(function ($interaction) {
    //             return $interaction->qaFlags->isNotEmpty();
    //         })->count();
    //     }

    //     // if ($count == 0) {
    //     //     $averageQualityPercentage = 0;
    //     // } else {

    //     //     $averageQualityPercentage = $averageQualityPercentage / $count;
    //     // }

    //     // Calculate averages and formatted totals
    //     $avgCallDurationFormatted = $totalInteractions ? gmdate("H:i:s", $totalCallDurationInSeconds / $totalInteractions) : '00:00:00';
    //     $totalCallDurationFormatted = gmdate("H:i:s", $totalCallDurationInSeconds);
    //     $avgHoldTimeFormatted = $totalInteractions ? gmdate("H:i:s", $totalHoldTimeInSeconds / $totalInteractions) : '00:00:00';
    //     $totalHoldTimeFormatted = gmdate("H:i:s", $totalHoldTimeInSeconds);

    //     $results = [
    //         'avg_interactions_duration' => $avgCallDurationFormatted,
    //         'avg_hold_time' => $avgHoldTimeFormatted,
    //         'total_hold_time' => $totalHoldTimeFormatted,
    //         'total_interactions' => $totalInteractions,
    //         'short_call_duration_count' => $totalShortCallDurationCount,
    //         'long_call_duration_count' => $totalLongCallDurationCount,
    //         'long_hold_duration_count' => $totalLongHoldDurationCount,
    //         'total_call_duration' => $totalCallDurationFormatted,
    //         'total_outbound' => $totalOutbound,
    //         'total_inbound' => $totalInbound,
    //         'countEvaluation' => $totalEvaluationCount,
    //         'avgEvaluationScore' => $totalEvaluationCount ? $totalEvaluationScore / $totalEvaluationCount : 0,
    //         'totalRing' => $totalRing,
    //         'averageRing' => $totalInteractions ? round($totalRing / $totalInteractions, 1) : 0,
    //         'qaFlagsCount' => $totalQaFlagsCount,
    //     ];

    //     $this->dataPage = $results;

    //     $count = $totalOutbound + $totalInbound;

    //     $averageOutbound = $count > 0 ? $totalOutbound / $count : 0;
    //     $averageInbound = $count > 0 ? $totalInbound / $count : 0;

    //     $this->dispatch('update-chart', [
    //         'chartTitle' => 'Call',
    //         'percentage_inbound' => round($averageInbound * 100),
    //         'percentage_outbound' => round($averageOutbound * 100),
    //     ]);

    //     // $this->getData('All');

    //     $this->groupsAccount = null;
    //     $this->accountNameFilter = 'All';
    //     $this->dispatch('clickFirstCard');
    // }

    public function getAllAccountsData()
    {

        ini_set('memory_limit', '-1');

        $this->accountIDFilter = false;
        $this->getData(null);
        $this->getChartData($this->cardSelected);
        return;
        // Initialize totals
        $totals = [
            'call_duration' => 0,
            'hold_time' => 0,
            'ring' => 0,
            'interactions' => 0,
            'outbound' => 0,
            'inbound' => 0,
            'evaluation_count' => 0,
            'evaluation_score' => 0,
            'short_call_count' => 0,
            'long_call_count' => 0,
            'long_hold_count' => 0,
            'qa_flags' => 0,
        ];

        // Fetch organizations with relationships to minimize queries
        $organizations = Organization::with([
            'evaluationForm.evaluationSubmissions' => function ($query) {
                $query->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
            },
            'callInteraction.qaFlags',
        ])->get();

        foreach ($organizations as $organization) {
            // Process evaluation forms
            foreach ($organization->evaluationForm as $evaluationForm) {
                $submissions = $evaluationForm->evaluationSubmissions;

                $totals['evaluation_count'] += $submissions->count();
                $totals['evaluation_score'] += $submissions->avg('quality_percentage') ?? 0;
            }

            // Process call interactions
            $filteredCallDurations = $organization->callInteraction->filter(function ($interaction) {
                return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false &&
                    !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
            });

            $totals['call_duration'] += $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->call_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });

            $totals['hold_time'] += $filteredCallDurations->sum(function ($interaction) {
                list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                return ($hours * 3600) + ($minutes * 60) + $seconds;
            });

            $totals['ring'] += $filteredCallDurations->sum('ring');
            $totals['interactions'] += $filteredCallDurations->count();
            $totals['outbound'] += $filteredCallDurations->where('call_type', 'Outbound')->count();
            $totals['inbound'] += $filteredCallDurations->where('call_type', 'Inbound')->count();
            $totals['short_call_count'] += $filteredCallDurations->filter(fn($i) => strtotime($i->call_duration) < strtotime('00:02:00'))->count();
            $totals['long_call_count'] += $filteredCallDurations->filter(fn($i) => strtotime($i->call_duration) > strtotime('00:08:00'))->count();
            $totals['long_hold_count'] += $filteredCallDurations->filter(fn($i) => strtotime($i->hold_duration) > strtotime('00:02:00'))->count();
            $totals['qa_flags'] += $filteredCallDurations->filter(fn($interaction) => $interaction->qaFlags->isNotEmpty())->count();
        }

        // Calculate averages and formatted totals
        $avgCallDurationFormatted = $totals['interactions'] ? gmdate("H:i:s", $totals['call_duration'] / $totals['interactions']) : '00:00:00';
        $totalCallDurationFormatted = gmdate("H:i:s", $totals['call_duration']);
        $avgHoldTimeFormatted = $totals['interactions'] ? gmdate("H:i:s", $totals['hold_time'] / $totals['interactions']) : '00:00:00';
        $totalHoldTimeFormatted = gmdate("H:i:s", $totals['hold_time']);

        $results = [
            'avg_interactions_duration' => $avgCallDurationFormatted,
            'avg_hold_time' => $avgHoldTimeFormatted,
            'total_hold_time' => $totalHoldTimeFormatted,
            'total_interactions' => $totals['interactions'],
            'short_call_duration_count' => $totals['short_call_count'],
            'long_call_duration_count' => $totals['long_call_count'],
            'long_hold_duration_count' => $totals['long_hold_count'],
            'total_call_duration' => $totalCallDurationFormatted,
            'total_outbound' => $totals['outbound'],
            'total_inbound' => $totals['inbound'],
            'countEvaluation' => $totals['evaluation_count'],
            'avgEvaluationScore' => $totals['evaluation_count'] ? $totals['evaluation_score'] / $totals['evaluation_count'] : 0,
            'totalRing' => $totals['ring'],
            'averageRing' => $totals['interactions'] ? round($totals['ring'] / $totals['interactions'], 1) : 0,
            'qaFlagsCount' => $totals['qa_flags'],
        ];

        $this->dataPage = $results;

        $count = $totals['outbound'] + $totals['inbound'];
        $averageOutbound = $count > 0 ? $totals['outbound'] / $count : 0;
        $averageInbound = $count > 0 ? $totals['inbound'] / $count : 0;

        $this->dispatch('update-chart', [
            'chartTitle' => 'Call',
            'percentage_inbound' => round($averageInbound * 100),
            'percentage_outbound' => round($averageOutbound * 100),
        ]);

        $this->groupsAccount = null;
        $this->accountNameFilter = 'All';
        $this->dispatch('clickFirstCard');
    }

    public function getGroupFilter()
    {
        if ($this->searchGroup && $this->accountIDFilter) {
            $query = UserGroup::query();

            if (!empty($this->accountIDFilter)) {
                $query->where('organization_id', $this->accountIDFilter);
            }
            $query->where('name', 'like', '%' . $this->searchGroup . '%');
            $this->groupsAccount = $query->get();
        }
    }

    public function isTrendingUp($dates, $avgDurations)
    {
        // Check if arrays are empty or if all data is flat
        if (empty($dates) || empty($avgDurations) || count($dates) !== count($avgDurations)) {
            return true; // No data or flat data is considered trending up
        }

        // Reverse the $avgDurations array to align with chronological order
        $avgDurations = array_reverse($avgDurations);

        // Calculate the total trend
        $totalTrend = 0;
        for ($i = 1; $i < count($avgDurations); $i++) {
            $totalTrend += $avgDurations[$i] - $avgDurations[$i - 1];
        }

        // Return true if trending up or flat, false if trending down
        return $totalTrend >= 0;
    }

    public function updateAllTrendCharts()
    {
        return true;
        // avg hold duration
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {
                // Get the organization by account ID
                $array = Organization::where('id', $this->accountIDFilter)
                    ->first();
                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate daily average durations (in minutes)
                $dailyAvgDurationsInMinutes = $groupedByDate->map(function ($interactions) {
                    $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                    });

                    $avgDurationInSeconds = $totalDurationInSeconds / $interactions->count();
                    return $avgDurationInSeconds / 60; // Convert average duration from seconds to minutes
                });

                // Prepare data for the chart (dates and corresponding average durations in minutes)
                $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes

                // dipatch the trend behavior
                $this->dispatch('chart2Trend', !$this->isTrendingUp($dates, $avgDurations));
            } elseif ($this->groupSelected) {

                $array = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate daily average durations (in minutes)
                $dailyAvgDurationsInMinutes = $groupedByDate->map(function ($interactions) {
                    $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                    });

                    $avgDurationInSeconds = $totalDurationInSeconds / $interactions->count();
                    return $avgDurationInSeconds / 60; // Convert average duration from seconds to minutes
                });

                // Prepare data for the chart (dates and corresponding average durations in minutes)
                $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes

                // dipatch the trend behavior
                $this->dispatch('chart2Trend', !$this->isTrendingUp($dates, $avgDurations));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalDailyDurations = collect();

            foreach ($allOrganizations as $organization) {
                // Filter call interactions by date range
                $filteredCallDurations = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate daily durations for the organization
                $dailyDurations = $groupedByDate->map(function ($interactions) {
                    $totalDurationInSeconds = $interactions->sum(function ($interaction) {
                        list($hours, $minutes, $seconds) = explode(":", $interaction->hold_duration);
                        return ($hours * 3600) + ($minutes * 60) + $seconds; // Convert time to seconds
                    });

                    return [
                        'total_seconds' => $totalDurationInSeconds,
                        'count' => $interactions->count(),
                    ];
                });

                // Merge the organization's daily durations into the global collection
                foreach ($dailyDurations as $date => $data) {
                    if (!$globalDailyDurations->has($date)) {
                        $globalDailyDurations->put($date, [
                            'total_seconds' => 0,
                            'count' => 0,
                        ]);
                    }

                    $currentData = $globalDailyDurations->get($date);
                    $globalDailyDurations->put($date, [
                        'total_seconds' => $currentData['total_seconds'] + $data['total_seconds'],
                        'count' => $currentData['count'] + $data['count'],
                    ]);
                }
            }

            // Calculate global daily averages (in minutes)
            $dailyAvgDurationsInMinutes = $globalDailyDurations->map(function ($data) {
                $avgDurationInSeconds = $data['count'] > 0 ? $data['total_seconds'] / $data['count'] : 0;
                return $avgDurationInSeconds / 60; // Convert seconds to minutes
            });

            // Prepare data for the chart
            $dates = $dailyAvgDurationsInMinutes->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $avgDurations = $dailyAvgDurationsInMinutes->values()->toArray(); // Corresponding average durations in minutes

            // dipatch the trend behavior
            $this->dispatch('chart2Trend', !$this->isTrendingUp($dates, $avgDurations));
        }

        // duration < 2 mins
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {

                $array = Organization::where('id', $this->accountIDFilter)
                    ->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->call_duration) < strtotime('00:02:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // dipatch the trend behavior
                $this->dispatch('chart3Trend', !$this->isTrendingUp($dates, $count));
            } elseif ($this->groupSelected) {

                $array = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->call_duration) < strtotime('00:02:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // dipatch the trend behavior
                $this->dispatch('chart3Trend', !$this->isTrendingUp($dates, $count));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalCallCounts = collect();

            foreach ($allOrganizations as $organization) {
                // Filter call interactions by date range
                $filteredCallDurations = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->arrival_time->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->call_duration) < strtotime('00:02:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Add the organization's call counts to the global collection
                foreach ($callCounts as $date => $count) {
                    $currentCount = $globalCallCounts->get($date, 0);
                    $globalCallCounts->put($date, $currentCount + $count);
                }
            }

            // Sort the global call counts by date
            $globalCallCounts = $globalCallCounts->sortKeys();

            // Prepare data for the chart (dates and corresponding call counts)
            $dates = $globalCallCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $counts = $globalCallCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

            // dipatch the trend behavior
            $this->dispatch('chart3Trend', !$this->isTrendingUp($dates, $counts));
        }

        // duration > 8 mins
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {

                $array = Organization::where('id', $this->accountIDFilter)
                    ->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->call_duration) > strtotime('00:08:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // dipatch the trend behavior
                $this->dispatch('chart4Trend', !$this->isTrendingUp($dates, $count));
            } elseif ($this->groupSelected) {

                $array = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->call_duration) > strtotime('00:08:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // dipatch the trend behavior
                $this->dispatch('chart4Trend', !$this->isTrendingUp($dates, $count));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalCallCounts = collect();

            foreach ($allOrganizations as $organization) {
                // Filter call interactions by date range
                $filteredCallDurations = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->call_duration) && strtotime($interaction->call_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->arrival_time->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations greater than 8 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is greater than 8 minutes
                        return strtotime($interaction->call_duration) > strtotime('00:08:00');
                    })->count(); // Count the number of calls with duration > 8 minutes
                });

                // Add the organization's call counts to the global collection
                foreach ($callCounts as $date => $count) {
                    $currentCount = $globalCallCounts->get($date, 0);
                    $globalCallCounts->put($date, $currentCount + $count);
                }
            }

            // Sort the global call counts by date
            $globalCallCounts = $globalCallCounts->sortKeys();

            // Prepare data for the chart (dates and corresponding call counts)
            $dates = $globalCallCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $counts = $globalCallCounts->values()->toArray(); // Corresponding counts of calls > 8 minutes

            // dipatch the trend behavior
            $this->dispatch('chart4Trend', !$this->isTrendingUp($dates, $counts));
        }

        // Hold > 2 mins
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {

                $array = Organization::where('id', $this->accountIDFilter)
                    ->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);
                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }
                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // dipatch the trend behavior
                $this->dispatch('chart5Trend', !$this->isTrendingUp($dates, $count));
            } elseif ($this->groupSelected) {

                $array = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $array->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with durations less than 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the call duration is less than 2 minutes
                        return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                    })->count(); // Count the number of calls with duration less than 2 minutes
                });

                // Prepare data for the chart (dates and corresponding call counts)
                $dates = $callCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $count = $callCounts->values()->toArray(); // Corresponding counts of calls < 2 minutes

                // dipatch the trend behavior
                $this->dispatch('chart5Trend', !$this->isTrendingUp($dates, $count));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalCallCounts = collect();

            foreach ($allOrganizations as $organization) {
                // Filter call interactions by date range
                $filteredCallDurations = $organization->callInteraction
                    ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }

                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->arrival_time->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count calls with hold_duration > 2 minutes per day
                $callCounts = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        // Check if the hold_duration is greater than 2 minutes
                        return strtotime($interaction->hold_duration) > strtotime('00:02:00');
                    })->count(); // Count the number of calls with hold_duration > 2 minutes
                });

                // Add the organization's call counts to the global collection
                foreach ($callCounts as $date => $count) {
                    $currentCount = $globalCallCounts->get($date, 0);
                    $globalCallCounts->put($date, $currentCount + $count);
                }
            }

            // Sort the global call counts by date
            $globalCallCounts = $globalCallCounts->sortKeys();

            // Prepare data for the chart (dates and corresponding call counts)
            $dates = $globalCallCounts->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $counts = $globalCallCounts->values()->toArray(); // Corresponding counts of calls with hold_duration > 2 minutes

            // dipatch the trend behavior
            $this->dispatch('chart5Trend', !$this->isTrendingUp($dates, $counts));
        }

        // total hold time
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {
                $organization = Organization::where('id', $this->accountIDFilter)->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $organization->callInteraction->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate total hold duration per day
                $totalHoldDurations = $groupedByDate->map(function ($interactions) {
                    return $interactions->reduce(function ($carry, $interaction) {
                        // Add hold durations together
                        $carry += strtotime($interaction->hold_duration) - strtotime('00:00:00');
                        return $carry;
                    }, 0);
                });

                // Prepare data for the chart (dates and corresponding total hold durations)
                $dates = $totalHoldDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $totalHoldTimeInMinutes = $totalHoldDurations->values()->map(function ($seconds) {
                    return round($seconds / 60, 2); // Convert seconds to minutes (rounded to 2 decimals)
                })->toArray();

                // dipatch the trend behavior
                $this->dispatch('chart12Trend', !$this->isTrendingUp($dates, $totalHoldTimeInMinutes));
            } elseif ($this->groupSelected) {
                $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredCallDurations = $userGroup->callInteraction
                    ->whereBetween('created_at', [$this->dateFrom, $this->dateTo]);

                if ($this->role == 4) {
                    $filteredCallDurations = $filteredCallDurations->where('user_id', auth()->user()->user_group_id);
                }

                $filteredCallDurations = $filteredCallDurations->filter(function ($interaction) {
                    return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate total hold duration per day
                $totalHoldDurations = $groupedByDate->map(function ($interactions) {
                    return $interactions->reduce(function ($carry, $interaction) {
                        // Add hold durations together
                        $carry += strtotime($interaction->hold_duration) - strtotime('00:00:00');
                        return $carry;
                    }, 0);
                });

                // Prepare data for the chart (dates and corresponding total hold durations)
                $dates = $totalHoldDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $totalHoldTimeInMinutes = $totalHoldDurations->values()->map(function ($seconds) {
                    return round($seconds / 60, 2); // Convert seconds to minutes (rounded to 2 decimals)
                })->toArray();

                // dipatch the trend behavior
                $this->dispatch('chart12Trend', !$this->isTrendingUp($dates, $totalHoldTimeInMinutes));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalHoldDurations = collect();

            foreach ($allOrganizations as $organization) {
                // Filter interactions by the specified date range
                $filteredCallDurations = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->filter(function ($interaction) {
                        return !empty($interaction->hold_duration) && strtotime($interaction->hold_duration) !== false;
                    });

                // Group by date (day)
                $groupedByDate = $filteredCallDurations->groupBy(function ($interaction) {
                    return $interaction->arrival_time->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate total hold duration per day for the organization
                $dailyHoldDurations = $groupedByDate->map(function ($interactions) {
                    return $interactions->reduce(function ($carry, $interaction) {
                        // Add hold durations together
                        $carry += strtotime($interaction->hold_duration) - strtotime('00:00:00');
                        return $carry;
                    }, 0);
                });

                // Merge the organization's daily hold durations into the global collection
                foreach ($dailyHoldDurations as $date => $duration) {
                    $currentDuration = $globalHoldDurations->get($date, 0);
                    $globalHoldDurations->put($date, $currentDuration + $duration);
                }
            }

            // Sort the global hold durations by date
            $globalHoldDurations = $globalHoldDurations->sortKeys();

            // Prepare data for the chart
            $dates = $globalHoldDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $totalHoldTimeInMinutes = $globalHoldDurations->values()->map(function ($seconds) {
                return round($seconds / 60, 2); // Convert seconds to minutes (rounded to 2 decimals)
            })->toArray();

            // dipatch the trend behavior
            $this->dispatch('chart12Trend', !$this->isTrendingUp($dates, $totalHoldTimeInMinutes));
        }

        // Total Flagged Calls
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {
                $organization = Organization::where('id', $this->accountIDFilter)->first();

                // Apply date range filter to the callInteraction relation
                $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                // Count flags for each interaction
                $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count total flagged calls per day
                $flaggedCalls = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        return $interaction->qaFlags()->exists(); // Check if interaction has related QA flags
                    })->count(); // Count interactions with flags
                });

                // Prepare data for the chart
                $dates = $flaggedCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $counts = $flaggedCalls->values()->toArray(); // Total flagged calls per day

                // dipatch the trend behavior
                $this->dispatch('chart7Trend', !$this->isTrendingUp($dates, $counts));
            } elseif ($this->groupSelected) {
                $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredInteractions = $userGroup->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                if ($this->role == 4) {
                    $filteredInteractions = $filteredInteractions->where('user_id', auth()->user()->user_group_id);
                }

                // Count flags for each interaction
                $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count total flagged calls per day
                $flaggedCalls = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        return $interaction->qaFlags()->exists(); // Check if interaction has related QA flags
                    })->count(); // Count interactions with flags
                });

                // Prepare data for the chart
                $dates = $flaggedCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $counts = $flaggedCalls->values()->toArray(); // Total flagged calls per day

                // dipatch the trend behavior
                $this->dispatch('chart7Trend', !$this->isTrendingUp($dates, $counts));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalFlaggedCalls = collect();

            foreach ($allOrganizations as $organization) {
                // Filter interactions by the specified date range
                $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                // Group by date (day)
                $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Count total flagged calls per day for the organization
                $dailyFlaggedCalls = $groupedByDate->map(function ($interactions) {
                    return $interactions->filter(function ($interaction) {
                        return $interaction->qaFlags()->exists(); // Check if interaction has related QA flags
                    })->count(); // Count interactions with flags
                });

                // Merge the organization's flagged calls into the global collection
                foreach ($dailyFlaggedCalls as $date => $count) {
                    $currentCount = $globalFlaggedCalls->get($date, 0);
                    $globalFlaggedCalls->put($date, $currentCount + $count);
                }
            }

            // Sort the global flagged calls by date
            $globalFlaggedCalls = $globalFlaggedCalls->sortKeys();

            // Prepare data for the chart
            $dates = $globalFlaggedCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $counts = $globalFlaggedCalls->values()->toArray(); // Total flagged calls per day

            // dipatch the trend behavior
            $this->dispatch('chart7Trend', !$this->isTrendingUp($dates, $counts));
        }

        // Average Ring
        if ($this->accountIDFilter) {
            if ($this->groupSelected == 'All') {
                $organization = Organization::where('id', $this->accountIDFilter)->first();

                // Apply date range filter to the callInteraction relation
                $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                // Group by date (day)
                $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate average ring duration per day
                $avgRingDurations = $groupedByDate->map(function ($interactions) {
                    $totalRing = $interactions->sum('ring'); // Sum of 'ring' values
                    $count = $interactions->count(); // Total interactions
                    return $count > 0 ? round($totalRing / $count, 2) : 0; // Calculate average, rounded to 2 decimals
                });

                // Prepare data for the chart
                $dates = $avgRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $averages = $avgRingDurations->values()->toArray(); // Average ring durations per day

                // dipatch the trend behavior
                $this->dispatch('chart8Trend', !$this->isTrendingUp($dates, $averages));
            } elseif ($this->groupSelected) {
                $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                // Apply date range filter to the callInteraction relation
                $filteredInteractions = $userGroup->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                if ($this->role == 4) {
                    $filteredInteractions = $filteredInteractions->where('user_id', auth()->user()->user_group_id);
                }

                // Group by date (day)
                $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate average ring duration per day
                $avgRingDurations = $groupedByDate->map(function ($interactions) {
                    $totalRing = $interactions->sum('ring'); // Sum of 'ring' values
                    $count = $interactions->count(); // Total interactions
                    return $count > 0 ? round($totalRing / $count, 2) : 0; // Calculate average, rounded to 2 decimals
                });

                // Prepare data for the chart
                $dates = $avgRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $averages = $avgRingDurations->values()->toArray(); // Average ring durations per day

                // dipatch the trend behavior
                $this->dispatch('chart8Trend', !$this->isTrendingUp($dates, $averages));
            } else {
            }
        } else {
            $allOrganizations = Organization::query()->get();

            $globalRingData = collect();

            foreach ($allOrganizations as $organization) {
                // Filter interactions by the specified date range
                $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                // Group by date (day)
                $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                    return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                });

                // Calculate total ring and count for each date
                $dailyRingData = $groupedByDate->map(function ($interactions) {
                    return [
                        'total_ring' => $interactions->sum('ring'), // Sum of 'ring' values
                        'count' => $interactions->count(), // Total interactions
                    ];
                });

                // Merge the organization's daily data into the global data
                foreach ($dailyRingData as $date => $data) {
                    if (!$globalRingData->has($date)) {
                        $globalRingData->put($date, [
                            'total_ring' => 0,
                            'count' => 0,
                        ]);
                    }

                    $currentData = $globalRingData->get($date);
                    $globalRingData->put($date, [
                        'total_ring' => $currentData['total_ring'] + $data['total_ring'],
                        'count' => $currentData['count'] + $data['count'],
                    ]);
                }
            }

            // Calculate average ring duration per day globally
            $avgRingDurations = $globalRingData->map(function ($data) {
                return $data['count'] > 0 ? round($data['total_ring'] / $data['count'], 2) : 0; // Calculate average, rounded to 2 decimals
            });

            // Sort by date
            $avgRingDurations = $avgRingDurations->sortKeys();

            // Prepare data for the chart
            $dates = $avgRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
            $averages = $avgRingDurations->values()->toArray(); // Average ring durations per day

            // dipatch the trend behavior
            $this->dispatch('chart8Trend', !$this->isTrendingUp($dates, $averages));
        }
    }

    public function getChartData($type)
    {

        $this->cardSelected = $type;
        $selectedOrg = Organization::where('id', $this->accountIDFilter)->first();
        $selectedUserGroup = UserGroup::where('id', $this->groupSelected)->first();
        $allOrganizations = Organization::query()->get();
        $groupQuery = "";
        $userOrgz = [];


        if (in_array(auth()->user()->role, [2, 5, 7])) {
            $userOrgz = auth()->user()->supervisorOrganizations->pluck('id')->toArray();
        } elseif (auth()->user()->role == 1) {
            $userOrgz = Organization::pluck('id')->toArray();
        }

        if ($type == 'Avg Call Duration Card') {

            // dd($this->accountIDFilter);
            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }

            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }

            $totalStats = DB::select("SELECT DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
               TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,
               COUNT(call_id) AS total_calls
           FROM interactions
           WHERE arrival_time BETWEEN ? AND ?
           $groupQuery
           AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
           GROUP BY receiving_hour
           ORDER BY receiving_hour
       ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));


            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $avgDurations = collect($totalStats)->pluck('avg_duration')->toArray();
            $avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);





            // Prepare data for the chart (dates and corresponding average durations in minutes)

            /*             $this->dispatch('chart1Trend', !$this->isTrendingUp($dates, $avgDurations));
 */

            // Prepare data for the chart (dates and corresponding average durations in minutes)

            // Dispatch data to the chart (adjusting as per chart's requirements)
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Avg. Call Duration (minutes)',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $avgDurations, // Pass the average durations in minutes
                'isDuration' => true,
            ]);
        } elseif ($type == 'Avg Hold Duration Card') {

            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }
            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }

            $totalStats = DB::select("
            SELECT
                DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
                TIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold,
                COUNT(call_id) AS total_calls
            FROM interactions
            WHERE arrival_time BETWEEN ? AND ?
            $groupQuery
            AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
            GROUP BY receiving_hour
             ORDER BY receiving_hour
        ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $avgDurations = collect($totalStats)->pluck('avg_hold')->toArray();
            $avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);

            // Prepare data for the chart

            // dipatch the trend behavior
            //$this->dispatch('chart2Trend', !$this->isTrendingUp($dates, $avgDurations));

            // Dispatch data to the chart
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Avg. Hold Duration (minutes) Across All Organizations',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $avgDurations, // Pass the average durations in minutes
                'isDuration' => true,
            ]);
        } elseif ($type == 'Total Evaluation') {
            if ($this->accountIDFilter) {
                if ($this->groupSelected == 'All') {
                    // Get the organization by account ID
                    // $array = Organization::where('id', $this->accountIDFilter)
                    //     ->first();

                    if (!$selectedOrg) {
                        return; // Or handle accordingly
                    }

                    $countEvaluation = 0;
                    $dailyCount = []; // Initialize an array to store the grouped counts

                    if ($this->role == 4) {
                        foreach ($selectedOrg->evaluationForm as $evaluationForm) {
                            // Get the count of submissions per day, filtered by date range and user
                            $submissions = $evaluationForm?->evaluationSubmissions()
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                                ->where('user_id', auth()->user()->id)
                                ->selectRaw('DATE(created_at) as date, count(*) as count')
                                ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                                ->get();

                            // Add to the daily count array
                            foreach ($submissions as $submission) {
                                $dailyCount[$submission->date] = $submission->count;
                            }
                        }
                    } else {
                        foreach ($selectedOrg->evaluationForm as $evaluationForm) {
                            // Get the count of submissions per day, filtered by date range
                            $submissions = $evaluationForm?->evaluationSubmissions()
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                                ->selectRaw('DATE(created_at) as date, count(*) as count')
                                ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                                ->get();

                            // Add to the daily count array
                            foreach ($submissions as $submission) {
                                $dailyCount[$submission->date] = $submission->count;
                            }
                        }
                    }

                    // Prepare data for the chart (dates and corresponding counts)
                    $dates = array_keys($dailyCount); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $counts = array_values($dailyCount); // Corresponding counts of evaluation submissions

                    // dipatch the trend behavior
                    $this->dispatch('chart3Trend', $this->isTrendingUp($dates, $counts));

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Evaluation Submissions by Date',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $counts, // Pass the count of evaluation submissions per date
                        'isDuration' => false,
                    ]);
                } elseif ($this->groupSelected) {

                    // Get the organization by account ID

                    // $array = UserGroup::where('id', $this->groupSelected)->first();

                    if (!$selectedUserGroup) {
                        return; // Or handle accordingly
                    }

                    $countEvaluation = 0;
                    $dailyCount = []; // Initialize an array to store the grouped counts

                    if ($this->role == 4) {
                        foreach ($selectedUserGroup->organization->evaluationForm as $evaluationForm) {
                            // Get the count of submissions per day, filtered by date range and user
                            $submissions = $evaluationForm?->evaluationSubmissions()
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                                ->where('user_id', auth()->user()->id)
                                ->selectRaw('DATE(created_at) as date, count(*) as count')
                                ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                                ->get();

                            // Add to the daily count array
                            foreach ($submissions as $submission) {
                                $dailyCount[$submission->date] = $submission->count;
                            }
                        }
                    } else {
                        foreach ($selectedUserGroup->organization->evaluationForm as $evaluationForm) {
                            // Get the count of submissions per day, filtered by date range
                            $submissions = $evaluationForm?->evaluationSubmissions()
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                                ->selectRaw('DATE(created_at) as date, count(*) as count')
                                ->groupByRaw('DATE(created_at)') // Ensure DATE(created_at) is in the GROUP BY clause
                                ->get();

                            // Add to the daily count array
                            foreach ($submissions as $submission) {
                                $dailyCount[$submission->date] = $submission->count;
                            }
                        }
                    }

                    // Prepare data for the chart (dates and corresponding counts)
                    $dates = array_keys($dailyCount); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $counts = array_values($dailyCount); // Corresponding counts of evaluation submissions

                    // dipatch the trend behavior
                    $this->dispatch('chart3Trend', $this->isTrendingUp($dates, $counts));

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Evaluation Submissions by Date',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $counts, // Pass the count of evaluation submissions per date
                        'isDuration' => false,
                    ]);
                } else {
                    $this->dispatch('update-chart2', [
                        'chartTitle' => '',
                        'dates' => [],
                        'avgDurations' => [],
                        'isDuration' => true,
                    ]);
                }
            } else {
                // $allOrganizations = Organization::query()->get();

                $globalDailyCount = collect();

                foreach ($allOrganizations as $organization) {
                    if (!$organization->evaluationForm) {
                        continue; // Skip if no evaluation forms
                    }

                    if ($this->role == 4) {
                        foreach ($organization->evaluationForm as $evaluationForm) {
                            // Get the count of submissions per day, filtered by date range and user
                            $submissions = $evaluationForm?->evaluationSubmissions()
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                                ->where('user_id', auth()->user()->id)
                                ->selectRaw('DATE(created_at) as date, count(*) as count')
                                ->groupByRaw('DATE(created_at)')
                                ->get();

                            // Add to the global daily count collection
                            foreach ($submissions as $submission) {
                                $currentCount = $globalDailyCount->get($submission->date, 0);
                                $globalDailyCount->put($submission->date, $currentCount + $submission->count);
                            }
                        }
                    } else {
                        foreach ($organization->evaluationForm as $evaluationForm) {
                            // Get the count of submissions per day, filtered by date range
                            $submissions = $evaluationForm?->evaluationSubmissions()
                                ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
                                ->selectRaw('DATE(created_at) as date, count(*) as count')
                                ->groupByRaw('DATE(created_at)')
                                ->get();

                            // Add to the global daily count collection
                            foreach ($submissions as $submission) {
                                $currentCount = $globalDailyCount->get($submission->date, 0);
                                $globalDailyCount->put($submission->date, $currentCount + $submission->count);
                            }
                        }
                    }
                }

                // Sort the global daily count by date
                $globalDailyCount = $globalDailyCount->sortKeys();

                // Prepare data for the chart (dates and corresponding counts)
                $dates = $globalDailyCount->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $counts = $globalDailyCount->values()->toArray(); // Corresponding counts of evaluation submissions

                // dipatch the trend behavior
                $this->dispatch('chart3Trend', $this->isTrendingUp($dates, $counts));

                // Dispatch data to the chart
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Evaluation Submissions by Date Across All Organizations',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $counts, // Pass the count of evaluation submissions per date
                    'isDuration' => false,
                ]);
            }
        } elseif ($type == 'Duration < 2 Minutes') {


            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }
            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }

            $totalStats = DB::select("
                    SELECT
                        DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
                        COUNT(call_id) AS total_calls_lessThan
                    FROM interactions
                    WHERE arrival_time BETWEEN ? AND ?
                    AND  call_duration < '00:02:00'
                    $groupQuery
                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                    GROUP BY receiving_hour
                     ORDER BY receiving_hour
                ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $avgDurations = collect($totalStats)->pluck('total_calls_lessThan')->toArray();
            //$avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);

            // dipatch the trend behaviordsf
            //$this->dispatch('chart3Trend', !$this->isTrendingUp($dates, $count));

            // Dispatch data to the chart (adjusting as per chart's requirements)
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Calls < 2 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $avgDurations, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false,
            ]);
        } elseif ($type == 'Duration > 8 Minutes') {



            // $array = Organization::where('id', $this->accountIDFilter)

            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }
            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }


            $totalStats = DB::select("
                    SELECT
                        DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
                        COUNT(call_id) AS total_calls_moreThan
                    FROM interactions
                    WHERE arrival_time BETWEEN ? AND ?
                    AND  call_duration > '00:08:00'
                    $groupQuery
                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                    GROUP BY receiving_hour
                     ORDER BY receiving_hour
                ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $avgDurations = collect($totalStats)->pluck('total_calls_moreThan')->toArray();
            //$avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);

            // dipatch the trend behavior
            $this->dispatch('chart4Trend', !$this->isTrendingUp($dates, $avgDurations));

            // Dispatch data to the chart (adjusting as per chart's requirements)
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Calls > 8 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $avgDurations, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false,
            ]);
        } elseif ($type == 'Hold Duration > 2 Minutes') {


            // $array = Organization::where('id', $this->accountIDFilter)
            //     ->first();

            // Apply date range filter to the callInteraction relation

            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }
            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }


            $totalStats = DB::select("
                    SELECT
                        DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
                        COUNT(call_id) AS holad_moreThan
                    FROM interactions
                    WHERE arrival_time BETWEEN ? AND ?
                    AND  hold_duration > '00:02:00'
                    $groupQuery
                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                    GROUP BY receiving_hour
                     ORDER BY receiving_hour
                ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $avgDurations = collect($totalStats)->pluck('holad_moreThan')->toArray();
            //$avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);

            // Dispatch data to the chart (adjusting as per chart's requirements)
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Hold Duration > 2 Minutes',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $avgDurations, // Pass the count of calls with durations < 2 minutes
                'isDuration' => false,
            ]);
        } elseif ($type == 'Avg Ring') {


            // $organization = Organization::where('id', $this->accountIDFilter)->first();

            // Apply date range filter to the callInteraction relation
            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }
            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }


            $totalStats = DB::select("
                    SELECT
                        DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
                          AVG(ring) AS avg_ring
                    FROM interactions
                    WHERE arrival_time BETWEEN ? AND ?

                    $groupQuery
                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                    GROUP BY receiving_hour
                     ORDER BY receiving_hour
                ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $avgDurations = collect($totalStats)->pluck('avg_ring')->toArray();
            //$avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);

            // Dispatch data to the chart
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Average Ring Duration',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $avgDurations, // Pass the average ring durations
                'isDuration' => true,
            ]);
        }
        // total hold time
        elseif ($type == 'Total Hold Time') {


            // $organization = Organization::where('id', $this->accountIDFilter)->first();

            // Apply date range filter to the callInteraction relation
            if ($this->accountIDFilter) {
                $userOrgz = [$this->accountIDFilter];
            }
            if ($this->groupSelected != "All" && !is_null($this->groupSelected)) {
                $groupQuery = "AND user_group_id  = $this->groupSelected";
            }


            $totalStats = DB::select("
                    SELECT
                        DATE_FORMAT(arrival_time, \"$this->queryGrouFormat\") AS receiving_hour,
                              SUM(TIME_TO_SEC(hold_duration)) / 60 AS total_hold_time_in_minutes

                    FROM interactions
                    WHERE arrival_time BETWEEN ? AND ?

                    $groupQuery
                    AND organization_id IN (" . implode(',', array_fill(0, count($userOrgz), '?')) . ")
                    GROUP BY receiving_hour
                     ORDER BY receiving_hour
                ", array_merge([$this->dateFrom, $this->dateTo], $userOrgz));
            $dates = collect($totalStats)->pluck('receiving_hour')->toArray();
            $total_hold_time = collect($totalStats)->pluck('total_hold_time_in_minutes')->toArray();
            //$avgDurations = array_map([$this, 'timeToSeconds'], $avgDurations);

            // Dispatch data to the chart
            $this->dispatch('update-chart2', [
                'chartTitle' => 'Total Hold Time',
                'dates' => $dates, // Pass the dates to the chart
                'avgDurations' => $total_hold_time, // Pass total hold time (in minutes)
                'isDuration' => true,
            ]);
        }

        // Total Flagged Calls
        elseif ($type == 'Total Flaged Calls') {
            if ($this->accountIDFilter) {
                if ($this->groupSelected == 'All') {
                    // $organization = Organization::where('id', $this->accountIDFilter)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedOrg->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    // Count flags for each interaction
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Count total flagged calls per day
                    $flaggedCalls = $groupedByDate->map(function ($interactions) {
                        return $interactions->filter(function ($interaction) {
                            return $interaction->qaFlags()->exists(); // Check if interaction has related QA flags
                        })->count(); // Count interactions with flags
                    });

                    // Prepare data for the chart
                    $dates = $flaggedCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $counts = $flaggedCalls->values()->toArray(); // Total flagged calls per day

                    // dipatch the trend behavior
                    $this->dispatch('chart7Trend', !$this->isTrendingUp($dates, $counts));

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Total Flagged Calls',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $counts, // Pass the count of flagged calls
                        'isDuration' => false,
                    ]);
                } elseif ($this->groupSelected) {
                    // $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedUserGroup->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    if ($this->role == 4) {
                        $filteredInteractions = $filteredInteractions->where('user_id', auth()->user()->user_group_id);
                    }

                    // Count flags for each interaction
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Count total flagged calls per day
                    $flaggedCalls = $groupedByDate->map(function ($interactions) {
                        return $interactions->filter(function ($interaction) {
                            return $interaction->qaFlags()->exists(); // Check if interaction has related QA flags
                        })->count(); // Count interactions with flags
                    });

                    // Prepare data for the chart
                    $dates = $flaggedCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $counts = $flaggedCalls->values()->toArray(); // Total flagged calls per day

                    // dipatch the trend behavior
                    $this->dispatch('chart7Trend', !$this->isTrendingUp($dates, $counts));

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Total Flagged Calls',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $counts, // Pass the count of flagged calls
                        'isDuration' => false,
                    ]);
                } else {
                    $this->dispatch('update-chart2', [
                        'chartTitle' => '',
                        'dates' => [],
                        'avgDurations' => [],
                        'isDuration' => false,
                    ]);
                }
            } else {
                // $allOrganizations = Organization::query()->get();

                $globalFlaggedCalls = collect();

                // foreach ($allOrganizations as $organization) {
                //     // Filter interactions by the specified date range
                //     $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                //     // Group by date (day)
                //     $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                //         return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                //     });

                //     // Count total flagged calls per day for the organization
                //     $dailyFlaggedCalls = $groupedByDate->map(function ($interactions) {
                //         return $interactions->filter(function ($interaction) {
                //             return $interaction->qaFlags()->exists(); // Check if interaction has related QA flags
                //         })->count(); // Count interactions with flags
                //     });

                //     // Merge the organization's flagged calls into the global collection
                //     foreach ($dailyFlaggedCalls as $date => $count) {
                //         $currentCount = $globalFlaggedCalls->get($date, 0);
                //         $globalFlaggedCalls->put($date, $currentCount + $count);
                //     }
                // }

                // Step 1: Fetch all relevant interactions and their related QA flags in one query
                $allInteractions = Interaction::whereIn('organization_id', $allOrganizations->pluck('id'))
                    ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->with('qaFlags') // Eager load related QA flags
                    ->get();

                // Step 2: Process interactions in memory
                $globalFlaggedCalls = collect();

                $allInteractions->groupBy(function ($interaction) {
                    // Group by date (e.g., 2024-12-01)
                    return $interaction->created_at->format($this->queryGrouFormat);
                })->each(function ($interactions, $date) use ($globalFlaggedCalls) {
                    // Count flagged interactions per date
                    $flaggedCount = $interactions->filter(function ($interaction) {
                        return $interaction->qaFlags->isNotEmpty(); // Check if QA flags are present
                    })->count();

                    // Update global flagged calls
                    $currentCount = $globalFlaggedCalls->get($date, 0);
                    $globalFlaggedCalls->put($date, $currentCount + $flaggedCount);
                });

                // Sort the global flagged calls by date
                $globalFlaggedCalls = $globalFlaggedCalls->sortKeys();

                // Prepare data for the chart
                $dates = $globalFlaggedCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $counts = $globalFlaggedCalls->values()->toArray(); // Total flagged calls per day

                // dipatch the trend behavior
                $this->dispatch('chart7Trend', !$this->isTrendingUp($dates, $counts));

                // Dispatch data to the chart
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Total Flagged Calls Across All Organizations',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $counts, // Pass the count of flagged calls
                    'isDuration' => false,
                ]);
            }
        }

        // Average Ring
        elseif ($type == 'Avg Ring') {
            if ($this->accountIDFilter) {
                if ($this->groupSelected == 'All') {
                    // $organization = Organization::where('id', $this->accountIDFilter)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedOrg->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    // Group by date (day)
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Calculate average ring duration per day
                    $avgRingDurations = $groupedByDate->map(function ($interactions) {
                        $totalRing = $interactions->sum('ring'); // Sum of 'ring' values
                        $count = $interactions->count(); // Total interactions
                        return $count > 0 ? round($totalRing / $count, 2) : 0; // Calculate average, rounded to 2 decimals
                    });

                    // Prepare data for the chart
                    $dates = $avgRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $averages = $avgRingDurations->values()->toArray(); // Average ring durations per day

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Average Ring Duration',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $averages, // Pass the average ring durations
                        'isDuration' => true,
                    ]);
                } elseif ($this->groupSelected) {
                    // $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedUserGroup->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    if ($this->role == 4) {
                        $filteredInteractions = $filteredInteractions->where('user_id', auth()->user()->user_group_id);
                    }

                    // Group by date (day)
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Calculate average ring duration per day
                    $avgRingDurations = $groupedByDate->map(function ($interactions) {
                        $totalRing = $interactions->sum('ring'); // Sum of 'ring' values
                        $count = $interactions->count(); // Total interactions
                        return $count > 0 ? round($totalRing / $count, 2) : 0; // Calculate average, rounded to 2 decimals
                    });

                    // Prepare data for the chart
                    $dates = $avgRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $averages = $avgRingDurations->values()->toArray(); // Average ring durations per day

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Average Ring Duration',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $averages, // Pass the average ring durations
                        'isDuration' => true,
                    ]);
                } else {
                    $this->dispatch('update-chart2', [
                        'chartTitle' => '',
                        'dates' => [],
                        'avgDurations' => [],
                        'isDuration' => true,
                    ]);
                }
            } else {
                // $allOrganizations = Organization::query()->get();

                $globalRingData = collect();

                // foreach ($allOrganizations as $organization) {
                //     // Filter interactions by the specified date range
                //     $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                //     // Group by date (day)
                //     $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                //         return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                //     });

                //     // Calculate total ring and count for each date for the organization
                //     $dailyRingData = $groupedByDate->map(function ($interactions) {
                //         return [
                //             'total_ring' => $interactions->sum('ring'), // Sum of 'ring' values
                //             'count' => $interactions->count(), // Total interactions
                //         ];
                //     });

                //     // Merge the organization's daily data into the global collection
                //     foreach ($dailyRingData as $date => $data) {
                //         if (!$globalRingData->has($date)) {
                //             $globalRingData->put($date, [
                //                 'total_ring' => 0,
                //                 'count' => 0,
                //             ]);
                //         }

                //         $currentData = $globalRingData->get($date);
                //         $globalRingData->put($date, [
                //             'total_ring' => $currentData['total_ring'] + $data['total_ring'],
                //             'count' => $currentData['count'] + $data['count'],
                //         ]);
                //     }
                // }

                // Step 1: Fetch all relevant interactions for all organizations in one query
                $allInteractions = Interaction::whereIn('organization_id', $allOrganizations->pluck('id'))
                    ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->get();

                // Step 2: Process interactions in memory
                $globalRingData = collect();

                $allInteractions->groupBy(function ($interaction) {
                    // Group interactions by date (e.g., 2024-12-01)
                    return $interaction->created_at->format($this->queryGrouFormat);
                })->each(function ($interactions, $date) use ($globalRingData) {
                    // Calculate total ring and count for each date
                    $totalRing = $interactions->sum('ring');
                    $count = $interactions->count();

                    // Update global ring data
                    $currentData = $globalRingData->get($date, ['total_ring' => 0, 'count' => 0]);
                    $globalRingData->put($date, [
                        'total_ring' => $currentData['total_ring'] + $totalRing,
                        'count' => $currentData['count'] + $count,
                    ]);
                });

                // Calculate global average ring duration per day
                $avgRingDurations = $globalRingData->map(function ($data) {
                    return $data['count'] > 0 ? round($data['total_ring'] / $data['count'], 2) : 0; // Calculate average, rounded to 2 decimals
                });

                // Sort by date
                $avgRingDurations = $avgRingDurations->sortKeys();

                // Prepare data for the chart
                $dates = $avgRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $averages = $avgRingDurations->values()->toArray(); // Average ring durations per day

                // dipatch the trend behavior
                $this->dispatch('chart8Trend', !$this->isTrendingUp($dates, $averages));

                // Dispatch data to the chart
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Average Ring Duration Across All Organizations',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $averages, // Pass the average ring durations
                    'isDuration' => true,
                ]);
            }
        }

        // Total Ring
        elseif ($type == 'Total Ring') {
            if ($this->accountIDFilter) {
                if ($this->groupSelected == 'All') {
                    // $organization = Organization::where('id', $this->accountIDFilter)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedOrg->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    // Group by date (day)
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Calculate total ring duration per day
                    $totalRingDurations = $groupedByDate->map(function ($interactions) {
                        return $interactions->sum('ring'); // Sum of 'ring' values
                    });

                    // Prepare data for the chart
                    $dates = $totalRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $totals = $totalRingDurations->values()->toArray(); // Total ring durations per day

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Total Ring Duration',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $totals, // Pass the total ring durations
                        'isDuration' => false,
                    ]);
                } elseif ($this->groupSelected) {
                    // $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedUserGroup->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    if ($this->role == 4) {
                        $filteredInteractions = $filteredInteractions->where('user_id', auth()->user()->user_group_id);
                    }

                    // Group by date (day)
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Calculate total ring duration per day
                    $totalRingDurations = $groupedByDate->map(function ($interactions) {
                        return $interactions->sum('ring'); // Sum of 'ring' values
                    });

                    // Prepare data for the chart
                    $dates = $totalRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $totals = $totalRingDurations->values()->toArray(); // Total ring durations per day

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Total Ring Duration',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $totals, // Pass the total ring durations
                        'isDuration' => false,
                    ]);
                } else {
                    $this->dispatch('update-chart2', [
                        'chartTitle' => '',
                        'dates' => [],
                        'avgDurations' => [],
                        'isDuration' => false,
                    ]);
                }
            } else {
                // $allOrganizations = Organization::query()->get();

                $globalRingDurations = collect();

                // foreach ($allOrganizations as $organization) {
                //     // Filter interactions by the specified date range
                //     $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                //     // Group by date (day)
                //     $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                //         return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                //     });

                //     // Calculate total ring duration per day for the organization
                //     $dailyRingDurations = $groupedByDate->map(function ($interactions) {
                //         return $interactions->sum('ring'); // Sum of 'ring' values
                //     });

                //     // Merge the organization's daily ring durations into the global collection
                //     foreach ($dailyRingDurations as $date => $duration) {
                //         $currentDuration = $globalRingDurations->get($date, 0);
                //         $globalRingDurations->put($date, $currentDuration + $duration);
                //     }
                // }

                // Step 1: Fetch all relevant interactions for all organizations in a single query
                $allInteractions = Interaction::whereIn('organization_id', $allOrganizations->pluck('id'))
                    ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->get();

                // Step 2: Process interactions in memory
                $globalRingDurations = collect();

                $allInteractions->groupBy(function ($interaction) {
                    // Group interactions by date (e.g., 2024-12-01)
                    return $interaction->created_at->format($this->queryGrouFormat);
                })->each(function ($interactions, $date) use ($globalRingDurations) {
                    // Calculate total ring duration for each date
                    $totalRingDuration = $interactions->sum('ring');

                    // Update global ring durations
                    $currentDuration = $globalRingDurations->get($date, 0);
                    $globalRingDurations->put($date, $currentDuration + $totalRingDuration);
                });

                // Sort the global ring durations by date
                $globalRingDurations = $globalRingDurations->sortKeys();

                // Prepare data for the chart
                $dates = $globalRingDurations->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $totals = $globalRingDurations->values()->toArray(); // Total ring durations per day

                // Dispatch data to the chart
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Total Ring Duration Across All Organizations',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $totals, // Pass the total ring durations
                    'isDuration' => false,
                ]);
            }
        }

        // Total Handled Calls
        elseif ($type == 'Total Handled Calls') {
            if ($this->accountIDFilter) {
                if ($this->groupSelected == 'All') {
                    // $organization = Organization::where('id', $this->accountIDFilter)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedOrg->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    // Group by date (day)
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Count total interactions per day
                    $handledCalls = $groupedByDate->map(function ($interactions) {
                        return $interactions->count(); // Count total interactions
                    });

                    // Prepare data for the chart
                    $dates = $handledCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $totals = $handledCalls->values()->toArray(); // Total interactions per day

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Total Handled Calls',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $totals, // Pass the total handled calls
                        'isDuration' => false,
                    ]);
                } elseif ($this->groupSelected) {
                    // $userGroup = UserGroup::where('id', $this->groupSelected)->first();

                    // Apply date range filter to the callInteraction relation
                    $filteredInteractions = $selectedUserGroup->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                    if ($this->role == 4) {
                        $filteredInteractions = $filteredInteractions->where('user_id', auth()->user()->user_group_id);
                    }

                    // Group by date (day)
                    $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                        return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                    });

                    // Count total interactions per day
                    $handledCalls = $groupedByDate->map(function ($interactions) {
                        return $interactions->count(); // Count total interactions
                    });

                    // Prepare data for the chart
                    $dates = $handledCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                    $totals = $handledCalls->values()->toArray(); // Total interactions per day

                    // Dispatch data to the chart
                    $this->dispatch('update-chart2', [
                        'chartTitle' => 'Total Handled Calls',
                        'dates' => $dates, // Pass the dates to the chart
                        'avgDurations' => $totals, // Pass the total handled calls
                        'isDuration' => false,
                    ]);
                } else {
                    $this->dispatch('update-chart2', [
                        'chartTitle' => '',
                        'dates' => [],
                        'avgDurations' => [],
                        'isDuration' => false,
                    ]);
                }
            } else {
                // $allOrganizations = Organization::query()->get();

                $globalHandledCalls = collect();

                // foreach ($allOrganizations as $organization) {
                //     // Filter interactions by the specified date range
                //     $filteredInteractions = $organization->callInteraction->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo]);

                //     // Group by date (day)
                //     $groupedByDate = $filteredInteractions->groupBy(function ($interaction) {
                //         return $interaction->created_at->format($this->queryGrouFormat); // Group by date (e.g., 2024-12-01)
                //     });

                //     // Count total interactions per day for the organization
                //     $dailyHandledCalls = $groupedByDate->map(function ($interactions) {
                //         return $interactions->count(); // Count total interactions
                //     });

                //     // Merge the organization's daily handled calls into the global collection
                //     foreach ($dailyHandledCalls as $date => $count) {
                //         $currentCount = $globalHandledCalls->get($date, 0);
                //         $globalHandledCalls->put($date, $currentCount + $count);
                //     }
                // }

                // Step 1: Fetch all relevant interactions for all organizations in a single query
                $allInteractions = Interaction::whereIn('organization_id', $allOrganizations->pluck('id'))
                    ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                    ->get();

                // Step 2: Process interactions in memory
                $globalHandledCalls = collect();

                $allInteractions->groupBy(function ($interaction) {
                    // Group interactions by date (e.g., 2024-12-01)
                    return $interaction->created_at->format($this->queryGrouFormat);
                })->each(function ($interactions, $date) use ($globalHandledCalls) {
                    // Count total interactions for each date
                    $dailyCount = $interactions->count();

                    // Update global handled calls
                    $currentCount = $globalHandledCalls->get($date, 0);
                    $globalHandledCalls->put($date, $currentCount + $dailyCount);
                });

                // Sort the global handled calls by date
                $globalHandledCalls = $globalHandledCalls->sortKeys();

                // Prepare data for the chart
                $dates = $globalHandledCalls->keys()->toArray(); // Dates (e.g., '2024-12-01', '2024-12-02', etc.)
                $totals = $globalHandledCalls->values()->toArray(); // Total interactions per day

                // Dispatch data to the chart
                $this->dispatch('update-chart2', [
                    'chartTitle' => 'Total Handled Calls Across All Organizations',
                    'dates' => $dates, // Pass the dates to the chart
                    'avgDurations' => $totals, // Pass the total handled calls
                    'isDuration' => false,
                ]);
            }
        }
    }

    public function getDate($type)
    {
        $this->dateTo = Carbon::now();
        $this->dateType = $type;

        switch ($type) {
            case 'Last 24 Hours':
                $this->dateFrom = Carbon::now()->subHours(24);
                $this->queryGrouFormat = '%H:00';
                break;

            case 'Last 7 Days':
                $this->dateFrom = Carbon::today()->subDays(6);
                $this->queryGrouFormat = '%Y-%m-%d';

                break;

            case 'Last 30 Days':
                $this->dateFrom = Carbon::today()->subDays(29);
                $this->queryGrouFormat = '%Y-%m-%d';

                break;

            case 'Last 60 Days':
                $this->dateFrom = Carbon::today()->subDays(59);
                $this->queryGrouFormat = '%Y-%m-%d';

                break;

            default:
                $this->dateFrom = null;
                $this->dateTo = null;
                $this->queryGrouFormat = '%Y-%m-%d';

                break;
        }
        $this->setAccountFilter($this->accountIDFilter, $this->accountNameFilter);
        $this->getData($this->groupSelected);
        $this->getChartData($this->cardSelected);
    }
    public function apply_custom_date()
    {
        $this->validate();
        $this->dateType = null;
        $this->dateFrom = $this->dateFromFilter . ' 00:00:00';
        $this->dateTo = $this->dateToFilter . ' 23:59:59';
        $this->setAccountFilter($this->accountIDFilter, $this->accountNameFilter);
        $this->getData($this->groupSelected);
        $this->getChartData($this->cardSelected);
        $this->dispatch('close-modal');
    }
    public function rules()
    {
        return [
            'dateFromFilter' => 'required',
            'dateToFilter' => 'required|after:dateFromFilter',
        ];
    }
    public function render()
    {

        ini_set('memory_limit', '-1');

        $this->dispatch('updateSupervisorFlags', $this->dataPage['qaFlagsCount']);

        if ($this->accountIDFilter !== null && $this->accountIDFilter !== false && ($this->selected_group === null || $this->selected_group === 'All')) {

            $data = Interaction::where('organization_id', $this->accountIDFilter)
                ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                ->selectRaw("
                        COUNT(*) as total_calls,
                        SUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,
                        SUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,
                        SUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections
                    ")
                ->first();

            // Access results
            $totalCalls = $data->total_calls;
            $customerDisconnections = $data->customer_disconnections;
            $agentDisconnections = $data->agent_disconnections;
            $systemDisconnections = $data->system_disconnections;
        } elseif ($this->selected_group !== null && $this->selected_group !== 'All') {

            $data = Interaction::where('user_group_id', $this->selected_group)
                ->whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                ->selectRaw("
                        COUNT(*) as total_calls,
                        SUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,
                        SUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,
                        SUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections
                    ")
                ->first();

            // Access results
            $totalCalls = $data->total_calls;
            $customerDisconnections = $data->customer_disconnections;
            $agentDisconnections = $data->agent_disconnections;
            $systemDisconnections = $data->system_disconnections;
        } else {
            // Calculate for all organizations (no account ID filter)
            $data = Interaction::whereBetween('arrival_time', [$this->dateFrom, $this->dateTo])
                ->selectRaw("
                COUNT(*) as total_calls,
                SUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,
                SUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,
                SUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections
            ")
                ->first();

            // Access results
            $totalCalls = $data->total_calls;
            $customerDisconnections = $data->customer_disconnections;
            $agentDisconnections = $data->agent_disconnections;
            $systemDisconnections = $data->system_disconnections;
        }

        // Calculate percentages
        $this->disconnected_by_customer = $totalCalls > 0
            ? round(($customerDisconnections / $totalCalls) * 100)
            : 0;

        $this->disconnected_by_agent = $totalCalls > 0
            ? round(($agentDisconnections / $totalCalls) * 100)
            : 0;

        $this->disconnected_by_system = $totalCalls > 0
            ? round(($systemDisconnections / $totalCalls) * 100)
            : 0;

        // update trend charts
        // $this->updateAllTrendCharts();

        return view('livewire.analytics-new', ['accounts' => $this->getAccounts()]);
        //if($this->page == 'pageOne'){
        // }else{
        //     return view('livewire.analytics-new-twe',['accounts' => $this->getAccounts()]);

        // }
    }

    private function timeToSeconds($time)
    {
        list($hours, $minutes, $seconds) = explode(':', $time);
        return ($hours * 60) + ($minutes * 1) + ($seconds / 60);
    }
}
