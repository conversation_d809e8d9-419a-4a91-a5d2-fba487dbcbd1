<?php

namespace App\Exports;

use App\Models\SkillGroup;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class SkillGroupExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $filter_name;
    protected $filter_acdid;
    protected $filter_type;
    protected $filter_language;

    public function __construct($filter_name, $filter_acdid, $filter_type, $filter_language)
    {
        $this->filter_name = $filter_name;
        $this->filter_acdid = $filter_acdid;
        $this->filter_type = $filter_type;
        $this->filter_language = $filter_language;
    }
    /**
     * @return \Illuminate\Support\Collection
     */

    public function collection()
    {
        $query = SkillGroup::query();

        if ($this->filter_name) {
            $query->where('name', 'like', "%{$this->filter_name}%");
        }

        if ($this->filter_acdid) {
            $query->where('acdid', 'like', "%{$this->filter_acdid}%");
        }

        if ($this->filter_type) {
            $query->where('type', 'like', "%{$this->filter_type}%");
        }

        if ($this->filter_language) {
            $query->where('language', 'like', "%{$this->filter_language}%");
        }

        $n = 1;
        return $query->get()
            ->map(function ($skill) use (&$n) {
                return [
                    'ID' => $n++,
                    'Skill Name' => $skill->name,
                    'ACDID' => $skill->acdid,
                    'Type' => $skill->type,
                    'Language' => $skill->language,
                    'Created At' => $skill->created_at,
                ];
            });
    }

    public function headings(): array
    {
        return [
            '#',
            'Skill Name',
            'ACDID',
            'Type',
            'Language',
            'Created At',
        ];
    }

    // private function getRoleName($roleId)
    // {
    //     // You can define a mapping of role IDs to role names here
    //     $roles = [
    //         1 => 'Admin',
    //         2 => 'Supervisor',
    //         3 => 'IT',
    //         4 => 'Agent',
    //     ];

    //     return $roles[$roleId] ?? 'Unknown';
    // }
}
