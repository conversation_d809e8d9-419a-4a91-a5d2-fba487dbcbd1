n<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scripts_words', function (Blueprint $table) {
            $table->enum('script_type', ['Greeting', 'Closure'])->nullable()->after('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scripts_words', function (Blueprint $table) {
            $table->dropColumn('script_type');
        });
    }
};
