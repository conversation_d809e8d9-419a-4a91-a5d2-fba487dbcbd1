@props([
    'callId',
    'timeData',
    'startTime',
    'callType',
    'callDetails',
    'detailsPercentage',
    'getCallQuality',
    'getCallIssues',
])
<style>
    .badge-dot {
        width: 13px;
        height: 13px;
        border-radius: 50%;
        display: inline-block;
    }

    .param-block {
        position: relative;
        text-align: center;
        width: 100px;
        /* adjust size as needed */
    }

    .param-block .badge-dot {
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
    }
</style>

<div>
    <div class="d-flex p-3 w-full details-header rounded-3" style='background-color:rgb(239 243 244) !important'>
        <h4 class="px-4 row col-8 fw-bold">Interaction Metrics</h4>
        <h4 class=" px-3 fw-bold row col-4 fw-bold">Recording Details</h4>

    </div>
    <div class="row px-3 py-3 my-1 mx-2" style="
    height: 315px;
    overflow: scroll;
">
        <div class="row col-8">

            <div class="col">
                {{--            <x-chunks.call-details-div :label="'Interaction Type'" :data="'Call'"/>
 --}}
                <x-chunks.call-details-div :label="'Interaction ID'" :data="$callId" />

                <x-chunks.call-details-div :label="'Start Time'" :data="$startTime" />

                <x-chunks.call-details-div :label="'End Time'" :data="$timeData['calculatedEndTime']" />

                <x-chunks.call-details-div :label="'Direction'" :data="$callType" />

                <x-chunks.call-details-div :label="'Queue'" :data="'static _ts _bbn'" />


                <x-chunks.call-details-div :label="'Duration'" :data="$timeData['duration']" />
                <x-chunks.call-details-div :label="'Caller Number'" :data="$callDetails['caller_id']" />

                <x-chunks.call-details-div :label="'Called Number'" :data="$callDetails['called_id']" />
                <x-chunks.call-details-div :label="'Disonnected By'" :data="$callDetails['call_ender']" />

            </div>

            <div class="col">

                @php
                    $enableBtn = false;
                    if ($getCallQuality === 'GOOD') {
                        $face = 'fa-face-smile fa-solid fs-3 text-success';
                        $prec2 = '100';
                        $plate = '#03a34d';
                        $enableBtn = true;
                    } elseif ($getCallQuality === 'BAD') {
                        # code...
                             $face = 'fa-face-frown fa-solid fs-3 text-danger';
                        $prec2 = '0';
                        $plate = 'rgba(var(--bs-danger-rgb),var(--bs-bg-opacity))!important';
                    } else {
                        # code...
                        $face = 'fa-face-meh fa-solid fs-3 text-warning';
                        $prec2 = '0';
                        $plate = 'rgba(var(--bs-warning-rgb),var(--bs-bg-opacity))!important';
                    }

                @endphp

                @php

    $issues = collect($getCallIssues)
                ->map(fn($i) => trim($i))
                ->filter(fn($i) => $i !== '');
@endphp
                <div class="details-text-container">
                    <x-chunks.progress-bar :label="'Customer Talk'" :prec="$detailsPercentage['left_channel_duration']" :plate="'#40798c'" />
                    <x-chunks.progress-bar :label="'Agent Talk'" :prec="$detailsPercentage['right_channel_duration']" :plate="'#66c3f3'" />
                    <x-chunks.progress-bar :label="'Silence Talk'" :prec="$detailsPercentage['silence_duration']" :plate="'#7e8083'" />
                    <x-chunks.progress-bar :label="'Other'" :prec="$detailsPercentage['other_duration']" :plate="'#f5bbbb'" />
                    <x-chunks.progress-bar :label="'Overtalk'" :prec="$detailsPercentage['overtalk_duration']" :plate="'#03a34d'" />

                    <div class="row">
                    <div class="col-4  details-text-container">
                        <h6 class="fw-bold">Voice Quality</h6>
                    </div>
                    <div class="col ">

                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: {{$prec2}}%; background-color:{{$plate}}" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>

                </div>
                <p class="progress-bar-text"><i class="{{$face}}"></i></p>
                    </div>
                </div>






                </div>

            </div>
        </div>

        <div class="col-4">


            <x-chunks.call-details-div :label="'Record Date'" :data="$timeData['calculatedEndTime']" :style="'padding-left:1%;text-align:center'" />
            <x-chunks.call-details-div :label="'Archive Date'" :data="'NA'" :style="'padding-left:1%;text-align:center'" />
            <x-chunks.call-details-div :label="'Exported Date'" :data="'NA'" :style="'padding-left:1%;text-align:center'" />
            <x-chunks.call-details-div :label="'Protected'" :data="'NO'" :style="'padding-left:1%;text-align:center'" />
            <div class="row">
                <div class="col-6  details-text-container">

                    <button id="train_btn" class="btn btn-success fw-bold" {{ $enableBtn == false ? 'disabled' : '' }} onclick="apply_ai()">Train</button>
                </div>
            </div>


        </div>

    </div>
</div>
<script>
    async function apply_ai() {
        let call_id = "{{ $callId }}";
        let f_call_id = call_id.split('@')[0];
        let trainBtn = document.getElementById('train_btn');

        trainBtn.disabled = true;
        trainBtn.textContent = 'Loading...';


        let baseUrl = @json(app()->environment('production') ? 'https://extensyailog.com' : 'https://development.extensyailog.com');

        let fetchAiResponse = await fetch(`${baseUrl}/calls/run/tools/${f_call_id}`);
        let requestResponse = await fetchAiResponse.json();
        console.log(requestResponse.status);
        if (requestResponse.status == true) {

            trainBtn.disabled = true;
            trainBtn.textContent = 'Loading...';
            setTimeout(() => {
                location.reload();
            }, 180000);
        } else {
            trainBtn.disabled = false;
            trainBtn.textContent = 'Train';
        }


    }
</script>
<script>
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltipTriggerList.forEach(el => new bootstrap.Tooltip(el));
</script>
