<div>
    <div class="col-12  mb-4 mt-3  ms-5 ">

        <button class="btn btn-md rounded-1 me-5" style="float:right;background-color: #00a34e; color:white" data-bs-toggle="modal" data-bs-target="#departmentModal"><i class="fa-solid fa-plus"></i><span> Add New Value</span></button>


        <button class="btn btn-md rounded-1 me-2" style="background-color: #00a34e; color:white" wire:click="changePage('scripts')"><span> Scripts</span></button>
        <button class="btn btn-md rounded-1 me-2" style="background-color: #00a34e; color:white" wire:click="changePage('variables')"><span> Variables</span></button>
    </div>

    <div class="table-responsive mb-2 ms-5 rounded-3">
        <table class="table table-striped table-bordered">
            <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 0; z-index: 4;height: 1rem !important;">

            <tr>
                <th scope="col" style="width: 45%;font-size: 1.1rem !important;">Value</th>
                <th scope="col" style="width: 45%;font-size: 1.1rem !important;">Variable</th>
                <th scope="col" class="text-center" style="width: 10%">Action</th>
            </tr>
            </thead>
            <tbody>
            @forelse($transcriptionBadWords as $transcriptionBadWord)
                <tr>
                    <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->value }}</td>
                    <td style="font-size: 1.1rem !important;color:#424242 !important">{{ $transcriptionBadWord->variable->variable }}</td>
                    <td>
                        <div class="btn-group btn-group-sm mx-3" role="group" aria-label="Basic mixed styles example">
                            <i wire:click="showUpdateModal({{ $transcriptionBadWord->id }})" class="fa-solid fa-pen" id="pencel_icon23" style="font-size: 20px;color: #00a34e;" data-bs-toggle="modal" data-bs-target="#departmentModal"></i>
                            {{--                            <img wire:click="showUpdateModal({{ $transcriptionBadWord->id }})" src="{{ asset('assets/SVG/assets-v2/union-1.svg') }}" alt="Edit" style="width: 1.5rem; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#departmentModal" />--}}
                        </div>
                        <i class="fa fa-trash fa-xl" aria-hidden="true" style="cursor: pointer; color:tomato" wire:click="showDeleteAlert('{{ $transcriptionBadWord->id }}')" title="Delete User"></i>

                    </td>

                </tr>
            @empty
            @endforelse
            </tbody>
        </table>
    </div>


    <div wire:ignore.self class="modal fade" id="departmentModal" tabindex="-1" role="dialog" aria-labelledby="departmentModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white; border-radius: 12px;">
                <!-- Modal Header -->
                <div class="modal-header" style="border: none; padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color: #eff3f4 !important;">
                            <i class="fa-solid fa-lock" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h5 class="modal-title fw-bold" id="departmentModalLabel" style="font-size: 24px; color: #40798c;">
                            @if ($modalIdShow != 'on')
                                {{ $modalId ? 'Update Value' : 'New Value' }}
                            @else
                                {{ 'View Value' }}
                            @endif
                        </h5>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id="closeModal" data-bs-dismiss="modal" aria-label="Close" wire:click="closeModal">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="modal-body" style="padding: 20px; border: none;">
                    <form>
                        <div class="mb-3">
                            <label for="status" style="color: #40798c;">Value:</label>
                            @if ($modalIdShow != 'on')
                                <input type="text" class="form-control" wire:model="value">
                                @error('value')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <input type="text" class="form-control" wire:model="value" disabled>
                                @error('value')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="status" style="color: #40798c;">Variables:</label>
                            @if ($modalIdShow != 'on')
                                <select class="form-control"  wire:model="script_variable_id" >
                                    <option class="fw-bold" value="">---</option>
                                    @foreach($variables  as $organization)
                                        <option class="fw-bold" value="{{$organization->id}}">{{$organization->variable}}</option>
                                    @endforeach
                                </select>
                                @error('organization_id')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @else
                                <select class="form-control"  wire:model="script_variable_id" disabled>
                                    <option class="fw-bold" value="">---</option>
                                    @foreach($variables  as $organization)
                                        <option class="fw-bold" value="{{$organization->id}}">{{$organization->variable}}</option>
                                    @endforeach
                                </select>
                                @error('organization_id')
                                <small class="text-danger"> {{ $message }} </small>
                                @enderror
                            @endif
                        </div>
                    </form>


                </div>

                <!-- Modal Footer -->
                <div class="modal-footer" style="border: none; padding: 20px;">
                    <button type="button" class="btn btn-outline-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" data-bs-dismiss="modal" wire:click="closeModal">
                        Close
                    </button>
                    @if ($modalIdShow != 'on')
                        <button class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="{{ $modalId ? 'update' : 'store' }}">
                            {{ $modalId ? 'Update' : 'Save' }}
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>


</div>
