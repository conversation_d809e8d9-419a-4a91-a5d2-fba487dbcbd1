<div class="container-fluid mt-3 px-4">
    <div style="position: absolute;top: 24px;left: 218px;color: #01a44f;">
        @if(!$editFlag)
            <i class="fas fa-edit" style="font-size:18px;" wire:click="$set('editFlag', true)"></i>
        @endif

        @if($editFlag)
            <i class="fa fa-check" style="font-size:18px;" wire:click="$set('editFlag', false)"></i>
        @endif
    </div>
    {{-- header row --}}
    <div class="parent-sections mx-3 ps-lg-5">

        <div class="section-one col-12 p-4 pb-2 bg-white shadow-sm">
            <div class="filter row col-12">
                <!-- Left Section -->
                <div class="col-lg-6 col-12 d-flex flex-wrap">
                    <!-- Dropdown -->
                    <div class="col-12 col-md-6 col-lg-3 mb-3 mb-md-0">
                        <div class="dropdown">
                            <button
                                class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-style color rounded-2 d-flex flex-row justify-content-between"
                                type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="mt-1">{{ $accountNameFilter ?? 'Please Select' }}</span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton" id="dropdownMenu"
                                style="max-height: 200px; overflow-y: auto;">

                                @forelse ($accounts as $account)
                                <li>
                                    <a class="dropdown-item"
                                        wire:click="setAccountFilter('{{ $account->id }}','{{ $account->name }}')">
                                        {{ $account->name }}
                                    </a>
                                </li>
                                @if (!$loop->last)
                                <hr class="m-0">
                                @endif
                                @empty
                                <li><a class="dropdown-item text-muted">No account found</a></li>
                                @endforelse
                            </ul>
                        </div>
                    </div>
                    <!-- Label -->
                    <div class="col-12 col-md-6 col-lg-8 d-flex flex-column justify-content-center color px-3">
                        <strong>Interactions Per Group</strong>
                    </div>
                </div>
                <!-- Right Section -->
                <div class="col-lg-2 col-12"></div>
                <div
                    class="col-lg-4 col-12 d-flex flex-column flex-lg-row justify-content-end align-items-lg-center text-lg-end">
                    <!-- Date Dropdown -->
                    <div class="dropdown me-0 me-lg-3 mb-3 mb-lg-0 w-100 w-lg-auto">
                        <button
                            class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-style color rounded-2 w-100 d-flex flex-row justify-content-between"
                            type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                            {{-- {{ $dateType ?? $dateFrom.' - '.$dateTo }} --}}
                            <span class="mt-1">{{ $dateType ?? 'Custome' }}</span>
                        </button>
                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu"
                            style="z-index: 9999999999999999">
                            <li><a class="dropdown-item" wire:click="getDate('Last 24 Hours')">Last 24 Hours</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" wire:click="getDate('Last 7 Days')">Last 7 Days</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" wire:click="getDate('Last 30 Days')">Last 30 Days</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" wire:click="getDate('Last 60 Days')">Last 60 Days</a></li>
                            <hr class="m-0">
                            <li><a class="dropdown-item" data-bs-toggle="modal"
                                    data-bs-target="#custom_time_modal">Custom</a></li>
                        </ul>
                    </div>
                    <!-- Search Box -->
                    <div class="d-flex align-items-center ps-2 rounded-2 bg-color w-100 w-lg-auto">
                        <i class="fas fa-search me-2 color"></i>
                        <input type="text" wire:input="setAccountFilter('{{ $accountIDFilter }}', '{{ $accountNameFilter }}')" class="rounded-2 form-control border-0 color shadow-none text-secondary"  wire:model='searchGroup' placeholder="Search Group">
                    </div>
                </div>
            </div>


            <div class="mt-3 row col-12">
                <div class="col-lg-12 table-responsive col-12 group-card" >
                    <table class="table table-borderless table-striped align-middle">
                        <tbody>
                            @if($accountIDFilter && !$searchGroup && $role != 4)
                                <tr class="group rounded-3" wire:click="getData('All')" @if($groupSelected == 'All') style="border: 1px solid green;" @endif>
                                    <td class="rounded-start ps-4 py-2">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-purple text-white rounded-circle d-flex justify-content-center align-items-center" style="width: 40px; height: 40px;">
                                                <strong>All</strong>
                                            </div>
                                            <div class="ms-3">

                                                <div><strong>All</strong></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-2">

                                        <div><strong></strong></div>
                                    </td>
                                    <td class="rounded-end py-2">

                                        <div><strong></strong></div>
                                    </td>
                                </tr>
                            @endif
                            @foreach ($groupsAccount ?? [] as $key => $group)
                            <tr class="main-row group rounded-3"  wire:ignore.self wire:click="getData({{ $group['group_id'] }})"

                            @if($groupSelected == $group['group_id']) style="border: 1px solid green;" @endif>

                                <td class="rounded-start ps-4 py-2">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar bg-purple text-white rounded-circle d-flex justify-content-center align-items-center" style="width: 40px; height: 40px;">
                                            <strong>{{ substr($group['group_name'], 0, 3) }}</strong>
                                        </div>
                                        <div class="ms-3">
                                            <small>Group</small>
                                            <div><strong>{{ $group['group_name'] }}</strong></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-2">
                                    <small>Avg. Interactions Duration</small>
                                    <div><strong>{{ $group['avg_interactions_duration']}}</strong></div>
                                </td>
                                <td class="rounded-end py-2">
                                    <small>Total Interactions</small>
                                    <div><strong>{{ $group['total_interactions'] }}</strong></div>
                                </td>
                            </tr>
                        @endforeach


                        </tbody>
                    </table>
                </div>
            </div>

            <div class="paginationTable toggle-details me-4" style="text-align: right; cursor: pointer;
                    @if(empty($groupsAccount) || count($groupsAccount) <= 1) display:none @endif">
                    Expand <i class="ms-2 fa-solid fa-caret-down"></i>
            </div>





        </div>

        <div class="section-tow px-0 col-12 p-4 mt-2 pb-2">

            <div class="row parent-cards m-0">

                <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                wire:click="getChartData('Avg Call Duration Card')"
                style="{{ in_array('Avg Call Duration Card', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Avg Call Duration Card', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                    @if($editFlag)
                        @if(in_array('Avg Call Duration Card', $userSettings))
                            <div
                                class="p-1 circle-x-y"
                                style="background: #a6ffd1;"
                                wire:click="add('Avg Call Duration Card')">
                                <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                            </div>
                        @else
                            <div
                                class="p-1 circle-x-y"
                                style="background: #ffd7d8;"
                                wire:click="remove('Avg Call Duration Card')">
                                <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                            </div>
                        @endif
                    @endif


                    <div class="d-flex flex-row justify-content-between">
                        <div class="text-muted">
                            Statistics
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Call Duration Card' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                        </div>
                    </div>
                    <div class="color">
                        <strong>Avg. Call Duration:</strong>
                    </div>
                    <div  class="d-flex flex-row justify-content-between mt-3">
                        <div class="mt-3">
                            <div><h3><strong>{{ $dataPage['avg_interactions_duration'] ?? '00:00:00' }}</strong></h3></div>
                            {{-- <div>
                                <span style="color: #01a44f">+21.01%</span>
                                <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                            </div> --}}
                        </div>
                        <div wire:ignore>
                            {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                            <canvas class="myChart3" id="myChart3-5" style="width: 180px !important;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                wire:click="getChartData('Avg Hold Duration Card')"
                style="{{ in_array('Avg Hold Duration Card', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Avg Hold Duration Card', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                    @if($editFlag)
                        @if(in_array('Avg Hold Duration Card', $userSettings))
                            <div
                                class="p-1 circle-x-y"
                                style="background: #a6ffd1;"
                                wire:click="add('Avg Hold Duration Card')">
                                <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                            </div>
                        @else
                            <div
                                class="p-1 circle-x-y"
                                style="background: #ffd7d8;"
                                wire:click="remove('Avg Hold Duration Card')">
                                <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                            </div>
                        @endif
                    @endif
                    <div class="d-flex flex-row justify-content-between">
                        <div class="text-muted">
                            Statistics
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Hold Duration Card' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                        </div>
                    </div>
                    <div class="color">
                        <strong>Avg. Hold Time:</strong>
                    </div>
                    <div  class="d-flex flex-row justify-content-between mt-3">
                        <div class="mt-3">
                            <div><h3><strong>{{ $dataPage['avg_hold_time'] ?? '00:00:00' }}</strong></h3></div>
                            {{-- <div>
                                <span style="color: #01a44f">+21.01%</span>
                                <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                            </div> --}}
                        </div>
                        <div wire:ignore>
                            {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                            <canvas class="myChart3" id="myChart3-4" style="width: 180px !important;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="card rounded-3 bg-color1 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                wire:click="getChartData('Duration < 2 Minutes')"
                style="{{ in_array('Duration < 2 Minutes', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Duration < 2 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                    @if($editFlag)
                        @if(in_array('Duration < 2 Minutes', $userSettings))
                            <div
                                class="p-1 circle-x-y"
                                style="background: #a6ffd1;"
                                wire:click="add('Duration < 2 Minutes')">
                                <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                            </div>
                        @else
                            <div
                                class="p-1 circle-x-y"
                                style="background: #ffd7d8;"
                                wire:click="remove('Duration < 2 Minutes')">
                                <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                            </div>
                        @endif
                    @endif
                    <div class="d-flex flex-row justify-content-between">
                        <div class="text-muted">
                            Statistics
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Duration < 2 Minutes' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                        </div>
                    </div>
                    <div class="color">
                        <strong>Duration < 2 Minutes:</strong>
                    </div>
                    <div  class="d-flex flex-row justify-content-between mt-3">
                        <div class="mt-3">
                            <div><h3><strong>{{ $dataPage['short_call_duration_count'] ?? '00:00:00' }}</strong></h3></div>
                            {{-- <div>
                                <span style="color: #01a44f">+21.01%</span>
                                <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                            </div> --}}
                        </div>
                        <div wire:ignore>
                            {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                            <canvas class="myChart3" id="myChart3-3" style="width: 180px !important;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="card rounded-3 bg-color2 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                wire:click="getChartData('Duration > 8 Minutes')"
                style="{{ in_array('Duration > 8 Minutes', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Duration > 8 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                        @if($editFlag)
                            @if(in_array('Duration > 8 Minutes', $userSettings))
                               <div
                                    class="p-1 circle-x-y"
                                    style="background: #a6ffd1;"
                                    wire:click="add('Duration > 8 Minutes')">
                                    <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                </div>
                            @else
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #ffd7d8;"
                                    wire:click="remove('Duration > 8 Minutes')">
                                    <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                </div>
                            @endif
                        @endif
                    <div class="d-flex flex-row justify-content-between">
                        <div class="text-muted">
                            Statistics
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Duration > 8 Minutes' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                        </div>
                    </div>
                    <div class="color">
                        <strong>Duration > 8 Minutes:</strong>
                    </div>
                    <div  class="d-flex flex-row justify-content-between mt-3">
                        <div class="mt-3">
                            <div><h3><strong>{{ $dataPage['long_call_duration_count'] ?? '00:00:00' }}</strong></h3></div>
                            {{-- <div>
                                <span style="color: #01a44f">+21.01%</span>
                                <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                            </div> --}}
                        </div>
                        <div wire:ignore>
                            {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                            <canvas class="myChart3" id="myChart3-2" style="width: 180px !important;"></canvas>

                        </div>
                    </div>
                </div>

                {{-- <div class="card rounded-3 bg-color3 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                wire:click="getChartData('Hold Duration > 2 Minutes')"
                style="{{ in_array('Hold Duration > 2 Minutes', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Hold Duration > 2 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                    @if($editFlag)
                        @if(in_array('Hold Duration > 2 Minutes', $userSettings))
                            <div
                                class="p-1 circle-x-y"
                                style="background: #a6ffd1;"
                                wire:click="add('Hold Duration > 2 Minutes')">
                                <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                            </div>
                        @else
                            <div
                                class="p-1 circle-x-y"
                                style="background: #ffd7d8;"
                                wire:click="remove('Hold Duration > 2 Minutes')">
                                <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                            </div>
                        @endif
                    @endif
                    <div class="d-flex flex-row justify-content-between">
                        <div class="text-muted">
                            Statistics
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Hold Duration > 2 Minutes' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                        </div>
                    </div>
                    <div class="color">
                        <strong>Hold > 2 Minutes:</strong>
                    </div>
                    <div  class="d-flex flex-row justify-content-between">
                        <div class="mt-3">
                            <div><h3><strong>{{ $dataPage['long_hold_duration_count'] ?? '00:00:00' }}</strong></h3></div>
                            <div>
                                <span style="color: #01a44f">+21.01%</span>
                                <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                            </div>
                        </div>
                        <div>
                            <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" >
                        </div>
                    </div>
                </div> --}}
                <div class="card rounded-3 bg-color3 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                wire:click="getChartData('Total Evaluation')"
                style="{{ in_array('Total Evaluation', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Total Evaluation', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                    @if($editFlag)
                        @if(in_array('Total Evaluation', $userSettings))
                            <div
                                class="p-1 circle-x-y"
                                style="background: #a6ffd1;"
                                wire:click="add('Total Evaluation')">
                                <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                            </div>
                        @else
                            <div
                                class="p-1 circle-x-y"
                                style="background: #ffd7d8;"
                                wire:click="remove('Total Evaluation')">
                                <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                            </div>
                        @endif
                    @endif
                    <div class="d-flex flex-row justify-content-between">
                        <div class="text-muted">
                            Statistics
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Evaluation' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                        </div>
                    </div>
                    <div class="color">
                        <strong>Total Evaluation:</strong>
                    </div>
                    <div  class="d-flex flex-row justify-content-between mt-3">
                        <div class="mt-3">
                            <div><h3><strong>{{ $dataPage['countEvaluation'] ?? '0' }}</strong></h3></div>
                            {{-- <div>
                                <span style="color: #01a44f">+21.01%</span>
                                <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                            </div> --}}
                        </div>
                        <div wire:ignore>
                            {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                            <canvas class="myChart3" id="myChart3-1" style="width: 180px !important;"></canvas>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="section-tow px-0 col-12 p-4 pt-0 mt-1 pb-2">

            <div class="row parent-cards m-0">

                <div class="card-2 rounded-3 bg-white pb-0 shadow-sm col-12 col-sm-8 col-md-6 col-lg-4 my-2 custom-col-n position-relative"
                style="{{ in_array('Chart Line', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Chart Line', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                        @if($editFlag)
                            @if(in_array('Chart Line', $userSettings))
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #a6ffd1;"
                                    wire:click="add('Chart Line')">
                                    <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                </div>
                            @else
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #ffd7d8;"
                                    wire:click="remove('Chart Line')">
                                    <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                </div>
                            @endif
                        @endif
                    <div class="col-12  d-flex flex-row justify-content-between border-buttom">
                        <div class="col-8">
                            <div>
                                <div class="text-muted">
                                   <h5>Statistics</h5>
                                </div>
                                <div class="color">
                                    <h4 class="mb-0"><strong>{{ $cardSelected ?? '' }}</strong></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div>
                                <div class="color">
                                    <strong></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <canvas id="chartCanvas" class="w-100" style=""></canvas>
                    </div>
                </div>

                <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col"
                style="border:none !important;{{ in_array('Chart Circle', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                {{ $editFlag && in_array('Chart Circle', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                >
                    @if($editFlag)
                        @if(in_array('Chart Circle', $userSettings))
                            <div
                                class="p-1 circle-x-y"
                                style="background: #a6ffd1;"
                                wire:click="add('Chart Circle')">
                                <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                            </div>
                        @else
                            <div
                                class="p-1 circle-x-y"
                                style="background: #ffd7d8;"
                                wire:click="remove('Chart Circle')">
                                <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                            </div>
                        @endif
                    @endif
                    <div class="col-12  d-flex flex-row justify-content-between border-buttom">
                        <div class="col-6">
                            <div>
                                <div class="color">
                                    <strong>Inbound</strong>
                                </div>
                                <div class="color">
                                    <strong>Calls:</strong>
                                </div>
                                <div class="t">
                                    <h3 class="mb-0"><strong>{{ $dataPage['total_inbound'] ?? '0' }}</strong></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 text-end">
                            <div>
                                <div class="color">
                                    <strong>Outbound</strong>
                                </div>
                                <div class="color">
                                    <strong>Calls:</strong>
                                </div>
                                <div class="t">
                                    <h3 class="mb-0"><strong>{{ $dataPage['total_outbound'] ?? '0' }}</strong></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="d-flex justify-content-center">
                            <canvas id="doughnutChart" width="200" height="200"></canvas>
                        </div>
                    </div>
                </div>


                <div class="card bg-white col-12 col-sm-6 col-md-4 px-0 col-lg-3 custom-col-n d-flex flex-column justify-content-between position-relative" style="border:none !important;">
                    <!-- First Card Section -->
                    <div class="rounded-3 bg-white shadow-sm card-2 row g-0 justify-content-between align-items-center mb-4"
                    style="{{ in_array('Total Interactions', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                    {{ $editFlag && in_array('Total Interactions', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                    >
                        @if($editFlag)
                            @if(in_array('Total Interactions', $userSettings))
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #a6ffd1;"
                                    wire:click="add('Total Interactions')">
                                    <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                </div>
                            @else
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #ffd7d8;"
                                    wire:click="remove('Total Interactions')">
                                    <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                </div>
                            @endif
                        @endif
                        <!-- Left Section -->
                        <div class="col-5 text-center text-md-start">
                            <div class="text-muted mb-2 text-start">Statistics</div>
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div>
                                    <div class="color">
                                        <strong>Total</strong>
                                    </div>
                                    <div class="color">
                                        <strong>Interactions:</strong>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <h1 class="mb-0"><strong>{{ $dataPage['total_interactions'] ?? '0' }}</strong></h1>
                                </div>
                            </div>
                        </div>
                        <!-- Right Section -->
                        <div class="col-7 d-flex justify-content-center justify-content-md-end mt-3 mt-md-0">
                            <img
                                src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-19 113005.png') }}"
                                alt="Statistics Icon"
                                class="img-fluid w-100">
                        </div>
                    </div>

                    <!-- Second Card Section -->
                    <div class="rounded-3 bg-white shadow-sm card-2 row g-0 justify-content-between align-items-center position-relative"
                    style="{{ in_array('Total Duration', $userSettings) && !$editFlag ? 'display: none;' : '' }}
                    {{ $editFlag && in_array('Total Duration', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}"
                    >
                        @if($editFlag)
                            @if(in_array('Total Duration', $userSettings))
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #a6ffd1;"
                                    wire:click="add('Total Duration')">
                                    <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                                </div>
                            @else
                                <div
                                    class="p-1 circle-x-y"
                                    style="background: #ffd7d8;"
                                    wire:click="remove('Total Duration')">
                                    <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                                </div>
                            @endif
                        @endif
                        <div class="d-flex flex-row justify-content-between">
                            <!-- Left Section -->
                            <div class="col-6 text-center text-md-start">
                                <div class="text-muted mb-2 text-start">Statistics</div>
                                <div class="d-flex justify-content-between align-items-center flex-wrap">
                                    <div class="color">
                                        <strong>Total Duration:</strong>
                                    </div>
                                </div>
                            </div>
                            <!-- Right Section -->
                            <div class="col-6 d-flex justify-content-start mt-3 mt-md-0 align-items-center">
                                <div class="text-center">
                                    <h1 class="mb-0"><strong>{{ $dataPage['total_call_duration'] ?? '00:00:00' }}</strong></h1>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 d-flex justify-content-center justify-content-md-end mt-3 mt-md-0">
                            <img
                                src="{{ asset('assets/SVG/assets-v2/pink-line.JPG') }}"
                                alt="Statistics Icon"
                                class="img-fluid w-100">
                        </div>
                    </div>
                </div>






            </div>
        </div>

    </div>

    {{-- custom time modal --}}
    <div class="modal fade" id="custom_time_modal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="  :50vw !important">

                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3"
                            style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel"
                            style="font-size: 30px;">Custom Period</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 fw-bold">Date From <i
                                    class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="from" id="from" class="form-control"
                                style="border: solid 1px #b6b6b6" placeholder="Date From" wire:model="dateFromFilter">
                            @error('dateFromFilter')
                            <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 fw-bold">Date To <i
                                    class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="to" id="to" class="form-control" style="border: solid 1px #b6b6b6"
                                placeholder="Date To" wire:model="dateToFilter">
                            @error('dateToFilter')
                            <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border: none;">
                    {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal"
                        style="background-color: #00a34e" id="closeCustomDate">Close</button>
                    <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply-custom-date"
                        wire:click="apply_custom_date">Apply</button> --}}
                    <button id="closeCustomDate" type="button" class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal">
                        <span style="font-size: 17px;">Close</span>
                    </button>
                    <button class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="apply_custom_date"
                        wire:loading.attr="disabled" wire:target="apply_custom_date">
                        <span wire:loading.remove wire:target="apply_custom_date" id="apply-custom-date"
                            style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="apply_custom_date"
                            role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
