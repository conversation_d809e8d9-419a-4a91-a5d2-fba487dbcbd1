<?php

namespace App\Livewire;

use Carbon\Carbon;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\SkillGroup;
use App\Models\Interaction;
use App\Models\Organization;
use Illuminate\Support\Facades\DB;

class Analytics extends Component
{
    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;

    public $total_interactions;
    public $total_interactions_count;

    public $average_duration;
    public $total_duration;

    public $durationOver8;
    public $durationUnder2;
    public $holdOverTwoSeconds;
    public $averageHoldTime;

    public $inboundCount;
    public $outboundCount;

    // user groups for the table
    public $userGroups;

    // skill groups for the filter
    public $skillGroups;

    // orgs for the filter 
    public $organizations;
    public $selected_organization;
    public $selected_organization_id;

    // filters 
    public $interval = 'Today';

    public $startDate;

    // bar chart data 
    public $barChartData = [];

    public function rules()
    {
        return [
            'custom_date_from' => 'required_if:filter_time_name,Custom|',
            'custom_date_to' => 'required_if:filter_time_name,Custom|after:custom_date_from'
        ];
    }

    public function apply_custom_date()
    {

        // $this->filter_time_name = 'Custom';
        $this->validate();

        $this->interval = 'Custom';

        $this->dispatch('closeCustomDateModal');
        $this->filter();
    }

    public function filter_time_set($time)
    {
        $this->filter_time_days = $time;

        $this->filter_time_name = match ((int) $time) {
            1 => 'Last 24 Hours',
            7 => 'Last 7 Days',
            30 => 'Last 30 Days',
            60 => 'Last 60 Days',
            default => 'Unknown time',
        };

        if ($this->filter_time_name == 'Custom') {
            $this->filter_time_days = null;
        }
    }

    // Convert HH:MM:SS to seconds
    private function convertToSeconds($time)
    {
        list($hours, $minutes, $seconds) = explode(':', $time);
        return ($hours * 3600) + ($minutes * 60) + $seconds;
    }

    // Convert seconds to HH:MM:SS
    private function convertToHMS($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    public function mount()
    {
        // $this->total_interactions = Interaction::whereBetween('arrival_time', [Carbon::today(), Carbon::today()])->get();
        $this->total_interactions = Interaction::whereNotNull('call_duration')->where('call_duration', '!=', '00:00:00')->whereDate('arrival_time', Carbon::today())->get();
        $this->total_interactions_count = $this->total_interactions->count();

        // calls duration
        if ($this->total_interactions_count == 0) {
            $this->total_duration = $this->average_duration = $this->durationOver8 = $this->durationUnder2 = $this->averageHoldTime = $this->holdOverTwoSeconds =  0;
        } else {

            $callDurations = Interaction::where('arrival_time', '>', now()->startOfDay())->pluck('call_duration');

            // Convert each duration to seconds and sum them up
            $totalSeconds = $callDurations->reduce(function ($carry, $item) {
                return $carry + $this->convertToSeconds($item ?? "00:00:00");
            }, 0);

            $this->total_duration = $this->convertToHMS($totalSeconds);

            // Average Duration 
            $this->average_duration = $this->convertToHMS($totalSeconds / $this->total_interactions_count);

            // Duration over 8
            $this->durationOver8 = $this->total_interactions->filter(function ($interaction) {
                return $this->convertToSeconds($interaction->call_duration ?? "00:00:00") > 8 * 60;
            })->count();

            // Duration less than 2
            $this->durationUnder2 = $this->total_interactions->filter(function ($interaction) {
                return $this->convertToSeconds($interaction->call_duration ?? "00:00:00") < 2 * 60;
            })->count();

            // hold > 2 seconds 
            $this->holdOverTwoSeconds = $this->total_interactions->filter(function ($interaction) {
                return $this->convertToSeconds($interaction->hold_duration ?? "00:00:00") > 2;
            })->count();

            // average hold time 
            $totalHoldTime = Interaction::where('arrival_time', '>', now()->startOfDay())->pluck('hold_duration');

            // Convert each duration to seconds and sum them up
            $totalHoldSeconds = $totalHoldTime->reduce(function ($carry, $item) {
                return $carry + $this->convertToSeconds($item ?? "00:00:00");
            }, 0);

            // average hold time 
            $this->averageHoldTime = $this->convertToHMS($totalHoldSeconds / $this->total_interactions_count);
        }


        $this->inboundCount = $this->total_interactions->where('call_type', 'Inbound')->count();
        $this->outboundCount = $this->total_interactions->where('call_type', 'Outbound')->count();

        // organizations for the filter 
        $this->organizations = Organization::orderBy('name', 'ASC')->get();

        // user groups for the table 
        $this->userGroups = UserGroup::orderBy('name', 'ASC')->get();

        // skill groups for the filter 
        $this->skillGroups = SkillGroup::orderBy('name', 'ASC')->get();

        // Get the current date
        $currentDate = Carbon::now()->toDateString();
        // $currentDate = Carbon::createFromDate(2024, 2, 4)->toDateString();

        $barChartData = [];
        // Loop through each hour of the day (from 0 to 23)
        // for ($hour = 0; $hour < 24; $hour++) {
        //     // Calculate the start and end of the hour range using Carbon
        //     // $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $currentDate . ' ' . sprintf("%02d:00:00", $hour));
        //     // $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $currentDate . ' ' . sprintf("%02d:59:59", $hour));
        //     $startTime = Carbon::createFromFormat('Y-m-d', $currentDate)->startOfDay();
        //     $endTime = Carbon::createFromFormat('Y-m-d', $currentDate)->endOfDay();


        //     // Query the interactions table for the count of interactions within this hour range
        //     $interactionsCount = Interaction::where('arrival_time', '>=', $startTime)
        //         ->where('arrival_time', '<=', $endTime)
        //         ->count();

        //     // Add the count to the bar chart data array
        //     $barChartData[] = $interactionsCount;
        // }

        $startDate = match ($this->interval) {
            'Today' => Carbon::today(),
            'Last 7 Days' => Carbon::today()->subDays(7),
            'Last 30 Days' => Carbon::today()->subDays(30),
            'Last 60 Days' => Carbon::today()->subDays(60),
            default => Carbon::today(),
        };

        for ($hour = 0; $hour < 24; $hour++) {
            if ($this->custom_date_from && $this->custom_date_to) {
                $dateFrom = Carbon::parse($this->custom_date_from)->toDateString();
                $dateTo =  Carbon::parse($this->custom_date_to)->toDateString();
            } else {
                if ($this->interval == 'Today') {
                    $dateFrom = now()->toDateString();
                    $dateTo = now()->toDateString();
                } else {
                    $dateFrom = $startDate->toDateString();
                    $dateTo = now()->toDateString();
                }
            }

            // Calculate the start and end of the hour range using Carbon
            $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $dateFrom . ' ' . sprintf("%02d:00:00", $hour));
            $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $dateTo . ' ' . sprintf("%02d:59:59", $hour));


            // Query the interactions table for the count of interactions within this hour range
            $interactionsCount = Interaction::where('arrival_time', '>=', $startTime)
                ->where('arrival_time', '<=', $endTime)
                ->count();

            // Add the count to the bar chart data array
            $barChartData[] = $interactionsCount;
        }

        $this->barChartData = json_encode($barChartData);
    }

    public function selectOrganizations($id)
    {
        if($id == 27) { //kease
            $id = 46; //ewa
        }

        $this->selected_organization_id = $id;
        $this->selected_organization = Organization::find($this->selected_organization_id)->name;
    }


    public function filter()
    {
        ini_set('max_execution_time', 500);
        ini_set('memory_limit', '4000M');

        // interval filter 
        if ($this->interval) {
            $startDate = match ($this->interval) {
                'Today' => Carbon::today(),
                'Last 7 Days' => Carbon::today()->subDays(7),
                'Last 30 Days' => Carbon::today()->subDays(30),
                'Last 60 Days' => Carbon::today()->subDays(60),
                default => Carbon::today(),
            };

            $this->startDate = $startDate;

            if ($this->custom_date_from && $this->custom_date_to) {
                $this->total_interactions = Interaction::whereNotNull('call_duration')->where('call_duration', '!=', "00:00:00")->whereBetween('arrival_time', [$this->custom_date_from, $this->custom_date_to])->when($this->selected_organization_id, function ($query) {
                    $query->whereHas('agent', function ($q) {
                        $q->where('organization_id', $this->selected_organization_id);
                    });
                })->get();

                $this->total_interactions_count = $this->total_interactions->count();
            } else {
                if ($this->interval == 'Today') {
                    $this->total_interactions = Interaction::whereDate('arrival_time', Carbon::today())->when($this->selected_organization_id, function ($query) {
                        $query->whereHas('agent', function ($q) {
                            $q->where('organization_id', $this->selected_organization_id);
                        });
                    })->get();
                    $this->total_interactions_count = $this->total_interactions->count();
                } else {
                    $this->total_interactions = Interaction::whereBetween('arrival_time', [$startDate, Carbon::now()])->when($this->selected_organization_id, function ($query) {
                        $query->whereHas('agent', function ($q) {
                            $q->where('organization_id', $this->selected_organization_id);
                        });
                    })->get();
                    $this->total_interactions_count = $this->total_interactions->count();
                }
            }


            // Doughnut chart data to update
            $this->inboundCount = $this->total_interactions->where('call_type', 'Inbound')->count();
            $this->outboundCount = $this->total_interactions->where('call_type', 'Outbound')->count();


            // other calls data 
            // calls duration
            if ($this->total_interactions_count == 0) {
                $this->total_duration = $this->average_duration = $this->durationOver8 = $this->durationUnder2 = $this->averageHoldTime = $this->holdOverTwoSeconds =  0;
            } else {
                $callDurations = Interaction::whereNotNull('call_duration')
                    ->where('call_duration', '!=', "00:00:00")
                    ->when($this->interval, function ($q) {
                        $q->whereBetween('arrival_time', [$this->startDate, now()]);
                    })
                    ->when($this->custom_date_from && $this->custom_date_to, function ($q) {
                        $q->whereBetween('arrival_time', [$this->custom_date_from, $this->custom_date_to]);
                    })
                    ->pluck('call_duration');


                // Convert each duration to seconds and sum them up
                $totalSeconds = $callDurations->reduce(function ($carry, $item) {
                    return $carry + $this->convertToSeconds($item ?? "00:00:00");
                }, 0);

                $this->total_duration = $this->convertToHMS($totalSeconds);

                // Average Duration 
                $this->average_duration = $this->convertToHMS($totalSeconds / $this->total_interactions_count);

                // Duration over 8
                $this->durationOver8 = $this->total_interactions->filter(function ($interaction) {
                    return $this->convertToSeconds($interaction->call_duration ?? "00:00:00") > 8 * 60;
                })->count();

                // Duration less than 2
                $this->durationUnder2 = $this->total_interactions->filter(function ($interaction) {
                    return $this->convertToSeconds($interaction->call_duration ?? "00:00:00") < 2 * 60;
                })->count();

                // hold > 2 seconds 
                $this->holdOverTwoSeconds = $this->total_interactions->filter(function ($interaction) {
                    return $this->convertToSeconds($interaction->hold_duration ?? "00:00:00") > 2;
                })->count();

                // average hold time 
                $totalHoldTime = Interaction::where('arrival_time', '>', now()->startOfDay())->pluck('hold_duration');

                // Convert each duration to seconds and sum them up
                $totalHoldSeconds = $totalHoldTime->reduce(function ($carry, $item) {
                    return $carry + $this->convertToSeconds($item ?? "00:00:00");
                }, 0);

                // average hold time 
                $this->averageHoldTime = $this->convertToHMS($totalHoldSeconds / $this->total_interactions_count);
            }


            // barchart 
            $barChartData = [];

            if ($this->custom_date_from && $this->custom_date_to) {
                $dateFrom = Carbon::parse($this->custom_date_from)->toDateString();
                $dateTo =  Carbon::parse($this->custom_date_to)->toDateString();
            } else {
                if ($this->interval == 'Today') {
                    $dateFrom = now()->toDateString();
                    $dateTo = now()->toDateString();
                } else {
                    $dateFrom = $startDate->toDateString();
                    $dateTo = now()->toDateString();
                }
            }

            $numOfDays = Carbon::parse($dateFrom)->diffInDays(Carbon::parse($dateTo));

            // to prevent division by zero
            if ($this->interval == 'Today') $numOfDays = 1;


            $publicArray = [];

            for ($days = 0; $days <= $numOfDays; $days++) {

                for ($hour = 0; $hour < 24; $hour++) {

                    $dateWithoutTime = Carbon::parse($dateFrom)->addDays($days)->format('Y-m-d');

                    // Calculate the start and end of the hour range using Carbon
                    $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $dateWithoutTime . ' ' . sprintf("%02d:00:00", $hour));
                    $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $dateWithoutTime . ' ' . sprintf("%02d:59:59", $hour));


                    $startTimeFormatted = Carbon::parse($startTime)->format('H:i:s');

                    if (isset($publicArray[$startTimeFormatted])) {
                        $publicArray[$startTimeFormatted] +=  Interaction::whereNotNull('call_duration')->where('call_duration', '!=', '00:00:00')->where('arrival_time', '>=', $startTime)
                            ->where('arrival_time', '<=', $endTime)
                            ->when($this->selected_organization_id, function ($query) {
                                $query->whereHas('agent', function ($q) {
                                    $q->where('organization_id', $this->selected_organization_id);
                                });
                            })
                            ->count();
                    } else {
                        $publicArray[$startTimeFormatted]   =  Interaction::whereNotNull('call_duration')->where('call_duration', '!=', '00:00:00')->where('arrival_time', '>=', $startTime)
                            ->where('arrival_time', '<=', $endTime)
                            ->when($this->selected_organization_id, function ($query) {
                                $query->whereHas('agent', function ($q) {
                                    $q->where('organization_id', $this->selected_organization_id);
                                });
                            })
                            ->count();
                    }

                    // Query the interactions table for the count of interactions within this hour range
                    $interactionsCount = Interaction::whereNotNull('call_duration')->where('call_duration', '!=', '00:00:00')->where('arrival_time', '>=', $startTime)
                        ->where('arrival_time', '<=', $endTime)
                        ->when($this->selected_organization_id, function ($query) {
                            $query->whereHas('agent', function ($q) {
                                $q->where('organization_id', $this->selected_organization_id);
                            });
                        })
                        ->count();

                    // Add the count to the bar chart data array
                    // $barChartData[] = $interactionsCount;
                }
            }

            $newArray = [];


            foreach ($publicArray as $key => $value) {
                $barChartData[$key] = round($value / $numOfDays);
            }

            $newData = [];

            foreach ($barChartData as $time => $value) {
                $hour = (int)substr($time, 0, 2);
                $newData[$hour] = $value;
            }


            $this->barChartData = json_encode($newData);
        }


        $this->dispatch('updatePieChart', [
            'inboundCount' => $this->inboundCount,
            'outboundCount' => $this->outboundCount
        ]);

        $this->dispatch('updateBarChart', [
            'barChartData' => $this->barChartData,
        ]);



        // organization filter 
        // if an org is selected, filter the table to include user groups that belong to the org  
        if ($this->selected_organization_id && $this->selected_organization != 'All') {
            $this->userGroups = Organization::find($this->selected_organization_id)->userGroups()->orderBy('name', 'ASC')->get();
        } else {
            $this->userGroups = UserGroup::orderBy('name', 'ASC')->get();
        }

        $this->dispatch('closeFiltersModal');

        // skill group filter 
    }

    public function clear()
    {


        $this->interval = 'Today';
        $this->custom_date_from = $this->custom_date_to = null;
        $this->selected_organization = 'All';
        $this->mount();
        $this->dispatch('updatePieChart', [
            'inboundCount' => $this->inboundCount,
            'outboundCount' => $this->outboundCount
        ]);

        $this->dispatch('updateBarChart', [
            'barChartData' => $this->barChartData,
        ]);
    }

    public function render()
    {
        ini_set('memory_limit', '4000M');
        ini_set('max_execution_time', 1000);

        return view('livewire.analytics');
    }
}
