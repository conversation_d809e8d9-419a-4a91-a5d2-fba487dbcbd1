
@php

$signedUrl = URL::temporarySignedRoute(
    'audio',
    now()->addMinutes(30),
    ['filename' => $pathVoice . '_final.wav']
    );

@endphp
<div class="container-fluid mt-3 px-4">
    <div class="mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            This is {{$form_name}} Form
                        </b>
                        {{-- <button class="btn btn-success shadow w-px-200 btn-color float-end " id="open_modal" data-bs-toggle="modal" data-bs-target="#evaluationModal" ><i class="fa fa-plus fa-style"></i> Add New Form</button> --}}

                                <input type="date" class="form-control" wire:model.defer="todayDate" style="border-radius: 5px;background-color: white;box-shadow: 0 0.0rem .5rem #00000026!important;float: right;width: 16%;margin-left: 10px;" readonly>
                                @error('todayDate')<small class="text-danger" style="right: 4%;position: absolute;margin-top: 45px;"> {{ $message }} </small> @enderror
                                <button type="button" class="btn btn-danger" style="float: right;">{{ $elapsedTime }}</button>
                    </h5>
                    <h6 class="text-muted">
                        <img  src="{{ asset('assets/images/evaluation/eye.png') }}" alt="i Icon" width="20" height="20" style="margin-top: -.4%;" >
                        <span style="">This page allows you to evaluate based on agent, source, reference Id.</span>
                    </h6>

                </div>
            </div>
        </div>

    </div>

    <div class="row section-one shadow">
        <div class="section-one-head"></div>
        <div class="section-one-field">
            <div class="row row-field">
                <div class="col-3">
                    <label for="name" class="col-form-label">Name:</label>
                    <div class="custom-select" style="position: relative">
                        <input type="text" class="form-control" wire:model="name" readonly/>

                    </div>
                </div>
                <div class="col-3">
                    <label for="year" class="col-form-label">Year:</label>
                    <div class="custom-select" style="position: relative">
                        <input type="text" class="form-control" wire:model="month" readonly/>
                    </div>
                </div>
                <div class="col-3">
                    <label for="month" class="col-form-label">Month:</label>
                    <div class="custom-select" style="position: relative">
                        <input type="text" class="form-control" wire:model="month" readonly/>
                    </div>
                </div>
                <div class="col-3">
                    <label for="referenceID" class="col-form-label">Reference ID:</label>
                    <input type="text" class="form-control" wire:model="referenceID" readonly/>
                </div>
            </div>
            <div class="row row-field">
                <div class="col-3">
                    <label for="week" class="col-form-label">Source:</label>
                    <input type="text" class="form-control" wire:model="source" readonly/>
                </div>
                <div class="col-3">
                    <label for="week" class="col-form-label">Week:</label>
                    <input type="text" class="form-control" wire:model="week" readonly/>
                </div>

                @foreach($arrayFields as $data)
                    @if($data)
                    <div class="col-3">
                        <label for="{{ is_array($data) ? $data['label'] : $data->label }}" class="col-form-label">
                            {{ is_array($data) ? $data['label'] : $data->label }}:
                        </label>
                        <input type="text" class="form-control" value="{{ is_array($data) ? $data['value'] : $data->value }}" readonly/>
                    </div>
                    @endif
                @endforeach

            </div>
        </div>
    </div>
    @if($referenceID)

    <style>

        .play-button:hover {
            color: #00a34e !important;
        }

        #playButton,
        #pauseButton,
        #openSpeedSelectorButton {
            font-size: 16px;
            cursor: pointer;
            width: 3rem;
            height: 3rem;
        }

        #openSpeedSelectorButton {
            font-size: 16px;
            cursor: pointer;
        }

        #playButton {
            background-color: #abc959;
            color: white;
            border: none;
        }

        #pauseButton {
            background-color: #02a34e;
            color: white;
            border: none;
        }

        #openSpeedSelectorButton {
            background-color: #2196F3;
            color: white;
            border: none;
        }

        #speedSelectorModal {
            display: inline;
        }

        .parent-btn {
            margin-top: 0.5rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        #duration {
            float: right;
            background-color: #c6d64e;
            border-radius: 0.5rem;
            padding: 0.1rem 0.3rem;
            font-weight: 400;
            color: black;
        }

        #time {
            background-color: #03a34d;
            border-radius: 0.5rem;
            padding: 0.1rem 0.3rem;
            font-weight: 400;
            color: white;
        }
        #playButton , #pauseButton {
            display: flex;
            justify-content: center; /* Center horizontally */
            align-items: center; /* Center vertically */
        }

    </style>



        <div class="row mx-3 d-flex ps-5" wire:ignore>
            {{-- filters card  --}}
            <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column" style="padding: 0px;">
                <div class="card rounded-3 bg-white shadow p-2">
                    <div class="card-body py-3">

                        <div id="waveform" class="mb-3"></div>

                        <div id="time" class="d-inline-block">0:00</div>
                        <div id="duration" class="d-inline-block">00:00</div>
                        <span style="margin-left: 48%" id="loadingMessageText" class="mt-5 text-nowrap"></span>
                        <div id="loadingMessage" class="preloader mt-0 mb-4" style="margin-left: 50%"></div>

                        <div class="parent-btn">
                            <button id="pauseButton" class="rounded-circle align-middle text-center" title="Pause" data-bs-toggle="tooltip">
                                <i class="fa-solid fa-pause fa-lg"></i>
                            </button>

                            <button id="playButton1" style="display: none">Play Audio</button>

                            <button id="playButton" class="rounded-circle align-middle text-center" title="Play" data-bs-toggle="tooltip">
                                <i class="fa-solid fa-play fa-lg"></i>
                            </button>

                            <button id="openSpeedSelectorButton" class="rounded-circle text-nowrap" title="Speed" data-bs-toggle="tooltip">1x</button>

                            <span class="modal-content3 shadow" id="speedSelectorModal" style="display: none;margin: 12px;">
                                <select id="speedSelector" class="selectTaq">
                                    <option value="0.5">.5x</option>
                                    <option value="1" selected>1x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2">2x</option>
                                </select>
                            </span>
                        </div>

                    </div>
                </div>
            </div>
        </div>




    <script src="https://unpkg.com/wavesurfer.js@7/dist/wavesurfer.min.js"></script>
    <script src="https://unpkg.com/wavesurfer.js@7/dist/plugin/wavesurfer.timeline.min.js"></script>
    <script src="https://unpkg.com/wavesurfer.js@7/dist/plugin/wavesurfer.minimap.min.js"></script>

    <script type="module">
        let voiceFileName;
        // Listen for the voiceFileName event
        document.addEventListener('voice-File', function(event) {

            voiceFileName = event.detail.voiceFileName;
            // voiceFileName = "testAudio.wav";
        });

        // Function to close modal when clicking outside of it
        window.addEventListener('click', function(event) {
            const speedSelectorModal = document.getElementById('speedSelectorModal');
            const openSpeedSelectorButton = document.getElementById('openSpeedSelectorButton');
            const speedSelector = document.getElementById('speedSelector');
            if (event.target !== speedSelectorModal && event.target !== openSpeedSelectorButton && event.target !== speedSelector) {
                speedSelectorModal.style.display = 'none'; // Hide the modal
            }
        });
        // Function to open modal when the button is clicked
        document.getElementById('openSpeedSelectorButton').addEventListener('click', function() {
            const speedSelectorModal = document.getElementById('speedSelectorModal');
            speedSelectorModal.style.display = 'inline'; // Display the modal
        });

        // Close the modal when clicking outside of it
        window.onclick = function(event) {
            const speedSelectorModal = document.getElementById('speedSelectorModal');
            if (event.target == speedSelectorModal) {
                speedSelectorModal.style.display = 'none';
            }
        };
        import WaveSurfer from 'https://unpkg.com/wavesurfer.js@7/dist/wavesurfer.esm.js';

        let wavesurfer; // Declare wavesurfer variable in a global scope
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        // Function to initialize WaveSurfer and start playing audio
        const initializeWaveSurfer = () => {
                wavesurfer = WaveSurfer.create({
                    container: '#waveform',
                    waveColor: '#03a34d',
                    progressColor: '#a8c858',
                    url:  @json($signedUrl),
                    backend: 'MediaElement',
                    responsive: true,
                    normalize: false,
                    barWidth: 5,
                    barRadius: 5,
                    responsive: true,
                    height: 80,
                    autoCenter: true,
                });

            const formatTime = (seconds) => {
                const minutes = Math.floor(seconds / 60)
                const secondsRemainder = Math.round(seconds) % 60
                const paddedSeconds = `0${secondsRemainder}`.slice(-2)
                return `${minutes}:${paddedSeconds}`
            }


            const timeEl = document.querySelector('#time')
            const durationEl = document.querySelector('#duration')
            wavesurfer.on('ready', function() {
                const duration = wavesurfer.getDuration();
                durationEl.textContent = formatTime(duration)
                const speedSelectorModal = document.getElementById('speedSelectorModal');
                speedSelectorModal.style.display = 'none';

                document.getElementById('loadingMessage').style.display = 'none'; // Hide loading message
                document.getElementById('loadingMessageText').style.display = 'none'; // Hide loading message
                document.getElementById('waveform').style.visibility = 'visible'; // Show waveform
                document.querySelector('.parent-btn').style.visibility = 'visible';
            });

            // Update time display when playback reaches certain point
            wavesurfer.on('timeupdate', function(time) {
                const formattedTime = formatTime(time); // Format the current time
                document.getElementById('time').textContent = formattedTime; // Update the time display in the div
            });
            // Listen for the 'finish' event to detect when the audio playback finishes
            wavesurfer.on('finish', function() {
                document.getElementById('pauseButton').style.opacity = 1;
                document.getElementById('playButton').style.opacity = 1;
            });

            // Listen for changes in playback speed
            document.getElementById('speedSelector').addEventListener('change', function() {
                const selectedSpeed = parseFloat(this.value); // Get the selected playback speed
                wavesurfer.setPlaybackRate(selectedSpeed); // Set the playback speed of the audio
                speedSelectorModal.style.display = 'none';
                document.getElementById('openSpeedSelectorButton').textContent = this.value + "x";
            });
        };
        // Event listener for the button click to initialize WaveSurfer
        document.getElementById('playButton1').addEventListener('click', () => {
            // Check if the AudioContext is suspended, and resume it if needed
            if (typeof Tone !== 'undefined' && Tone.context.state === 'suspended') {
                Tone.context.resume().then(() => {
                    initializeWaveSurfer();
                });
            } else {
                initializeWaveSurfer();
            }
        });
        // Event listener for the play button
        document.getElementById('playButton').addEventListener('click', () => {
            if (wavesurfer && !wavesurfer.isPlaying()) {
                wavesurfer.play();
                document.getElementById('playButton').style.opacity = .5;
                document.getElementById('pauseButton').style.opacity = 1;
            }

            // save who played the call
            var call_id = '{{ $pathVoice }}';
                var user_id = '{{ Auth::id() }}';

                $.ajax({
                    url: '{{ route('userPlayedInteraction') }}',
                    type: 'POST',
                    data: {
                        interaction_id: call_id,
                        user_id: user_id,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        console.log(response);
                    },
                    error: function(response) {
                        alert(response.responseJSON.message);
                    }
                });
        });
         // Show loading message when loading starts
        //  wavesurfer.on('loading', function(percentage) {
        //         document.getElementById('loadingMessage').style.display = 'inline'; // Show loading message
        //         document.getElementById('waveform').style.visibility = 'hidden'; // Optionally hide waveform initially
        //         document.getElementById('loadingMessageText').innerHTML = "Fetching: " + percentage + "%"; // Hide loading message
        //         document.getElementById('loadingMessageText').style.display = 'block'; // Hide loading message
        //     });
        // Event listener for the pause button
        document.getElementById('pauseButton').addEventListener('click', () => {
            if (wavesurfer && wavesurfer.isPlaying()) {
                wavesurfer.pause();
                document.getElementById('pauseButton').style.opacity = .5;
                document.getElementById('playButton').style.opacity = 1;
            }
        });
        // Initialize WaveSurfer when the DOM content is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                document.getElementById('playButton1').click();
            }, 500);

        });
    </script>


    @endif
    <div class="section-tow shadow">
        <div class="section-tow-boxes">
            <div class="row-box-one">
                <div class="box">
                    <div class="box-number"><strong>{{$totalWeightPoints}}</strong></div>
                    <div class="box-text">Total Weight Points</div>
                </div>
                <div class="box">
                    <div class="box-number"><strong>{{$totalPossibleWeightPoints}}</strong></div>
                    <div class="box-text">Total Possible Weighted Points</div>
                </div>
            </div>
            <div class="row-box-tow">
                <div class="box">
                    <div class="box-number"><strong>{{$totalScore}}</strong></div>
                    <div class="box-text">Total Score</div>
                </div>
                <div class="box">
                    <div class="box-number"><strong>{{$totalPossibleScore}}</strong></div>
                    <div class="box-text">Total Possible Score</div>
                </div>
            </div>
        </div>
        <div class="section-tow-chart">
            <div class="section-tow-colors-group">
                <div class="box-color">
                    <div class="green-color"></div>
                    <span class="text-span">80% - 100% Excellent</span>
                </div>
                <div class="box-color">
                    <div class="yellow-color"></div>
                    <span class="text-span">60% - 80% Very Good</span>
                </div>
                <div class="box-color">
                    <div class="beige-color"></div>
                    <span class="text-span">40% - 60% Good</span>
                </div>
                <div class="box-color">
                    <div class="orange-color"></div>
                    <span class="text-span">20% - 40% Poor</span>
                </div>
                <div class="box-color">
                    <div class="red-color"></div>
                    <span class="text-span">0% - 20% Very Poor</span>
                </div>
            </div>
            <div class="section-tow-circle-chart">
                <canvas id="myChart"></canvas>
            </div>
        </div>
    </div>
    <div class="section-three shadow">
        <div class="section-three-container">
            <div class="container-color">
                <div class="green-color-2"></div>
                <span class="text-span-2">Group Header</span>
                <div class="yellow-color-2"></div>
                <span class="text-span-2">Question Header</span>
            </div>
                <div class="div-table">
                    <table class="table table-bordered">
                        <thead style="height: 0rem !important">
                        <tr class="table-header">
                            <th scope="col" style="width: 50%">Groups</th>
                            <th scope="col" style="width: 15%">Possible Scores</th>
                            <th scope="col" style="width: 15%">Actual</th>
                            <th scope="col" style="width: 20%">Comments</th>
                        </tr>
                        </thead>
                        <tbody>
                        @if (!empty($array))
                            @php
                                $index2 = 0;
                            @endphp
                            @foreach ($array as $groupName => $groupWeights)

                                <tr class="group-table">

                                    <th class="group-table-th group-table-2" colspan="4">{{$groupName}}</th>
                                </tr>
                                @foreach ($groupWeights as $groupWeight => $dataQuestions)
                                    @foreach ($dataQuestions as $headerName => $dataQuestionArray)
                                        <tr class="group-header">
                                            <th class="group-header-2" colspan="4">{{$headerName}}</th>
                                        </tr>
                                        @foreach ($dataQuestionArray as $dataQuestion)
                                            <tr>
                                                <td>{{$dataQuestion['question_name']}}</td>
                                                <td class="td-style">
                                                    @php
                                                        $data = $dataQuestion['answers'][0]['mark_and_weight'];
                                                        $data = json_decode($data, true);
                                                        $marks = array_column($data, 'weight');
                                                   echo $maxMark = max($marks);

                                                    @endphp
                                                </td>
                                                <td>
                                                    <div class="custom-select" style="position: relative">
                                                       <input type="text" class="form-control" value="{{ isset($arraySubmittion[$index2]['mark']) ? $arraySubmittion[$index2]['mark'] : '' }}" style="text-align: center;" readonly>

                                                    </div>
                                                </td>
                                                <td>
                                                    <textarea class="form-control text-area"   readonly>{{ isset($arraySubmittion[$index2]['comment']) ? $arraySubmittion[$index2]['comment'] : '' }}</textarea>
                                                </td>
                                            </tr>
                                            @php

                                                $index2++;
                                            @endphp
                                        @endforeach
                                    @endforeach
                                    <tr class="group-weight">
                                        <th class="group-weight-th">Weighted Value</th>
                                        <th class="group-weight-th-number">{{$groupWeight}}%</th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                @endforeach
                            @endforeach
                            <tr class="group-comment">
                                <th colspan="4" class="group-weight-th">Comment</th>
                            </tr>
                            <tr class="group-comment">
                                <td colspan="4">
                                    <textarea class="form-control text-area-last" wire:model.defer="commentEvaluation" placeholder="Type your comment here..." readonly></textarea>
                                </td>
                            </tr>
                        @else
                            <tr><td colspan="4">No data available.</td></tr>
                        @endif
                        </tbody>
                    </table>
                </div>
        </div>
    </div>

{{--     @if (!empty($array))
        <div class="section-four">
            <div class="col-6">
                <a href="{{ route('evaluation.report') }}" class="btn btn-dark mx-1 shadow" style="padding-top: 8px;">
                    <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;"> Previous
                </a>
            </div>

    @endif --}}









</div>

