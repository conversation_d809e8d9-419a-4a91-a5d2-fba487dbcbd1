<?php

namespace App\Exports\Ticket;

ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 300);

use App\Models\Evaluation;
use App\Models\EvaluationQuestion;
use App\Models\EvaluationSubmission;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithMapping;

class EvaluationReportsExport implements FromCollection, WithMapping, WithHeadings, ShouldAutoSize, WithChunkReading, ShouldQueue
{
    use Exportable;

    protected $yearSelected;
    protected $monthSelected;
    protected $weekSelected;
    protected $referenceIDSelected;
    protected $sourceSelected;
    protected $organizationSelected;
    protected $agentNameOpsIdSelected;
    protected $date;
    protected $dateFrom;
    protected $dateTo;
    protected $role;
    protected $reportType;
    protected $evaluation_id;
    public $evaluationSubmission = [];
    protected array $nonCriticalCounts = [];

    public function __construct(
        $yearSelected, $monthSelected, $weekSelected, $referenceIDSelected, $sourceSelected,$organizationSelected,$agentNameOpsIdSelected,
        $date, $dateFrom, $dateTo, $role, $reportType, $evaluation_id
    ) {
        $this->yearSelected = $yearSelected;
        $this->monthSelected = $monthSelected;
        $this->weekSelected = $weekSelected;
        $this->referenceIDSelected = $referenceIDSelected;
        $this->sourceSelected = $sourceSelected;
        $this->organizationSelected = $organizationSelected;
        $this->agentNameOpsIdSelected = $agentNameOpsIdSelected;
        $this->date = $date;
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
        $this->role = $role;
        $this->reportType = $reportType;
        $this->evaluation_id = $evaluation_id;
    }
    protected function prepareData($allTickets)
    {
        foreach ($allTickets as $ticket) {
            $this->nonCriticalCounts[$ticket->id] = $this->calculateNonCriticalCount($ticket->submissionAnswers);
        }
    }

    protected function calculateNonCriticalCount($submissionAnswers)
    {
        $count = 0;
        foreach ($submissionAnswers as $submissionAnswer) {
            if ($submissionAnswer->mark !== 'Fatal Critical' && $submissionAnswer->mark !== 'Fatal Per Group') {
                // Check if question and its first answer exist
                $question = $submissionAnswer->question;
                if (!$question || !isset($question->answers[0])) {
                    continue; // Skip this iteration if not valid
                }

                $data2 = $question->answers[0]->mark_and_weight;
                $data2 = json_decode($data2, true);

                $maxMarkItem = array_reduce($data2, function ($carry, $item) {
                    return ($carry['weight'] ?? 0) > $item['weight'] ? $carry : $item;
                });

                if ($maxMarkItem['weight'] != $submissionAnswer->mark) {
                    $count++;
                }
            }
        }
        return $count;
    }

    public function collection()
    {
        $report = EvaluationSubmission::with('submissionAnswers');

        // Common filters
        if ($this->yearSelected) $report->where('year', $this->yearSelected);
        if ($this->monthSelected) $report->where('month', $this->monthSelected);
        if ($this->weekSelected) $report->where('week', $this->weekSelected);
        if ($this->referenceIDSelected) $report->where('referenceID', $this->referenceIDSelected);
        if ($this->sourceSelected) {
            $report->whereHas('Interaction', function ($query) {
                $query->where('call_type', $this->sourceSelected);
            });
        }
        if ($this->date && $this->dateFrom && $this->dateTo) {
            $report->whereBetween('created_at', [
                "{$this->dateFrom} 00:00:00",
                "{$this->dateTo} 23:59:59"
            ]);
        }

        // Role-based filters
        $userId = auth()->user()->id;
        $organizationId = auth()->user()->organization_id;
        $supervisorOrgs = Auth::user()->supervisorOrganizations->pluck('id');

        if ($this->role == '4') {
            $report->where('user_id', $userId);
        } elseif ($this->role == '6') {
            $report->whereHas('user', fn($query) => $query->where('organization_id', $organizationId));
        } elseif (in_array($this->role, ['2', '5'])) {
            $report->whereHas('user.organization', fn($query) => $query->whereIn('id', $supervisorOrgs));
        }
        if($this->organizationSelected){
            $report->whereHas('user.organization', fn($query) => $query->where('id', $this->organizationSelected));
        }
        if($this->agentNameOpsIdSelected){
            $report->whereHas('user', function ($query) {
                $query->where('agent_id', $this->agentNameOpsIdSelected)
                      ->orWhere('full_name', 'like', '%' . $this->agentNameOpsIdSelected . '%');
            });
        }
        // Specific report type filters
        if (($this->reportType == "evaluation_parameteres_report" || $this->reportType == "account_wise_defect_report") && $this->evaluation_id) {
            $report->where('evaluation_id', $this->evaluation_id);
        }
        $report->orderBy('id', 'desc');
        if ($this->reportType == "evaluation_agent_avg_score_report"){
            $this->prepareData($report->get());
            return $report->get();
        }elseif($this->reportType == "account_wise_defect_report"){
            if($this->evaluation_id){
                $this->evaluationSubmission = $report->first();
                $firstReport = $report->first();

                if ($firstReport) {
                    $submissionAnswers = $firstReport->submissionAnswers()->get();
                    $this->prepareData($report);
                    return $submissionAnswers;
                } else {
                    $this->prepareData([]);
                    return [];
                }
                // ->paginate($this->paginateCount)
            }
        }else{
            $this->prepareData($report->limit(1000)->get());
            return $report->limit(1000)->get();
        }
    }
    public function map($ticket): array
    {
        $mappedData = [];
        $data = $ticket;
        $data = $ticket;
        $countNoneCriticle = $this->nonCriticalCounts[$ticket->id] ?? 0;
        switch ($this->reportType) {
            case 'evaluation_report':
                    $mappedData[] = [
                        'Reference ID' => $data->referenceID,
                        'Name' => $data->name,
                        'User Id' => $data->user_id,
                        'Date & Time' => $data->created_at,
                        'Source' => $data->Interaction->call_type ?? '',
                        'Quality Percentage' => intval($data->quality_percentage) . '%',
                        'Evaluation Duration' => $data->evaluation_duration ?? '00:00:00',
                    ];
                break;

            case 'evaluation_avg_score_report':
                    $mappedData[] = [
                        'User ID' => $data->user->agent_id,
                        'Name' => $data->name,
                        'Call Date & Time' => $data->Interaction->arrival_time,
                        'Evaluation Date & Time' => $data->created_at,
                        'Interaction ID' => $data->Interaction->call_type === 'Outbound' ? $data->Interaction->called_id : $data->Interaction->caller_id,
                        'Unique ID' => $data->Interaction->call_id,
                        'Tenure' => '',
                        'Evaluator Name' => $data->evaluator->full_name ?? '',
                        'Quality Percentage' => intval($data->quality_percentage) . '%',
                        'QA Comment' => $data->commentEvaluation,
                    ];
                break;

            case 'evaluation_parameteres_report':
                if ($ticket->evaluation_id) {
                        $row = [
                            'Reference ID' => $data->referenceID,
                            'Call ID' => $data->Interaction->call_id ?? '-',
                            'Name' => $data->name,
                            'User Id' => $data->user_id,
                            'Date & Time' => $data->created_at,
                            'Source' => $data->Interaction->call_type ?? '-',
                            'Quality Percentage' => intval($data->quality_percentage) . '%',
                            'Evaluation Duration' => $data->evaluation_duration ?? '00:00:00',
                            'Ai Score' => $data->Interaction->ai_score ?? '-',
                            'Language' => $data->Interaction->language ?? '-',
                            'Account' => $data->evaluation->organization->name ?? '-',
                            'Original call duration' => $data->Interaction->call_duration ?? '-',
                        ];
                        $eval = Evaluation::query()->where('id', $this->evaluation_id)->first();
                        $arr = []; // Initialize the array to store question names
                        if ($eval) {
                            foreach ($eval->groups->where('status',1) as $group) {
                                foreach ($group->questions->where('status',1) as $question) {
					$arr[] = $question->question_name;
                                   // 	$arr[] = 'Comment'; // Collect question names
                                }
                            }
                        }
                        // Assuming $data->submissionAnswers is related to the questions
                        foreach ($data->submissionAnswers as $key => $question) {
                            if (isset($arr[$key])) {
                        $row[] = $question->comment ?? '';

                                $row[$arr[$key]] = $question->mark;
                        }
                        }


                        //foreach ($data->submissionAnswers as $key => $question) {
                        // if (isset($arr[$key])) { // Ensure the index exists in $arr
                            //    $row[$arr[$key]] = $question->mark; // Use question name as key
                        //  }
                        //	}



                        $mappedData[] = $row;
                }
                break;

            case 'evaluation_agent_avg_score_report':
                $totalRecords = $ticket->count();
                $averageQuality = $totalRecords > 0 ? intval($ticket->avg('quality_percentage')) : 'N/A';
                $firstRecord = $ticket->first();

                if ($totalRecords > 0) {
                    $mappedData[] = [
                        'User ID' => $firstRecord->user->agent_id,
                        'Name' => $firstRecord->name,
                        'Tenure' => '',
                        'Number Of Evaluated Records' => $totalRecords,
                        'Quality Score' => $averageQuality,
                        'From The Date Range Selected' => '',
                    ];
                }
                break;

            case 'agent_wise_defect_report':
                    $mappedData[] = [
                        'User ID' => $data->user->agent_id,
                        'Name' => $data->name,
                        'Tenure' => '', // Add tenure data if available
                        'Caller ID' => $data->Interaction->caller_id ?? '-',
                        'Unique ID' => $data->Interaction->call_id ?? '-',
                        'Language' => $data->Interaction->language ?? '-',
                        'Number of Hold' => $data->Interaction->hold_count ?? '-',
                        'Hold Duration' => $data->Interaction->hold_duration ?? '-',
                        'Date Of Call' => $data->Interaction->arrival_time ?? '-',
                        'QA Name' => $data->evaluator->full_name ?? '',
                        'Date of Evaluation' => $data->created_at,
                        'Number of Critical Errors' => $data->submissionAnswers->filter(function ($answer) {
                            return $answer->mark === 'Fatal Critical' || $answer->mark === 'Fatal Per Group';
                        })->count() ?? 0, // Add count if available
                        'Number of Non-critical Errors' => $countNoneCriticle,
                        'Evaluation Score' => intval($data->quality_percentage) . '%',
                    ];
                    break;

            case 'interaction_report':
                    $mappedData[] = [
                        'Interaction ID' => $data->interaction_id,
                        'Caller ID' => $data->caller_id,
                        'Called ID' => $data->called_id,
                        'Call Type' => $data->call_type,
                        'Start Time' => $data->start_time,
                        'End Time' => $data->end_time,
                    ];
                break;

            case 'qa_occupancy_report':
                $mappedData[] = [
                    'Organization Name' => $data->user->organization->name,
                    'User ID' => $data->user->agent_id,
                    'Name' => $data->name,
                    'Evaluator Name' => $data->evaluator->full_name ?? '',
                    'Call Type' => $data->Interaction->call_type,
                    'Language' => $data->Interaction->language,
                    'Caller ID' => $data->Interaction->caller_id,
                    'Unique ID' => $data->Interaction->call_id,
                    'Call Time' => $data->created_at->format('h:i:s'),
                    'Date of Call' => $data->created_at->format('yy/m/d'),
                    'Evaluation Duration' => $data->evaluation_duration ?? '00:00:00',
                    'Quality Score' => intval($data->quality_percentage) . '%',
                ];
                break;


            default:
                // Handle unknown report types or throw an exception
                break;
        }

        return $mappedData;
    }
    
    public function headings(): array
    {
        switch ($this->reportType) {
            case "evaluation_report":
                return [
                    'Reference ID',
                    'Name',
                    'User Id',
                    'Date & Time',
                    'Source',
                    'Quality Percentage',
                    'Evaluation Duration',
                    'Comment',
                ];
            case "evaluation_avg_score_report":
                return [
                    'User ID',
                    'Name',
                    'Call Date & Time',
                    'Evaluation Date & Time',
                    'Interaction ID',
                    'Unique ID',
                    'Tenure',
                    'Evaluator Name',
                    'Quality Percentage',
                    'QA Comment'
                ];
            case "evaluation_parameteres_report":
                    $headings = [
                        'Reference ID',
                        'Call ID',
                        'Name',
                        'User Id',
                        'Date & Time',
                        'Source',
                        'Quality Percentage',
                        'Evaluation Duration',
                        'Ai Score',
                        'Language',
                        'Account',
                        'Original call duration',
                    ];
                    $eval = Evaluation::query()->where('id', $this->evaluation_id)->first();
                    if ($eval) {
                        foreach ($eval->groups->where('status',1) as $group) {
                            foreach ($group->questions->where('status',1) as $question) {
 $headings[] = 'Comment';

 $headings[] = $question->question_name;
                               
                            }
                        }
                    }
                    return $headings;
            case "evaluation_agent_avg_score_report":
                return [
                    'User ID',
                    'Name',
                    'Tenure',
                    'Number OF Evaluated Records',
                    'Quality Score',
                    'From The Date Range Selected'
                ];
            case "agent_wise_defect_report":
                return [
                    'User ID',
                    'Name',
                    'Tenure',
                    'Caller ID',
                    'Unique ID',
                    'Language',
                    'Number of Hold',
                    'Hold Duration',
                    'Date Of Call',
                    'QA Name',
                    'Date of Evaluation',
                    'Number of Critical Errors',
                    'Number of Non-critical Errors',
                    'Evaluation Score'
                ];
            case "account_wise_defect_report":
                return [
                    'Account',
                    'Evaluator',
                    'Source',
                    'Parameter Name',
                    'Count Of Deduction',
                    'AVG %',
                    'From The Date Range Selected'
                ];
            case "qa_occupancy_report":
                return [
                    'Account',
                    'User ID',
                    'User Name',
                    'Evaluator Name',
                    'Source',
                    'Language',
                    'Caller ID',
                    'Call Id',
                    'Evaluation Time',
                    'Evaluation Date',
                    'Evaluation Duration',
                    'Interaction Score'
                ];
            default:
                return ['ID', 'User ID', 'Score', 'Created At']; // Default headings
        }
    }




    public function chunkSize(): int
    {
        return 100;
    }
}
