<?php

namespace App\Http\Controllers\Evaluation;


use Carbon\Carbon;
use App\Models\Encryption;
use App\Models\Interaction;
use Illuminate\Http\Request;
use App\Models\Tenant\Evaluation;
use App\Http\Controllers\Controller;
use App\Models\EvaluationSubmission;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class EvaluationController extends Controller
{



    /**
     * Handle the incoming request.
     */
    public function index()
    {
        return view('evaluation.index');
    }

    public function createGroup($id)
    {
        return view('evaluation.createGroup', compact('id'));
    }
    public function createQuestion($evaluation_id, $group_id)
    {

        return view('evaluation.createQuestion', compact('evaluation_id', 'group_id'));
    }
    public function editQuestion($evaluation_id, $group_id, $question_id)
    {

        return view('evaluation.editQuestion', compact('evaluation_id', 'group_id', 'question_id'));
    }
    public function createFormBuilder($evaluation_id)
    {

        return view('evaluation.createFormBuilder', compact('evaluation_id'));
    }
    public function submitEvaluationForm($evaluation_id)
    {
        $voiceRecord = Interaction::query()->where('id', request()->query('chat_id'))->first();

        // Get The encryption status of call type
        $encryptionStatus = Encryption::where('type', 'call')->first();

        $callId = $voiceRecord->call_id;
        $callIdToServe = $voiceRecord->call_id;

        $callAccount = Interaction::firstWhere('call_id', $callId)?->organization_id;


        // Check if the call_id already has the suffix @10.202.1.66....
        if (!str_ends_with($callId, '@10.202.1.66')) {
            $callId .= '@10.202.1.66';
        }

        $fileName = "{$callId}.wav";
        //        $filePath = "audio/{$fileName}";

        $filePath = "audio/{$fileName}";

        return view('evaluation.submitEvaluationForm', compact('evaluation_id', 'filePath', 'callIdToServe'));

 
    }
    public function submitEvaluationForm1($evaluation_id)
    {
        // $filePath = "audio/{erewrwer}";
        // return view('evaluation.submitEvaluationForm', compact('evaluation_id', 'filePath'));
        $voiceRecord = Interaction::query()->where('id', request()->query('chat_id'))->first();

        // Get The encryption status of call type
        $encryptionStatus = Encryption::where('type', 'call')->first();

        $callId = $voiceRecord->call_id;
        $callIdToServe = $voiceRecord->call_id;

        $callAccount = Interaction::firstWhere('call_id', $callId)?->organization_id;


        // Check if the call_id already has the suffix @10.202.1.66....
        if (!str_ends_with($callId, '@10.202.1.66')) {
            $callId .= '@10.202.1.66';
        }

        $fileName = "{$callId}.wav";
        //        $filePath = "audio/{$fileName}";

        if ($encryptionStatus->status) {
            $filePath = "audio/{$fileName}";
        } else {
            $filePath = "audio/{$fileName}";
        }
        if (file_exists($filePath)) {

            return view('evaluation.submitEvaluationForm', compact('evaluation_id', 'filePath', 'callIdToServe'));
        }

        // If the file does not exist, download it
        $callDate = $voiceRecord->arrival_time;


        $response = null;
        $linuxResponse = null;

        // if modanisa 43 or bci 14, get it from 73 ftp server
        if ($callDate < Carbon::parse('06-11-2024')) {
            if (in_array($callAccount, [43, 14])) {
                $response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadRecordingModanisaBci.php', [
                    'call_id' => $callId
                ]);
            }
            // else if not modanisa or bci
            else {
                if ($callDate <= Carbon::parse('24-10-2024')) {
                    $response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadCallSub.php', [
                        'call_id' => $callId,
                        'call_day' => $callDate->format('Y_m_'),
                    ]);
                } else {
                    $response = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadRecording.php', [
                        'call_id' => $callId
                    ]);
                }
            }
        } else {
            $linuxResponse = Http::withoutVerifying()->timeout(12000)->get('https://oms.extwebonline.com/Extensya_APIs/recording//linux-downloadCalls.php', [
                'call_id' => $callId,
                'call_day' => $callDate->format('Y-m-d'),
            ]);
        }


        // Check if the request was successful
        if ($response) {
            if ($response->successful()) {


                if ($encryptionStatus->status) {

                    $encryptedContent = encrypt($response->body(), config('app.key'));

                    Storage::disk('public')->put($filePath, $encryptedContent);
                } else {
                    Storage::disk('public')->put($filePath, $response->body());
                }


                return view('evaluation.submitEvaluationForm', compact('evaluation_id', 'filePath', 'callIdToServe'));
            } else {
                session()->flash('alert_no_call', 'This interaction is still not fetched from the system. Please try again later.');

                // Redirect back to the original page
                return back();
                // return "Failed to download the file: " . $response->status();
            }
        }


        return view('evaluation.submitEvaluationForm', compact('evaluation_id', 'filePath', 'callIdToServe'));

 /*        if ($linuxResponse) {
            if ($linuxResponse->successful()) {


                if ($encryptionStatus->status) {

                    $encryptedContent = encrypt($linuxResponse->body(), config('app.key'));

                    Storage::disk('public')->put($filePath, $encryptedContent);
                } else {
                    Storage::disk('public')->put($filePath, $linuxResponse->body());
                }
                return view('evaluation.submitEvaluationForm', compact('evaluation_id', 'filePath'));
            } else {
                session()->flash('alert_no_call', 'This interaction is still not fetched from the system. Please try again later.');

                // Redirect back to the original page
                return back();
                // return "Failed to download the file: " . $response->status();
            }
        } */
    }
    public function report($reportType)
    {

        return view('evaluation.report', compact('reportType'));
    }
    public function reports()
    {

        return view('evaluation.reports');
    }
    public function reportAnswers($submit_id)
    {

        return view('evaluation.reportAnswers', compact('submit_id'));
    }
}
