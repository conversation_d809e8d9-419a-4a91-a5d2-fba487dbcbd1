<?php

namespace App\Console\Commands;

use PDO;
use PDOException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestRemoteDatabaseConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $remoteDbConfig = [
            'host' => '***********',
            'port' => '3306',
            'username' => 'sdk_db_user',
            'password' => 'sdk_db_user@123',
            'database' => 'agent_rta',
        ];

        $dsn = "mysql:host={$remoteDbConfig['host']};port={$remoteDbConfig['port']};dbname={$remoteDbConfig['database']}";

        try {
            $remoteDb = new PDO($dsn, $remoteDbConfig['username'], $remoteDbConfig['password']);
            $remoteDb->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            $callId = 'some_call_id'; 
            $stmt = $remoteDb->prepare("SELECT RTargetAgentSelected, RTargetPlaceSelected, ClientName, Language, AttributeTimeinSecs FROM genesys_irecorder_mapping_data LIMIT 1");
            $stmt->execute();
            $remoteRecord = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($remoteRecord) {
                Log::info('First Remote Record: ', $remoteRecord);
                $this->info('First Remote Record: ' . json_encode($remoteRecord));
            } else {
                Log::info('No record found.');
                $this->info('No record found.');
            }
        } catch (PDOException $e) {
            Log::error("Connection failed: " . $e->getMessage());
            $this->error("Connection failed: " . $e->getMessage());
        }
    }
}
