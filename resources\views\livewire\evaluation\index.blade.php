<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">

            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button id="open_modal"
                    data-bs-toggle="modal"
                    data-bs-target="#evaluationModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 150px; height: 40px; border-color: #01a44f; background: #01a44f;">
                    <i
                        class="fa fa-plus fa-style text-white ms-3"
                        style="font-size: 20px;    margin-inline-end: auto;"></i>
                    <span style="font-size: 14px;">Add New Evaluation</span>
                </button>
            </div>
        </div>
    </div>



        <div class="parent-sections mx-3 ps-5">

            <div class="section-one">
                    <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">

                    <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                        <thead style="background-color: #f8fafc;font-size: .8rem">
                        <tr>
                            <th scope="col" class="checked text-center" style="width: 10%;">
                                <div class="form-check">
                                    #
                                </div>
                            </th>
                            <th scope="col" style="width: 50%;padding: 11px !important;">Evaluation Form Name</th>
                            <th scope="col" style="width: 10%;padding: 11px !important;">Organization</th>
                            <th scope="col" style="width: 10%;padding: 11px !important;">Edit</th>
                            <th scope="col" style="width: 10%;padding: 11px !important;">Status</th>
                            <th scope="col" style="width: 10%;padding: 11px !important;">Admin Page</th>
                            {{-- <th scope="col" style="width: 10%;">View</th> --}}
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($Evaluation_form ?? [] as $form)
                            <tr id="row{{$form->id}}" style="cursor: pointer;"  wire:ignore.self>
                                <td class='text-muted' scope="row text-center">
                                    <div class="form-check">
                                        {{$loop->index +1}}
                                    </div>
                                </td>
                                <td class='text-muted' wire:click="showEdit({{$form->id }})">
                                    {!! $form->evaluation_name !!}
                                    <span class="span_icon" wire:click="showUpdateModal({{$form->id }})"  wire:ignore>
                                        <i class="fa-solid fa-pen pencel_icon_one" id="pencel_icon{{$form->id}}" style="color: #989898;visibility: hidden;" ></i>
                                    </span>
                                </td>
                                <td class='text-muted' wire:click="showEdit({{$form->id }})">
                                    {!! $form->organization->name ?? '-' !!}
                                    <span class="span_icon" wire:click="showUpdateModal({{$form->id }})"  wire:ignore>
                                        <i class="fa-solid fa-pen pencel_icon_one" id="pencel_icon1{{$form->id}}" style="color: #989898;visibility: hidden;" ></i>
                                    </span>
                                </td>

                                <td class='text-muted'>
                                    <a href="{{ route('evaluation.createGroup', ['id' => $form->id]) }}">
                                        <i class="fa-solid fa-pen" id="pencel_icon{{$form->id}}" style="font-size: 20px;color: #00a34e;"></i>
                                    </a>
                                </td>
                                <td class="text-center text-muted"> <!-- Centering the content within the table cell -->
                                    <div class="d-flex align-items-center gap-2 justify-content-center align-items-center ">
                                        <div class="position-relative">
                                            <input type="checkbox" class="d-none" id="customSwitch{{ $form->id }}" wire:click="statusUpdateModal({{$form->id }})" />
                                            <label for="customSwitch{{ $form->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $form->status ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                <div class="switch-handle position-absolute"
                                                    style="
                                                        width: 18px;
                                                        height: 18px;
                                                        background-color: {{ $form->status ? '#ffffff' : '#FF5E60' }};
                                                        border-radius: 50%;
                                                        top: 3px;
                                                        left: {{ $form->status ? '22px' : '3px' }};
                                                        transition: left 0.3s, background-color 0.3s;">
                                                    @if ($form->status)
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                            <path
                                                                d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                        </svg>
                                                    @else
                                                        <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                            <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        </svg>
                                                    @endif
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-muted">
                                    @if($form->status == '1')
                                        <a href="{{ route('evaluation.createFormBuilder', ['evaluation_id' => $form->id]) }}">
                                        <i class="fa fa-user" style="font-size: 22px;color: #00a34e;"></i>
                                        </a>
                                    @else
                                        <i class="fa fa-user" style="font-size: 22px;color: #989898;"></i>
                                    @endif
                                </td>
                                {{-- <td class="text-muted">
                                    @if($form->status == '1')
                                        <a href="{{ route('evaluation.submitEvaluationForm', ['evaluation_id' => $form->id]) }}">
                                        <i class="fas fa-eye" style="font-size: 22px;color: #00a34e;" ></i>
                                        </a>
                                    @else
                                        <i class="fas fa-eye" style="font-size: 22px;color: #989898;"></i>
                                    @endif
                                </td> --}}
                            </tr>


                        @empty
                            <tr>
                                <td colspan="11" class="text-muted text-center"> There is no Evaluation form found</td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
                {{-- {!! $CannedResponse_apps->links() !!}--}}
            </div>
        </div>










    <div wire:ignore.self class="modal fade" id="evaluationModal" tabindex="-1" role="dialog" aria-labelledby="evaluationModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                {{-- <i class="fa-solid fa-filter" ></i> --}}
                                <i class="fas fa-file-alt" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h6 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 20px;">
                                @if(!$modalIdShow)
                                    {{ $modalId ? 'Update Evaluation Form' : 'Add New Evaluation Form' }}
                                @else
                                    View Evaluation Form
                                @endif
                            </h6>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">

                        <div class="col-12">

                            <div class="mb-3">
                                <label for="title" class="col-form-label">Form Name:</label>
                                @if(!$modalIdShow)
                                    <input type="text" class="form-control" wire:model.defer="form_name" id="form_name" >
                                    @error('form_name')<small class="text-danger"> {{ $message }} </small> @enderror
                                @else
                                    <input type="text" class="form-control" wire:model="form_name" disabled>
                                @endif
                            </div>


                        <div class="mb-3">
                            <label for="title" class="col-form-label ">Organization:</label>
                            @if(!$modalIdShow)
                                <select class="form-control" wire:model.defer="organization_id" id="organization_id" >
                                    <option value="" selected>Please Select</option>
                                    @forelse ($organizations as $organization)
                                    <option value="{{ $organization->id }}">{{ $organization->name }}</option>
                                    @empty
                                        No organization found
                                    @endforelse
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="arrow-icon">
                                    <path d="M7 10l5 5 5-5z"/>
                                </svg>
                                @error('organization_id')<small class="text-danger"> {{ $message }} </small> @enderror
                            @else
                                <input type="text" class="form-control" wire:model="form_name" disabled>
                            @endif
                        </div>

                    </div>
                    </div>

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal" aria-label="Close">
                        Close
                    </button>
                    @if(!$modalIdShow)
                        <button
                            class="btn btn-success rounded-3 px-4"
                            style="height: 40px; border-color: #01a44f; background: #01a44f;"
                            wire:click="{{ $modalId ? 'update' : 'store' }}" >
                            {{ $modalId ? 'Update' : 'Submit' }}
                        </button>
                    @endif
                </div>
        </div>
    </div>
</div>
