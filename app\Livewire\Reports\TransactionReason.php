<?php
namespace App\Livewire\Reports;

use App\Exports\TransactionReasonExport;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use <PERSON>tinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithoutUrlPagination;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class TransactionReason extends Component
{
    use LivewireAlert, WithPagination, WithoutUrlPagination;

    public $perPage = 15;

    // filters
    public $filterApplied    = false;
    public $filter_time_name = 'Last 24 Hours';
    public $filter_time_days = 1;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_callType = 'Call'; // Fixed to always be "Call"
    public $filter_account;

    protected $paginationTheme = 'bootstrap';

    public function mount()
    {
        $this->filter_time_set('1');
        $this->filterApplied = true;
    }

    public function recordsPerPage($value)
    {
        $this->perPage = (int) $value;
        $this->resetPage();
    }

    public function apply_custom_date()
    {
        $this->filter_time_name = 'Custom';
        $this->filter_time_days = null;

        $this->validate();

        $this->dispatch('closeCustomDateModal');
    }

    public function filter_time_set($time)
    {
        $this->filter_time_days = $time;

        // Clear custom dates when selecting a time period
        $this->custom_date_from = null;
        $this->custom_date_to   = null;

        $this->filter_time_name = match ((int) $time) {
            1 => 'Last 24 Hours',
            7       => 'Last 7 Days',
            30      => 'Last 30 Days',
            60      => 'Last 60 Days',
            default => 'All Time',
        };

        if ($this->filter_time_name == 'Custom') {
            $this->filter_time_days = null;
        }
    }

    public function rules()
    {
        return [
            'custom_date_from' => 'required_if:filter_time_name,Custom|',
            'custom_date_to'   => 'required_if:filter_time_name,Custom|after:custom_date_from',
        ];
    }

    public function clear()
    {
        $this->filterApplied    = false;
        $this->filter_time_name = 'All Time';
        $this->custom_date_from = null;
        $this->custom_date_to   = null;
        $this->filter_time_days = null;
        $this->filter_callId    = null;
        $this->filter_callType  = 'Call'; // Keep it as "Call" even when clearing
        $this->filter_account   = null;
    }

    public function closeModal()
    {
        $this->resetValidation();
        $this->dispatch('closeModal');
    }

    public function filter()
    {
        $this->filterApplied = true;
        $this->resetPage();
    }

    public function getData()
    {
        $this->filter();
        $this->dispatch('close-modal');
    }

    /**
     * Export transaction reason data to Excel
     */
    public function export()
    {
        // Extend maximum execution time and memory
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $export = new TransactionReasonExport(
            $this->filter_time_name,
            $this->filter_time_days,
            $this->custom_date_from,
            $this->custom_date_to,
            $this->filter_callId,
            $this->filter_account
        );

        // Return the export response
        return Excel::download($export, 'transaction_reason_report.xlsx');
    }

    public function render()
    {
        // Base query joining interactions with calls_summary table
/*         $query = DB::table('interactions')
            ->leftJoin('calls_summary', function($join) {
                $join->on(DB::raw("SUBSTRING_INDEX(interactions.call_id, '@', 1)"), '=', 'calls_summary.call_id');
            })
            ->leftJoin('organizations', 'interactions.organization_id', '=', 'organizations.id')
            ->select([
                'interactions.call_id as transaction_id',
                'organizations.name as account',
                DB::raw("'Call' as type_call"), // Fixed to always show "Call"
                'calls_summary.call_type as call_reason', // Call Reason from calls_summary.call_type
                'calls_summary.sub_type as sub_call_reason', // Sub Call Reason from calls_summary.sub_type
                'interactions.arrival_time',
                'interactions.id as interaction_id'
            ])
            ->orderByDesc('interactions.arrival_time'); */
        $latestSummarySubquery = DB::table('calls_summary as cs1')
            ->select('cs1.id', 'cs1.call_id', 'cs1.call_type', 'cs1.sub_type')
            ->whereRaw('cs1.id = (
        SELECT MAX(cs2.id)
        FROM calls_summary as cs2
        WHERE cs2.call_id = cs1.call_id
    )');

        $query = DB::table('interactions')
            ->joinSub($latestSummarySubquery, 'latest_summary', function ($join) {
                $join->on(DB::raw("SUBSTRING_INDEX(interactions.call_id, '@', 1)"), '=', 'latest_summary.call_id');
            })
            ->join('organizations', 'interactions.organization_id', '=', 'organizations.id')
            ->select([
                'interactions.call_id as transaction_id',
                'organizations.name as account',
                DB::raw("'Call' as type_call"),
                'latest_summary.call_type as call_reason',
                'latest_summary.sub_type as sub_call_reason',
                'interactions.arrival_time',
                'interactions.id as interaction_id',
            ])
            ->orderByDesc('interactions.arrival_time');

        // Apply role-based filtering
        if (Auth::user()->role == 4) {
            $query->where('interactions.user_id', Auth::id());
        }

        // Apply filters when filterApplied is true
        if ($this->filterApplied) {
            // Call ID filter
            if ($this->filter_callId) {
                $query->where('interactions.call_id', 'like', "%{$this->filter_callId}%");
            }

            // Account filter
            if ($this->filter_account && $this->filter_account !== 'All') {
                $query->where('organizations.name', 'like', "%{$this->filter_account}%");
            }

            // Time filter
            if ($this->filter_time_name == 'Custom' && $this->custom_date_from && $this->custom_date_to) {
                $custom_date_from = $this->custom_date_from . ' 00:00:00';
                $custom_date_to   = $this->custom_date_to . ' 23:59:59';
                $query->whereBetween('interactions.arrival_time', [$custom_date_from, $custom_date_to]);
            } elseif ($this->filter_time_days && $this->filter_time_name != 'Custom') {
                $startDate = now()->subDays($this->filter_time_days)->toDateString();
                $query->whereDate('interactions.arrival_time', '>=', $startDate);
            }
        }

        // Get paginated results
        $records = $query->paginate($this->perPage);

        return view('livewire.reports.transaction-reason', [
            'records'       => $records,
            'organizations' => Organization::orderBy('name')->get(),
        ]);
    }
}
