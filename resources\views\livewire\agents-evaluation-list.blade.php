<div class="container-fluid mt-3 px-4">
    {{-- header row  --}}
    {{-- <div class="row mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            Agent's Evaluations List
                        </b>
                    </h5>
                    <h6 class="text-muted">
                        View agents' evaluations and average score
                        <i class="fa-solid fa-list-check fa-2xl float-end" style="color: #00a34e"></i>
                    </h6>
                </div>
            </div>
        </div>
    </div> --}}


    {{-- bottom row  --}}
    {{-- <div class="row mx-3 d-flex ps-5">
        <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column">
            <div class="card rounded-3 bg-white shadow p-2">
                <div class="card-body py-3">
                    <h5 class="fw-bold">Filters</h5>
                    <hr>

                    <div class="col-md-12">
                        <form class="row g-2 mb-3" wire:submit.prevent>
                            <div class="col-md-4">
                                <label for="call id" class="mb-2 fw-bold">Event Date <i class="fa-solid fa-calendar-days fa-lg" style="color: #00a34e"></i></label>
                                <input style="font-size: 0.85rem; padding-top: 0.48rem !important;padding-bottom: 0.48rem !important;" type="date" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Full Name" wire:model='eventDate'>
                            </div>

                            <div class="col-md-4">
                                <label for="Duration" class="mb-2 fw-bold">Organization <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Skill Group"></i></label>
                                <div class="dropdown">
                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filtered_org ?? '--' }}</span>
                                    </button>
                                    <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton" style="height: 10rem; overflow:auto">
                                        @forelse ($orgs as $org)
                                            @if ($org->name && $org->name !== 'Ewa')
                                                <!-- Exclude "Ewa" organization -->
                                                <li>
                                                    <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterOrg('{{ $org->id }}')">
                                                        {{ $org->name === 'Ewa' ? 'Kease' : $org->name }}
                                                    </span>
                                                </li>
                                                @if (!$loop->last)
                                                    <hr class="m-0">
                                                @endif
                                            @endif
                                        @empty
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item text-muted">No Organizations Found</span>
                                            </li>
                                            @if (!$loop->last)
                                                <hr class="m-0">
                                            @endif
                                        @endforelse
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <label for="Duration" class="mb-2 fw-bold">User Group <i class="fa-solid fa-people-group fa-lg" style="color: #00a34e" title="User Group"></i></label>
                                <div class="dropdown">
                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filtered_group_name ?? '--' }}</span>
                                    </button>
                                    <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton" style="height: 10rem; overflow:auto">
                                        @forelse ($groups as $group)
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterUserGroup('{{ $group->id }}')">{{ $group->name }}</span>
                                            </li>
                                            @if (!$loop->last)
                                                <hr class="m-0">
                                            @endif
                                        @empty
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item text-muted">No Groups Found</span>
                                            </li>
                                        @endforelse
                                    </div>
                                </div>
                            </div>


                        </form>
                    </div>

                    <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply" wire:click="applyFilters">Apply</button>
                    <button class="btn btn-md btn-outline-success ms-1" id="clear" wire:click="clear">Clear</button>
                </div>
            </div>
        </div>
    </div> --}}

    <div class="header">
        <div class="row justify-content-between ms-2 ps-lg-5">
        <div class="col-2 mb-1 ">

         {{--        <input id="searchInput" type="text" class="form-control mb-1 text-muted p-1 rounded-2" placeholder="Search..." style="width: 15rem; background: url('{{ asset('assets/SVG/assets-v2/88.svg') }}') no-repeat left 0.5rem center; background-size: 1rem; padding-left: 2rem;" wire:model.live.debounce.300ms="searchCalls" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';"> --}}
                <div class="d-flex align-items-center ps-2 rounded-2 bg-color w-100 w-lg-auto">
                    <i class="fas fa-search me-2 color"></i>
                    <input type="text" class="rounded-2 form-control border-0 color shadow-none text-secondary"  wire:model.live.debounce.300ms="searchAgents" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';"  placeholder="Search...">
            </div>


        </div>

        {{-- <div class="col-auto mt-3 mt-sm-0 pe-3">
            <button data-bs-toggle="modal" data-bs-target="#filterModal" class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3" style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                <i class="fas fa-filter text-white me-2" style="font-size: 20px;"></i>
                <span style="font-size: 17px;">Filter</span>
            </button>
        </div> --}}

        <div class="col-auto mt-3 mt-sm-0">
            <button data-bs-toggle="modal" data-bs-target="#filterModal" class="btn btn-success d-flex flex-row justify-content-between align-items-center w-100 rounded-2" style="min-width: 8rem; height: 2.5rem; border-color: #01a44f; background: #01a44f;">
                <span style="font-size: 17px;">Filter</span>
                <img src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="filter">
            </button>
        </div>


    </div>
</div>


    <div class="parent-sections mx-3 ps-5" style="    width: 99%;">

        {{-- <div class="col-12 col-md-12 col-lg-12 border bg-white shadow mt-5 pt-3 rounded-3 px-3" style="letter-spacing: 1px;" wire:key="sss"> --}}
        {{-- big table div --}}
        {{-- <div class="row px-4"> --}}

        {{-- <div class="d-flex mb-1">
                <label for="" class="col-md-1 align-self-center me-1 fs-6" style="width: fit-content">Search: </label>
                <input id="searchInput" type="text" class="col-md-1 form-control mb-1 d-inline text-muted p-1" placeholder=' &#xF002;' style="font-family:Arial, FontAwesome;width:9rem" wire:model.live.debounce.300ms="searchAgents" onclick="this.placeholder=''" onblur="this.placeholder='&#xF002;'">
            </div> --}}

        {{-- table  --}}
        <div class="table-responsive px-0 rounded-2" style="height:fit-content; max-height:90vh; overflow-y:auto">
            <table class="table table-hover table-striped overflow-auto mb-0" id="table" style="width:100%;">
                <thead id="thead" class="thead text-muted" style="font-size: 0.7rem;position: sticky; top: 0; z-index: 4;height: 1rem !important;">
                    <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                        {{-- <th scope="col" class="text-center align-middle">ID</th> --}}
                        <th scope="col" style="cursor:pointer;padding: 14px !important;"  class="text-center align-middle" wire:click="setSortBy('full_name')">
                            User Name
                            @if ($sortBy !== 'full_name')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                            @endif
                        </th>
                        <th scope="col" class="text-center align-middle">User ID</th>
                        <th scope="col" style="cursor:pointer;padding: 11px !important;" class="text-center align-middle" wire:click="setSortBy('organization_id')">
                            Organization
                            @if ($sortBy !== 'organization_id')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                            @endif
                        </th>
                        <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('user_group_id')">
                            Group
                            @if ($sortBy !== 'user_group_id')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                            @endif
                        </th>
                        <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('full_name')">
                            No. of Evaluations
                            @if ($sortBy !== 'full_name')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                            @endif
                        </th>
                        <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('full_name')">
                            AVG. Evaluations Score
                            @if ($sortBy !== 'full_name')
                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                            @elseif ($sortDir === 'ASC')
                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                            @else
                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                            @endif
                        </th>

                    </tr>
                </thead>
                <tbody class="" style="font-size:0.8rem" id="tbody">

                    @forelse($agents as $user)
                        <tr class="align-middle">
                            {{-- <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td> --}}
                            <td class="text-muted text-center py-3 align-middle" style="min-width: 20rem"> {{ $user->full_name }} </td>
                            <td class="text-muted text-center py-3 align-middle" style="width: 20rem"> {{ $user->agent_id }} </td>
                            {{-- <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $user?->organization?->name ?? '-' }} </td> --}}
                            <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $user?->organization?->name === 'Ewa' ? 'Kease' : $user?->organization?->name ?? '-' }}
                            </td>
                            <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $user?->userGroup?->name ?? '-' }} </td>
                            <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                @php

                                    $agentEvaluations = $user
                                        ->agentEvaluation()
                                        ->where('created_at', 'like', '%' . $eventDate . '%')
                                        ->whereHas('Interaction.organization', function ($q) use ($user) {
                                            $q->where('id', $user->organization_id);
                                        })
                                        ->count();
                                @endphp
                                {{ $agentEvaluations }}
                            </td>
                            <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                @php
                                    $qualityPercentages = $user
                                        ->agentEvaluation()
                                        ->where('created_at', 'like', '%' . $eventDate . '%')
                                        ->whereHas('Interaction.organization', function ($q) use ($user) {
                                            $q->where('id', $user->organization_id);
                                        })
                                        ->pluck('quality_percentage');

                                    $averageQualityPercentage = round($qualityPercentages->avg(), 2);

                                @endphp
                                {{ isset($averageQualityPercentage) ? $averageQualityPercentage . '%' : '-' }}
                            </td>

                        </tr>
                    @empty
                        <tr>
                            <td colspan="18" class="text-muted text-center bg-white"> No users found</td>
                        </tr>
                    @endforelse

                </tbody>
            </table>
        </div>

        <div class="d-flex justify-content-between mt-3">
            <!-- Dropdown for Number of Items per Page -->
            <div>
                <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                    <option value="10">10</option>
                    <option value="15" selected>15</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>Results Per Page</span>
            </div>

            <!-- Pagination Links -->
            <div>
                {{ $agents->links(data: ['scrollTo' => false]) }}
            </div>
        </div>

        {{-- <div class=" mt-2 pe-0">
            {{ $agents->links(data: ['scrollTo' => false]) }}
        </div> --}}
        {{-- </div> --}}

        {{-- </div> --}}
    </div>


    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white;">
                <!-- Modal Header -->
                <div class="modal-header" style="border: none;">
                    {{-- <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filters</h4>
                    </div> --}}
                    <div class="d-flex align-items-center">
                        <!-- Icon Container -->
                        <div class="rounded-circle d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color: #eff3f4;">
                            <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">
                        </div>
                        <!-- Title -->
                        <h4 class="modal-title" id="filterModalLabel" style="font-size: 30px; color: #333;">
                            Filters
                        </h4>
                    </div>

                    <button type="button" class="border-0 bg-transparent" id="closeModal" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="modal-body" style="border: none;">
                    <div class="col-md-12">
                        <form class="row g-2 mb-3">
                            <!-- Event Date -->
                            <div class="col-md-6">
                                <label for="call id" class="mb-2" style="color: #40798C;">Event Date <i class="fa-solid fa-calendar-days fa-lg" style="color: #00a34e"></i></label>
                                <input style="color:#6c757d; background-color:#eff3f4 !important; border:none !important; height:2.5rem;" type="date" class="form-control" wire:model='eventDate'>
                            </div>

                            <!-- User Name -->
                            @if (Auth::user()->role != 4)
                                <div class="col-md-6">
                                    <label for="call id" class="mb-2" style="color: #40798C;">User Name <i class="fa-solid fa-user fa-lg" style="color: #00a34e"></i></label>
                                    <input style="color:#6c757d; background-color:#eff3f4 !important; border:none !important; height:2.5rem;" type="text" class="form-control" wire:model='userName'>
                                </div>
                            @endif

                            <!-- Organization -->
                            <!-- Organization -->
                            <div class="col-md-6">
                                <label for="organization" class="mb-2" style="color: #40798C;">
                                    Organization <i class="fa-solid fa-building fa-lg" style="color: #00a34e"></i>
                                </label>
                                <select id="organization" wire:change="filterOrg($event.target.value)" class="form-select form-select-sm" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:2.5rem;">
                                    <option value="">--</option>
                                    @foreach ($orgs as $org)
                                        @if ($org->name && $org->name !== 'Ewa')
                                            <option value="{{ $org->id }}">
                                                {{ $org->name === 'Ewa' ? 'Kease' : $org->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>

                            <!-- User Group -->
                            <div class="col-md-6">
                                <label for="user_group" class="mb-2" style="color: #40798C;">
                                    User Group <i class="fa-solid fa-people-group fa-lg" style="color: #00a34e"></i>
                                </label>
                                <select id="user_group" wire:change="filterUserGroup($event.target.value)" class="form-select form-select-sm" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:2.5rem;">
                                    <option value="">--</option>
                                    @foreach ($groups as $group)
                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                        </form>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="modal-footer" style="border: none;">
                    <button type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" wire:click="clear" wire:loading.attr="disabled" wire:target="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear" role="status" aria-hidden="true"></span>
                    </button>
                    <button class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="applyFilters" wire:loading.attr="disabled" wire:target="applyFilters">
                        <span wire:loading.remove wire:target="applyFilters" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="applyFilters" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>


        <!-- Add Evaluation Modal -->
        <div class="modal fade" id="add-evaluation" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header text-white" style="background-color: #00a34e">
                        <h5 class="modal-title" id="staticBackdropLabel">Add Evaluation</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                    </div>

                    <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                        <form>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Organization Name:</label>
                                <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                            </div>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Parent Organization:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        @if ($role)
                                            @if ($role == 1)
                                                Admin
                                            @elseif ($role == 2)
                                                Supervisor
                                            @elseif ($role == 3)
                                                IT
                                            @else
                                                Agent
                                            @endif
                                        @else
                                            {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                        @endif
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">User Groups:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        @if ($role)
                                            @if ($role == 1)
                                                Admin
                                            @elseif ($role == 2)
                                                Supervisor
                                            @elseif ($role == 3)
                                                IT
                                            @else
                                                Agent
                                            @endif
                                        @else
                                            {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                        @endif
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Administrators:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        @if ($role)
                                            @if ($role == 1)
                                                Admin
                                            @elseif ($role == 2)
                                                Supervisor
                                            @elseif ($role == 3)
                                                IT
                                            @else
                                                Agent
                                            @endif
                                        @else
                                            {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                        @endif
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Description:</label>
                                <textarea type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName"></textarea>
                            </div>

                            {{-- <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Organization:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>

                        @php
                            $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                        @endphp
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Supervisors:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="searchSupervisors" autocomplete="off">
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Agents:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName">
                            @php
                                $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                            @endphp
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div> --}}




                        </form>
                    </div>


                    <div class="modal-footer bg-white">
                        <button type="button" class="btn btn-primary" style="background-color: #00a34e" wire:click="editUser">Apply</button>
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" wire:click="clear" id="close">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div wire:loading class="loading-overlay">
        <div class="spinner"></div>
    </div>
