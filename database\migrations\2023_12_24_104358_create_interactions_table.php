<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interactions', function (Blueprint $table) {
            // $table->id();
            // $table->string('call_id')->unique();
            // $table->string('customer_number')->nullable();
            // $table->string('agent_extension')->nullable();
            // $table->string('routing_point')->nullable();
            // $table->string('agent_ip')->nullable();
            // $table->string('message_type')->nullable();
            // $table->datetime('arrival_time')->nullable();
            // $table->unsignedBigInteger('user_id');
            // $table->foreign('user_id')->references('id')->on('users')->onDelete(null);
            // $table->timestamps();
            
            $table->id();
            $table->string('call_id')->unique();
            $table->unsignedBigInteger('user_id')->nullable(); // agent info
            $table->string('caller_id')->nullable();
            $table->string('called_id')->nullable();
            $table->string('call_type')->nullable();
            $table->string('call_ender')->nullable();
            $table->string('agent_extension')->nullable();
            $table->time('call_duration')->nullable();
            $table->time('hold_duration')->nullable();
            $table->integer('hold_count')->nullable();
            $table->integer('pause_count')->nullable();
            $table->string('agent_ip')->nullable();
            $table->string('language')->nullable();
            $table->datetime('arrival_time')->nullable();
            $table->string('Genesys_CallUUID')->nullable();

            // Agent foreign key
            $table->foreign('user_id')->references('id')->on('users')->onDelete(null);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interactions');
    }
};
