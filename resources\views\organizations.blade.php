@extends('layouts.app')

@section('title', 'Organizations')

@section('style')
    <style>
        thead th {
            background-color: #40798C !important;
            color: #FFFFFF !important;
            font-size: medium;
        }

        tbody td {
            font-size: larger !important;
            height: 4.5rem;
            font-weight: 600;
        }

        body {
            background-color: #FFFFFF !important;
        }

        #searchInput {
            height: 2.8rem !important;
            width: 100% !important;
            /* Increase the height for a larger input */
            padding-left: 2.5rem !important;
            /* Increase padding for better spacing */
            border: none !important;
            /* Slightly darker border */
            border-radius: 0.5rem;
            /* Rounded corners */
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow */
            transition: box-shadow 0.3s ease, border-color 0.3s ease;
            /* Smooth transition */
            font-size: 1.2rem;
            /* Slightly larger text size */
            background-position: left 0.5rem center;
            /* Icon positioning */
        }

        #searchInput:focus {
            outline: none;
            /* Remove default outline */
            box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
            /* Larger shadow on focus */
            border-color: rgba(0, 0, 0, 0.3);
            /* Slightly darker border on focus */
        }

        /* Placeholder styling */
        #searchInput::placeholder {
            font-family: inherit;
            /* Use inherited font style */
            color: #01A44F;
            /* Green placeholder text */
            font-size: 1.2rem;
            /* Match placeholder size with input text */
        }

        .main-buttons-container button {
            height: 2.3rem;
            font-size: 14px;
        }

        .main-buttons-container button:hover {
            background-color: #018F3E !important;
        }

        /* pagination  */
        ul.pagination {
            gap: 0;
        }

        ul.pagination li button,
        ul.pagination li span {
            padding: 0.7rem;
            padding-top: 0.4rem;
            padding-bottom: 0.4rem;
        }

        ul.pagination li button:hover {
            background-color: rgb(196, 183, 183) !important;
        }

        ul.pagination>li>button,
        ul.pagination>li>span {
            color: black !important;
            font-weight: 600 !important;
            background-color: white;
        }

        .page-item span,
        .page-item button {
            border-radius: 0.7rem !important;
        }

        .page-item.active span,
        .page-item.active button {
            border-radius: 0.5rem !important;
        }

        .page-item.active>span {
            background-color: #00a34e !important;
            color: white !important;
        }

        div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
            font-size: 0.9rem;
        }

        div.tab-pane label {
            font-weight: 600 !important;
        }

        div.tab-pane hr {
            display: none;
        }

        /* modal  */
        #add-multi .modal-header {
            background-color: transparent !important;
        }

        .modal-title {
            color: black;
            font-weight: 600;
        }

        legend {
            color: #40798C !important;
        }

        .tab-button {
            border: none !important;
            background-color: white !important;
            color: black !important;
            font-weight: 500;
            border: 1px solid rgb(235, 226, 226) !important;
            border-radius: 3rem !important;
        }

        .tab-button.active {
            color: #00a34e !important;
        }

        .modal-content,
        .modal-header {
            background-color: white !important;
        }

        .modal-header {
            border: none !important;
        }

        .modal-close {
            height: 40px !important;
            border-color: #eff3f4 !important;
            background: #eff3f4 !important;
            color: #6c97a6 !important;
        }

        .modal-apply {
            height: 40px !important;
            border-color: #01a44f !important;
        }

        .modal-apply-close-btns {
            width: 10rem;
        }

        .modal-footer {
            border: none !important;
        }

        /* input {
                                                                    border: solid 1px #b6b6b6 !important;
                                                                    border-radius: 0.6rem !important;
                                                                    background-color: white !important;
                                                                    padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
                                                                } */
        .dropdown-menu::-webkit-scrollbar {
            display: none !important;
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 1rem !important;
        }

        .page-link[aria-label="« Previous"],
        .page-link[aria-label="Next »"] {
            padding: 0.8rem !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        input {
            border: solid 1px #b6b6b6 !important;
            /* border-radius: 0.6rem !important; */
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .list-group-item:hover {
            color: white;
            background-color: #00a34e;
        }

        th {
            text-wrap: nowrap;
        }

        .dropdown-menu.w-100.show {
            transform: translate3d(0px, 39.2px, 0px) !important;
        }

        /* sub table  */
        .sub-table-tr-head th,
        .sub-table-tr-body td {
            /* background-color: #FFFFFF !important;
                    padding-top: 1rem !important; */
            /* background-color: white !important; */
        }

        #subTableTd {
            background-color: #f2f2f2 !important;
        }

        #subTableTd.odd-row {
            background-color: #f2f2f2 !important;
            /* Light gray */
        }

        #subTableTd.even-row {
            background-color: #ffffff !important;
            /* White */
        }

        /* sub table  */
        .sub-table {
            border-collapse: collapse;
            padding: 2rem;
            /* border: solid 1px transparent; */
            border-radius: 1rem;
            overflow: hidden;
        }

        .sub-table td,
        .sub-table th {
            border-left: none !important;
            border-right: none !important;
            font-weight: 300 !important;
            font-size: 0.9rem !important;
            border-bottom: 1px solid #d1d5db !important;
        }

        .sub-table tr {
            border: 1px solid transparent !important;
        }

        .sub-table th {
            color: #40798C;
            font-weight: 800 !important;
            background-color: white !important;
        }

        .sub-table tr:last-child td {
            border-bottom: none;
        }

        tr+tr~td {
            background-color: initial !important;
        }

        .no-border {
            border-bottom: none !important;
        }

        #table {
            border: none !important;
        }

        #table td {
            border: none;
        }

        #table td, #table th {
            font-size: small !important;
        }

        .form-control,
        .form-select,
        .dropdown-toggle-style {
            background-color: #eff3f4 !important;
            color: #40798c !important;
            border: none !important;
            height: 40px;
        }
        .color {
            color: #40798c !important;
        }
        .bg-color {
            background-color: #eff3f4 !important;
        }
    </style>
@endsection


@section('content')
    @livewire('organizations')
    <script>
        window.addEventListener('closeModal', () => {
            // document.querySelector('#close').click();
            document.querySelector('#closeEdit').click();
            document.querySelector('#closeAdd').click();
            document.querySelector('#close').click();
            document.querySelector('#close-skill').click();
            document.querySelector('#closeAddMulti').click();
        });

        // document.addEventListener('click', (event) => {
        //     // Check if the clicked element is a <td> within a table row
        //     const clickedTd = event.target.closest('td');
        //     const clickedRow = clickedTd?.parentElement;

        //     if (clickedRow && clickedRow.tagName === 'TR' && clickedRow.hasAttribute('data-row-id')) {
        //         // Determine if the clicked row is odd or even
        //         const rowIndex = Array.from(clickedRow.parentNode.children).indexOf(clickedRow);
        //         const isOddRow = rowIndex % 2 === 0;

        //         // Define the background color based on odd/even row
        //         const bgColor = isOddRow ? '#f2f2f2' : '#ffffff';

        //         // Find the submenu row (next row in the table)
        //         const subTableRow = clickedRow.nextElementSibling;

        //         // Check if the submenu row exists and has a table
        //         if (subTableRow && subTableRow.querySelector('table')) {
        //             // Apply the background color to all <td> elements inside the submenu row
        //             Array.from(subTableRow.querySelectorAll('td')).forEach(td => {
        //                 td.style.setProperty('background-color', bgColor, 'important'); // Enforce the style
        //             });
        //         }
        //     }
        // });
    </script>
@endsection
