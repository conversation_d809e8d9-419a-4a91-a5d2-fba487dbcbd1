<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transcription_bad_words', function (Blueprint $table) {
            $table->id();
            $table->string('bad_word');
            $table->json('words_detected');
            $table->string('call_id');
            $table->integer('calls_transcription_id');
            $table->foreign('calls_transcription_id')
                ->references('id')->on('calls_transcription')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transcription_bad_words');
    }
};
