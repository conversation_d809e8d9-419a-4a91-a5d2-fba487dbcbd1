@props(['label', 'prec', 'plate'])
<div class="row">
    <div class="col-4  details-text-container">
        <h6 class="fw-bold">{{$label}}</h6>
    </div>
    <div class="col ">

<div class="progress">
    <div class="progress-bar" role="progressbar" style="width: {{$prec}}%; background-color:{{$plate}}" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>

  </div>
  <p class="progress-bar-text">{{$prec}}%</p>
    </div>
</div>

<style>
@keyframes fillProgressBar {
    from {
        width: 0;
    }
    to {
        width: {{$prec}}; /* The final width will be overridden by inline style */
    }
}
.progress-bar {
    border-radius: 1rem;
    animation: fillProgressBar 2s ease-in-out forwards;
}

.progress-bar-text {
    display: inline-block;
}
</style>
