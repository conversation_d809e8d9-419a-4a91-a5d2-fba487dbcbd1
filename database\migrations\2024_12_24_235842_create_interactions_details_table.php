<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interactions_details', function (Blueprint $table) {
            $table->id();
            $table->string('call_id');
            $table->foreign('call_id')
            ->references('call_id')->on('interactions')
            ->onDelete('cascade');
            $table->double('engage_duration')->default(0);
            $table->double('left_channel_duration')->default(0);
            $table->double('right_channel_duration')->default(0);
            $table->double('silence_duration')->default(0);
            $table->double('overtalk_duration')->default(0);
            $table->double('other_duration')->default(0);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interactions_details');
    }
};
