<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('video_captures', function (Blueprint $table) {
            $table->id();
            $table->string('file')->nullable();
            $table->string('timecode')->nullable();
            $table->string('status');
            $table->string('ip');
            $table->string('call_id')->nullable();
            $table->foreignId('interaction_id')->nullable()->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('video_captures');
    }
};
