<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class CheckAudioExists implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $callId;

    /**
     * Create a new job instance.
     *
     * @param  string  $callId
     * @return void
     */
    public function __construct($callId)
    {
        $this->callId = $callId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Check if the call_id already has the suffix @10.202.1.66....
        $callId = $this->callId;
        if (!str_ends_with($callId, '@10.202.1.66')) {
            $callId .= '@10.202.1.66';
        }

        $fileName = "{$callId}.wav";
        $filePath = "audio/{$fileName}";
        
        // Check if the file exists
        if (!file_exists($filePath)) {
            
            // If the file does not exist, download it
            $response = Http::withoutVerifying()->timeout(120)->get('https://oms.extwebonline.com/Extensya_APIs/recording/downloadRecording.php', [
                'call_id' => $callId,
            ]);

            // Check if the request was successful
            if ($response->successful()) {
                // Save the file content to storage/app/audio directory
                Storage::disk('public')->put($filePath, $response->body());
            } else {
         
                // Handle the case where the request was not successful
                return "Failed to download the file: " . $response->status();
            
            }
        }
        
    }
}
