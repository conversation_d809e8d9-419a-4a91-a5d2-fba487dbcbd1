@php
    // Cache all user permissions and parameters
    $user = Auth::user();
    $userRole = $user->role;
    $userPermissions = $user->permissions()->pluck('permission_id')->toArray();
    $userParameters = $user->parameters()->pluck('parameter_id')->toArray();



    // Pre-fetch any other relationships you check frequently
    $recordsWithAiFlags = $records->whereNotNull('ai_flag')->where('ai_flag', 1)->pluck('id')->toArray();
    $recordsWithTopicsFlags = $records->whereNotNull('topics_flag')->where('topics_flag', 1)->pluck('id')->toArray();
@endphp



<div class="container-fluid mx-3 mt-3 px-4">

    @if (session('alert_no_call'))
        <div class="row  ps-5">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                {{ session('alert_no_call') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-between me-3 ps-lg-5">
            {{-- <div class="col-auto">
            <button
                wire:target="export"
                wire:click="export"
                title="Export"
                class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                wire:loading.attr="disabled">

                <i
                    wire:loading.remove
                    wire:target="export"
                    class="fas fa-file-excel text-white me-2"
                    style="font-size: 20px;"></i>

                <span
                    wire:loading.class="spinner-border spinner-border-sm"
                    wire:target="export"
                    style="width: 1rem; height: 1rem;"
                    role="status"
                    aria-hidden="true"></span>

                <span
                    wire:loading.remove
                    wire:target="export"
                    style="font-size: 17px;">Extract Excel</span>
            </button>
        </div> --}}

            <div class="col-3 mb-1 d-flex ">

                {{--                 <div class="d-flex mb-1">
                    <input id="searchInput" type="text" class="form-control mb-1 text-muted p-1 rounded-2" placeholder="Search..." style="width: 15rem; background: url('{{ asset('assets/SVG/assets-v2/88.svg') }}') no-repeat left 0.5rem center; background-size: 1rem; padding-left: 2rem;" wire:model.live.debounce.300ms="searchCalls" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';">
                </div> --}}
                <div class="d-flex align-items-center ps-2 rounded-2 bg-color w-100 w-lg-auto">
                    <i class="fas fa-search me-2 color"></i>
                    <input type="text" class="rounded-2 form-control border-0 color shadow-none text-secondary" wire:model.live.debounce.300ms="searchCalls" onfocus="this.placeholder='';" onblur="this.placeholder='Search...';" placeholder="Search...">
                </div>
                <label class="d-inline ms-3 w-75 align-content-around text-muted fw-bold" style="white-space: nowrap;"><input type="checkbox" name="showZero" id="" checked wire:click="toggleZero()"> Hide  0 duration</label>

                <label class="d-inline ms-3 w-75 align-content-around text-muted fw-bold" style="white-space: nowrap;"><input type="checkbox" name="showTransc" id=""  wire:click="toggleTranscribed()"> Transcribed</label>

            </div>

            <div class="col-auto mt-3 mt-sm-0 pe-0">

                <button data-bs-toggle="modal" data-bs-target="#filterModal" class="btn btn-success d-flex flex-row justify-content-between align-items-center w-100 rounded-2" style="min-width: 8rem; height: 2.5rem; border-color: #01a44f; background: #01a44f;">
                    {{-- <i class="fas fa-filter text-white me-2" style="font-size: 20px;"></i> --}}
                    <span style="font-size: 17px;">Filter</span>
                    <img src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="filter">
                </button>


            </div>

        </div>
    </div>


    {{-- <div class="d-flex mb-1">
        <label for="" class="col-md-1 align-self-center me-1 fs-6" style="width: fit-content">Search Anything: </label>
        <input id="searchInput" type="text" class="col-md-1 form-control mb-1 d-inline text-muted p-1" placeholder=' &#xF002;' style="font-family:Arial, FontAwesome;width:9rem" wire:model.live.debounce.300ms="searchCalls" onclick="this.placeholder=''" onblur="this.placeholder='&#xF002;'">
    </div> --}}
    {{-- table  --}}
    <div class="parent-sections  ps-5 pe-3">

        <div class="section-one">
            <div class="div-table rounded-2 shadow-sm mb-3">

                {{-- <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                    <thead id="thead" class="thead text-muted" style="font-size: 0.7rem"> --}}
                <table class="table table-hover table-striped " id="table" style="margin-bottom: 0px; border-radius: 0px;    ">
                    <thead id="thead" class="thead text-muted" style="font-size: 0.7rem;position: sticky; top: 0; z-index: 4;height: 1rem !important;">
                        <tr class="text-muted" style="vertical-align:middle; background-color:#40788b !important">

                            {{-- @if (Auth::user()->role != 4)
                                <th scope="col" class="text-center fixed-col" style="width: 4rem">
                                </th>
                            @endif --}}

                            {{-- @if (Auth::user()->parameters()->where('parameter_id', 8)->exists())
                                <th scope="col" class="text-center fixed-col" style="width: 4rem"> --}}
                            {{-- <img src="{{ asset('assets/images/calltype.png') }}" alt="" srcset="" style="width:1.2rem; height:1.2rem" title="Call Type"> --}}
                            {{-- </th>
                            @endif --}}


                            {{-- @if (Auth::user()->parameters()->where('parameter_id', 33)->exists()) --}}
                            @if (in_array(33, $userParameters))
                            <th scope="col" class="text-center first-th0" data-order="desc" data-default="true">
                                AI Flags
                            </th>
                            @endif

                            <th scope="col" class="text-center first-th1" data-order="desc" data-default="true">
                                QA Flags
                            </th>

                            <th scope="col" class="text-center first-th" style="cursor: pointer;" onclick="sortTable(this)" data-order="desc" data-default="true">
                                Date-Time
                                <i class="fa-solid fa-sort text-white d-none"></i>
                                <i class="fa-solid fa-sort-up text-white d-none"></i>
                                <i class="fa-solid fa-sort-down text-white"></i>
                            </th>

                            <th scope="col" class="text-center first-th2" style="left: 427px;">
                                Actions
                                {{-- <i class="fa-solid fa-link fa-xl" title="Action" style="color: white"></i> --}}
                            </th>
                            {{-- @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                <th scope="col" class="text-center"><i class="fa-solid fa-microphone-lines fa-xl" title="Record" style="color: white"></i></th>
                            @endif
                            @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                <th scope="col" class="text-center"><i class="fas fa-video fa-xl" title="Record_video" style="color: white"></i></th>
                            @endif --}}

                            {{-- @if (Auth::user()->role != 4)
                                <th scope="col" class="text-center"><i class="fa-solid fa-headphones fa-xl" title="Played By" style="color: white"></i></th>
                            @endif --}}
                            {{-- @if (Auth::user()->parameters()->where('parameter_id', 27)->exists())
                                <th scope="col" class="text-center"><i class="fa-solid fa-comment-dots fa-xl" style="color: white" title="Comments"></i></th>
                            @endif --}}
                            {{-- @if (Auth::user()->parameters()->where('parameter_id', 22)->exists())
                                <th scope="col" class="text-center"><i class="fa-solid fa-newspaper fa-xl" style="color: white" title="Evaluation"></i></th>
                            @endif --}}
                            @if (in_array(25, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Agent Name
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif



                            {{-- <th scope="col" class="text-center"><i class="fa-solid fa-user-tie fa-xl" style="color: white" title="Evaluator"></i></th> --}}
                            {{-- <th scope="col" class="text-center"><i class="fa-solid fa-rectangle-list fa-xl" style="color: white" title="Evaluation Form"></i></th> --}}
                            <th scope="col" class="text-center fixed-col">
                                User ID
                                {{-- <i class="fa-solid fa-hashtag fa-xl" style="color: white" title="Ops ID"></i> --}}
                            </th>
                            @if (in_array(4, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Called ID
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif

                            @if (in_array(6, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Caller ID
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif

                            @if (in_array(31, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Account
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif
                            @if (in_array(29, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Group
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif
                            @if (in_array(5, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Call Ender
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif

                            {{-- @if (Auth::user()->parameters()->where('parameter_id', 23)->exists())
                                <th scope="col" class="text-center"><i class="fa-solid fa-flag fa-xl" style="color: white" title="Qa Flags"></i></th>
                            @endif --}}
                            @if (in_array(2, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Duration
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif


                            {{-- @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                <th scope="col" class="text-center"><i class="fa-solid fa-microphone-lines fa-xl" title="Record" style="color: white"></i></th>
                            @endif --}}


                            @if (in_array(19, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Evaluation Score
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif

                            <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                Extension
                                <i class="fa-solid fa-sort text-white"></i>
                                <i class="fa-solid fa-sort-up text-white d-none"></i>
                                <i class="fa-solid fa-sort-down text-white d-none"></i>
                            </th>



                            @if (in_array(7, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Hold Duration
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif


                            @if (in_array(11, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Hold Count
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif



                            @if (in_array(13, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Ring
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif

                            <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                Language
                                <i class="fa-solid fa-sort text-white"></i>
                                <i class="fa-solid fa-sort-up text-white d-none"></i>
                                <i class="fa-solid fa-sort-down text-white d-none"></i>
                            </th>

                            @if (in_array(1, $userParameters))
                                <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                    Call ID
                                    <i class="fa-solid fa-sort text-white"></i>
                                    <i class="fa-solid fa-sort-up text-white d-none"></i>
                                    <i class="fa-solid fa-sort-down text-white d-none"></i>
                                </th>
                            @endif

                            <th scope="col" class="text-center fixed-col" style="cursor: pointer;" onclick="sortTable(this)" data-order="none">
                                Unique ID
                                <i class="fa-solid fa-sort text-white"></i>
                                <i class="fa-solid fa-sort-up text-white d-none"></i>
                                <i class="fa-solid fa-sort-down text-white d-none"></i>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="" style="font-size:0.8rem" id="tbody">
                        @forelse($records as $record)
                            <tr class="align-middle">

                                {{-- flags  --}}
                                {{-- @if (Auth::user()->role != 4)
                                    <td class="text-muted text-center py-3 text-nowrap align-middle fixed-col" style="width:15rem; padding:2rem;">
                                        <div style="width: 1rem; height: 1rem; display: inline-block;">
                                            @if ($record->qaFlags()->exists())
                                                <img wire:click="showDeleteFlagAlert('{{ $record->id }}')" src="{{ asset('assets/SVG/assets-v2/square red.svg') }}" style="width: 100%; height: 100%; object-fit: contain; cursor: pointer;" alt="">
                                            @endif
                                        </div>
                                    </td>
                                @endif --}}

                                {{-- @if (Auth::user()->role != 4)
                                    <td class="text-muted text-center py-3 text-nowrap align-middle fixed-col" style="width:15rem; padding:2rem;">
                                        <div style="width: 1rem; height: 1rem; display: inline-block;">
                                            @if ($record->qaFlags()->exists())
                                                @if (Auth::user()->role == 2)
                                                    <!-- If the user's role is 2, allow deletion and show the cursor pointer -->
                                                    <img wire:click="showDeleteFlagAlert('{{ $record->id }}')" src="{{ asset('assets/SVG/assets-v2/square red.svg') }}" style="width: 100%; height: 100%; object-fit: contain; cursor: pointer;" title="Clear Flag" alt="Flag">
                                                @else
                                                    <!-- If the user's role is not 2, show only the flag without cursor pointer -->
                                                    <img src="{{ asset('assets/SVG/assets-v2/square red.svg') }}" style="width: 100%; height: 100%; object-fit: contain;" alt="Flag">
                                                @endif
                                            @endif
                                        </div>
                                    </td>
                                @endif --}}



                                {{-- call type  --}}
                                {{-- @if (Auth::user()->parameters()->where('parameter_id', 8)->exists())
                                    @if ($record->call_type == 'Inbound')
                                        <td class="text-muted text-center py-3 text-nowrap align-middle fixed-col" style="width:15rem">
                                            <div style="width: 2.5rem; height: 2.5rem; background-color: #D7E5FF; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
                                                <img src="{{ asset('assets/SVG/assets-v2/Icon (Traced).svg') }}" alt="" style="width: 100%; height: 100%; object-fit: scale-down; border-radius: 50%; padding:8px">
                                            </div>
                                        </td>
                                    @elseif ($record->call_type == 'Outbound')
                                        <td class="text-muted text-center py-3 text-nowrap align-middle fixed-col" style="width:15rem">
                                            <div style="width: 2.5rem; height: 2.5rem; background-color: #FFF0D7; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
                                                <img src="{{ asset('assets/SVG/assets-v2/Icon (Traced)-1.svg') }}" alt="" style="width: 100%; height: 100%; object-fit: scale-down; border-radius: 0%; padding:8px">
                                            </div>
                                        </td>
                                    @else
                                        <td class="text-muted text-center py-3 text-nowrap align-middle fixed-col" style="width:15rem">-</td>
                                    @endif
                                @endif --}}


                                {{-- date time  --}}
                                @php
                                    $superImage = 'super_off';
                                    $aiImage = 'ai_off';
                                    if ($record->ai_flag) {
                                        $aiImage = 'ai_on';
                                    }
                                    if ($record->qa_flags_count > 0) {
                                        $superImage = 'super_on';
                                    }
                                @endphp


                                    @if(in_array(33, $userParameters))
                                <td class="text-muted text-center  text-nowrap align-middle fixed-col0" style="padding: 0 10px !important;;">
                                    <div
                                        style="
                                            display: flex;
                                            align-content: flex-start;
                                            align-items: center;
                                            flex-wrap: wrap;
                                            flex-direction: column;
                                                justify-content: space-between;
                                            align-items: flex-start;">


                                        @if ($record->ai_flag && $record->topics_flag  )
                                            <img src="{{ asset('assets/images/two_ai_flags.svg') }}" style="width: 30px;margin-top: -9px;height: 100%;object-fit: contain;margin-left: 9px;" title="AI Flag" alt="AI Flag">
                                        @elseif ($record->topics_flag)
                                            <img src="{{ asset('assets/images/bad_word_flag.svg') }}" style="width: 16px;height: 100%;object-fit: contain;margin-left: 17px;margin-bottom: 5px;" title="Bad Word Flag" alt="Bad Word Flag">
                                        @elseif ($record->ai_flag)
                                            <img src="{{ asset('assets/images/negative_flag.svg') }}" style="width: 16px;height: 100%;object-fit: contain;margin-left: 17px;margin-bottom: 5px;" title="Classification Flag" alt="Classification Flag">
                                        @endif

                                    </div>

                                </td>

                                @endif

                                <td class="text-muted text-center  text-nowrap align-middle fixed-col1" style="padding: 0 10px !important;;">
                                    <div
                                        style="
                                                display: flex
                                            ;
                                                align-content: flex-start;
                                                align-items: center;
                                                flex-wrap: wrap;
                                                flex-direction: column;
                                                    justify-content: space-between;
                                                align-items: flex-start;">



                                        @if ($record->qa_flags_count > 0)

                                            <img src="{{ asset('assets/images/qa_flag.svg') }}" style="width: 15px;height: 5%;object-fit: contain;margin-left: 20px;" alt="Flag">
                                        @endif

                                    </div>


                                </td>

                                {{-- <td class="text-muted text-center  text-nowrap align-middle fixed-col" style="width:16rem; padding-right:0.7rem; border-right: solid 2px #C2C2C2;display: flex;flex-direction: row;justify-content: space-around;"> --}}

                                {{-- <td class="text-muted text-center  text-nowrap align-middle fixed-col1" style="padding: 0 10px !important;;">
                                    <div
                                        style="
                                                display: flex
                                            ;
                                                align-content: flex-start;
                                                align-items: center;
                                                flex-wrap: wrap;
                                                flex-direction: column;
                                                    justify-content: space-between;
                                                align-items: flex-start;">



                                           @if (in_array($record->id, $recordsWithQaFlags))

                                           <img src="{{ asset('assets/images/negative_flag.svg') }}"
                                               style="width: 25px;height: 5%;object-fit: contain;margin-left: 20px;"
                                               alt="Flag" title="QA Flag">
                                   @endif

                               </div> --}}


                                {{-- </td> --}}

                                <td class="text-muted text-center text-nowrap align-middle fixed-col"
                                    style="width:16rem; background-color: white !important;">
                                    <div style="display: flex; flex-direction: row; align-items: center;">
                                        @if ($record->call_type == 'Inbound')
                                            <div style="margin-right: 0.5rem; width: 2.5rem; height: 2.5rem; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
                                                <img src="{{ asset('assets/SVG/assets-v2/in_icon.svg') }}" alt="" style="border-radius: 50%;padding: 5px 0 0;width: 33px;">
                                            </div>
                                        @elseif ($record->call_type == 'Outbound')
                                            <div style="margin-right: 0.5rem; width: 2.5rem; height: 2.5rem; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
                                                <img src="{{ asset('assets/SVG/assets-v2/out_icnon.svg') }}" alt="" style="border-radius: 50%;padding: 5px 0 0;width: 33px;">
                                            </div>
                                        @endif
                                        <div style="height: 2.8rem; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
                                            {{ $record->arrival_time == null || $record->arrival_time == '1970-01-01 00:00:00' ? '-' : Carbon::parse($record->arrival_time)->format('Y-m-d h:i A') }}
                                        </div>
                                    </div>
                                </td>
                                {{--            </div> --}}

                                {{-- actions  --}}
                               <td class="text-center align-middle fixed-col2" style="width:15rem; border-right: solid 3px #C2C2C2 !important; position: sticky; ">
                                    <div class="dropdown">
                                        <button class="btn btn-link text-muted dropdown-toggle-action d-flex align-items-center fw-bold" type="button" data-bs-toggle="dropdown" data-bs-display="static" data-bs-offset="0,0" onclick="toggleArrow(this)" style="text-decoration: auto;" aria-expanded="false" >
                                            Actions <i class="ms-2 mt-1 fa-solid fa-caret-right" id="dropdown-arrow-{{ $loop->index }}"></i>
                                        </button>

                                        <ul class="dropdown-menu" style="inset: {{($loop->index <= ($loop->count - 5)) || $loop->count < 5  ? "1" : "-186"}}px auto auto 112px !important; width: 200px !important; z-index: 9999999999999 !important;" >

                                            @if ($userRole != 4)
                                                <li class="dropdown-item d-flex justify-content-between align-items-center" style="cursor: pointer;"
                                                    @if ($record->qa_flags_count > 0) @if ($userRole == 5)
                                                        wire:click=""
                                                        title="QA Flag Active"
                                                    @else
                                                        wire:click="showDeleteFlagAlert('{{ $record->id }}')"
                                                        title="{{ $record->qaFlags()->first()->name }}" @endif
                                                @else
                                                    @if ($userRole == 5) wire:click=""
                                                        title="No QA Flag"
                                                    @else
                                                        wire:click="showAddFlagAlert('{{ $record->id }}')"
                                                        title="Add QA Flag" @endif
                                                    @endif
                                                    >
                                                    <!-- Dynamic Text Based on Flag Existence -->
                                                    <span class="textColor">
                                                        @if ($record->qa_flags_count > 0)
                                                            @if ($userRole == 5)
                                                                QA Flag Active
                                                            @else
                                                                Clear QA Flag
                                                            @endif
                                                        @else
                                                            @if ($userRole == 5)
                                                                No QA Flag
                                                            @else
                                                                Add QA Flag
                                                            @endif
                                                        @endif
                                                    </span>

                                                    <!-- Icon Logic -->
                                                    @if ($record->qa_flags_count > 0)
                                                        @if ($userRole == 5)
                                                            <!-- Active QA Flag (View Only for Role 5) -->
                                                            <i class="fa-solid fa-check" style="color:#03a34d;" title="QA Flag Active"></i>
                                                        @else
                                                            <!-- QA Flag with Delete Option -->
                                                            <i class="fa-solid fa-trash" style="color:#FF5E60;" title="{{ $record->qaFlags()->first()->name }}"></i>
                                                        @endif
                                                    @else
                                                        @if ($userRole == 5)
                                                            <!-- No QA Flag (View Only for Role 5) -->
                                                            -
                                                        @else
                                                            <!-- Add QA Flag -->
                                                            <i class="fa-solid fa-flag" style="color:#03a34d;" title="Add QA Flag"></i>
                                                        @endif
                                                    @endif
                                                </li>
                                            @endif


                                            @if (in_array(1, $userPermissions))
                                                <li>
                                                    <a class="dropdown-item d-flex justify-content-between align-items-center" target="_blank" href="{{ route('play.new', ['call_id' => $record->call_id, 'agent_name' => $record?->agent?->full_name, 'agent_id' => $record?->agent?->agent_id]) }}">
                                                        <span class="textColor">Record</span>
                                                        <i class="fa-solid fa-microphone" style="color:#01A44F;"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item d-flex justify-content-between align-items-center" target="_blank" href="{{ route('playvideo', ['call_id' => $record->call_id, 'agent_name' => $record?->agent?->full_name, 'agent_id' => $record?->agent?->agent_id]) }}">
                                                        <span class="textColor">Video Record</span>
                                                        <i class="fa-solid fa-video" style="color:#01A44F;"></i>
                                                    </a>
                                                </li>
                                            @endif
                                            {{-- @if (Auth::user()->role != 4)
                                                <li>
                                                    <a class="dropdown-item d-flex justify-content-between align-items-center" href="#">
                                                        <span class="textColor">Played By</span>
                                                        @if ($record->listeners->isNotEmpty())
                                                            <i class="fa-solid fa-headphones" style="color:#03a34d;" wire:click="getCallListeners('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#view-listeners"></i>
                                                        @else
                                                            -
                                                        @endif
                                                    </a>
                                                </li>
                                            @endif --}}
                                            @if ($userRole != 4)
                                                <li>
                                                    @if ($record->listeners->isNotEmpty())
                                                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" wire:click="getCallListeners('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#view-listeners" style="cursor: pointer;">
                                                            <span class="textColor">Played By</span>
                                                            <i class="fa-solid fa-headphones" style="color:#03a34d;"></i>
                                                        </a>
                                                    @else
                                                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" style="cursor: not-allowed;">
                                                            <span class="textColor">Played By</span>
                                                            <span>-</span>
                                                        </a>
                                                    @endif
                                                </li>
                                            @endif



                                            {{-- <li>
                                                <a class="dropdown-item d-flex justify-content-between align-items-center" href="#">
                                                    <span class="textColor">Comments</span>
                                                    @if (Auth::user()->role != 4)
                                                        @if ($record->comments()->exists())
                                                            <i class="fa-solid fa-eye" style="cursor: pointer; color:#03a34d;" wire:click="getComments('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#view-comments"></i>
                                                        @else
                                                            <i class="fa-solid fa-pen" style="cursor: pointer; color:#c4d54a;" wire:click="addComment('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#add-comment"></i>
                                                        @endif
                                                    @else
                                                        -
                                                    @endif
                                                </a>
                                            </li> --}}

                                            <li>
                                                @if ($userRole != 4)
                                                    @if ($record->comments_count > 0)
                                                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" wire:click="getComments('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#view-comments" style="cursor: pointer;">
                                                            <span class="textColor">Comments</span>
                                                            <i class="fa-solid fa-eye" style="color:#03a34d;"></i>
                                                        </a>
                                                    @else
                                                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" wire:click="addComment('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#add-comment" style="cursor: pointer;">
                                                            <span class="textColor">Comments</span>
                                                            <i class="fa-solid fa-pen" style="color:#03a34d;"></i>
                                                        </a>
                                                    @endif
                                                @else
                                                    <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" style="cursor: not-allowed;">
                                                        <span class="textColor">Comments</span>
                                                        <span>-</span>
                                                    </a>
                                                @endif
                                            </li>



                                            {{-- @if (Auth::user()->parameters()->where('parameter_id', 22)->exists())
                                                <li>
                                                    <a class="dropdown-item d-flex justify-content-between align-items-center" href="#">
                                                        <span class="textColor">Evaluation</span>
                                                        @if ($record?->callEvaluation?->id)
                                                            <i class="fa-solid fa-eye" style="cursor: pointer; color:#03a34d;" wire:click="getEvaluationReport('{{ $record->callEvaluation->id }}')" data-bs-toggle="modal" data-bs-target="#viewEvaluationReport"></i>
                                                        @else
                                                            @if (Auth::user()->permissions()->where('permission_id', 9)->exists())
                                                                <i class="fa-solid fa-plus" style="cursor: pointer; color:#c4d54a;" data-bs-toggle="modal" data-bs-target="#add-evaluation" wire:click="addEvaluation('{{ $record->id }}')" title="Add Evaluation"></i>
                                                            @else
                                                                -
                                                            @endif
                                                        @endif
                                                    </a>
                                                </li>
                                            @endif --}}
                                            @if (in_array(22, $userParameters))
                                                <li>
                                                    @if ($record?->callEvaluation?->id)
                                                        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" wire:click="getEvaluationReport('{{ $record->callEvaluation->id }}')" data-bs-toggle="modal" data-bs-target="#viewEvaluationReport" style="cursor: pointer;">
                                                            <span class="textColor">Evaluation</span>
                                                            <i class="fa-solid fa-eye" style="color:#03a34d;"></i>
                                                        </a>
                                                    @else
                                                        @if (in_array(9, $userPermissions))
                                                            <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" wire:click="addEvaluation('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#add-evaluation" style="cursor: pointer;" title="Add Evaluation">
                                                                <span class="textColor">Evaluation</span>
                                                                <i class="fa-solid fa-plus" style="color:#03a34d;"></i>
                                                            </a>
                                                        @else
                                                            <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" style="cursor: not-allowed;">
                                                                <span class="textColor">Evaluation</span>
                                                                <span>-</span>
                                                            </a>
                                                        @endif
                                                    @endif
                                                </li>
                                            @endif

                                        </ul>
                                    </div>
                                </td>
                                {{-- @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        <a target="_blank" href="{{ route('play.new', ['call_id' => $record->call_id, 'agent_name' => $record?->agent?->full_name, 'agent_id' => $record?->agent?->agent_id]) }}">
                                            <i class="fa-solid fa-play fa-xl play-button" aria-hidden="true" style="cursor: pointer;color:#c4d54a"></i>
                                        </a>
                                    </td>
                                @endif

                                @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        <a target="_blank" href="{{ route('playvideo', ['call_id' => $record->call_id, 'agent_name' => $record?->agent?->full_name, 'agent_id' => $record?->agent?->agent_id]) }}">
                                            <i class="fa-solid fa-video fa-xl play-button" aria-hidden="true" style="cursor: pointer;color:#c4d54a"></i>
                                        </a>
                                    </td>
                                @endif --}}

                                {{-- @if (Auth::user()->role != 4)
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        @if ($record->listeners->isNotEmpty())
                                            <i class="fa-solid fa-lg fa-headphones-simple" style="cursor: pointer;color:#03a34d;" wire:click="getCallListeners('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#view-listeners"></i>
                                        @else
                                            -
                                        @endif
                                    </td>
                                @endif --}}
                                {{-- @if (Auth::user()->parameters()->where('parameter_id', 27)->exists())
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        @if (Auth::user()->role != 4)
                                            @if ($record->comments()->exists())
                                                <i class="fa-solid fa-lg fa-eye" style="cursor: pointer;color:#03a34d;" wire:click="getComments('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#view-comments"></i>
                                            @else
                                                <i class="fa-solid fa-lg fa-pen-clip" style="cursor: pointer;color:#c4d54a;" wire:click="addComment('{{ $record->id }}')" data-bs-toggle="modal" data-bs-target="#add-comment"></i>
                                            @endif
                                        @else
                                            -
                                        @endif
                                    </td>
                                @endif --}}
                                {{-- @if (Auth::user()->parameters()->where('parameter_id', 22)->exists())
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        @if (!Auth::user()->role != 4)
                                            @if ($record?->callEvaluation?->id)
                                                <i class="fa-solid fa-lg fa-eye" style="cursor: pointer;color:#03a34d;" wire:click="getEvaluationReport('{{ $record->callEvaluation->id }}')" data-bs-toggle="modal" data-bs-target="#viewEvaluationReport"></i>
                                            @else
                                                @if (Auth::user()->permissions()->where('permission_id', 9)->exists())
                                                    <i class="fa-solid fa-plus fa-lg me-1" aria-hidden="true" style="cursor: pointer;color:#c4d54a" data-bs-toggle="modal" data-bs-target="#add-evaluation" wire:click="addEvaluation('{{ $record->id }}')" title="Add Evaluation"></i>
                                                @else
                                                    -
                                                @endif
                                            @endif
                                        @else
                                            -
                                        @endif
                                    </td>
                                @endif --}}
                                @if (in_array(25, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->agent?->full_name }} </td>
                                @endif
                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->callEvaluation?->evaluator ? $record?->callEvaluation?->evaluator?->full_name : '-' }} </td> --}}
                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->callEvaluation?->evaluation->evaluation_name ?? '-' }} </td> --}}
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record?->agent?->agent_id }} </td>
                                @if (in_array(4, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->called_id ?? '-' }} </td>
                                @endif
                                @if (in_array(6, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->caller_id ?? '-' }} </td>
                                @endif
                                @if (in_array(31, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        {{ (str_contains($record->agent?->organization?->name, 'Ewa') ? 'Kease' : $record->agent?->organization?->name) . ' | ' . (str_contains($record->organization?->name, 'Ewa') ? 'Kease' : $record->organization?->name) }}
                                    </td>
                                @endif
                                @if (in_array(29, $userParameters))
                                    {{-- <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->agent->userGroup?->name ?? '-' }} </td> --}}
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        {{ $record?->userGroup?->name ? Str::after($record?->userGroup?->name, ' - ') : '-' }}
                                    </td>
                                @endif
                                @if (in_array(5, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_ender ?? '-' }} </td>
                                @endif

                                {{-- flag  --}}
                                {{-- @if (Auth::user()->parameters()->where('parameter_id', 23)->exists())
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        @if ($record->qaFlags()->exists())
                                            @if (Auth::user()->role == 5)
                                                <i class="fa-regular fa-lg fa-font-awesome" style="color:#03a34d; rotate:-35deg"></i>
                                            @else
                                                <i class="fa-regular fa-lg fa-font-awesome" style="cursor: pointer;color:white; rotate:-35deg" wire:click="showDeleteFlagAlert('{{ $record->id }}')" title="{{ $record->qaFlags()->first()->name }}"></i>
                                            @endif
                                        @else
                                            @if (Auth::user()->role == 5)
                                                -
                                            @else
                                                <i class="fa-solid fa-plus fa-lg me-1" aria-hidden="true" style="cursor: pointer;color:#c4d54a" wire:click="showAddFlagAlert('{{ $record->id }}')" title="Add QA Flag"></i>
                                            @endif
                                        @endif
                                    </td>
                                @endif --}}


                                @if (in_array(2, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        {{ $record->call_duration ? Carbon::parse($record->call_duration)->format('i:s') : '-' }}
                                    </td>
                                @endif



                                @if (in_array(19, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        {{ $record->callEvaluation?->quality_percentage ? $record->callEvaluation?->quality_percentage . '%' : '-' }}
                                    </td>
                                @endif
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->agent_extension ?? '-' }} </td>

                                @if (in_array(7, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                        {{ $record->hold_duration ? Carbon::parse($record->hold_duration)->format('i:s') : '-' }}
                                    </td>
                                @endif
                                @if (in_array(11, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->hold_count ?? '-' }} </td>
                                @endif
                                {{-- @if (Auth::user()->parameters()->where('parameter_id', 31)->exists())
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->agent?->organization?->name . ' | ' . $record?->organization?->name }} </td>
                                @endif --}}


                                @if (in_array(13, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->ring }} </td>
                                @endif
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ Str::title($record->language) ?? '-' }} </td>
                                @if (in_array(1, $userParameters))
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_id ?? '-' }} </td>
                                @endif
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->Genesys_CallUUID }} </td>
                            </tr>
                            @empty
                                <tr>
                                    <td colspan="25" class="text-muted text-center bg-white"> No Interactions found</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between">
                    <!-- Dropdown for Number of Items per Page -->
                    <div>
                        <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline " style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                            <option value="10">10</option>
                            <option value="15" selected>15</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>Interactions Per Page</span>
                    </div>

                    <div class="d-flex justify-content-between">
                        <!-- Dropdown for Number of Items per Page -->
{{--                        <div>--}}
{{--                            <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline " style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">--}}
{{--                                <option value="10">10</option>--}}
{{--                                <option value="15" selected>15</option>--}}
{{--                                <option value="30">30</option>--}}
{{--                                <option value="50">50</option>--}}
{{--                                <option value="100">100</option>--}}
{{--                            </select>--}}
{{--                            <span>Interactions Per Page</span>--}}
{{--                        </div>--}}

                        <!-- Pagination Links -->
                        <div class="d-flex gap-2">
                            <div
                                x-data="{
        inputPage: {{ $records->currentPage() }},
        maxPage: {{ $records->lastPage() }},
        goToPage(e) {
            if (e.key === 'Enter') {
                let page = this.inputPage;
                if (page > this.maxPage) page = this.maxPage;
                if (page < 1) page = 1;
                this.inputPage = page;
                $wire.set('currentPage', page);
            }
        }
    }"
                                x-init="
        Livewire.hook('message.processed', () => {
            let input = $el.querySelector('input[type=number]');
            let dynamicMax = Number(input?.getAttribute('max'));
            if (!isNaN(dynamicMax)) maxPage = dynamicMax;
        })
    "
                            >
                                <label for="pageSelect" class="mr-2">Go to Page:</label>
                                <input
                                    type="number"
                                    min="1"
                                    max="{{ $records->lastPage() }}"
                                    x-model.number="inputPage"
                                    @input="
            let maxAttr = Number($el.getAttribute('max'));
            if (!isNaN(maxAttr)) {
                maxPage = maxAttr;
                if (inputPage > maxPage) inputPage = maxPage;
            }
        "
                                    @keydown.enter="goToPage($event)"
                                    class="form-control bg-white d-inline"
                                    style="width: 80px; cursor: pointer; height:2rem; background-color:#eff3f4 !important"
                                />
                            </div>
                    <!-- Pagination Links -->
                    <div >
                        {{ $records->links(data: ['scrollTo' => false]) }}
                        <div  wire:target="gotoPage, nextPage, previousPage, interactionsPerPage" wire:loading class="loading-overlay" role="status">
                            <div class="spinner"></div>
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    </div>
                </div>

            </div>
        </div>

        {{-- view flags modal  --}}
        <div class="modal fade" id="view-flags" tabindex="-1" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">

                    <div class="modal-header text-white" style="background-color: #00a34e">
                        <h5 class="modal-title">QA Flag</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Remove Flag ?</p>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e">Close</button>
                    </div>
                </div>
            </div>
        </div>

        {{-- add flag modal  --}}
        <div class="modal fade" id="add-flag" tabindex="-1" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">

                    <div class="modal-header text-white" style="background-color: #00a34e">
                        <h5 class="modal-title">QA Flags</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Add Flag</p>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e">Close</button>
                    </div>
                </div>
            </div>
        </div>

        {{-- view comments modal  --}}
        <div class="modal fade" id="view-comments" data-bs-backdrop="static" tabindex="-1" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">

                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <i class="fa-solid fa-eye" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Comments</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive shadow-none rounded-4" style="max-height: 40rem;">
                            <table class="table table-striped table-bordered" id="view-comments-table">
                                <thead id="view-comments-table--head">
                                    <tr>
                                        <th scope="col"class="text-center">#</th>
                                        <th scope="col"class="text-center">Comment</th>
                                        <th scope="col"class="text-center">By</th>
                                        <th scope="col"class="text-center">Date</th>
                                        <th scope="col"class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="view-comments-table--body">
                                    @forelse ($selectedInteractionComments ?? [] as $item)
                                        <tr>
                                            <th scope="row" class="text-center align-middle">{{ $loop->index + 1 }}
                                            </th>
                                            <td class="text-center align-middle">{{ $item->comment }}</td>
                                            <td class="text-center align-middle">{{ $item->commenter->username }}</td>
                                            <td class="text-center align-middle text-nowrap">
                                                {{ $item->created_at?->format('d-m-Y') ?? '-' }}</td>
                                            @if (in_array(Auth::user()->role, [1, 3]) || Auth::id() === $item->commenter->id)
                                                <td class="text-center align-middle">
                                                    <i class="fa fa-trash fa-xl" aria-hidden="true" style="cursor: pointer; color:tomato" wire:click="showDeleteCommentAlert('{{ $item->id }}')" title="Delete User"></i>
                                                </td>
                                            @else
                                                <td class="text-center align-middle">--</td>
                                            @endif
                                        </tr>
                                    @empty
                                        <tr>
                                            <th scope="row" colspan="5" class="text-center">No Comments Found</th>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        {{-- @if ($add_comment_field == 'true')
                        <div class="mt-2">
                            <textarea name="" id="" cols="70" rows="3" wire:model="added_comment" class="rounded border-secondary textarea-with-padding" placeholder="Add a comment"></textarea>
                            <button type="button" class="form-control btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" wire:click="saveComment">Submit</button>
                        </div>
                    @endif --}}
                    </div>

                    {{-- <div class="modal-footer">
                    <button type="button" class="btn btn-success" style="background-color: #00a34e" wire:click="$set('add_comment_field', 'true')">Add Comment</button>
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeViewComments" wire:click="clear">Close</button>
                </div> --}}
                    <div class="modal-footer" style="border: none;">
                        {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeAddComment" wire:click="clearComment">Close</button> --}}
                        <button id="closeViewComments" wire:click="clear" data-bs-dismiss="modal" type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">Close
                        </button>
                        <button type="button" class="btn btn-success rounded-3 px-4" data-bs-dismiss="modal" style="height: 40px; border-color: #01a44f; background: #01a44f" wire:click="addComment('{{ $selectedInteractionId }}')" data-bs-toggle="modal" data-bs-target="#add-comment">Add Comment</button>

                    </div>
                </div>
            </div>
        </div>

        {{-- view call listeners modal  --}}
        <div class="modal fade" id="view-listeners" tabindex="-1" data-bs-backdrop="static" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <i class="fa-solid fa-user-check" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Who Played the Call</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close" wire:click = "clearWhoListenedToCall">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive shadow-none rounded-0" style="max-height: 40rem">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th scope="col"class="text-center">#</th>
                                        <th scope="col"class="text-center">Name</th>
                                        <th scope="col"class="text-center">User ID</th>
                                        <th scope="col"class="text-center">At</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($callListeners ?? [] as $item)
                                        <tr>
                                            <th scope="row" class="text-center align-middle">{{ $loop->index + 1 }}
                                            </th>
                                            <td class="text-center align-middle">{{ $item->username }}</td>
                                            <td class="text-center align-middle">{{ $item->agent_id }}</td>
                                            <td class="text-center align-middle text-nowrap">
                                                {{ $item->pivot?->created_at?->format('d-m-Y h:i A') }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <th scope="row" colspan="5" class="text-center">No one played the call
                                            </th>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {{-- <div class="modal-footer">
                        <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeViewListeners" wire:click="clearWhoListenedToCall">Close</button>
                    </div> --}}
                    <div class="modal-footer" style="border: none;">
                        {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeAddComment" wire:click="clearComment">Close</button> --}}
                        <button id="closeViewListeners" wire:click="clearWhoListenedToCall" data-bs-dismiss="modal" type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">Close
                        </button>
                        {{-- <button type="button" class="btn btn-success rounded-3 px-4" data-bs-dismiss="modal" style="height: 40px; border-color: #01a44f; background: #01a44f" wire:click='saveComment'>Submit</button> --}}

                    </div>
                </div>
            </div>
        </div>

        {{-- add comment modal  --}}
        {{-- <div wire:ignore.self ria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static"> --}}

        <div class="modal fade" id="add-comment" tabindex="-1" role="dialog" aria-hidden="true" ria-labelledby="addCommentModalLabel" data-bs-backdrop="static" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="background-color: white;">

                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <i class="fa-solid fa-plus" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Add Comment</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <textarea name="" id="" cols="70" rows="8" wire:model="added_comment" class="form-control rounded border-secondary textarea-with-padding" placeholder="Add a comment"></textarea>
                        @error('added_comment')
                        @enderror
                        {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" wire:click="saveComment">Submit</button> --}}
                    </div>

                    <div class="modal-footer" style="border: none;">
                        {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeAddComment" wire:click="clearComment">Close</button> --}}
                        <button id="closeAddComment" wire:click="clearComment" data-bs-dismiss="modal" type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">Close
                        </button>
                        <button type="button" class="btn btn-success rounded-3 px-4" data-bs-dismiss="modal" style="height: 40px; border-color: #01a44f; background: #01a44f" wire:click='saveComment'>Submit</button>

                    </div>
                </div>
            </div>
        </div>

        {{-- View Evaluation Report Modal  --}}
        <div class="modal modal-lg fade" id="viewEvaluationReport" tabindex="-1" wire:ignore.self style="">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <i class="fa fa-check" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Evaluation Details</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="section-one">
                            <div class="div-table">
                                <table class="table table-bordered" id='dataTable' style="margin-bottom: 0px;border-radius: 10px;">
                                    <thead class=" thead">
                                        <tr>
                                            <th scope="col">Call ID</th>
                                            <th scope="col">Name</th>
                                            {{-- <th scope="col">User Id</th> --}}
                                            <th scope="col">Date & Time</th>
                                            <th scope="col">Evaluated By</th>
                                            <th scope="col">View</th>
                                        </tr>
                                    </thead>
                                    <tbody style="text-align: center;">
                                        @forelse($this->evaluationReport ?? [] as $data)
                                            <tr>
                                                <td>{{ $data['referenceID'] }}</td>
                                                <td>{{ $data['name'] }}</td>
                                                {{-- <td>{{$data['user_id']}}</td> --}}
                                                <td>{{ $data['created_at'] }}</td>
                                                <td>{{ \App\Models\User::where('id', $data['created_by'])->pluck('full_name')->first() }}
                                                </td>
                                                <td>
                                                    <a href="{{ route('evaluation.reportAnswers', ['submit_id' => $data['id']]) }}">
                                                        <i class="fas fa-eye" style="font-size: 20px;color: #00a34e;"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="11" class="text-muted text-center">
                                                    <div class="loader" id="loader">
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer" style="border: none;">
                        {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeAddComment" wire:click="clearComment">Close</button> --}}
                        <button id="closeAddComment" wire:click="clear" data-bs-dismiss="modal" type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">Close
                        </button>

                    </div>
                </div>
            </div>
        </div>

        {{-- add eval modal  --}}
        <div class="modal fade" id="add-evaluation" tabindex="-1" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <i class="fa-solid fa-plus" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Add Evaluation</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="col-12">
                            <div class="custom-select" style="position: relative">

                                <select id="evaluationSelect" class="form-control " wire:model="evaluationID">
                                    <option value="0" Selected>Please Select</option>
                                    @foreach ($evaluations as $evaluation)
                                        <option value="{{ $evaluation->id }}">{{ $evaluation->evaluation_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    {{-- <div class="modal-footer">
                            <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeAddComment" wire:click="clearComment">Close</button>

                            <button wire:loading.remove type="button" class="btn btn-success" id='evaluationLink' style="background-color: #00a34e;display:none" wire:click='redirectToEvaluationPage'>Evaluation Page</button>

                            <button type="button" class="btn btn-success" id='btnDisabled' style="background-color: #00a34e;" disabled>Evaluation Page</button>
                        </div>
                    --}}
                    <div class="modal-footer border-0">
                        <button id="closeAddComment"
                                type="button"
                                class="btn btn-secondary rounded-3 px-4"
                                style="height: 40px; background-color: #eff3f4; border-color: #eff3f4; color: #6c97a6;"
                                wire:click="clearComment"
                                data-bs-dismiss="modal">
                            Close
                        </button>

                        <button id="evaluationLink"
                                type="button"
                                class="btn btn-success rounded-3 px-4 d-none"
                                style="height: 40px; background-color: #01a44f; border-color: #01a44f;"
                                wire:click="redirectToEvaluationPage"
                                wire:loading.remove>
                            Evaluation Page
                        </button>

                        <button id="btnDisabled"
                                type="button"
                                class="btn btn-success rounded-3 px-4"
                                style="height: 40px; background-color: #01a44f; border-color: #01a44f;"
                                disabled>
                            Evaluation Page
                        </button>
                    </div>

                </div>
            </div>
        </div>


        {{-- custom time modal --}}
        <div class="modal fade" id="custom_time_modal" data-bs-backdrop="static" tabindex="-1" wire:ignore.self>
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <i class="fa fa-calendar" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Custom Period</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 col-12">
                                <label for="call id" class="mb-2 ">Date From <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                                <input type="date" name="from" id="from" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Date From" wire:model="custom_date_from">
                                @error('custom_date_from')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6 col-12">
                                <label for="call id" class="mb-2 ">Date To <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                                <input type="date" name="to" id="to" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Date To" wire:model="custom_date_to">
                                @error('custom_date_to')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer" style="border: none;">
                        {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeAddComment" wire:click="clearComment">Close</button> --}}
                        <button id="closeCustomDate" data-bs-dismiss="modal" type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">Close
                        </button>
                        <button type="button" class="btn btn-success rounded-3 px-4" data-bs-dismiss="modal" style="height: 40px; border-color: #01a44f; background: #01a44f" wire:click="apply_custom_date">Apply</button>

                    </div>
                </div>
            </div>
        </div>




        <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                {{-- <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i> --}}
                                <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="filter" style="width: 1.5rem;">
                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModall' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">
                        <div class="col-md-12">

                            <div x-data="{ filterType: 'General' }"
                                 x-on:reset-filter-type.window="filterType = 'General'">
                                <div class="col-md-6 mb-3">
                                    <label for="exampleInputEmail1">
                                        <label class="mb-1">Filter Type</label>
                                    </label>
                                    <div class="dropdown">
                                        <select class="form-select" x-model="filterType" aria-label="Select Filter Type" style="font-size: 0.85rem !important;">
                                            <option value="General">General</option>
                                            <option value="Telephony">Telephony</option>
                                            <option value="Quality">Quality</option>
                                            <option value="Users & Action">Users & Action</option>
                                            <option value="Telephony Custom Attributes">Telephony Custom Attributes</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <template x-if="filterType === 'General'">
                                        <form class="row g-2 mb-3">
                                            <div class="col-md-6">
                                                <label for="date" class="mb-2 ">Time <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e" title="Date"></i></label>
                                                <div class="dropdown">
                                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%">
                                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_time_name ?? 'All Time' }}</span>
                                                    </button>
                                                    <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                        {{-- <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(null)">All Time</span>
                                                    </li>
                                                    <hr class="m-0"> --}}
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(1)">Last 24 Hours</span>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(7)">Last 7 Days</span>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(30)">Last 30 Days</span>
                                                        </li>
                                                        <hr class="m-0">

                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(60)">Last 60 Days</span>
                                                        </li>
                                                        <hr class="m-0">

                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#custom_time_modal">Custom</span>
                                                        </li>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                {{-- <label for="call id" class="mb-2 ">Call ID <i class="fa-solid fa-address-book fa-lg" style="color: #00a34e" title="Call ID"></i></label> --}}
                                                <label for="call id" class="mb-2 ">Call ID
                                                    <img src="{{ asset('assets/images/callid.png') }}" alt="" srcset="" style="width:1.3rem; height:1.3rem">
                                                </label>
                                                <input type="text" name="call-id" id="call-id" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Call ID" wire:model="filter_callId">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="Agent name" class="mb-2 ">User ID <i class="fa-solid fa-hashtag fa-lg" style="color:#00a34e" title="User ID"></i></label>

                                                <input type="text" name="agent" id="agent" style="border: solid 1px #b6b6b6" class="form-control" placeholder="User ID" wire:model="filter_agentId">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="Duration" class="mb-2 ">Duration <i class="fa-solid fa-stopwatch fa-lg" style="color:#00a34e" title="Duration"></i></label>
                                                <div class="input-group mb-2">
                                                    <div class="input-group-prepend">
                                                        <div>
                                                            <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px;" wire:model="filter_duration_sign">
                                                                <option class="fw-bold" value="=">=</option>
                                                                <option class="fw-bold" value=">">></option>
                                                                <option class="fw-bold" value="<">
                                                                    < </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="number" name="duration" id="duration" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Duration in seconds" wire:model="filter_duration">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="mb-2 ">Flags <i class="fa-solid fa-flag-o fa-lg" style="color:#00a34e" title="Duration"></i></label>
                                                <div class="dropdown">
                                                    <select class="form-select" wire:model="byFlagType" aria-label="Select Flag Type" style="padding-top: 0.46rem; padding-bottom: 0.46rem; min-width: fit-content;">
                                                        <option value="All" style="overflow:hidden; text-overflow:ellipsis; font-size: 0.85rem !important;">All</option>
                                                        <option value="Classification Flags" style="overflow:hidden; text-overflow:ellipsis; font-size: 0.85rem !important;">Classification Flags</option>
                                                        <option value="Bad Words Flags" style="overflow:hidden; text-overflow:ellipsis; font-size: 0.85rem !important;">Bad Words Flags</option>
                                                        <option value="QA Flags" style="overflow:hidden; text-overflow:ellipsis; font-size: 0.85rem !important;">QA Flags</option>
                                                    </select>
                                                </div>

                                            </div>
                                            <div class="col-md-6">
                                                <label for="account" class="mb-2 ">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Account"></i></label>
                                                {{-- <select class="form-select form-select mb-3" wire:model="selectedAccount">
                                                <option selected value="all">All</option>
                                                <option value="gen01">gen01</option>
                                            </select> --}}
                                                <select wire:model="filter_account"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem; overflow: auto;">
                                                    <option value="">All</option>

                                                    @foreach ($organizations->where('name', '!=', 'Ewa') as $item)
                                                        <option value="{{ $item->name }}">{{ $item->name }}</option>
                                                    @endforeach
                                                </select>

                                            </div>
                                        </form>
                                        </script>
                                    </template>
                                    <template x-if="filterType === 'Telephony'">
                                        <form class="row g-2 mb-3">
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Call Type')) --}}
                                            <div class="col-md-6">
                                                {{-- <label for="call type" class="mb-2 ">Call Type <i class="fa-solid fa-phone fa-lg" style="color: #00a34e" title="Call Type"></i></label> --}}
                                                <label for="call type" class="mb-2 ">Call Type
                                                    <img src="{{ asset('assets/images/calltype.png') }}" alt="call type" srcset="" style="width: 1.2rem; height:1.2rem">
                                                </label>
                                                <select wire:model="filter_callType"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="padding-top: 0.46rem; padding-bottom: 0.46rem; width:100%; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    <option value="Inbound">Inbound</option>
                                                    <option value="Outbound">Outbound</option>
                                                </select>

                                            </div>
                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Called ID')) --}}
                                            <div class="col-md-6">
                                                <label for="called id" class="mb-2 ">Called ID
                                                    <img src="{{ asset('assets/images/callin.png') }}" alt="call type" srcset="" style="width: 1.4rem; height:1.4rem">
                                                </label>
                                                {{-- <label for="call id" class="mb-2 ">Called ID <i class="fa-solid fa-arrow-down-wide-short fa-lg" style="color: #00a34e" title="Called ID"></i></label> --}}
                                                <input style="border: solid 1px #b6b6b6" type="text" name="call-id" id="called-id" class="form-control" placeholder="Called ID" wire:model="filter_calledId">
                                            </div>
                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Caller ID')) --}}
                                            <div class="col-md-6">
                                                <label for="called id" class="mb-2 ">Caller ID
                                                    <img src="{{ asset('assets/images/callout.png') }}" alt="call type" srcset="" style="width: 1.4rem; height:1.4rem">
                                                </label>
                                                {{-- <label for="caller id" class="mb-2 ">Caller ID <i class="fa-solid fa-arrow-up-wide-short fa-lg" style="color: #00a34e" title="Caller ID"></i></label> --}}
                                                <input type="text" name="call-id" id="caller-id" class="form-control" placeholder="Caller ID" wire:model="filter_callerId" style="border: solid 1px #b6b6b6">
                                            </div>

                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Call Ender')) --}}
                                            <div class="col-md-6">
                                                {{-- <label for="call id" class="mb-2 ">Call Ender <i class="fa-solid fa-phone-slash fa-lg" style="color: #00a34e" title="Call Ender"></i></label> --}}
                                                <label for="call id" class="mb-2 ">Call Ender
                                                    <img src="{{ asset('assets/images/decline.png') }}" alt="" srcset="" style="width:1.4rem; height:1.4rem">
                                                </label>
                                                <select wire:model="filter_callEnder"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="padding-top: 0.46rem !important; padding-bottom: 0.46rem !important; width: 100%; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    <option value="Agent">Agent</option>
                                                    <option value="Customer">Customer</option>
                                                </select>

                                                {{-- <input type="text" name="call-ender" id="call-ender" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Call Ender" wire:model="filter_callEnder"> --}}
                                            </div>
                                            {{-- @endif --}}

                                            {{-- <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Extension <i class="fa-solid fa-tty fa-lg" title="Extension" style="color: #00a34e"></i></label>
                                            <input type="text" name="extension" id="extension" class="form-control" placeholder="Extension" wire:model="extension">
                                        </div> --}}
                                            {{-- <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Pause Duration <i class="fa-regular fa-circle-pause fa-lg" style="color: #00a34e" title="Pause Duration"></i></label>
                                            <input type="number" name="pause-duration" id="pause-duration" class="form-control" placeholder="Duration in seconds" wire:model="pauseDuration">
                                        </div> --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Hold Duration')) --}}
                                            <div class="col-md-6">
                                                <label for="call id" class="mb-2 ">Hold Duration
                                                    <img src="{{ asset('assets/images/holdduration.png') }}" alt="" srcset="" style="width:1.3rem; height:1.3rem">
                                                </label>
                                                {{-- <label for="Duration" class="mb-2 ">Hold Duration <i class="fa-solid fa-phone-flip fa-lg" style="color: #00a34e" title="Hold Duration"></i></label> --}}
                                                <div class="input-group mb-2">
                                                    <div class="input-group-prepend">
                                                        <div>
                                                            <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px;" wire:model="filter_holdDuration_sign">
                                                                <option class="fw-bold" value="=">=</option>
                                                                <option class="fw-bold" value=">">></option>
                                                                <option class="fw-bold" value="<">
                                                                    < </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="number" style="border: solid 1px #b6b6b6" name="hold-duration" id="hold-duration" class="form-control" placeholder="Duration in seconds" wire:model="filter_holdDuration">
                                                </div>
                                                {{-- <input type="number" style="border: solid 1px #b6b6b6" name="hold-duration" id="hold-duration" class="form-control" placeholder="Duration in seconds" wire:model="filter_holdDuration"> --}}
                                            </div>
                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Call Count')) --}}
                                            <div class="col-md-6">
                                                <label for="call type" class="mb-2">Hold Count
                                                    <img src="{{ asset('assets/images/pause.png') }}" alt="call type" srcset="" style="width: 1.2rem; height:1.2rem">
                                                </label>
                                                {{-- <label for="Duration" class="mb-2 ">Hold Count <i class="fa-solid fa-circle-pause fa-lg" style="color: #00a34e" title="Hold Count"></i></label> --}}
                                                <div class="input-group mb-2">
                                                    <div class="input-group-prepend">
                                                        <div>
                                                            <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px;" wire:model="filter_holdCount_sign">
                                                                <option class="fw-bold" value="=">=</option>
                                                                <option class="fw-bold" value=">">></option>
                                                                <option class="fw-bold" value="<">
                                                                    < </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input style="border: solid 1px #b6b6b6" type="number" name="hold-count" id="hold-count" class="form-control" placeholder="Hold Count" wire:model="filter_holdCount">
                                                </div>
                                                {{-- <input style="border: solid 1px #b6b6b6" type="number" name="hold-count" id="hold-count" class="form-control" placeholder="Hold Count" wire:model="filter_holdCount"> --}}
                                            </div>
                                            {{-- @endif --}}

                                            <div class="col-md-6">
                                                <label for="is played" class="mb-2 ">Is Played <i class="fa-solid fa-headphones-simple fa-lg" style="color: #00a34e" title="Is Played"></i></label>
                                                {{-- <label for="call id" class="mb-2 ">Is Played
                                                    <img src="{{ asset('assets/images/decline.png') }}" alt="" srcset="" style="width:1.4rem; height:1.4rem">
                                                </label> --}}
                                                <select wire:model="filter_played"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="padding-top: 0.46rem !important; padding-bottom: 0.46rem !important; width: 100%; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    <option value="Played">Played</option>
                                                    <option value="Not Played">Not Played</option>
                                                </select>

                                                {{-- <input type="text" name="call-ender" id="call-ender" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Call Ender" wire:model="filter_callEnder"> --}}
                                            </div>

                                            {{-- <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Ring <i class="fa-solid fa-phone-volume fa-lg" style="color: #00a34e" title="Ring"></i></label>
                                            <input type="number" name="ring" id="ring" class="form-control" placeholder="Duration in seconds" wire:model="ring">
                                        </div> --}}
                                            {{-- <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Digits Count</label>
                                            <input type="number" name="digits-count" id="digits-count" class="form-control" placeholder="# of digits" wire:model="digitsCount">
                                        </div> --}}
                                            {{-- <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Transferred To <i class="fa-solid fa-cloud-arrow-up fa-lg" style="color: #00a34e" title="Transferred To"></i></label>
                                            <input type="text" name="transferred-to" id="transferred-to" class="form-control" placeholder="Trnasferred To" wire:model="transferredTo">
                                        </div> --}}
                                            {{-- <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Transferred From <i class="fa-solid fa-cloud-arrow-down fa-lg" style="color: #00a34e" title="Transferred From"></i></label>
                                            <input type="text" name="transferred-from" id="transferred-from" class="form-control" placeholder="Transferred From" wire:model="transferredFrom">
                                        </div> --}}
                                        </form>
                                    </template>
                                    <template x-if="filterType === 'Quality'">
                                        <form class="row g-2 mb-3">
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Is Assgined')) --}}
                                            {{-- <div class="col-md-6">
                                            <label for="Is Assigned" class="mb-2 ">Is Assigned <i class="fa-solid fa-people-arrows fa-lg" title="Is Assigned" style="color: #00a34e"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_isAssigned', 'Assigned')">Assigned</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_isAssigned', 'Not Assigned')">Not Assigned</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Evaluation Score')) --}}
                                            <div class="col-md-6">
                                                <label for="Duration" class="mb-2 ">Evaluation Score <i class="fa-solid fa-ranking-star fa-lg" style="color: #00a34e" title="Evaluation Score"></i></label>
                                                {{-- <input type="number" style="border: solid 1px #b6b6b6; padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%" name="evaluation-score" id="evaluation-score" class="form-control" placeholder="Evaluation Score" wire:model="filter_evaluationScore"> --}}
                                                <div class="input-group mb-2">
                                                    <div class="input-group-prepend">
                                                        <div>
                                                            <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px; padding-top: 0.46rem; padding-bottom: 0.46rem;" wire:model="filter_evaluationScore_sign">
                                                                <option class="fw-bold" value="=">=</option>
                                                                <option class="fw-bold" value=">">></option>
                                                                <option class="fw-bold" value="<">
                                                                    < </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="number" style="border: solid 1px #b6b6b6; padding-top: 0.46rem;padding-bottom: 0.46rem;" name="evaluation-score" id="evaluation-score" class="form-control" placeholder="Evaluation Score" wire:model="filter_evaluationScore">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="unique-id" class="mb-2 ">Unique ID <i class="fa-solid fa-fingerprint fa-lg" style="color: #00a34e" title="Unique ID"></i></label>
                                                <input type="text" style="border: solid 1px #b6b6b6; padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%" name="unique-id" id="unique-id" class="form-control" placeholder="Unique ID" wire:model="filter_uniqueId" min="1">
                                            </div>
                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Evaluated Using')) --}}
                                            {{-- <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">Evaluated Using</label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Report List</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Report List</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                            {{-- @endif --}}

                                            {{-- <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">Fatal Errors <i class="fa-solid fa-ban fa-lg" style="color: #00a34e" title="Includes Fatal Error"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Includes</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Does Not Include</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Is Evaluated')) --}}
                                            <div class="col-md-6">
                                                <label for="call type" class="mb-2 ">Is Evaluated <i class="fa-solid fa-user-tie fa-lg" style="color: #00a34e" title="Evaluator"></i></label>
                                                {{-- <select class="form-select form-select mb-3" wire:model="isEvaluated">
                                                <option value="all">All</option>
                                                <option value="evaluated">Evaluated</option>
                                                <option value="notEvaluated">Not Evaluated</option>
                                            </select> --}}
                                                <select wire:model="filter_isEvaluated"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    <option value="Evaluated">Evaluated</option>
                                                    <option value="Not Evaluated">Not Evaluated</option>
                                                </select>

                                            </div>

                                            <div class="col-md-6">
                                                <label for="call type" class="mb-2 ">Evaluation Form <i class="fa-solid fa-rectangle-list fa-lg" style="color: #00a34e" title="Evaluator"></i></label>
                                                <select wire:model="filter_evaluationForm"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    @foreach ($evaluation_forms as $form)
                                                        <option value="{{ $form->evaluation_name }}">{{ $form->evaluation_name }}</option>
                                                    @endforeach
                                                </select>

                                            </div>
                                            {{-- @endif --}}

                                            {{-- <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">Penalties <i class="fa-solid fa-hammer fa-lg" style="color: #00a34e" title="Penalties"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Includes</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Does Not Include</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                            {{-- <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">Agent Feedback</label>
                                            <select class="form-select form-select mb-3" wire:model="agentFeedback">
                                                <option value="all">All</option>
                                                <option value="satisfied">Satisfied</option>
                                                <option value="notSatisfied">Not Satisfied</option>
                                                <option value="na">NA</option>
                                            </select>
                                        </div> --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'QA Flagged Interactions')) --}}
                                            <div class="col-md-6">
                                                <label for="call type" class="mb-2 ">QA Flagged Interactions <i class="fa-solid fa-flag fa-lg" style="color: #00a34e" title="Flagged Interactions"></i></label>
                                                {{-- <select class="form-select form-select mb-3" wire:model="QaFlaggedInteractions">
                                                <option value="all">All Flags</option>
                                                <option value="noFlags">No Flags</option>
                                                <option value="list">GCC Flags</option>
                                            </select> --}}
                                                <select wire:model="filter_qaFlaggedInteractions"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    <option value="Flagged">Flagged</option>
                                                    <option value="Not Flagged">Not Flagged</option>
                                                </select>

                                            </div>
                                            {{-- @endif --}}
                                            {{-- @if (Auth::user()->parameters->contains('name', 'Flag Date')) --}}
                                            {{-- <div class="col-md-6">
                                                <label for="Is Assigned" class="mb-2 ">Flag Date</label>
                                                <div class="dropdown">
                                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                    </button>
                                                    <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Select Date</span>
                                                        </li>
                                                    </div>
                                                </div>
                                            </div> --}}
                                            {{-- @endif --}}
                                        </form>
                                    </template>
                                    <template x-if="filterType === 'Users & Action'">
                                        <form class="row g-2 mb-3">
                                            <div class="col-md-4">
                                                <label for="agent name" class="mb-2 ">Agent Name <i class="fa-solid fa-id-card fa-lg" title="Agent Name" style="color: #00a34e"></i></label>
                                                <input style="border: solid 1px #b6b6b6; padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%" type="text" name="agent-name" id="agent-name" class="form-control" placeholder="Agent Name" wire:model="filter_agentName">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="account" class="mb-2 ">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Account"></i></label>
                                                {{-- <select class="form-select form-select mb-3" wire:model="selectedAccount">
                                                <option selected value="all">All</option>
                                                <option value="gen01">gen01</option>
                                            </select> --}}
                                                <select wire:model="filter_account"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem; overflow: auto;">
                                                    <option value="">All</option>

                                                    @foreach ($organizations->where('name', '!=', 'Ewa') as $item)
                                                        <option value="{{ $item->name }}">{{ $item->name }}</option>
                                                    @endforeach
                                                </select>

                                            </div>
                                            <div class="col-md-4">
                                                <label for="account" class="mb-2 ">Skill Group <i class="fa-solid fa-people-roof fa-lg" style="color: #00a34e" title="Skill Group"></i></label>
                                                {{-- <select class="form-select form-select mb-3" wire:model="skillGroup">
                                                <option selected value="all">All</option>
                                                <option value="gen01">gen01 - Outbound</option>
                                            </select> --}}
                                                <select wire:model="filter_skillGroup"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem;">
                                                    <option value="All">All</option>

                                                    @forelse ($skillGroups as $item)
                                                        <option value="{{ $item->name }}">{{ $item->name }}</option>
                                                    @empty
                                                        <option disabled>No Groups Found</option>
                                                    @endforelse
                                                </select>

                                            </div>
                                            {{-- <div class="col-md-6">
                                            <label for="account" class="mb-2 ">Call Importance</label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Normal</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Urgent</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                        </form>
                                    </template>
                                    <template x-if="filterType === 'Telephony Custom Attributes'">
                                        <form class="row g-2 mb-3">
                                            {{-- <div class="col-md-6">
                                                <label for="pause count" class="mb-2 ">Pause Count <i class="fa-solid fa-pause fa-lg" style="color: #00a34e" title="Pause Count"></i></label>
                                                <input style="border: solid 1px #b6b6b6;" type="number" name="pause-count" id="pause-count" class="form-control" placeholder="Pause Count" wire:model="filter_pauseCount">
                                            </div> --}}
                                            <div class="col-md-6">
                                                <label for="call id" class="mb-2 ">Language <i class="fa-solid fa-language fa-lg" style="color: #00a34e" title="Language"></i></label>
                                                {{-- <input style="border: solid 1px #b6b6b6;" type="text" name="language" id="language" class="form-control" placeholder="Language" wire:model="filter_language"> --}}
                                                <div class="dropdown">
                                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%">
                                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">
                                                            @php
                                                                $allLanguages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
                                                                $isAllSelected = empty(array_diff($allLanguages, $filter_selected_languages)) && empty(array_diff($filter_selected_languages, $allLanguages));
                                                            @endphp
                                                            @if ($isAllSelected)
                                                                {{ 'All' }}
                                                            @elseif (!$filter_selected_languages || count($filter_selected_languages) <= 1)
                                                                {{ $filter_selected_languages[0] ?? '--' }}
                                                            @else
                                                                {{ 'Multi Selection' ?? '--' }}
                                                            @endif
                                                        </span>
                                                    </button>
                                                    <ul class="dropdown-menu w-100" wire:key="sdojisaodhn89" onclick="event.stopPropagation()" wire:ignore.self aria-labelledby="dropdownMenuButton1" id="dropdownMenu2323sd" style="height: 10rem; overflow:auto;">
                                                        <li>
                                                            <label class="dropdown-item">
                                                                <input type="checkbox" wire:key="select-all-checkbox" wire:click="selectAllLanguages()" @if (!array_diff($all_languages, $filter_selected_languages)) checked @endif> All
                                                            </label>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="arabic-check" wire:click="filter_languages('Arabic')" @if (in_array('Arabic', $filter_selected_languages)) checked @endif> Arabic
                                                            </label></li>

                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="english-check" wire:click="filter_languages('English')" @if (in_array('English', $filter_selected_languages)) checked @endif> English
                                                            </label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="kurdish-check" wire:click="filter_languages('Kurdish')" @if (in_array('Kurdish', $filter_selected_languages)) checked @endif>
                                                                Kurdish</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="spanish-check" wire:click="filter_languages('Spanish')" @if (in_array('Spanish', $filter_selected_languages)) checked @endif>
                                                                Spanish</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="german-check" wire:click="filter_languages('German')" @if (in_array('German', $filter_selected_languages)) checked @endif>
                                                                German</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="french-check" wire:click="filter_languages('French')" @if (in_array('French', $filter_selected_languages)) checked @endif>
                                                                French</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="italian-check" wire:click="filter_languages('Italian')" @if (in_array('Italian', $filter_selected_languages)) checked @endif>
                                                                Italian</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="russian-check" wire:click="filter_languages('Russian')" @if (in_array('Russian', $filter_selected_languages)) checked @endif>
                                                                Russian</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="urdu-check" wire:click="filter_languages('Urdu')" @if (in_array('Urdu', $filter_selected_languages)) checked @endif> Urdu</label>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="hebrew-check" wire:click="filter_languages('Hebrew')" @if (in_array('Hebrew', $filter_selected_languages)) checked @endif>
                                                                Hebrew</label></li>
                                                        <hr class="m-0">
                                                        <li><label class="dropdown-item"><input type="checkbox" wire:key="turkish-check" wire:click="filter_languages('Turkish')" @if (in_array('Turkish', $filter_selected_languages)) checked @endif>
                                                                Turkish</label></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </form>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer" style="border: none;">
                        <button type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" wire:click="clear" wire:loading.attr="disabled" wire:target="clear">
                            <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                            <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear" role="status" aria-hidden="true"></span>
                        </button>
                        <button class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="getData" wire:loading.attr="disabled" wire:target="getData">
                            <span wire:loading.remove wire:target="getData" style="font-size: 15px; ">Apply</span>
                            <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- <div wire:loading class="loading-overlay">
            <div class="spinner"></div>
        </div> --}}
        <script>
            function sortTable(header) {
                const table = header.closest('table');
                if (!table) return;

                const rows = Array.from(table.querySelectorAll('tbody tr'));
                const columnIndex = Array.from(header.parentElement.children).indexOf(header);

                const isAscending = header.getAttribute('data-order') === 'asc';

                // Update the sorting order on the clicked header
                const newOrder = isAscending ? 'desc' : 'asc';
                header.setAttribute('data-order', newOrder);

                // Update all headers to reset their icons
                Array.from(header.parentElement.children).forEach((th) => {
                    const sortIcon = th.querySelector('.fa-sort');
                    const upIcon = th.querySelector('.fa-sort-up');
                    const downIcon = th.querySelector('.fa-sort-down');

                    if (th === header) {
                        if (sortIcon) sortIcon.classList.add('d-none');
                        if (upIcon) upIcon.classList.toggle('d-none', newOrder !== 'asc');
                        if (downIcon) downIcon.classList.toggle('d-none', newOrder !== 'desc');
                    } else {
                        th.setAttribute('data-order', 'none');
                        if (sortIcon) sortIcon.classList.remove('d-none');
                        if (upIcon) upIcon.classList.add('d-none');
                        if (downIcon) downIcon.classList.add('d-none');
                    }
                });

                // Sort rows based on the selected column
                rows.sort((rowA, rowB) => {
                    const cellA = rowA.children[columnIndex].innerText.trim();
                    const cellB = rowB.children[columnIndex].innerText.trim();

                    if (!isNaN(Date.parse(cellA)) && !isNaN(Date.parse(cellB))) {
                        // Date sorting
                        const dateA = new Date(cellA);
                        const dateB = new Date(cellB);
                        return newOrder === 'asc' ? dateA - dateB : dateB - dateA;
                    } else {
                        // String sorting
                        return newOrder === 'asc' ?
                            cellA.localeCompare(cellB) :
                            cellB.localeCompare(cellA);
                    }
                });

                // Append sorted rows back to the tbody
                const tbody = table.querySelector('tbody');
                rows.forEach((row) => tbody.appendChild(row));
            }

            // Automatically set the default sorting for Date-Time column
            window.addEventListener('DOMContentLoaded', () => {
                const defaultHeader = document.querySelector('th[data-default="true"]');
                if (defaultHeader) {
                    defaultHeader.setAttribute('data-order', 'desc'); // Set default order to descending
                    sortTable(defaultHeader);
                }
            });
        </script>







