<?php

namespace App\Livewire\Evaluation;

use App\Models\Evaluation;
use App\Models\EvaluationFields;
use App\Models\EvaluationQuestion;
// use HTMLPurifier;
// use HTMLPurifier_Config;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;


class CreateForm<PERSON>uilder extends Component
{
    use WithPagination,LivewireAlert;

    public $order_by = null;
    public $sort_by = null;
    public $limit = null;

    public $form_name;


    public $user_id;
    public $modalId;
    public $modalIdShow;

    public $evaluation_id;
    public $itemType;
    public $type;
    public $label;
    public $value;
    public $required;
    public $inputs;
    public $counter;
    public $flag;
    public $modalUpdate;
    public $userid;


    protected $paginationTheme = 'bootstrap';


    protected $listeners = ['dropItem' => 'openModal'];

    public function openModal($data)
    {

$this->itemType = $data;
        if($this->itemType =='Dropdown') {
            $this->type = 'Dropdown';
        }elseif ($this->itemType =='Week'){
            $this->type = 'Week';
            $this->label = 'Week';
        }
        $this->modalIdShow = true;
        $this->modalUpdate = false;
        $this->dispatch('open-modal');
    }

    public function closeModal()
    {
        $this->modelFormReset();
        $this->dispatch('close-modal');
    }

    public function mount($evaluation_id)
    {
        $this->evaluation_id = $evaluation_id;
        $this->required = false;
        $this->counter = 1;
        $this->inputs = [];
        $this->value = [];
        $this->flag = false;
        $this->modalIdShow = false;
        $this->modalUpdate = false;
        $this->userid = auth()->id();
        $this->getEvaluationName($evaluation_id);
        $this->getEvaluationFields();
    }

    public function getEvaluationFields(){

        try
        {
            return EvaluationFields::query()
                ->where('evaluation_id',$this->evaluation_id)
                ->get();
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function getEvaluationName($evaluation_id){

        $evaluation = Evaluation::query()
        ->where('id',$evaluation_id)->first();
        $this->form_name = $evaluation->evaluation_name;

    }

    public function rules(){
        $rules = [
            'label' => ['required','string'],
            'type' => ['required','string'],
        ];

        if($this->itemType =='Dropdown') {
        for ($i = 0; $i <= count($this->inputs); $i++) {
            $rules['value.'.$i] = 'required';
        }
        }
        return $rules;
    }
    public function messages()
    {
        $messages = [];

        for ($i = 0; $i <= count($this->inputs); $i++) {
            $messages['value.'.$i.'.required'] = 'The option field is required.';
        }

        return $messages;
    }
    public function modelData(){

        // $config = HTMLPurifier_Config::createDefault();
        // $purifier = new HTMLPurifier($config);

        // $this->form_name = $purifier->purify($this->form_name);

        return [
            'label'            => $this->label,
            'type'            => $this->type,
            'value'            => json_encode($this->value),
            'required'            => $this->required,
            'created_by'           => $this->userid,
            'evaluation_id'           => $this->evaluation_id,
        ];
    }

    public function store(){
       
        $this->validate();
        try {

            EvaluationFields::create($this->modelData());

            $this->alert('success', 'Successfully Added !',[
                //'position' => 'bottom-end',
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){

            $this->alert('error', $e,[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

        $this->modelFormReset();
        $this->dispatch('close-modal');
    }

    public function addNewElement($cunter){
        $this->counter = $cunter + 1;
        array_push($this->inputs,$cunter);
    }
    public function removeElement($key){
        unset($this->inputs[$key]);
        unset($this->value[$key+1]);
    }

    public function deleteField($id){
        EvaluationFields::destroy($id);

        $this->alert('success', 'Successfully Deleted!', [
            //'position' => 'bottom-end',
            'timerProgressBar' => true,
            'timer' => '6000',
        ]);
        if($this->modalId){
            $this->closeModal();
        }
    }

    public function getFieldToUpdate($id){

        if(!$this->modalIdShow) {

            try {
                $data = EvaluationFields::query()
                    ->where('id', $id)
                    ->get();

                $this->type = $data[0]->type;
                if($data[0]->type == 'Text' || $data[0]->type == 'Number' || $data[0]->type == 'Email' || $data[0]->type == 'Comment'){
                    $this->itemType = 'Text';
                }else{
                    $this->itemType = $data[0]->type;
                }

                $this->label = $data[0]->label;
                $array = json_decode($data[0]->value, true);
                $this->value = $array;
                $this->modalId = $data[0]->id;
                $this->dispatch('open-modal');
                $i = 0;
                foreach ($array as $item) {
                    if ($i == 1) {
                        $this->addNewElement($this->counter);
                    }
                    $i = 1;
                }
                if ($data[0]->required) {
                    $this->flag = true;
                    $this->dispatch('off-checkbox');
                    $this->required = true;
                }
                $this->modalIdShow = true;
                $this->modalUpdate = true;
            }
            catch (\Exception $e){

            }
        }else{

            $this->alert('error', 'You should cancel the open module before you click here.', [
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

        }
    }



    public function update(){

        $this->validate();
        try {

            EvaluationFields::findOrFail($this->modalId)->update($this->modelData());

            $this->alert('success', 'Successfully Added !',[
                //'position' => 'bottom-end',
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);

            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){

            $this->alert('error', $e,[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

        $this->modelFormReset();
        $this->dispatch('close-modal');

    }


    public function showModal($id){

        $this->modalId =$id;
        $this->modalIdShow = "on";
    }

    public function getRequired(){

        if($this->flag){
            $this->flag = false;
        }else{
            $this->required = !$this->required;
        }

    }


    public function modelFormReset(){

        $this->type = "";
        $this->label = "";
        $this->modalId = "";
        $this->value = [];

        if($this->required) {
            $this->flag = true;
            $this->dispatch('off-checkbox');
            $this->required = false;

        }
        $this->counter = 1;
        $this->inputs = [];
        $this->modalIdShow = false;
        $this->resetValidation();
    }

    public function render()
    {

        return view('livewire.evaluation.create-form-builder',['Evaluation_Fields' => $this->getEvaluationFields(),]);
    }
}
