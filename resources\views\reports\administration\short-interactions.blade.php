@extends('layouts.app')

@section('style')
    {{-- <style>
        input {
            border: solid 1px #b6b6b6 !important;
            border-radius: 0.6rem !important;
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        } --}}
{{-- Tilte Section --}}
@section('title', 'Short Interactions Report')
    <style>
        /* hide the scrollbar for lists  */
        .dropdown-menu::-webkit-scrollbar {
            display: none
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 0 !important;
        }

        input {
            border: solid 1px #b6b6b6 !important;
            /* border-radius: 0.6rem !important; */
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .list-group-item:hover {
            color: white;
            background-color: #00a34e;
        }

        th {
            text-wrap: nowrap;
        }

        .dropdown-menu::-webkit-scrollbar {
            display: none
        }

        #dropDownList {
            border-radius: 7px !important;
        }

        .dropdown-toggle::after {
            vertical-align: top !important;
        }

        input::placeholder {
            font-size: 0.85rem;
        }

        .header-button {
            transition: box-shadow 0.3s !important;
        }

        .header-button:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        }

        .dropdown-menu.w-100.show {
            transform: translate3d(0px, 39.2px, 0px) !important;
        }
    </style>
    {{-- </style> --}}
@endsection


@section('content')
    @livewire('reports.short-interactions')
    <script>
        window.addEventListener('closeModal', () => {
            document.querySelector('#closeAddComment').click();
            document.querySelector('#closeViewComments').click();
        });

        window.addEventListener('closeCustomDateModal', () => {
            document.querySelector('#closeCustomDate').click();
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Call the function when the page loads

            document.getElementById('evaluationSelect').value;

            // Add event listener to the select element

        });
        document.getElementById('evaluationSelect').addEventListener('change', function() {
            // Call the function when the select element's value changes
            updateEvaluationLink();
        });

        function updateEvaluationLink() {
            var selectedValue = document.getElementById('evaluationSelect').value;

            if (selectedValue == 0) {
                document.getElementById('evaluationLink').style.display = 'none';
                document.getElementById('btnDisabled').style.display = '';
            } else {
                document.getElementById('evaluationLink').style.display = '';
                document.getElementById('btnDisabled').style.display = 'none';
            }
        }
    </script>
@endsection
