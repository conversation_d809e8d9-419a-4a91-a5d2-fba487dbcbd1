<?php

namespace App\Livewire\Reports;

use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\Organization;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\UserPermissionsExport;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;

class UserPermissions extends Component
{
    use LivewireAlert, WithPagination;

    public $perPage = 15;

    // filters
    public $filter_name;
    public $filter_agentId;
    public $filter_role;
    public $filter_role_name;
    public $filter_org;
    public $filter_org_name;

    public $filtersApplied = false;


    protected $paginationTheme = 'bootstrap';


    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }

    public function applyFilters()
    {
        $this->filtersApplied = true;
    }

    public function clear()
    {
        $this->filter_name = null;
        $this->filter_agentId = null;
        $this->filter_role = null;
        $this->filter_org = null;
        $this->filter_org_name = null;
        $this->filtersApplied = false;
        $this->filter_role_name = 'All';
    }


    public function filterRole($id)
    {
        if ($id == 'All') {
            $id = null;
        }

        $this->filter_role = $id;
        $this->filter_role_name = match ($id) {
            1 => 'Admins',
            2 => 'Supervisors',
            3 => 'IT',
            4 => 'Agents',
            default => 'All'
        };
    }

    public function filterOrg($id)
    {
        $this->filter_org = $id;

        if ($id == 'All') {
            $id = null;
        }

        $this->filter_org_name = Organization::find($id)->name;
    }


    public function export()
    {
        $export = new UserPermissionsExport($this->filter_name, $this->filter_agentId, $this->filter_role, $this->filter_org);

        // Return the export response
        return Excel::download($export, 'usersPermissions.xlsx');
    }
    public function getData() 
    {
        $this->applyFilters();
        $this->dispatch('close-modal');
    }
    public function render()
    {
        return view('livewire.reports.user-permissions', [
            'users' =>  $this->filtersApplied ?
                User::when($this->filter_name, function ($q1) {
                    $q1->where('full_name', 'like', "%$this->filter_name%");
                })
                ->when($this->filter_agentId, function ($q2) {
                    $q2->where('agent_id', 'like', "%$this->filter_agentId%");
                })
                ->when($this->filter_role, function ($q3) {
                    $q3->where('role', $this->filter_role);
                })
                ->when($this->filter_org, function ($q4) {
                    $q4->where(function ($q) {
                        $q->where('organization_id', $this->filter_org)
                            ->orWhereHas('supervisorOrganizations', function ($q5) {
                                $q5->where('organizations.id', $this->filter_org);
                            });
                    });
                })
                ->paginate($this->perPage)
                :
                User::paginate($this->perPage),
            'groups' => UserGroup::all(),
            'orgs' => Organization::all()
        ]);
    }
}
