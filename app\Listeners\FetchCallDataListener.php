<?php

namespace App\Listeners;

use App\Events\CallDataRequested;
use App\Jobs\FetchExternalCallJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class FetchCallDataListener
{
    public function handle(CallDataRequested $event): void
    {
//        // Dispatch job to fetch from external sources
//        FetchExternalCallJob::dispatch($event->callId);
//        FetchExternalCallJob::dispatch($event->callId)->onQueue('fetchCallDetails');

    }
}
