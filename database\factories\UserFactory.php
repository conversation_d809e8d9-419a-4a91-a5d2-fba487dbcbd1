<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fullNameSegments = [
            $this->faker->firstName,
            $this->faker->lastName,
            $this->faker->firstName,
            $this->faker->lastName,
        ];

        return [
            'full_name' => implode(' ', $fullNameSegments),
            'username' => $this->faker->unique()->userName,
            'email' => $this->faker->unique()->safeEmail,
            'agent_id' => $this->faker->numberBetween(1000000, 9999999),
            'password' => bcrypt('Aa@123456789'),
            'role' => $this->faker->randomElement([1, 2, 4, 5]),
            'terminated' => $this->faker->boolean,
            'enabled' => 1,
            'email_verified_at' => now(),
            'remember_token' => Str::random(10),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
