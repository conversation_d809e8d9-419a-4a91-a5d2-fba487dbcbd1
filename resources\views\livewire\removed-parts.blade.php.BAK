{{-- first horizontal row  --}}
<div class="section-tow px-0 col-12 p-4 mt-2 pb-2">

    {{-- <div class=" "> --}}

    {{-- @if ($page == 'pageOne') --}}
    <div class="row parent-cards m-0 col-6 " style="@if ($page != 'pageOne') opacity: 0;position: absolute; @endif">
        <x-swipe>
            <div class="card rounded-3 bg-white shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Avg Call Duration Card')" style="{{ in_array('Avg Call Duration Card', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Avg Call Duration Card', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Avg Call Duration Card', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Avg Call Duration Card')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Avg Call Duration Card')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif


                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Call Duration Card' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Avg. Call Duration:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['avg_interactions_duration'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart1" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-white shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Avg Hold Duration Card')" style="{{ in_array('Avg Hold Duration Card', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Avg Hold Duration Card', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Avg Hold Duration Card', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Avg Hold Duration Card')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Avg Hold Duration Card')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Hold Duration Card' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Avg. Hold Time:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['avg_hold_time'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart2" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color1 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Duration < 2 Minutes')" style="{{ in_array('Duration < 2 Minutes', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Duration < 2 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Duration < 2 Minutes', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Duration < 2 Minutes')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Duration < 2 Minutes')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Duration < 2 Minutes' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Duration < 2 Minutes:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['short_call_duration_count'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart3" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color2 shadow-sm custom-card-col swiper-slide " wire:click="getChartData('Duration > 8 Minutes')" style="{{ in_array('Duration > 8 Minutes', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Duration > 8 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Duration > 8 Minutes', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Duration > 8 Minutes')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Duration > 8 Minutes')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Duration > 8 Minutes' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Duration > 8 Minutes:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['long_call_duration_count'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart4" style="width: 180px !important;"></canvas>

                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color3 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Hold Duration > 2 Minutes')" style="{{ in_array('Hold Duration > 2 Minutes', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Hold Duration > 2 Minutes', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Hold Duration > 2 Minutes', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Hold Duration > 2 Minutes')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Hold Duration > 2 Minutes')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Hold Duration > 2 Minutes' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Hold > 2 Minutes:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['long_hold_duration_count'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart5" style="width: 180px !important;"></canvas>

                    </div>
                </div>
            </div>


            {{-- Total Hold Time --}}
            <div class="card rounded-3 bg-color3 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Hold Time')" style="{{ in_array('Total Hold Time', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Hold Time', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Hold Time', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Hold Time')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Hold Time')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Hold Time' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Hold Time:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['total_hold_time'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart12" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>




            {{-- Total Flagged Calls  --}}
            <div class="card rounded-3 bg-white shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Flaged Calls')"
                style="{{ in_array('Total Flaged Calls', $userSettings) && !$editFlag ? 'display: none;' : '' }} {{ $role == 4 ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Flaged Calls', $userSettings) ? 'opacity: .5;background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Flaged Calls', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Flaged Calls')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Flaged Calls')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif


                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Flaged Calls' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Flaged Calls:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['qaFlagsCount'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart7" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>


            <div class="card rounded-3 bg-white shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Avg Ring')" style="{{ in_array('Avg Ring', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Avg Ring', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Avg Ring', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Avg Ring')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Avg Ring')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Ring' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Avg. Ring:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['averageRing'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart8" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color1 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Ring')" style="{{ in_array('Total Ring', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Ring', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Ring', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Ring')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Ring')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Ring' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Ring:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['totalRing'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart9" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color2 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Avg Evaluation Score')" style="{{ in_array('Avg Evaluation Score', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Avg Evaluation Score', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Avg Evaluation Score', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Avg Evaluation Score')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Avg Evaluation Score')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Evaluation Score' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Avg Evaluation Score:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['avgEvaluationScore'] ?? '00:00:00' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart10" style="width: 180px !important;"></canvas>

                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-white shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Call Ender ( Agent )')" style="{{ in_array('Total Call Ender ( Agent )', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Call Ender ( Agent )', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Call Ender ( Agent )', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Call Ender ( Agent )')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Call Ender ( Agent )')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif


                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Call Ender ( Agent )' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Call Ender ( Agent ):</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['totalEnderAgent'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart13" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-white shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Call Ender ( Customer )')"
                style="{{ in_array('Total Call Ender ( Customer )', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Call Ender ( Customer )', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Call Ender ( Customer )', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Call Ender ( Customer )')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Call Ender ( Customer )')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif


                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Call Ender ( Customer )' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Call Ender ( Customer ):</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['totalEnderCustomer'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart14" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color1 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Call Ender ( System)')"
                style="{{ in_array('Total Call Ender ( System)', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Call Ender ( System)', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Call Ender ( System)', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Call Ender ( System)')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Call Ender ( System)')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Call Ender ( System)' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Call Ender ( System):</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['totalEnderSystem'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart15" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>

            <div class="card rounded-3 bg-color3 shadow-sm custom-card-col swiper-slide" wire:click="getChartData('Total Evaluation')" style="{{ in_array('Total Evaluation', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Evaluation', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Evaluation', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Evaluation')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Evaluation')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <div class="text-muted">
                        Statistics
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Evaluation' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                    </div>
                </div>
                <div class="color">
                    <strong>Total Evaluation:</strong>
                </div>
                <div class="d-flex flex-row justify-content-between mt-3">
                    <div class="mt-3">
                        <div>
                            <h3><strong>{{ $dataPage['countEvaluation'] ?? '0' }}</strong></h3>
                        </div>
                        {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                    </div>
                    <div wire:ignore>
                        {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                        <canvas class="myChart4" id="chart11" style="width: 180px !important;"></canvas>
                    </div>
                </div>
            </div>


        </x-swipe>

    </div>
    <div class="row parent-cards m-0" style="@if ($page != 'pageTwe') opacity: 0;position: absolute; @endif">


        <div class="card rounded-3 bg-color3 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Hold Time')" style="{{ in_array('Total Hold Time', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Hold Time', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Hold Time', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Hold Time')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Hold Time')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Hold Time' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Hold Time:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['total_hold_time'] ?? '00:00:00' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart12" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Handled Calls')"
            style="{{ in_array('Total Handled Calls', $userSettings) && !$editFlag ? 'display: none;' : '' }} {{ $role != 4 ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Handled Calls', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Handled Calls', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Handled Calls')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Handled Calls')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif


            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Handled Calls' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Handled Calls:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['totalHandledCalls'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart6" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Flaged Calls')"
            style="{{ in_array('Total Flaged Calls', $userSettings) && !$editFlag ? 'display: none;' : '' }} {{ $role == 4 ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Flaged Calls', $userSettings) ? 'opacity: .5;background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Flaged Calls', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Flaged Calls')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Flaged Calls')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif


            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Flaged Calls' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Flaged Calls:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['qaFlagsCount'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart7" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Avg Ring')" style="{{ in_array('Avg Ring', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Avg Ring', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Avg Ring', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Avg Ring')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Avg Ring')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Ring' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Avg. Ring:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['averageRing'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart8" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-color1 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Ring')" style="{{ in_array('Total Ring', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Ring', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Ring', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Ring')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Ring')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Ring' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Ring:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['totalRing'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart9" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-color2 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Avg Evaluation Score')" style="{{ in_array('Avg Evaluation Score', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Avg Evaluation Score', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Avg Evaluation Score', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Avg Evaluation Score')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Avg Evaluation Score')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Avg Evaluation Score' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Avg Evaluation Score:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['avgEvaluationScore'] ?? '00:00:00' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart10" style="width: 180px !important;"></canvas>

                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Call Ender ( Agent )')"
            style="{{ in_array('Total Call Ender ( Agent )', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Call Ender ( Agent )', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Call Ender ( Agent )', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Call Ender ( Agent )')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Call Ender ( Agent )')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif


            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Call Ender ( Agent )' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Call Ender ( Agent ):</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['totalEnderAgent'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart13" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Call Ender ( Customer )')"
            style="{{ in_array('Total Call Ender ( Customer )', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Call Ender ( Customer )', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Call Ender ( Customer )', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Call Ender ( Customer )')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Call Ender ( Customer )')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif


            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Call Ender ( Customer )' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Call Ender ( Customer ):</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['totalEnderCustomer'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart14" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-color1 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Call Ender ( System)')"
            style="{{ in_array('Total Call Ender ( System)', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Call Ender ( System)', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Call Ender ( System)', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Call Ender ( System)')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Call Ender ( System)')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Call Ender ( System)' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Call Ender ( System):</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['totalEnderSystem'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart15" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>

        <div class="card rounded-3 bg-color3 shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" wire:click="getChartData('Total Evaluation')" style="{{ in_array('Total Evaluation', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Evaluation', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Total Evaluation', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Evaluation')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Evaluation')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="d-flex flex-row justify-content-between">
                <div class="text-muted">
                    Statistics
                </div>
                <div class="text-muted">
                    <i class="fas fa-star" style="font-size:14px; {{ $cardSelected == 'Total Evaluation' ? 'color: black;' : 'color: #d9d9d9;' }}"></i>
                </div>
            </div>
            <div class="color">
                <strong>Total Evaluation:</strong>
            </div>
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="mt-3">
                    <div>
                        <h3><strong>{{ $dataPage['countEvaluation'] ?? '0' }}</strong></h3>
                    </div>
                    {{-- <div>
                            <span style="color: #01a44f">+21.01%</span>
                            <img src="{{ asset('assets/SVG/assets-v2/Arrow.svg') }}" alt="" >
                        </div> --}}
                </div>
                <div wire:ignore>
                    {{-- <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-18 111112.png') }}" alt="" > --}}
                    <canvas class="myChart4" id="chart11" style="width: 180px !important;"></canvas>
                </div>
            </div>
        </div>
    </div>




    {{-- @elseif($page == "pageTwe") --}}

    {{-- @endif --}}
    {{-- </div> --}}
</div>

{{-- second horizontal row  --}}
<div class="section-tow px-0 col-12 p-4 pt-0 mt-1 pb-2" style="@if ($page != 'pageOne') display:none @endif">

    <div class="row parent-cards m-0">

        <div class="card-2 rounded-3 bg-white pb-0 shadow-sm col-12 col-sm-6 col-md-6 col-lg-6 my-2  position-relative" style="{{ in_array('Chart Line', $userSettings) && !$editFlag ? 'display: none;' : '' }}
        {{ $editFlag && in_array('Chart Line', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Chart Line', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Chart Line')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Chart Line')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="col-12  d-flex flex-row justify-content-between border-buttom">
                <div class="col-8">
                    <div>
                        <div class="text-muted">
                            <h5>Statistics</h5>
                        </div>
                        <div class="color">
                            <h4 class="mb-0"><strong>{{ $cardSelected ?? '' }}</strong></h4>
                        </div>
                    </div>
                </div>
                <div class="col-4 text-end">
                    <div>
                        <div class="color">
                            <strong></strong>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <canvas id="chartCanvas" class="w-100" style=""></canvas>
            </div>
        </div>

        <div class="card rounded-3 bg-white shadow-sm col-12 col-sm-6 col-md-4 col-lg-3 my-2 custom-col" style="border:none !important;{{ in_array('Chart Circle', $userSettings) && !$editFlag ? 'display: none;' : '' }}
        {{ $editFlag && in_array('Chart Circle', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
            @if ($editFlag)
                @if (in_array('Chart Circle', $userSettings))
                    <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Chart Circle')">
                        <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                    </div>
                @else
                    <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Chart Circle')">
                        <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                    </div>
                @endif
            @endif
            <div class="col-12  d-flex flex-row justify-content-between border-buttom">
                <div class="col-6">
                    <div>
                        <div class="color">
                            Inbound
                        </div>
                        <div class="color">
                            Calls:
                        </div>
                        <div class="t">
                            <h3 class="mb-0"><strong>{{ $dataPage['total_inbound'] ?? '0' }}</strong></h3>
                        </div>
                    </div>
                </div>
                <div class="col-6 text-end">
                    <div>
                        <div class="color">
                            Outbound
                        </div>
                        <div class="color">
                            Calls:
                        </div>
                        <div class="t">
                            <h3 class="mb-0"><strong>{{ $dataPage['total_outbound'] ?? '0' }}</strong></h3>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div class="d-flex justify-content-center">
                    <canvas id="doughnutChart" width="200" height="200"></canvas>
                </div>
            </div>
        </div>


        <div class="card bg-white col-12 col-sm-6 col-md-4 px-0 col-lg-3  d-flex flex-column justify-content-between position-relative" style="border:none !important;">
            <!-- First Card Section -->
            <div class="rounded-3 bg-white shadow-sm card-2 row g-0 justify-content-between align-items-center mb-4" style="{{ in_array('Total Interactions', $userSettings) && !$editFlag ? 'display: none;' : '' }}
            {{ $editFlag && in_array('Total Interactions', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Interactions', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Interactions')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Interactions')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <!-- Left Section -->
                <div class="col-5 text-center text-md-start">
                    <div class="text-muted mb-2 text-start">Statistics</div>
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <div class="color">
                                <strong>Total</strong>
                            </div>
                            <div class="color">
                                <strong>Interactions:</strong>
                            </div>
                        </div>
                        <div class="text-center">
                            <h1 class="mb-0"><strong>{{ $dataPage['total_interactions'] ?? '0' }}</strong>
                            </h1>
                        </div>
                    </div>
                </div>
                <!-- Right Section -->
                <div class="col-7 d-flex justify-content-center justify-content-md-end mt-3 mt-md-0">
                    <img src="{{ asset('assets/SVG/assets-v2/Screenshot 2024-11-19 113005.png') }}" alt="Statistics Icon" class="img-fluid w-100">
                </div>
            </div>

            <!-- Second Card Section -->
            <div class="rounded-3 bg-white shadow-sm card-2 row g-0 justify-content-between align-items-center position-relative" style="{{ in_array('Total Duration', $userSettings) && !$editFlag ? 'display: none;' : '' }}
              {{ $editFlag && in_array('Total Duration', $userSettings) ? 'opacity: .5; background-color: #f3f3f3 !important;' : '' }}">
                @if ($editFlag)
                    @if (in_array('Total Duration', $userSettings))
                        <div class="p-1 circle-x-y" style="background: #a6ffd1;" wire:click="add('Total Duration')">
                            <i class="fa fa-check" style="font-size:16px; color:#01a44f;"></i>
                        </div>
                    @else
                        <div class="p-1 circle-x-y" style="background: #ffd7d8;" wire:click="remove('Total Duration')">
                            <i class="fa fa-close" style="font-size:16px; color:red;"></i>
                        </div>
                    @endif
                @endif
                <div class="d-flex flex-row justify-content-between">
                    <!-- Left Section -->
                    <div class="col-6 text-center text-md-start">
                        <div class="text-muted mb-2 text-start">Statistics</div>
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="color">
                                <strong>Total Duration:</strong>
                            </div>
                        </div>
                    </div>
                    <!-- Right Section -->
                    <div class="col-6 d-flex justify-content-start mt-3 mt-md-0 align-items-center">
                        <div class="text-center">
                            <h1 class="mb-0">
                                <strong>{{ $dataPage['total_call_duration'] ?? '00:00:00' }}</strong>
                            </h1>
                        </div>
                    </div>
                </div>
                <div class="col-12 d-flex justify-content-center justify-content-md-end mt-3 mt-md-0">
                    <img src="{{ asset('assets/SVG/assets-v2/pink-line.JPG') }}" alt="Statistics Icon" class="img-fluid w-100">
                </div>
            </div>
        </div>






    </div>
</div>
