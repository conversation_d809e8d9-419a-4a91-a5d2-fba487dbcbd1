<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;

class UserExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $filter_name;
    protected $filter_agent_id;
    protected $filter_user_group;
    protected $filter_role;
    protected $filter_terminated;
    protected $filter_enabled;

    public function __construct($filter_name, $filter_agent_id, $filter_user_group, $filter_role, $filter_terminated, $filter_enabled)
    {
        $this->filter_name = $filter_name;
        $this->filter_agent_id = $filter_agent_id;
        $this->filter_user_group = $filter_user_group;
        $this->filter_role = $filter_role;
        $this->filter_terminated = $filter_terminated;
        $this->filter_enabled = $filter_enabled;
    }
    /**
     * @return \Illuminate\Support\Collection
     */

    public function collection()
    {
        $query = User::query();

        if ($this->filter_name) {
            $query->where('full_name', 'like', "%{$this->filter_name}%");
        }

        if ($this->filter_agent_id) {
            $query->where('agent_id', 'like', "%{$this->filter_agent_id}%");
        }

        if ($this->filter_user_group) {
            $query->where('user_group_id', $this->filter_user_group);
        }

        if ($this->filter_role) {
            $query->where('role', $this->filter_role);
        }

        if ($this->filter_terminated !== null) {
            $query->where('terminated', $this->filter_terminated);
        }

        if ($this->filter_enabled !== null) {
            $query->where('enabled', $this->filter_enabled);
        }

        $n = 1;
        return $query->get()->map(function ($user) use (&$n) {
                return [
                    'ID' => $n++,
                    'Full Name' => $user->full_name,
                    'Role' => $this->getRoleName($user->role),
                    'Username' => $user->username,
                    'Organization' => $user->organization ? $user->organization->name : null,
                    'Group' => $user->role == 4 ? ($user->userGroup ? $user->userGroup->name : null) : 'Multi',
                    'Terminated' => $user->terminated ? 'Yes' : 'No',
                    'Created At' => $user->created_at,
                ];
            });
    }

    public function headings(): array
    {
        return [
            '#',
            'Full Name',
            'Role',
            'Username',
            'Organization',
            'Group',
            'Terminated',
            'Created At',
        ];
    }

    private function getRoleName($roleId)
    {
        // You can define a mapping of role IDs to role names here
        $roles = [
            1 => 'Admin',
            2 => 'Supervisor',
            4 => 'Agent',
            5 => 'Quality',
            6 => 'Client',
        ];

        return $roles[$roleId] ?? 'Unknown';
    }
}
