@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Analytics')

@section('style')
    <style>
        .header{

        }
        .form-control , .form-select , .dropdown-toggle-style{
            background-color: #eff3f4 !important;
            color: #40798c !important;
            border: none !important;
            height: 40px;
        }

        .paginationTable{
            color: #40798c;
        }
        .dropdown-toggle-style{
            width: 100%;
        }
        .dropdown-toggle-style:hover{
            color: #40798c !important;
        }
        .color{
            color: #40798c !important;
        }
        .dropdown-item:hover {
            color: white !important;
            background-color: #00a34e !important;
        }
        .bg-color{
            background-color: #eff3f4 !important;
        }
        small{
            color: #40798c !important;
        }
        .bg-purple {
            background-color: #eed7ff;
        }
        .bg-purple strong {
            color: #c577ff;
        }
        .table-borderless td {
            border: none;
        }
        .main-row {

            border-radius: 0.5rem;
        }
        .details-row {

            overflow-y: auto;
        }
        /* Target odd rows and apply red background color */
        .table-striped tbody tr:nth-child(odd) td {
            background-color: #eff3f4 !important;  /* Red for odd rows */
            color: black !important;  /* Optional: Text color for contrast */
        }
        .table-striped tbody tr:nth-child(even) td {
            background-color: white !important;  /* Yellow for even rows */
            color: black !important;  /* Optional: Text color for contrast */
        }
        .table-striped > tbody > tr:nth-of-type(odd) > * {
            --bs-table-bg-type: unset !important;
            background-color: transparent !important;
        }
        /* Override even row styles to remove background */
        .table-striped > tbody > tr:nth-of-type(even) > * {
            --bs-table-bg-type: unset !important;
            background-color: transparent !important;
        }
        .main-row {
            border-radius: 0.5rem; /* Optional if you want a universal radius */
            overflow: hidden; /* Ensures corners are rounded */
        }

        .rounded-start {
            border-top-left-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
        }

        .rounded-end {
            border-top-right-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
        }

        .filter{
            /* padding: 0px !important;
            margin: 0px !important; */
        }
        .table-groups{
            /* background-color: #eff3f4 !important; */
        }
        .table-responsive {
            box-shadow: none !important;
            border-radius: 0 !important;
            border: none !important;
            max-height: 115px;
        }
        .parent-cards{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
        .card{
            border: none !important;
            padding: 10px 20px;
        }
        .card-2{
            border: none !important;
            padding-left: 40px !important;
            padding-right: 40px !important;
            padding: 2%;

        }
        @media (min-width: 1200px) {
            .custom-col {
                width: 19% !important;
            }
            .custom-col-n{
                width: 39.5% !important;
            }

        }
        .bg-color1{
            background-color: #faf3ff !important;
        }
        .bg-color2{
            background-color: #fffbf3 !important;
        }
        .bg-color3{
            background-color: #f3f8ff !important;
        }
        .border-buttom{
            border-bottom: 3px solid #e5e5ef;
            padding-bottom: 10px !important;
        }
        .card{
            border: 1px solid #f2f2f2 !important;
        }
        .group{
          cursor: pointer;
        }
        .circle-x-y{
          background: #ffd7d8;
          position: absolute;
          border-radius: 50%;
          top: -10px;
          right: -10px;
          cursor: pointer;
          width: 23px !important;
        }
        .zoom-out {
            /* -webkit-transform: scale(0.9);
            -moz-transform: scale(0.9);
            -ms-transform: scale(0.9);
            -o-transform: scale(0.9);
            transform: scale(0.9);
            transform-origin: top left; */
            zoom: 0.76;
        }
    </style>
@endsection

@section('content')

    <div class="container-fluid">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <livewire:analytics-new>
     </div>

@endsection


<script type="module">
    window.addEventListener('close-modal', event => {
        document.getElementById('closeCustomDate').click()
    });
    window.addEventListener('style-row', event => {
        let groupId = event.detail[0].groupId; // Access groupId from the event

        // Get all rows with the 'main-row' class
        let rows = document.querySelectorAll('.main-row');

        // Remove the border from all rows
        rows.forEach(row => {
            row.style.border = ''; // Reset border
        });

        // Apply the border style to the specific row
        let rowElement = document.getElementById('row' + groupId);
        if (rowElement) {
            rowElement.style.border = '1px solid green'; // Apply the border style
        }
    });


    document.addEventListener('DOMContentLoaded', function () {
        const toggleButton = document.querySelector('.toggle-details');
        const tableResponsive = document.querySelector('.table-responsive');
        let isExpanded = false;

        toggleButton.addEventListener('click', function() {
            isExpanded = !isExpanded;

            // Adjust the max-height of the table-responsive class
            if(isExpanded) {
                tableResponsive.style.maxHeight = '250px';
            } else {
                tableResponsive.style.maxHeight = '115px';
            }

            // Update the button text and icon
            toggleButton.innerHTML = isExpanded ?
                'Minimize <i class="ms-2 fa-solid fa-caret-up"></i>' :
                'Expand <i class="ms-2 fa-solid fa-caret-down"></i>';
        });
    });


</script>
<script type="module">
    document.addEventListener('DOMContentLoaded', function () {
      const ctx = document.getElementById('doughnutChart').getContext('2d');

      // Initialize the chart with default data
      const chart = new Chart(ctx, {
          type: 'doughnut',
          data: {
              labels: ['Inbound Calls', 'Outbound Calls'], // Labels for hover titles
              datasets: [{
                  data: [0, 0], // Default values
                  backgroundColor: ['#01A44F', '#EFF3F4'],
                  hoverOffset: 0,
                  borderWidth: 5
              }]
          },
          options: {
              responsive: true,
              cutout: '80%',
              rotation: -90,
              circumference: 180,
              plugins: {
                  tooltip: {
                      enabled: true,
                      callbacks: {
                          label: function (tooltipItem) {
                              const dataset = tooltipItem.dataset;
                              const dataIndex = tooltipItem.dataIndex;
                              const value = dataset.data[dataIndex];
                              return `${chart.data.labels[dataIndex]}: ${value}%`;
                          }
                      }
                  },
                  legend: { display: false }
              },
              interaction: { mode: 'nearest', intersect: true }
          },
          plugins: [{
              id: 'customLabel',
              beforeDraw(chart) {
                  const { ctx, chartArea } = chart;
                  const centerX = (chartArea.left + chartArea.right) / 2;
                  const centerY = chartArea.bottom;

                  const inboundPercentage = chart.data.datasets[0].data[0];
                  const chartTitle = chart.data.labels ? chart.data.labels[0] : 'Calls'; // Use labels for the title

                  // Draw percentage text
                  ctx.save();
                  ctx.font = 'bold 40px Poppins';
                  ctx.fillStyle = '#1F363D';
                  ctx.textAlign = 'center';
                  ctx.fillText(`${inboundPercentage}%`, centerX, centerY - 80);

                  // Draw label
                  ctx.font = '12px Poppins';
                  ctx.fillStyle = '#40798C';
                  ctx.fillText(chartTitle, centerX, centerY - 55);
                  ctx.restore();
              }
          }]
      });

      // Listen for Livewire event to update chart
      window.addEventListener('update-chart', event => {
          const { chartTitle, percentage_inbound, percentage_outbound } = event.detail[0];

          // Update chart data
          chart.data.datasets[0].data = [percentage_inbound, 100 - percentage_inbound];
          chart.data.labels = ['Inbound Calls', 'Outbound Calls']; // Update labels dynamically if needed

          // Redraw chart
          chart.update();
      });
  });

</script>
<script>
    window.addEventListener('closeModal', () => {
        document.querySelector('#close').click();
        document.querySelector('#closeEdit').click();
    });
</script>
<script type="module">
    const canvasElements = document.querySelectorAll('.myChart3'); // Select all canvases with the 'myChart3' class
    const borderColors = [
        'rgba(75, 192, 192, 1)', // Color for the first canvas
        'rgba(255, 99, 132, 1)', // Color for the second canvas
        'rgba(54, 162, 235, 1)', // Color for the third canvas
        'rgba(153, 102, 255, 1)', // Color for the fourth canvas
        'rgba(255, 159, 64, 1)'   // Color for the fifth canvas
    ];

    const easingTypes = [
        'easeInSine',  // Easing for the first canvas
        'easeOutBounce',  // Easing for the second canvas
        'easeInQuad',  // Easing for the third canvas
        'easeOutExpo',  // Easing for the fourth canvas
        'easeInOutCubic'   // Easing for the fifth canvas
    ];

    const initializedCharts = [];

    canvasElements.forEach((canvas, index) => {
        // Check if the chart for the canvas is already initialized
        if (initializedCharts[index]) return; // Skip if already initialized

        const ctx = canvas.getContext('2d');
        const data = {
            labels: ["", "", "", "", "", "", "", "", "", ""], // Dummy labels (hidden)
            datasets: [{
                data: [0, 5, 15, 10, 20 ,30 ,15 ,30 ,10 ,15 ,0], // Data for the line
                borderColor: borderColors[index % borderColors.length],
                borderWidth: 3, // Line thickness
                tension: 0.0, // Smooth curve
                pointRadius: 0 // No points
            }]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false // No legend
                    }
                },
                animation: {
                    duration: 1000,
                    easing: easingTypes[index],  // Apply different easing type based on index
                },
                scales: {
                    x: {
                        display: false // Hide x-axis
                    },
                    y: {
                        display: false // Hide y-axis
                    }
                }
            }
        };

        const chart = new Chart(ctx, config); // Create chart for each canvas
        initializedCharts[index] = chart; // Mark the chart as initialized
    });
</script>


<script type="module">
    document.addEventListener('DOMContentLoaded', function() {
      // Default chart settings
      const data = {
        labels: [], // To be filled with dates
        datasets: [{
          cubicInterpolationMode: 'monotone',
          data: [], // To be filled with daily average durations in minutes
          label: 'Avg. Duration (minutes)', // This is what shows up in the legend
          fill: true,
          tension: 0.4,
          pointHoverRadius: 6,
          pointHoverBackgroundColor: '#1f363d',
          pointRadius: 0,
          pointBackgroundColor: 'transparent',
          borderColor: '#01a44f',
          backgroundColor: function(context) {
            const bgColor = ['rgb(179, 228, 202)', 'rgba(181, 228, 203, 0)'];
            if (!context.chart.chartArea) {
              return;
            }
            const { ctx, chartArea: { top, bottom } } = context.chart;
            const gradientBg = ctx.createLinearGradient(0, top, 0, bottom);
            gradientBg.addColorStop(0, bgColor[0]);
            gradientBg.addColorStop(0.85, bgColor[1]);
            return gradientBg;
          }
        }]
      };

      const ctx = document.getElementById('chartCanvas').getContext('2d');
      const chartInstance = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false
          },
          plugins: {
            legend: {
              display: false // This disables the legend (toggle) visibility
            }
          },
          scales: {
            y: {
              display: false,
              grid: {
                display: false, // Disables the grid lines on the y-axis
              }
            },
            x: {
              ticks: {
                font: {
                  color: 'white', // Text color white
                },
                color: 'green',
                padding: 10, // Add padding for better visibility
                backgroundColor: 'green', // Set background color of tick labels to green
                borderColor: 'green', // Optional: Add border color around tick labels
                borderWidth: 1 // Optional: Set border width around tick labels
              },
              grid: {
                display: false, // Disables the grid lines on the x-axis
              }
            }
          }
        }
      });

      // Listen for the update-chart2 event
      window.addEventListener('update-chart2', function(event) {
        const { chartTitle, dates, avgDurations, isDuration = false } = event.detail[0];

        // Update chart title and data
        chartInstance.data.labels = dates; // Set dates as x-axis labels
        chartInstance.data.datasets[0].data = avgDurations; // Set the avg durations for y-axis
        chartInstance.data.datasets[0].label = chartTitle; // Set the chart title

        // Update the chart with new data
        chartInstance.options.scales.y.ticks.callback = function(value) {
              return isDuration ? value.toFixed(2) : value.toFixed(0); // Show durations with 2 decimals or counts as integers
          };
        chartInstance.update();
      });
    });
  </script>
