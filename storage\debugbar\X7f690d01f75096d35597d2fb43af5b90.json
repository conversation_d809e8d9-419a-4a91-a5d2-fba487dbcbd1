{"__meta": {"id": "X7f690d01f75096d35597d2fb43af5b90", "datetime": "2025-07-22 15:51:42", "utime": 1753188702.3866, "method": "GET", "uri": "/analytics-new", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[15:51:42] LOG.emergency: Unable to create configured logger. Using emergency logger. {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "emergency", "time": 1753188702.073455, "xdebug_link": null, "collector": "log"}, {"message": "[15:51:42] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php on line 722", "message_html": null, "is_string": false, "label": "warning", "time": 1753188702.074238, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.299048, "end": 1753188702.386655, "duration": 1.0876071453094482, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": **********.299048, "relative_start": 0, "end": **********.654917, "relative_end": **********.654917, "duration": 0.3558690547943115, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.654936, "relative_start": 0.35588812828063965, "end": 1753188702.386658, "relative_end": 2.86102294921875e-06, "duration": 0.7317218780517578, "duration_str": "732ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30445096, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "analytics-new", "param_count": null, "params": [], "start": **********.823598, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/analytics-new.blade.phpanalytics-new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Fanalytics-new.blade.php&line=1", "ajax": false, "filename": "analytics-new.blade.php", "line": "?"}}, {"name": "livewire.analytics-new", "param_count": null, "params": [], "start": 1753188702.276045, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/analytics-new.blade.phplivewire.analytics-new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Fanalytics-new.blade.php&line=1", "ajax": false, "filename": "analytics-new.blade.php", "line": "?"}}, {"name": "components.swipe", "param_count": null, "params": [], "start": 1753188702.284336, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/components/swipe.blade.phpcomponents.swipe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Fcomponents%2Fswipe.blade.php&line=1", "ajax": false, "filename": "swipe.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": 1753188702.315157, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "livewire-alert::components.scripts", "param_count": null, "params": [], "start": 1753188702.370047, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\vendor\\jantinnerezo\\livewire-alert\\src/../resources/views/components/scripts.blade.phplivewire-alert::components.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Fjantinnerezo%2Flivewire-alert%2Fresources%2Fviews%2Fcomponents%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}, {"name": "livewire-alert::components.flash", "param_count": null, "params": [], "start": 1753188702.372304, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\vendor\\jantinnerezo\\livewire-alert\\src/../resources/views/components/flash.blade.phplivewire-alert::components.flash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Fjantinnerezo%2Flivewire-alert%2Fresources%2Fviews%2Fcomponents%2Fflash.blade.php&line=1", "ajax": false, "filename": "flash.blade.php", "line": "?"}}]}, "route": {"uri": "GET analytics-new", "middleware": "web, auth, checkEnabledUser, checkPasswordPolicy, resetNextLogin", "controller": "App\\Http\\Controllers\\AnalyticsController@indexNew", "namespace": null, "prefix": "", "where": [], "as": "analytics-new", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FHttp%2FControllers%2FAnalyticsController.php&line=94\" onclick=\"\">app/Http/Controllers/AnalyticsController.php:94-97</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.29244, "accumulated_duration_str": "292ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.735389, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "ilog", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 36 limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.772636, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ilog", "start_percent": 0, "width_percent": 1.59}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 397}, {"index": 21, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 22, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.891738, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:397", "source": "app/Livewire/AnalyticsNew.php:397", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=397", "ajax": false, "filename": "AnalyticsNew.php", "line": "397"}, "connection": "ilog", "start_percent": 1.59, "width_percent": 1.529}, {"sql": "SELECT AVG(quality_percentage) AS avg_quality, COUNT(a.id) AS total_qa_submissions\nFROM evaluation_submissions a\nINNER JOIN evaluations b ON a.evaluation_id = b.id\nWHERE\na.created_at BETWEEN '2025-07-21 15:51:41' AND '2025-07-22 15:51:41'\nAND\nb.organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 632}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 13, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.911168, "duration": 0.016239999999999997, "duration_str": "16.24ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:632", "source": "app/Livewire/AnalyticsNew.php:632", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=632", "ajax": false, "filename": "AnalyticsNew.php", "line": "632"}, "connection": "ilog", "start_percent": 3.119, "width_percent": 5.553}, {"sql": "SELECT COUNT(call_id) AS totalCalls,call_type FROM interactions   WHERE arrival_time BETWEEN '2025-07-21 15:51:41' AND '2025-07-22 15:51:41'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50) GROUP BY call_type", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 642}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 13, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.935727, "duration": 0.01423, "duration_str": "14.23ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:642", "source": "app/Livewire/AnalyticsNew.php:642", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=642", "ajax": false, "filename": "AnalyticsNew.php", "line": "642"}, "connection": "ilog", "start_percent": 8.672, "width_percent": 4.866}, {"sql": "SELECT\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS avg_duration,\nTIME_FORMAT(SEC_TO_TIME(AVG(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS avg_hold_duration,\nAVG(ring) AS avg_ring,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(hold_duration))), '%H:%i:%s') AS total_hold_time,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,\nTIME_FORMAT(SEC_TO_TIME(SUM(TIME_TO_SEC(call_duration))), '%H:%i:%s') AS total_call_duration,\nSUM(call_duration) AS total_ring,\nCOUNT(call_id) AS total_calls\nFROM interactions\nWHERE arrival_time BETWEEN '2025-07-21 15:51:41' AND '2025-07-22 15:51:41'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 649}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 13, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.9564211, "duration": 0.02836, "duration_str": "28.36ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:649", "source": "app/Livewire/AnalyticsNew.php:649", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=649", "ajax": false, "filename": "AnalyticsNew.php", "line": "649"}, "connection": "ilog", "start_percent": 13.538, "width_percent": 9.698}, {"sql": "SELECT\nCOUNT(CASE WHEN call_duration < '00:02:00' THEN 1 END) AS less_than_2_minutes,\nCOUNT(CASE WHEN call_duration >= '00:08:00' THEN 1 END) AS greater_or_equal_8_minutes,\nCOUNT(CASE WHEN hold_duration >= '00:02:00' THEN 1 END) AS greater_or_equal_2_minutes_hold\nFROM interactions\nWHERE arrival_time BETWEEN '2025-07-21 15:51:41' AND '2025-07-22 15:51:41'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 667}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 13, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.991701, "duration": 0.02953, "duration_str": "29.53ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:667", "source": "app/Livewire/AnalyticsNew.php:667", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=667", "ajax": false, "filename": "AnalyticsNew.php", "line": "667"}, "connection": "ilog", "start_percent": 23.236, "width_percent": 10.098}, {"sql": "SELECT\nCOUNT(a.interaction_id) AS totalFlags\nFROM interaction_qa_flag a INNER JOIN interactions b\nON a.interaction_id = b.id AND\narrival_time BETWEEN '2025-07-21 15:51:41' AND '2025-07-22 15:51:41'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 682}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 13, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753188702.028079, "duration": 0.0159, "duration_str": "15.9ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:682", "source": "app/Livewire/AnalyticsNew.php:682", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=682", "ajax": false, "filename": "AnalyticsNew.php", "line": "682"}, "connection": "ilog", "start_percent": 33.333, "width_percent": 5.437}, {"sql": "SELECT\nCOUNT(*) AS totalaiFlags\nFROM interactions WHERE\narrival_time BETWEEN '2025-07-21 15:51:41' AND '2025-07-22 15:51:41'\nAND ai_flag = '1'\nAND organization_id IN (4,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50)", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41", "4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 693}, {"index": 12, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3303}, {"index": 13, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753188702.051595, "duration": 0.00901, "duration_str": "9.01ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:693", "source": "app/Livewire/AnalyticsNew.php:693", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=693", "ajax": false, "filename": "AnalyticsNew.php", "line": "693"}, "connection": "ilog", "start_percent": 38.77, "width_percent": 3.081}, {"sql": "select * from `organizations` where `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2214}, {"index": 17, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3304}, {"index": 18, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753188702.075723, "duration": 0.01524, "duration_str": "15.24ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2214", "source": "app/Livewire/AnalyticsNew.php:2214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2214", "ajax": false, "filename": "AnalyticsNew.php", "line": "2214"}, "connection": "ilog", "start_percent": 41.851, "width_percent": 5.211}, {"sql": "select * from `user_groups` where `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2215}, {"index": 17, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3304}, {"index": 18, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753188702.0997589, "duration": 0.03585, "duration_str": "35.85ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2215", "source": "app/Livewire/AnalyticsNew.php:2215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2215", "ajax": false, "filename": "AnalyticsNew.php", "line": "2215"}, "connection": "ilog", "start_percent": 47.063, "width_percent": 12.259}, {"sql": "select * from `organizations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 2216}, {"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3304}, {"index": 17, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1753188702.1409159, "duration": 0.03449, "duration_str": "34.49ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:2216", "source": "app/Livewire/AnalyticsNew.php:2216", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=2216", "ajax": false, "filename": "AnalyticsNew.php", "line": "2216"}, "connection": "ilog", "start_percent": 59.322, "width_percent": 11.794}, {"sql": "select `parameter_name` from `analytics_parameters_activitions` where `user_id` = 36 and `is_active` = 0", "type": "query", "params": [], "bindings": ["36", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 89}, {"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": 1753188702.1871371, "duration": 0.046799999999999994, "duration_str": "46.8ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:89", "source": "app/Livewire/AnalyticsNew.php:89", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=89", "ajax": false, "filename": "AnalyticsNew.php", "line": "89"}, "connection": "ilog", "start_percent": 71.115, "width_percent": 16.003}, {"sql": "select\nCOUNT(*) as total_calls,\nSUM(CASE WHEN call_ender = 'Customer' THEN 1 ELSE 0 END) as customer_disconnections,\nSUM(CASE WHEN call_ender = 'Agent' THEN 1 ELSE 0 END) as agent_disconnections,\nSUM(CASE WHEN call_ender NOT IN ('Agent', 'Customer') THEN 1 ELSE 0 END) as system_disconnections\nfrom `interactions` where `arrival_time` between '2025-07-21 15:51:41' and '2025-07-22 15:51:41' limit 1", "type": "query", "params": [], "bindings": ["2025-07-21 15:51:41", "2025-07-22 15:51:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3374}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753188702.242059, "duration": 0.005809999999999999, "duration_str": "5.81ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:3374", "source": "app/Livewire/AnalyticsNew.php:3374", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=3374", "ajax": false, "filename": "AnalyticsNew.php", "line": "3374"}, "connection": "ilog", "start_percent": 87.119, "width_percent": 1.987}, {"sql": "select * from `organizations` where `id` in (4, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50)", "type": "query", "params": [], "bindings": ["4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 124}, {"index": 16, "namespace": null, "name": "app/Livewire/AnalyticsNew.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\AnalyticsNew.php", "line": 3399}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": 1753188702.2561681, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "AnalyticsNew.php:124", "source": "app/Livewire/AnalyticsNew.php:124", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FAnalyticsNew.php&line=124", "ajax": false, "filename": "AnalyticsNew.php", "line": "124"}, "connection": "ilog", "start_percent": 89.105, "width_percent": 0.451}, {"sql": "select exists(select * from `permissions` inner join `permission_user` on `permissions`.`id` = `permission_user`.`permission_id` where `permission_user`.`user_id` = 36 and `permission_id` = 1) as `exists`", "type": "query", "params": [], "bindings": ["36", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/layouts/app.blade.php", "line": 193}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753188702.320238, "duration": 0.0273, "duration_str": "27.3ms", "memory": 0, "memory_str": null, "filename": "layouts.app:193", "source": "view::layouts.app:193", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=193", "ajax": false, "filename": "app.blade.php", "line": "193"}, "connection": "ilog", "start_percent": 89.557, "width_percent": 9.335}, {"sql": "select exists(select * from `permissions` inner join `permission_user` on `permissions`.`id` = `permission_user`.`permission_id` where `permission_user`.`user_id` = 36 and `permission_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["36", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/layouts/app.blade.php", "line": 216}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753188702.353278, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "layouts.app:216", "source": "view::layouts.app:216", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=216", "ajax": false, "filename": "app.blade.php", "line": "216"}, "connection": "ilog", "start_percent": 98.892, "width_percent": 1.108}]}, "models": {"data": {"App\\Models\\Organization": {"value": 151, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Interaction": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FInteraction.php&line=1", "ajax": false, "filename": "Interaction.php", "line": "?"}}}, "count": 153, "is_counter": true}, "livewire": {"data": {"analytics-new #LuGHYqbwYPlXHVOi6wWK": "array:4 [\n  \"data\" => array:23 [\n    \"totalCallDurationAllGroups\" => null\n    \"selected_group\" => null\n    \"disconnected_by_customer\" => 0\n    \"disconnected_by_agent\" => 0\n    \"disconnected_by_system\" => 0\n    \"accountIDFilter\" => null\n    \"accountNameFilter\" => null\n    \"dateFrom\" => Carbon\\Carbon @********** {#968\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000003c80000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-21 15:51:41.866177 Asia/Amman (+03:00)\n    }\n    \"dateTo\" => Carbon\\Carbon @********** {#967\n      #endOfTime: false\n      #startOfTime: false\n      #constructedObjectId: \"00000000000003c70000000000000000\"\n      #localMonthsOverflow: null\n      #localYearsOverflow: null\n      #localStrictModeEnabled: null\n      #localHumanDiffOptions: null\n      #localToStringFormat: null\n      #localSerializer: null\n      #localMacros: null\n      #localGenericMacros: null\n      #localFormatFunction: null\n      #localTranslator: null\n      #dumpProperties: array:3 [\n        0 => \"date\"\n        1 => \"timezone_type\"\n        2 => \"timezone\"\n      ]\n      #dumpLocale: null\n      #dumpDateProperties: null\n      date: 2025-07-22 15:51:41.866163 Asia/Amman (+03:00)\n    }\n    \"dateFromFilter\" => null\n    \"dateToFilter\" => null\n    \"dateType\" => \"Last 24 Hours\"\n    \"groupsAccount\" => null\n    \"dataPage\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => null\n      \"avg_hold_time\" => null\n      \"total_hold_time\" => null\n      \"total_interactions\" => 0\n      \"short_call_duration_count\" => 0\n      \"long_call_duration_count\" => 0\n      \"long_hold_duration_count\" => 0\n      \"total_call_duration\" => null\n      \"total_outbound\" => 0\n      \"total_inbound\" => 0\n      \"countEvaluation\" => 0\n      \"avgEvaluationScore\" => null\n      \"totalRing\" => null\n      \"averageRing\" => 0.0\n      \"totalHandledCalls\" => 0\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 0\n    ]\n    \"dataPage2\" => array:19 [\n      \"group_id\" => \"All\"\n      \"group_name\" => \"All\"\n      \"avg_interactions_duration\" => null\n      \"avg_hold_time\" => null\n      \"total_hold_time\" => null\n      \"total_interactions\" => 0\n      \"short_call_duration_count\" => 0\n      \"long_call_duration_count\" => 0\n      \"long_hold_duration_count\" => 0\n      \"total_call_duration\" => null\n      \"total_outbound\" => 0\n      \"total_inbound\" => 0\n      \"countEvaluation\" => 0\n      \"avgEvaluationScore\" => null\n      \"totalRing\" => null\n      \"averageRing\" => 0.0\n      \"totalHandledCalls\" => 0\n      \"qaFlagsCount\" => 0\n      \"aiFlagsCount\" => 0\n    ]\n    \"searchGroup\" => null\n    \"groupSelected\" => null\n    \"editFlag\" => false\n    \"userSettings\" => []\n    \"cardSelected\" => null\n    \"role\" => 2\n    \"page\" => \"pageOne\"\n    \"queryGrouFormat\" => \"%H:00\"\n  ]\n  \"name\" => \"analytics-new\"\n  \"component\" => \"App\\Livewire\\AnalyticsNew\"\n  \"id\" => \"LuGHYqbwYPlXHVOi6wWK\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/analytics-new\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "36", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753187629\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f739afb-a885-4f76-bc89-0822320e62c1\" target=\"_blank\">View in Telescope</a>", "path_info": "/analytics-new", "status_code": "<pre class=sf-dump id=sf-dump-478116411 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-478116411\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1175278877 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1175278877\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-857249775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-857249775\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-504060457 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1253 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndORVZmZm1CQWx1OVVtcUxhcW9Wbnc9PSIsInZhbHVlIjoiNEJpVnZuTkZPUmgxdk1ndVNLeXltN3hiL3lRMkpmckV4Y3ZFczI4Qm0xaXM0eldYak1IU1NYQitGUlhyVUFsODNvTWVWN3NxV2p2bEpkbEc0R1NVWDF2Q0d5dTgrQTgyQWZTNFBMQTFqY25YU1F2UldyNDhxaUNFZ1FiSSthUWpaakExbUJPM0h2cDlyMHI3YU1BaC82d09tYlRzS29EbFpwRTNYVU5GbGY0clN1bTFBTlQzekk0Wi96RUR5aUdwZWtIRFdGaENSYjBDQjlxNnlNOFdKeFg5dXZWQ0twbGNSMkRGcVFGREJ6QT0iLCJtYWMiOiJjYWYxMjVmN2NiZGM3YzJlMDVmYWQ1NjI3YzdjZGQxZmE0MmYyMTVmZDQ5MGY2NTA2NDU3ODc1NThiNTAwYWFmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxCOFpYOFByaFBhOFF6Z2NyenBEbWc9PSIsInZhbHVlIjoiQThTMFY1a0FCM2dIWHlvL1E0UmJ5UlVnZVVVWmZkdElrMWZ2T3U0ODAvbEVobzZzd2JERXBtUks3dkY1TGQxU0lFSDFXSm1nZit0c0lFNEIvcUpadllhK1djb2UyS2xrZzNEUVZ1WUJJMC9qUkZhN1lQNDIwci81NEM4SG5XeVIiLCJtYWMiOiI3OTQ1NmFlNThhOGZiZTdhZDIxY2I5YTRlMTE4YWE2NWZjNDFhZGNjZTE2ODEwOTEzMWFmYzBiOTQ5YjY2MzViIiwidGFnIjoiIn0%3D; i_log_session=eyJpdiI6ImNEVmU2Wkw5YzNKZmlHN2lraWhpeWc9PSIsInZhbHVlIjoiclhWOTVtOXlaaEZJY2Z0ZVVSZ2dFbzRBSUJuMEx6YmhCRFErQ1J0RWk3dlI5ZTJBQzVXV1NUMmx2cFRqYm5KV3NQQTVhdkRJMitkQStSMzhPbEQyN0R5cVQ1OHlDMER5UUtNSEl1c0lzZmtZNTFIeEg2Qmo2M2t1U291dXp3OUoiLCJtYWMiOiJkYzZkMGM4ODQ3NGNjM2NlMTE0MDkzNDgwZWVlNjRlNTdlNGM3ZTllZmUwMTZiOWYzMmI4YzRjZTExMDkyMmI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504060457\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1387324619 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">36|5ib4AQhtLHzATEGHqeW0FyHXea5XYBhkXcIWMbiaY723twUGMepP4FnyoU3L|$2y$12$sKmCpsW.aT14AEFPyOgHeO2lhV7BRWjK7d1ywUnOncv84uKprV4di</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>i_log_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3Bna7bWbMH5plZLa1koljiLCJBixIU0hg7L8yoS8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387324619\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1290835539 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 12:51:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6InhER0N2VDk5eFM2SDIwam1URnlQbGc9PSIsInZhbHVlIjoiRTBvZXdBaGVFR21tdm5EN0h4UWtobDR6RzdYa1Eza1dXdEZkNlRiRTl4eUhtTnBGYnlrRSt2djdZd3A5a1NocnlTZTZ6QkRCTU94VVNyZEMvc1Z0NDNzZ2RERDRvb0t0K0J1N2NINGV5RHNlQmxYeDE3ekM4Qi9XODRUWnRKR2ciLCJtYWMiOiIxYmYxMDFlYzE2YjIxYTA0MjcxNzBlYWY4ZTdhZGQ4YTk2NDUwN2Q0ZDYwMTZhMmEwODkzN2JjODhmYjRmZTdmIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:51:42 GMT; Max-Age=21600; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">i_log_session=eyJpdiI6IjN5STdDSnNSVFpqVWtDR3grSUlLVFE9PSIsInZhbHVlIjoia2Q3L2R4UzhScmFwQmFjY1NLQVlxbUJXVUVSWlpJbVpiMHFiYmZmVHdsc2ZtbE5PT2l6THo5WU5xRmdreTVUU0d2ZFlwL09iaTlEZElwN0ZJQnhVL0lZM2U4dnkxWmNxOUxVdWk0Tkg2a0JvRlpGUk4remd2RE9Bc1ZnSVVtaTkiLCJtYWMiOiJkMjM2NWZmOTU1NWE2MWY2NzI2YjAxNDhhMDFlYjE0M2VhYjMxYjk4YTRmYTIzNzMwYTJlN2U4ZTJjMmQ0MGEzIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:51:42 GMT; Max-Age=21600; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhER0N2VDk5eFM2SDIwam1URnlQbGc9PSIsInZhbHVlIjoiRTBvZXdBaGVFR21tdm5EN0h4UWtobDR6RzdYa1Eza1dXdEZkNlRiRTl4eUhtTnBGYnlrRSt2djdZd3A5a1NocnlTZTZ6QkRCTU94VVNyZEMvc1Z0NDNzZ2RERDRvb0t0K0J1N2NINGV5RHNlQmxYeDE3ekM4Qi9XODRUWnRKR2ciLCJtYWMiOiIxYmYxMDFlYzE2YjIxYTA0MjcxNzBlYWY4ZTdhZGQ4YTk2NDUwN2Q0ZDYwMTZhMmEwODkzN2JjODhmYjRmZTdmIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:51:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">i_log_session=eyJpdiI6IjN5STdDSnNSVFpqVWtDR3grSUlLVFE9PSIsInZhbHVlIjoia2Q3L2R4UzhScmFwQmFjY1NLQVlxbUJXVUVSWlpJbVpiMHFiYmZmVHdsc2ZtbE5PT2l6THo5WU5xRmdreTVUU0d2ZFlwL09iaTlEZElwN0ZJQnhVL0lZM2U4dnkxWmNxOUxVdWk0Tkg2a0JvRlpGUk4remd2RE9Bc1ZnSVVtaTkiLCJtYWMiOiJkMjM2NWZmOTU1NWE2MWY2NzI2YjAxNDhhMDFlYjE0M2VhYjMxYjk4YTRmYTIzNzMwYTJlN2U4ZTJjMmQ0MGEzIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:51:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290835539\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1793973632 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/analytics-new</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>36</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753187629</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793973632\", {\"maxDepth\":0})</script>\n"}}