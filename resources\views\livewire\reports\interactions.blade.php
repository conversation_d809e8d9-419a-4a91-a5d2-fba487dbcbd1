<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto">
                <button
                    wire:target="export"
                    wire:click="export"
                    title="Export"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                    wire:loading.attr="disabled">

                    <i
                        wire:loading.remove
                        wire:target="export"
                        class="fas fa-file-excel text-white me-2"
                        style="font-size: 20px;"></i>

                    <span
                        wire:loading.class="spinner-border spinner-border-sm"
                        wire:target="export"
                        style="width: 1rem; height: 1rem;"
                        role="status"
                        aria-hidden="true"></span>

                    <span
                        wire:loading.remove
                        wire:target="export"
                        style="font-size: 17px;">Extract Excel</span>
                </button>
            </div>
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button
                    data-bs-toggle="modal"
                    data-bs-target="#filterModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon" >
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>


    {{-- <div class="d-flex mb-1">
        <label for="" class="col-md-1 align-self-center me-1 fs-6" style="width: fit-content">Search: </label>
        <input id="searchInput" type="text" class="col-md-1 form-control mb-1 d-inline text-muted p-1" placeholder=' &#xF002;' style="font-family:Arial, FontAwesome;width:9rem" wire:model.live.debounce.300ms="searchCalls" onclick="this.placeholder=''" onblur="this.placeholder='&#xF002;'">
    </div> --}}

    <div class="parent-sections mx-3 ps-5">

        <div class="section-one">
                <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">

                    <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">

                        <thead id="thead" class="text-muted thead" style="font-size: 0.7rem">
                            <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                <th colspan="col" scope="col" class="text-center"><i class="fa-regular fa-calendar-days fa-xl" title="DateTime" style="color: white"></i></th>
                                @if (Auth::user()->parameters()->where('parameter_id', 8)->exists())
                                    <th scope="col" class="text-center">

                                        <div style="text-align: center;">
                                            <small style="display: block; font-size: 0.8rem; color: white;margin-left: 16px"><i class="fa fa-exchange" title="Call Type" title="Record" style="color: white"></i></small>
                                            <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                          </div>
                                    </th>
                                @endif
                                @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-microphone-lines fa-xl" title="Record" style="color: white"></i></th>
                                @endif
                                @if (Auth::user()->role != 4)
                                    <th scope="col" class="text-center"><i class="fa-solid fa-headphones fa-xl" title="Played By" style="color: white"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 27)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-comment-dots fa-xl" style="color: white" title="Comments"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 22)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-newspaper fa-xl" style="color: white" title="Evaluation"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 25)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-id-card fa-xl" title="Agent Name" style="color: white"></i></th>
                                @endif
                                <th scope="col" class="text-center"><i class="fa-solid fa-user-tie fa-xl" style="color: white" title="Evaluator"></i></th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-rectangle-list fa-xl" style="color: white" title="Evaluation Form"></i></th>
                                <th scope="col" class="text-center"><i class="fa-solid fa-hashtag fa-xl" style="color: white" title="User ID"></i></th>
                                @if (Auth::user()->parameters()->where('parameter_id', 4)->exists())
                                    <th scope="col" class="text-center">
                                        <div style="display: flex; justify-content: space-around; align-items: center; width: 100%; text-align: center; color: white;">
                                            <!-- Icon 1: Incoming Call -->
                                            <div style="position: relative; display: inline-block;">
                                              <small style="position: absolute; top: -14px; left: 80%; transform: translateX(-50%); font-size: 0.8rem;">
                                                <i class="fa fa-arrow-down" title="Incoming Call" style="color: white;"></i>
                                              </small>
                                              <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                            </div>
                                    </th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 6)->exists())
                                    <th scope="col" class="text-center">
                                        <div style="position: relative; display: inline-block;">
                                            <small style="position: absolute; top: -14px; left: 80%; transform: translateX(-50%); font-size: 0.8rem;">
                                              <i class="fa fa-arrow-up" title="Outgoing Call" style="color: white;"></i>
                                            </small>
                                            <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                        </div>
                                    </th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 5)->exists())
                                    <th scope="col" class="text-center">
                                        <div style="position: relative; display: inline-block;">
                                            <small style="position: absolute; top: -14px; left: 80%; transform: translateX(-50%); font-size: 0.8rem;">
                                              <i class="fa fa-exchange" title="Mixed Call" style="color: white;"></i>
                                            </small>
                                            <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                          </div>
                                    </th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 23)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-flag fa-xl" style="color: white" title="QA Flags"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 2)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-stopwatch fa-xl" style="color: white" title="Duration"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 19)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-ranking-star fa-xl" style="color: white" title="Evaluation Score"></i></th>
                                @endif
                                <th scope="col" class="text-center"><i class="fa-solid fa-tty fa-xl" title="Extension" style="color: white"></i></th>
                                @if (Auth::user()->parameters()->where('parameter_id', 29)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-people-group fa-xl" title="Group" style="color: white"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 7)->exists())
                                    <th scope="col" class="text-center">
                                          <div style="position: relative; display: inline-block;">
                                            <small style="position: absolute; top: -14px; left: 80%; transform: translateX(-50%); font-size: 0.8rem;">
                                              <i class="far fa-clock" title="Mixed Call" style="color: white;"></i>
                                            </small>
                                            <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                          </div>
                                    </th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 11)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-circle-pause fa-xl" style="color: white" title="Hold Count"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 31)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-building fa-xl" style="color: white" title="Account"></i></th>
                                @endif
                                @if (Auth::user()->parameters()->where('parameter_id', 13)->exists())
                                    <th scope="col" class="text-center"><i class="fa-solid fa-phone-volume fa-xl" style="color: white" title="Ring"></i></th>
                                @endif
                                <th scope="col" class="text-center"><i class="fa-solid fa-language fa-xl" style="color: white" title="Language"></i></th>
                                @if (Auth::user()->parameters()->where('parameter_id', 1)->exists())
                                    <th scope="col" class="text-center">
                                        <div style="text-align: center;">
                                            <small style="display: block; font-size: 0.8rem; color: white;margin-left: 16px">ID</small>
                                            <i class="fa fa-phone fa-xl" style="color: white;" title="Call ID"></i>
                                          </div>

                                    </th>
                                @endif
                                <th scope="col" class="text-center">
                                    <i class="fa-solid fa-fingerprint fa-xl" style="color: white" title="Unique ID"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="" style="font-size:0.8rem" id="tbody">
                            @forelse($records as $record)
                                <tr class="align-middle">
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->arrival_time == null || $record->arrival_time == '1970-01-01 00:00:00' ? '-' : Carbon::parse($record->arrival_time)->format('Y-m-d h:i A') }}</td>
                                    @if (Auth::user()->parameters()->where('parameter_id', 8)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_type ?? '-' }} </td>
                                    @endif
                                    @if (Auth::user()->permissions()->where('permission_id', 1)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                            <a target="_blank" href="{{ route('play.new', ['call_id' => $record->call_id, 'agent_name' => $record?->agent?->full_name, 'agent_id' => $record?->agent?->agent_id]) }}">
                                                <i class="fa-solid fa-play fa-xl play-button" aria-hidden="true" style="cursor: pointer;color:#c4d54a"></i>
                                            </a>
                                        </td>
                                    @endif
                                    @if (Auth::user()->role != 4)
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                            @if ($record->listeners->isNotEmpty())
                                                <i class="fa-solid fa-lg fa-headphones-simple" style="color:#03a34d;" data-bs-toggle="modal" data-bs-target="#view-listeners" title="Played"></i>
                                            @else
                                                -
                                            @endif
                                        </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 27)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                            @if ($record->comments()->exists())
                                                <i class="fa-solid fa-lg fa-comment-dots" style="color:#03a34d;" title="contains Comments"></i>
                                            @else
                                                -
                                            @endif
                                        </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 22)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                            @if ($record?->callEvaluation?->id)
                                                <i class="fa-solid fa-lg fa-file-circle-check" style="color:#03a34d;" title="Evaluated"></i>
                                            @else
                                                -
                                            @endif
                                        </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 25)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->agent?->full_name }} </td>
                                    @endif
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->callEvaluation?->evaluator ? $record?->callEvaluation?->evaluator?->full_name : '-' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->callEvaluation?->evaluation->evaluation_name ?? '-' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record?->agent?->agent_id }} </td>
                                    @if (Auth::user()->parameters()->where('parameter_id', 4)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->called_id ?? '-' }} </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 6)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->caller_id ?? '-' }} </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 5)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_ender ?? '-' }} </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 23)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                            @if ($record->qaFlags()->exists())
                                                <i class="fa-regular fa-lg fa-font-awesome" style="color:#03a34d; rotate:-35deg" title="Flagged"></i>
                                            @else
                                                -
                                            @endif
                                        </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 2)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_duration ? Carbon::parse($record->call_duration)->format('i:s') : '-' }} </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 19)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->callEvaluation?->quality_percentage ? $record->callEvaluation?->quality_percentage . '%' : '-' }} </td>
                                    @endif
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->agent_extension ?? '-' }} </td>
                                    @if (Auth::user()->parameters()->where('parameter_id', 29)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->agent->userGroup->name ?? '-' }} </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 7)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->hold_duration ? Carbon::parse($record->hold_duration)->format('i:s') : '-' }} </td>
                                    @endif
                                    @if (Auth::user()->parameters()->where('parameter_id', 11)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->hold_count ?? '-' }} </td>
                                    @endif
                                    {{-- @if (Auth::user()->parameters()->where('parameter_id', 31)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->agent?->organization?->name . ' | ' . $record?->organization?->name }} </td>
                                    @endif --}}
                                    @if (Auth::user()->parameters()->where('parameter_id', 31)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                            {{ (str_contains($record->agent?->organization?->name, 'Ewa') ? 'Kease' : $record->agent?->organization?->name) . ' | ' . (str_contains($record->organization?->name, 'Ewa') ? 'Kease' : $record->organization?->name) }}
                                        </td>
                                    @endif

                                    @if (Auth::user()->parameters()->where('parameter_id', 13)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->ring }} </td>
                                    @endif
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ Str::title($record->language) ?? '-' }} </td>
                                    @if (Auth::user()->parameters()->where('parameter_id', 1)->exists())
                                        <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->call_id ?? '-' }} </td>
                                    @endif
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ $record->Genesys_CallUUID }} </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="25" class="text-muted text-center bg-white"> No Interactions found</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <!-- Dropdown for Number of Items per Page -->
                    <div style="align-self: baseline !important">
                        <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                            <option value="10">10</option>
                            <option value="15" selected>15</option>
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>Interactions Per Page</span>
                    </div>

                    <!-- Pagination Links -->
                    <div>
                        {{ $records->links(data: ['scrollTo' => false]) }}
                    </div>
                </div>
            </div>
        </div>


    {{-- custom time modal --}}
    <div class="modal fade" id="custom_time_modal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog ">
            <div class="modal-content" >

                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa fa-calendar" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Custom Period</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 ">Date From <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="from" id="from" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Date From" wire:model="custom_date_from">
                            @error('custom_date_from')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 ">Date To <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="to" id="to" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Date To" wire:model="custom_date_to">
                            @error('custom_date_to')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border: none;">
                    {{-- <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeCustomDate">Close</button>
                    <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply-custom-date" wire:click="apply_custom_date">Apply</button> --}}
                    <button
                        id="closeCustomDate"
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal">
                        <span style="font-size: 17px;">Close</span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="apply_custom_date"
                        wire:loading.attr="disabled"
                        wire:target="apply_custom_date">
                        <span wire:loading.remove wire:target="apply_custom_date" id="apply-custom-date" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="apply_custom_date" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>



{{--
    <div class="mx-3 ps-5">
        <div class="col-6">
            <a href="{{ route('admin.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div> --}}

    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">

                            </div>
                            <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">
                        <div class="col-md-12">
                            <div x-data="{ filterType: 'General' }"
                                x-on:reset-filter-type.window="filterType = 'General'">
                                <div class="col-md-6 mb-3">
                                    <label for="exampleInputEmail1">
                                        {{-- <h6 id="" class="" style="color: #00a34e">Filter Type</h6> --}}
                                        <label id="" class="">Filter Type</label>

                                    </label>
                                    <div class="dropdown">
                                        <select class="form-select" x-model="filterType" aria-label="Select Filter Type" style="font-size: 0.85rem !important;">
                                            <option value="General">General</option>
                                            <option value="Telephony">Telephony</option>
                                            <option value="Quality">Quality</option>
                                            <option value="Users & Action">Users & Action</option>
                                            <option value="Telephony Custom Attributes">Telephony Custom Attributes</option>
                                        </select>
                                    </div>
                                </div>

                                <template x-if="filterType === 'General'">
                                    <form class="row g-2 mb-3" wire:submit.prevent>
                                        <div class="col-md-6">
                                            <label for="date" class="mb-2 ">Time <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e" title="Date"></i></label>
                                            {{-- <select wire:model="selectedTime" class="form-select form-select mb-3">
                                                <option selected value="all">All Time</option>
                                                <option value="1">Last 24 Hours</option>
                                                <option value="7">Last 7 Days</option>
                                                <option value="40">Last 30 Days</option>
                                                <option value="60">Last 60 Days</option>
                                            </select> --}}
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style " type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem;">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_time_name ?? 'All Time' }}</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(null)">All Time</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(1)">Last 24 Hours</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(7)">Last 7 Days</span>
                                                    </li>
                                                    <hr class="m-0">

                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(30)">Last 30 Days</span>
                                                    </li>
                                                    <hr class="m-0">

                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(60)">Last 60 Days</span>
                                                    </li>
                                                    <hr class="m-0">

                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#custom_time_modal">Custom</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            {{-- <label for="call id" class="mb-2 ">Call ID <i class="fa-solid fa-address-book fa-lg" style="color:#00a34e"></i></label> --}}
                                            <label for="call id" class="mb-2 ">Call ID
                                                <img src="{{ asset('assets/images/callid.png') }}" alt="" srcset="" style="width:1.3rem; height:1.3rem">
                                            </label>
                                            <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="text" name="call-id" id="call-id" class="form-control" style="border: solid 1px #b6b6b6" placeholder="Call #" wire:model="filter_callId">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="Agent name" class="mb-2 ">User ID <i class="fa-solid fa-hashtag fa-lg" style="color:#00a34e" title="User ID"></i></label>
                                            <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="text" name="agent" id="agent" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Ops ID" wire:model="filter_agentId">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Duration <i class="fa-solid fa-stopwatch fa-lg" style="color:#00a34e" title="Duration"></i></label>
                                            {{-- <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="number" name="duration" id="duration" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Duration in seconds" wire:model="filter_duration"> --}}
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div>
                                                        <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px; height:2.5rem !important" wire:model="filter_duration_sign">
                                                            <option class="fw-bold" value="=">=</option>
                                                            <option class="fw-bold" value=">">></option>
                                                            <option class="fw-bold" value="<">
                                                                < </option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <input type="number" name="duration" id="duration" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Duration in seconds" wire:model="filter_duration">
                                            </div>
                                        </div>
                                            <div class="col-md-6">
                                                <label for="account" class="mb-2 ">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Account"></i></label>
                                                {{-- <select class="form-select form-select mb-3" wire:model="selectedAccount">
                                                <option selected value="all">All</option>
                                                <option value="gen01">gen01</option>
                                            </select> --}}
                                                <select wire:model="filter_account"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem; overflow: auto;">
                                                    <option value="">All</option>

                                                    @foreach ($organizations->where('name', '!=', 'Ewa') as $item)
                                                        <option value="{{ $item->name }}">{{ $item->name }}</option>
                                                    @endforeach
                                                </select>

                                            </div>
                                    </form>
                                </template>
                                <template x-if="filterType === 'Telephony'">
                                    <form class="row g-2 mb-3">
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Call Type')) --}}
                                        <div class="col-md-6">
                                            {{-- <label for="call type" class="mb-2 ">Call Type <i class="fa-solid fa-phone fa-lg" style="color: #00a34e" title="Call Type"></i></label> --}}
                                            <label for="call type" class="mb-2 ">Call Type
                                                <img src="{{ asset('assets/images/calltype.png') }}" alt="call type" srcset="" style="width: 1.2rem; height:1.2rem">
                                            </label>

                                            <select wire:model="filter_callType"
                                                    class="form-select form-select-sm dropdown-toggle-style p-2"
                                                    style="padding-top: 0.46rem; padding-bottom: 0.46rem; width:100%; font-size: 0.85rem;">
                                                <option value="All">All</option>
                                                <option value="Inbound">Inbound</option>
                                                <option value="Outbound">Outbound</option>
                                            </select>
                                        </div>
                                        {{-- @endif --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Called ID')) --}}
                                        <div class="col-md-6">
                                            <label for="called id" class="mb-2 ">Called ID
                                                <img src="{{ asset('assets/images/callin.png') }}" alt="call type" srcset="" style="width: 1.4rem; height:1.4rem">
                                            </label>
                                            {{-- <label for="called id" class="mb-2 ">Called ID <i class="fa-solid fa-arrow-down-wide-short fa-lg" style="color: #00a34e" title="Called ID"></i></label> --}}
                                            <input style="padding-top: 0.46rem;padding-bottom: 0.46rem; font-size:0.85rem" type="text" name="call-id" id="called-id" class="form-control" placeholder="Called ID" wire:model="filter_calledId">
                                        </div>
                                        {{-- @endif --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Caller ID')) --}}
                                        <div class="col-md-6">
                                            <label for="called id" class="mb-2 ">Caller ID
                                                <img src="{{ asset('assets/images/callout.png') }}" alt="call type" srcset="" style="width: 1.4rem; height:1.4rem">
                                            </label>
                                            {{-- <label for="caller id" class="mb-2 ">Caller ID <i class="fa-solid fa-arrow-up-wide-short fa-lg" style="color: #00a34e" title="Caller ID"></i></label> --}}
                                            <input style="padding-top: 0.46rem;padding-bottom: 0.46rem; font-size:0.85rem" type="text" name="call-id" id="caller-id" class="form-control" placeholder="Caller ID" wire:model="filter_callerId">
                                        </div>
                                        {{-- @endif --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Call Ender')) --}}
                                        <div class="col-md-6">
                                                {{-- <label for="call id" class="mb-2 ">Call Ender <i class="fa-solid fa-phone-slash fa-lg" style="color: #00a34e" title="Call Ender"></i></label> --}}
                                            <label for="call id" class="mb-2 ">Call Ender
                                                <img src="{{ asset('assets/images/decline.png') }}" alt="" srcset="" style="width:1.4rem; height:1.4rem">
                                            </label>
                                            <select wire:model="filter_callEnder"
                                                    class="form-select form-select-sm dropdown-toggle-style p-2"
                                                    style="padding-top: 0.46rem !important; padding-bottom: 0.46rem !important; width: 100%; font-size: 0.85rem;">
                                                <option value="All">All</option>
                                                <option value="Agent">Agent</option>
                                                <option value="Customer">Customer</option>
                                            </select>
                                            {{-- <input type="text" name="call-ender" id="call-ender" style="border: solid 1px #b6b6b6" class="form-control" placeholder="Call Ender" wire:model="filter_callEnder"> --}}
                                        </div>
                                        {{-- @endif --}}

                                        {{-- <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Extension <i class="fa-solid fa-tty fa-lg" title="Extension" style="color: #00a34e"></i></label>
                                            <input  style="padding-top: 0.46rem;padding-bottom: 0.46rem;" type="text" name="extension" id="extension" class="form-control" placeholder="Extension" wire:model="extension">
                                        </div> --}}
                                        {{-- <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Pause Duration <i class="fa-regular fa-circle-pause fa-lg" style="color: #00a34e" title="Pause Duration"></i></label>
                                            <input  style="padding-top: 0.46rem;padding-bottom: 0.46rem;" type="number" name="pause-duration" id="pause-duration" class="form-control" placeholder="Duration in seconds" wire:model="pauseDuration">
                                        </div> --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Hold Duration')) --}}
                                        <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Hold Duration
                                                <img src="{{ asset('assets/images/holdduration.png') }}" alt="" srcset="" style="width:1.3rem; height:1.3rem">
                                            </label>
                                            {{-- <label for="Duration" class="mb-2 ">Hold Duration <i class="fa-solid fa-phone-flip fa-lg" style="color: #00a34e" title="Hold Duration"></i></label> --}}
                                            {{-- <input style="padding-top: 0.46rem;padding-bottom: 0.46rem; font-size:0.85rem" type="number" name="hold-duration" id="hold-duration" class="form-control" placeholder="Duration in seconds" wire:model="filter_holdDuration"> --}}
                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div>
                                                        <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px; height:2.5rem !important" wire:model="filter_holdDuration_sign">
                                                            <option class="fw-bold" value="=">=</option>
                                                            <option class="fw-bold" value=">">></option>
                                                            <option class="fw-bold" value="<">
                                                                < </option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <input type="number" style="border: solid 1px #b6b6b6" name="hold-duration" id="hold-duration" class="form-control" placeholder="Duration in seconds" wire:model="filter_holdDuration">
                                            </div>

                                        </div>
                                        {{-- @endif --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Call Count')) --}}
                                        <div class="col-md-6 mb-3">
                                            <label for="call type" class="mb-2 ">Hold Count
                                                <img src="{{ asset('assets/images/pause.png') }}" alt="call type" srcset="" style="width: 1.2rem; height:1.2rem">
                                            </label>
                                            {{-- <input style="padding-top: 0.46rem;padding-bottom: 0.46rem; font-size:0.85rem" type="number" name="hold-count" id="hold-count" class="form-control" placeholder="Hold Count" wire:model="filter_holdCount"> --}}

                                            <div class="input-group mb-2">
                                                <div class="input-group-prepend">
                                                    <div>
                                                        <select class="form-select  text-center m-auto" style="cursor: pointer; border: solid 1px #b6b6b6; border-radius: 5px 0 0 5px; height:2.5rem !important" wire:model="filter_holdCount_sign">
                                                            <option class="fw-bold" value="=">=</option>
                                                            <option class="fw-bold" value=">">></option>
                                                            <option class="fw-bold" value="<">
                                                                < </option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <input style="border: solid 1px #b6b6b6" type="number" name="hold-count" id="hold-count" class="form-control" placeholder="Hold Count" wire:model="filter_holdCount">
                                            </div>
                                        </div>
                                        {{-- @endif --}}
                                        {{-- <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Ring <i class="fa-solid fa-phone-volume fa-lg" style="color: #00a34e" title="Ring"></i></label>
                                            <input type="number" name="ring" id="ring" class="form-control" placeholder="Duration in seconds" wire:model="ring">
                                        </div> --}}
                                        {{-- <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Digits Count</label>
                                            <input type="number" name="digits-count" id="digits-count" class="form-control" placeholder="# of digits" wire:model="digitsCount">
                                        </div> --}}
                                        {{-- <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Transferred To <i class="fa-solid fa-cloud-arrow-up fa-lg" style="color: #00a34e" title="Transferred To"></i></label>
                                            <input type="text" name="transferred-to" id="transferred-to" class="form-control" placeholder="Trnasferred To" wire:model="transferredTo">
                                        </div> --}}
                                        {{-- <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Transferred From <i class="fa-solid fa-cloud-arrow-down fa-lg" style="color: #00a34e" title="Transferred From"></i></label>
                                            <input type="text" name="transferred-from" id="transferred-from" class="form-control" placeholder="Transferred From" wire:model="transferredFrom">
                                        </div> --}}
                                    </form>
                                </template>
                                <template x-if="filterType === 'Quality'">
                                    <form class="row g-2 mb-3">
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Is Assgined')) --}}
                                        {{-- <div class="col-md-6">
                                            <label for="Is Assigned" class="mb-2 ">Is Assigned <i class="fa-solid fa-people-arrows fa-lg" title="Is Assigned" style="color: #00a34e"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_isAssigned', 'Assigned')">Assigned</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_isAssigned', 'Not Assigned')">Not Assigned</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                        {{-- @endif --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Evaluation Score')) --}}
                                        <div class="col-md-6">
                                            <label for="Duration" class="mb-2 ">Evaluation Score <i class="fa-solid fa-ranking-star fa-lg" style="color: #00a34e" title="Evaluation Score"></i></label>
                                            <input style="padding-top: 0.46rem;padding-bottom: 0.46rem;font-size:0.85rem" type="number" name="evaluation-score" id="evaluation-score" class="form-control" placeholder="Evaluation Score" wire:model="filter_evaluationScore">
                                        </div>

                                        <div class="col-md-6">
                                            <label for="unique-id" class="mb-2 ">Unique ID <i class="fa-solid fa-fingerprint fa-lg" style="color: #00a34e" title="Unique ID"></i></label>
                                            <input type="text" style="border: solid 1px #b6b6b6; padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%" name="unique-id" id="unique-id" class="form-control" placeholder="Unique ID" wire:model="filter_uniqueId" min="1">
                                        </div>

                                        <div class="col-md-6">
                                                <label for="call type" class="mb-2 ">Is Evaluated <i class="fa-solid fa-user-tie fa-lg" style="color: #00a34e" title="Evaluator"></i></label>

                                                <select wire:model="filter_isEvaluated"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    <option value="Evaluated">Evaluated</option>
                                                    <option value="Not Evaluated">Not Evaluated</option>
                                                </select>

                                            </div>

                                        <div class="col-md-6">
                                                <label for="call type" class="mb-2 ">Evaluation Form <i class="fa-solid fa-rectangle-list fa-lg" style="color: #00a34e" title="Evaluator"></i></label>
                                                <select wire:model="filter_evaluationForm"
                                                        class="form-select form-select-sm dropdown-toggle-style p-2"
                                                        style="width: 100%; height: 2.5rem; font-size: 0.85rem;">
                                                    <option value="All">All</option>
                                                    @foreach ($evaluation_forms as $form)
                                                        <option value="{{ $form->evaluation_name }}">{{ $form->evaluation_name }}</option>
                                                    @endforeach
                                                </select>

                                            </div>
                                        {{-- @endif --}}

                                        {{-- <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">Penalties <i class="fa-solid fa-hammer fa-lg" style="color: #00a34e" title="Penalties"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Includes</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Does Not Include</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                        {{-- <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">Agent Feedback</label>
                                            <select class="form-select form-select mb-3" wire:model="agentFeedback">
                                                <option value="all">All</option>
                                                <option value="satisfied">Satisfied</option>
                                                <option value="notSatisfied">Not Satisfied</option>
                                                <option value="na">NA</option>
                                            </select>
                                        </div> --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'QA Flagged Interactions')) --}}
                                        <div class="col-md-6">
                                            <label for="call type" class="mb-2 ">QA Flagged Interactions <i class="fa-solid fa-flag fa-lg" style="color: #00a34e" title="Flagged Interactions"></i></label>

                                            <select wire:model="filter_qaFlaggedInteractions"
                                                    class="form-select form-select-sm dropdown-toggle-style p-2"
                                                    style="width: 100%; height: 2.5rem; font-size: 0.85rem;">
                                                <option value="All">All</option>
                                                <option value="Flagged">Flagged</option>
                                                <option value="Not Flagged">Not Flagged</option>
                                            </select>

                                            </div>
                                        {{-- @endif --}}
                                        {{-- @if (Auth::user()->parameters->contains('name', 'Flag Date')) --}}
                                        {{-- <div class="col-md-6">
                                            <label for="Is Assigned" class="mb-2 ">Flag Date</label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Select Date</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                        {{-- @endif --}}
                                    </form>
                                </template>
                                <template x-if="filterType === 'Users & Action'">
                                    <form class="row g-2 mb-3">
                                        <div class="col-md-6">
                                            <label for="agent name" class="mb-2 ">Agent Name <i class="fa-solid fa-id-card fa-lg" title="Agent Name" style="color: #00a34e"></i></label>
                                            <input style="padding-top: 0.46rem;padding-bottom: 0.46rem;font-size:0.85rem" type="text" name="agent-name" id="agent-name" class="form-control" placeholder="Agent Name" wire:model="filter_agentName">
                                        </div>
                                        {{-- <div class="col-md-6">
                                            <label for="account" class="mb-2 ">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e;" title="Account"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_account ?? 'All' }}</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton" style="height:10rem; overflow:auto">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_account', 'All')">All</span>
                                                    </li>
                                                    <hr class="m-0">

                                                    @forelse ($organizations as $item)
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_account', '{{ $item->name }}')">{{ $item->name }}</span>
                                                        </li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                    @endforelse
                                                </div>
                                            </div>
                                        </div> --}}

                                        <div class="col-md-6">
                                            <label for="account" class="mb-2">Account
                                                <i class="fa-solid fa-building fa-lg" style="color: #00a34e;" title="Account"></i>
                                            </label>
                                            <select id="account" wire:model="filter_account" class="form-select form-select-sm" style="color:#6c757d; background-color:#eff3f4 !important; border:none; height:2.5rem;">
                                                <option value="All">All</option>

                                                @foreach ($organizations as $item)
                                                        <option value="{{ $item->name }}">
                                                            {{  $item->name }}
                                                        </option>
                                                @endforeach
                                            </select>
                                        </div>







                                        {{-- <div class="col-md-6">
                                            <label for="account" class="mb-2 ">Skill Group <i class="fa-solid fa-people-roof fa-lg" style="color: #00a34e" title="Skill Group"></i></label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_skillGroup ?? 'All' }}</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_skillGroup', 'All')">All</span>
                                                    </li>
                                                    @forelse ($skillGroups as $item)
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="$set('filter_skillGroup', '{{ $item->name }}')">{{ $item->name }}</span>
                                                        </li>
                                                        @if (!$loop->last)
                                                            <hr class="m-0">
                                                        @endif
                                                    @empty
                                                        <li>
                                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">No Groups Found</span>
                                                        </li>
                                                    @endforelse
                                                </div>
                                            </div>
                                        </div> --}}
                                        {{-- <div class="col-md-6">
                                            <label for="account" class="mb-2 ">Call Importance</label>
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">All</span>
                                                </button>
                                                <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">All</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Normal</span>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li>
                                                        <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item">Urgent</span>
                                                    </li>
                                                </div>
                                            </div>
                                        </div> --}}
                                    </form>
                                </template>
                                <template x-if="filterType === 'Telephony Custom Attributes'">
                                    <form class="row g-2 mb-3">
                                        {{-- <div class="col-md-6">
                                            <label for="pause count" class="mb-2 ">Pause Count <i class="fa-solid fa-pause fa-lg" style="color: #00a34e" title="Pause Count"></i></label>
                                            <input style="padding-top: 0.46rem;padding-bottom: 0.46rem;font-size:0.85rem" type="number" name="pause-count" id="pause-count" class="form-control" placeholder="Pause Count" wire:model="filter_pauseCount">
                                        </div> --}}
                                        <div class="col-md-6">
                                            <label for="call id" class="mb-2 ">Language <i class="fa-solid fa-language fa-lg" style="color: #00a34e" title="Language"></i></label>
                                            {{-- <input style="padding-top: 0.46rem;padding-bottom: 0.46rem;font-size:0.85rem" type="text" name="language" id="language" class="form-control" placeholder="Language" wire:model="filter_language"> --}}
                                            <div class="dropdown">
                                                <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem; width:100%">
                                                    <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">
                                                        @php
                                                            $allLanguages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
                                                            $isAllSelected = empty(array_diff($allLanguages, $filter_selected_languages)) && empty(array_diff($filter_selected_languages, $allLanguages));
                                                        @endphp
                                                        @if ($isAllSelected)
                                                            {{ 'All' }}
                                                        @elseif (!$filter_selected_languages || count($filter_selected_languages) <= 1)
                                                            {{ $filter_selected_languages[0] ?? '--' }}
                                                        @else
                                                            {{ 'Multi Selection' ?? '--' }}
                                                        @endif
                                                    </span>
                                                </button>
                                                <ul class="dropdown-menu w-100" wire:key="sdojisaodhn89" onclick="event.stopPropagation()" wire:ignore.self aria-labelledby="dropdownMenuButton1" id="dropdownMenu2323sd" style="height: 10rem; overflow:auto;">
                                                    <li>
                                                        <label class="dropdown-item">
                                                            <input type="checkbox" wire:key="select-all-checkbox" wire:click="selectAllLanguages()" @if (!array_diff($all_languages, $filter_selected_languages)) checked @endif> All
                                                        </label>
                                                    </li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="arabic-check" wire:click="filter_languages('Arabic')" @if (in_array('Arabic', $filter_selected_languages)) checked @endif> Arabic </label></li>

                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="english-check" wire:click="filter_languages('English')" @if (in_array('English', $filter_selected_languages)) checked @endif> English </label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="kurdish-check" wire:click="filter_languages('Kurdish')" @if (in_array('Kurdish', $filter_selected_languages)) checked @endif> Kurdish</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="spanish-check" wire:click="filter_languages('Spanish')" @if (in_array('Spanish', $filter_selected_languages)) checked @endif> Spanish</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="german-check" wire:click="filter_languages('German')" @if (in_array('German', $filter_selected_languages)) checked @endif> German</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="french-check" wire:click="filter_languages('French')" @if (in_array('French', $filter_selected_languages)) checked @endif> French</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="italian-check" wire:click="filter_languages('Italian')" @if (in_array('Italian', $filter_selected_languages)) checked @endif> Italian</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="russian-check" wire:click="filter_languages('Russian')" @if (in_array('Russian', $filter_selected_languages)) checked @endif> Russian</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="urdu-check" wire:click="filter_languages('Urdu')" @if (in_array('Urdu', $filter_selected_languages)) checked @endif> Urdu</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="hebrew-check" wire:click="filter_languages('Hebrew')" @if (in_array('Hebrew', $filter_selected_languages)) checked @endif> Hebrew</label></li>
                                                    <hr class="m-0">
                                                    <li><label class="dropdown-item"><input type="checkbox" wire:key="turkish-check" wire:click="filter_languages('Turkish')" @if (in_array('Turkish', $filter_selected_languages)) checked @endif> Turkish</label></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </form>
                                </template>
                        </div>
                        </div>
                    </div>

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        wire:click="clear"
                        wire:loading.attr="disabled"
                        wire:target="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="getData"
                        wire:loading.attr="disabled"
                        wire:target="getData">
                        <span wire:loading.remove wire:target="getData" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                    </button>
                </div>
        </div>
        </div>
    </div>
</div>
