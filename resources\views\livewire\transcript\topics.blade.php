<div class="col-12 events-container rounded">
    <div class="d-flex p-3 w-full details-header px-3" style='background-color:rgb(239 243 244) !important; position: sticky;top:0'>
        <div id="search-wrapper">
            <input type="text" id="search" style="width: 300px;"
                   wire:model="searchTerm"

                   placeholder="Search by Category">
            <i class="search-icon fas fa-search" wire:click="filteredTopics" style="cursor: pointer"></i>
        </div>
    </div>

    <div class="col-12" style="margin-top: 0px;">
        <table class="table table-striped table-bordered">
            <thead id="thead" class="thead text-muted" style="font-size: 0.8rem;position: sticky; top: 70px; z-index: 4;">
            <tr style="text-align: -webkit-center;">
                <th style="text-align: -webkit-center;background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 50%;">Category</th>
                <th style="text-align: -webkit-center; background: #40798c;margin: 0 !important;font-size: 16px;color: white;width: 50%;">Detected Topics</th>
            </tr>
            </thead>
            <tbody>
            <tr></tr>

                @forelse ($topics as $key => $value)
                    <tr style="text-align: -webkit-center;">
                        <td style="width: 50%;">{{ ucfirst($key) }}</td> <!-- Display the key -->
                        <td style="width: 50%;">{{ $value }}</td>       <!-- Display the value -->
                    </tr>
                @empty
                    <tr class="text-center">
                        <td class="text-center" colspan="2">No topics available</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
