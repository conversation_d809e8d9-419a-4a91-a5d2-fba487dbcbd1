<?php
namespace App\Livewire\Transcript;

use App\Models\TranscriptionBadWord;
use App\Models\TranscriptionClassifications;
use App\Models\TranscriptionScripts;
use App\Models\TranscriptionScriptsWord;
use Livewire\Component;
use Livewire\Features\SupportDispatching\DispatchesBrowserEvents;

class BadWord extends Component
{

    public $callId;
    public $decodedTopics  = []; // To store the decoded detected_topics
    public $decodedScripts = []; // To store the decoded detected_topics
    public $searchTerm     = '';
    public $searchTerm2    = '';

    public $aiType = "Keywords";

    public $keywordsType         = null;
    public $scriptsType          = null;
    public $classificationType   = null;
    public $transcriptionScripts = [];

    public function aiTypess()
    {
//        dd($this->aiType);
        $this->keywordsType = null;
        $this->scriptsType  = null;
        $this->filteredTopics();
        $this->filteredTopicsS();

    }

    public function seywordsType()
    {
        $this->filteredTopics();
    }

    public function sweywordsType()
    {
        $this->filteredTopicsS();
    }

    public function mount($callId)
    {
        $this->callId = $callId;

        // Fetch the transcription topics for this call and decode the detected_topics
        $topics = TranscriptionBadWord::where('call_id', $this->callId)->when($this->keywordsType != null, function ($q) {
            $q->where('type', $this->keywordsType)->get();
        })->get();

        foreach ($topics as $topic) {
            // Decode the JSON detected_topics and store it
            $this->decodedTopics[$topic->words_detected]              = json_decode($topic->words_detected, true);
            $this->decodedTopics[$topic->words_detected]['type']      = $topic->type;
            $this->decodedTopics[$topic->words_detected]['side_type'] = $topic->side_type;
        }

        /*        $this->transcriptionScripts = TranscriptionScripts::whereRaw('CAST(best_ratio AS DECIMAL(10,2)) >= ?', [75.0])
            ->where('call_id', $this->callId)
            ->get(); */
        $this->transcriptionScripts[] = TranscriptionScripts::where('call_id', $this->callId)
        ->where('scripts_type', 'Closure')
        ->orderByRaw('CAST(best_ratio AS DECIMAL(10,2)) DESC')
        ->first();

        $this->transcriptionScripts[] = TranscriptionScripts::where('call_id', $this->callId)
        ->where('scripts_type', 'Greeting')
        ->orderByRaw('CAST(best_ratio AS DECIMAL(10,2)) DESC')
        ->first();

//        $topics2 = TranscriptionScriptsWord::where('call_id', $this->callId)->when($this->scriptsType != null ,function ($q){
//            $q->where('type',$this->keywordsType)->get();
//        })->get();
//
//        foreach ($topics2 as $topic) {
//            // Decode the JSON detected_topics and store it
//            $this->decodedScripts[$topic->words_detected]= json_decode($topic->words_detected, true);
//            $this->decodedScripts[$topic->words_detected]['type']= $topic->type;
//            $this->decodedScripts[$topic->words_detected]['side_type']= $topic->side_type;
//        }

    }

    // Method to filter the topics based on the search term
    public function filteredTopics()
    {
        $this->decodedTopics = [];

        // If there's no search term, return all decoded topics
        if (empty($this->searchTerm)) {

            // Fetch the transcription topics for this call and decode the detected_topics
            $topics = TranscriptionBadWord::where('call_id', $this->callId)->when($this->keywordsType != null, function ($q) {
                $q->where('type', $this->keywordsType)->get();
            })->get();

            foreach ($topics as $topic) {
                // Decode the JSON detected_topics and store it
                $this->decodedTopics[$topic->words_detected]              = json_decode($topic->words_detected, true);
                $this->decodedTopics[$topic->words_detected]['type']      = $topic->type;
                $this->decodedTopics[$topic->words_detected]['side_type'] = $topic->side_type;
            }

        } else {
            // Fetch the transcription topics for this call and decode the detected_topics
            $topics = TranscriptionBadWord::where('call_id', $this->callId)->when($this->keywordsType != null, function ($q) {
                $q->where('type', $this->keywordsType)->get();
            })->get();

            foreach ($topics as $topic) {
                // Decode the JSON detected_topics and store it
                $this->decodedTopics[$topic->words_detected]              = json_decode($topic->words_detected, true);
                $this->decodedTopics[$topic->words_detected]['type']      = $topic->type;
                $this->decodedTopics[$topic->words_detected]['side_type'] = $topic->side_type;
            }

        }

        $this->decodedTopics = collect($this->decodedTopics)->filter(function ($value, $key) {
            // Check if the key contains the search term
            return stripos($key, $this->searchTerm) !== false;
        });

    }
    public function filteredTopicsS()
    {
        $this->decodedScripts = [];
        // If there's no search term, return all decoded topics
        if (empty($this->searchTerm2)) {

            // Fetch the transcription topics for this call and decode the detected_topics
            $topics = TranscriptionScriptsWord::where('call_id', $this->callId)->when($this->scriptsType != null, function ($q) {
                $q->where('type', $this->scriptsType)->get();
            })->get();

            foreach ($topics as $topic) {
                // Decode the JSON detected_topics and store it
                $this->decodedScripts[$topic->words_detected]              = json_decode($topic->words_detected, true);
                $this->decodedScripts[$topic->words_detected]['type']      = $topic->type;
                $this->decodedScripts[$topic->words_detected]['side_type'] = $topic->side_type;
            }

        } else {
            // Fetch the transcription topics for this call and decode the detected_topics
            $topics = TranscriptionScriptsWord::where('call_id', $this->callId)->when($this->scriptsType != null, function ($q) {
                $q->where('type', $this->scriptsType)->get();
            })->get();

            foreach ($topics as $topic) {
                // Decode the JSON detected_topics and store it
                $this->decodedScripts[$topic->words_detected]              = json_decode($topic->words_detected, true);
                $this->decodedScripts[$topic->words_detected]['type']      = $topic->type;
                $this->decodedScripts[$topic->words_detected]['side_type'] = $topic->side_type;
            }

        }

        $this->decodedScripts = collect($this->decodedScripts)->filter(function ($value, $key) {
            // Check if the key contains the search term
            return stripos($key, $this->searchTerm2) !== false;
        });

    }

    public function bads()
    {
        $bads = TranscriptionBadWord::where('call_id', $this->callId)->get();

        return $bads;
    }

    public function classification()
    {
//        dd('s');
        $classification = TranscriptionClassifications::where('call_id', $this->callId)->
            when($this->classificationType != null, function ($q) {

            $q->whereRaw('UPPER(classification) = ?', [$this->classificationType])
                ->where('call_id', $this->callId)
                ->get();
        })
            ->get();

        return $classification;
    }

    public function render()
    {
        return view('livewire.transcript.bad-word', [
            'bads'            => $this->decodedTopics,
            'bads2'           => $this->decodedScripts,
            'classifications' => $this->classification(),
        ]);
    }

    public array $expandedGroups = [
        'positive' => false,
        'negative' => false,
        'neutral'  => false,
    ];

    public function toggleGroup($type)
    {
        if (array_key_exists($type, $this->expandedGroups)) {
            $this->expandedGroups[$type] = !$this->expandedGroups[$type];
        }
    }

    public function groupedClassifications()
    {
        return $this->classification()->groupBy(function ($item) {
            return strtolower($item->classification);
        });
    }
    public $modalType = null;
    public $modalItems = [];

    public function showModal($type)
    {
        $this->modalType = $type;

        $this->modalItems = $this->classification()
            ->filter(fn ($item) => strtolower($item->classification) === strtolower($type))
            ->values()
            ->toArray();

            $this->dispatch('show-classification-modal');
                }

}

