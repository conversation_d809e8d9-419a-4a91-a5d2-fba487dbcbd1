<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FtpController extends Controller
{
    public function testFtp()
    {
        // Use the 'ftp' disk defined in config/filesystems.php
        $disk = Storage::disk('ftp');

        // Example: List contents of a directory
        $contents = $disk->allFiles('/path/to/remote/directory');

        // Example: Get contents of a file
        $contents = $disk->get('/path/to/remote/file.txt');

        // Example: Put a file on the server
        $localFilePath = '/path/to/local/file.txt';
        $remoteFilePath = '/path/to/remote/file.txt';
        $disk->put($remoteFilePath, file_get_contents($localFilePath));

        // Example: Delete a file
        $disk->delete('/path/to/remote/file.txt');

        return "FTP operations completed.";
    }
}
