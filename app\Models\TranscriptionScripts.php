<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TranscriptionScripts extends Model
{
    use HasFactory;

    protected $fillable = [
    'id',
    'input_text',
    'best_script',
    'best_ratio',
    'scripts',
    'call_id',
    'calls_transcription_id',
    'created_at',
    'updated_at',
    'scripts_type',
    'language',
];


    public function callsTranscription()
    {
        return $this->belongsTo(CallsTranscription::class, 'calls_transcription_id');
    }
}
