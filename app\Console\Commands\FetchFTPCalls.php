<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FetchFTPCalls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetchCalls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch FTP Calls';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fetching FTP data');
        Log::info("Started Fetching FTP data");
    }
}
