<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use App\Models\UserGroup;
use App\Models\Organization;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class UserGroups extends Component
{
    use LivewireAlert, WithPagination;

    // add modal 
    public $added_org;
    public $added_name = null;
    public $added_supervisor;
    public $added_org_id;
    public $selected_supervisors = [];
    public $selected_agents = [];

    // search supervisor (modal edit user group)
    public $searchSupervisors = '';
    public $possibleSupervisors = [];
    public $possibleSupervisors_ids = [];

    // search agents (modal edit user group)
    public $searchAgents = '';
    public $possibleAgents = [];


    // edit modal 
    public $selectedGroupId;
    public $selectedGroupName = null;
    public $selectedGroupOrg;
    public $selectedGroupOrg_id;
    public $selectedGroupSupervisors = [];
    public $selectedGroupAgents = [];
    public $removed_agents = [];

    public $theIdToDelete;

    // live search 
    public $searchGroup = '';
    public $sortBy = 'name';
    public $sortDir = 'ASC';

    protected $paginationTheme = 'bootstrap';

    public function getListeners()
    {
        return [
            'confirmed'
        ];
    }

    public function showDeleteAlert($id)
    {
        $this->theIdToDelete = $id;
        $this->alert('warning', 'Are you sure you want to delete this group?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirmed()
    {
        if (UserGroup::find($this->theIdToDelete)->delete()) {
            $this->alert("success", "Group Deleted Successfully");
        };
    }

    public function clear()
    {
        $this->resetValidation();
        $this->added_org = null;
        $this->added_name = null;
        $this->added_supervisor = null;
        $this->added_org_id = null;
        $this->selected_supervisors =  [];
        $this->selected_agents = [];
        $this->selectedGroupId = null;
        $this->selectedGroupName  = null;
        $this->selectedGroupOrg = null;
        $this->selectedGroupOrg_id = null;
        $this->selectedGroupSupervisors = [];
        $this->selectedGroupAgents = [];
        $this->removed_agents = [];
        $this->theIdToDelete = null;
    }

    public function setSortBy($sortByField)
    {
        if ($this->sortBy === $sortByField) {
            $this->sortDir = ($this->sortDir == 'ASC') ? 'DESC' : 'ASC';
            return;
        }

        $this->sortBy = $sortByField;
        $this->sortDir = 'DESC';
    }

    public function selectSupervisor($id)
    {
        $supervisor = User::find($id);

        // if ($userGroup && !in_array($userGroup->name, $this->selectedUserGroups)) {
        //     $this->selectedUserGroups[] = $userGroup->name;
        // }

        if ($supervisor && !in_array($supervisor->full_name, $this->possibleSupervisors)) {
            $this->possibleSupervisors[] = $supervisor->full_name;
            $this->possibleSupervisors_ids[] = $supervisor->id;
        }

        $this->searchSupervisors = '';
    }

    // add modal
    public function add_org($id)
    {
        $this->added_org_id = $id;
        $this->added_org = Organization::find($id)->name;

        // clear the already selected supervisors and agents 
        $this->selected_supervisors = [];
        $this->selected_agents = [];

        // get possible supervisors (that belong to the selected org)
        $this->possibleSupervisors = User::where('role', 2)->whereHas('supervisorOrganizations', function ($query) {
            $query->where('organization_id', $this->added_org_id);
        })->pluck('full_name', 'id');


        // get possible agents (that belong to the selected org)
        $this->possibleAgents = User::where('organization_id', $this->added_org_id)->pluck('full_name', 'id');
    }

    // add modal
    public function select_supervisor_addModal($index)
    {
        $this->selected_supervisors[$index] = User::find($index)->full_name;
    }

    // add modal
    public function remove_supervisor($index)
    {
        unset($this->selected_supervisors[$index]);
    }

    // add modal
    public function remove_agent($index)
    {
        unset($this->selected_agents[$index]);
    }

    // add modal
    public function select_agent_addModal($index)
    {
        $this->selected_agents[$index] = User::find($index)->full_name;
    }

    public function rules()
    {
        $rules = [];

        // Add rules for editing if selectedGroup is present
        if ($this->selectedGroupId) {
            $rules = [
                'selectedGroupName' => ['required', 'string', 'max:255', Rule::unique('user_groups', 'name')->ignore($this->selectedGroupId)],
                'selectedGroupOrg' => 'required',
            ];
        }

        // Add rules for adding group
        else {
            $rules = [
                'added_name' => ['required', 'string', 'max:255', 'unique:user_groups,name'],
                'added_org' => ['required'],
            ];
        }

        return $rules;
    }

    public function submitAddGroup()
    {
        $this->validate();

        // save the group 
        $group = UserGroup::create([
            'name' => $this->added_name,
            'organization_id' => $this->added_org_id,
        ]);

        // save the group supervisors 
        $group->supervisors()->sync(array_keys($this->selected_supervisors));

        // save the group agents 
        foreach ($this->selected_agents as $agent_id => $agent) {
            User::find($agent_id)->update([
                'user_group_id' => $group->id
            ]);
        }


        $this->alert("success", "Group Added Successfully");

        $this->closeModal();
    }

    // ============================================ edit modal =============================================================
    public function selectGroup($id)
    {
        $this->selectedGroupId = $id;
        $group = UserGroup::find($id);
        $this->selectedGroupName = $group->name;
        $this->selectedGroupOrg = $group?->organization?->name;
        $this->selectedGroupOrg_id = $group->organization_id ?? null;

        // get possible supervisors (that belong to the selected org)
        $this->possibleSupervisors = User::where('role', 2)->whereHas('supervisorOrganizations', function ($query) {
            $query->where('organization_id', $this->selectedGroupOrg_id);
        })->pluck('full_name', 'id');



        // get possible agents (that belong to the selected org)
        $this->possibleAgents = User::whereNotNull('organization_id')->where('organization_id', $this->selectedGroupOrg_id)->pluck('full_name', 'id');

        // get the supervisors of the selected group 
        $this->selectedGroupSupervisors = $group->supervisors()->pluck('full_name', 'users.id')->toArray();

        // get the agents of the selected user group 
        $this->selectedGroupAgents = $group->users->pluck('full_name', 'id')->toArray();
    }

    public function select_supervisor_editModal($index)
    {
        $this->selectedGroupSupervisors[$index] = User::find($index)->full_name;
    }

    public function select_agent_editModal($index)
    {
        $this->selectedGroupAgents[$index] = User::find($index)->full_name;
    }

    // edit modal
    public function remove_supervisor_edit($index)
    {
        unset($this->selectedGroupSupervisors[$index]);
    }

    // edit modal
    public function remove_agent_edit($index)
    {
        // Initialize $removed_agents array if not already initialized
        if (!isset($this->removed_agents)) {
            $this->removed_agents = [];
        }

        // Add the removed agent to the $removed_agents array with the corresponding index as the key
        $this->removed_agents[$index] = $this->selectedGroupAgents[$index];

        // Remove the agent from the $selectedGroupAgents array
        unset($this->selectedGroupAgents[$index]);
    }

    // edit modal
    public function select_agent_edit($index)
    {
        $this->selectedGroupAgents[$index] = User::find($index)->full_name;
    }

    public function submitEditGroup()
    {

        $this->validate();

        // save the group 
        $group = UserGroup::find($this->selectedGroupId)->update([
            'name' => $this->selectedGroupName,
            'organization_id' => $this->selectedGroupOrg_id,
        ]);

        // save the group supervisors 
        UserGroup::find($this->selectedGroupId)->supervisors()->sync(array_keys($this->selectedGroupSupervisors));

        // make the removed agents have a null user group 
        foreach ($this->removed_agents as $agent_id => $agent) {
            User::find($agent_id)->update([
                'user_group_id' => null
            ]);
        }

        // clear the removed agents array 
        $this->removed_agents = [];

        // save the group agents 
        foreach ($this->selectedGroupAgents as $agent_id => $agent) {
            User::find($agent_id)->update([
                'user_group_id' => $this->selectedGroupId
            ]);
        }

        $this->alert("success", "Group Edited Successfully");

        $this->closeModal();
    }

    public function changeOrg($id)
    {

        // if the user changes the already selected org, then clear the selected agents and supervisors (that belong the group already)
        if ($id != $this->selectedGroupOrg_id) {

            $this->selectedGroupOrg_id = $id;
            $this->selectedGroupOrg = Organization::find($id)->name;

            // add the removed agents to the removed agents array (so that if we submit the form, they get a null user group and not remain with the same old group)
            foreach ($this->selectedGroupAgents as $agent_id => $agent) {
                $this->remove_agent_edit($agent_id);
            }

            // clear the selected agents and supervisors arrays
            $this->selectedGroupAgents = [];
            $this->selectedGroupSupervisors = [];

            // the new possible supervisors according to the org
            $this->possibleSupervisors = User::where('role', 2)->whereHas('supervisorOrganizations', function ($query) {
                $query->where('organization_id', $this->selectedGroupOrg_id);
            })->pluck('full_name', 'id');

            // the new possible agents according to the org
            $this->possibleAgents = User::where('organization_id', $this->selectedGroupOrg_id)->pluck('full_name', 'id');
        }
    }

    public function closeModal()
    {
        $this->dispatch('closeModal');
    }

    public function render()
    {
        return view('livewire.user-groups', [
            'userGroups' => UserGroup::search($this->searchGroup)->orderBy($this->sortBy, $this->sortDir)->paginate(5),
            'allOrgs' => Organization::all(),
            'possibleOrgs' => Organization::all(),
        ]);
    }
}
