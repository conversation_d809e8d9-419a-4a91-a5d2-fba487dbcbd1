<div class="container-fluid mt-3 px-4">


    {{-- custom time modal --}}
    <div class="modal fade" id="custom_time_modal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="width:50vw !important">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <h5 class="modal-title">Custom Period</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 fw-bold">Date From <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="from" id="from" class="form-control bg-white" style="border: solid 1px #b6b6b6" placeholder="Date From" wire:model="custom_date_from">
                            @error('custom_date_from')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6 col-12">
                            <label for="call id" class="mb-2 fw-bold">Date To <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e"></i></label>
                            <input type="date" name="to" id="to" class="form-control bg-white" style="border: solid 1px #b6b6b6" placeholder="Date To" wire:model="custom_date_to">
                            @error('custom_date_to')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="background-color: #00a34e" id="closeCustomDate">Close</button>
                    <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply-custom-date" wire:click="apply_custom_date">Apply</button>
                </div>
            </div>
        </div>
    </div>

    {{-- header row  --}}
    <div class="row mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            Analytics
                        </b>
                    </h5>
                    {{-- <i class="fa-regular fa-file-excel fa-2xl float-end" style="cursor: pointer"></i> --}}
                    <h6 class="text-muted">
                        View statistics and analytics for all records
                        <i class="fa-solid fa-chart-simple fa-2xl float-end" style="color: #00a34e"></i>
                    </h6>
                </div>
            </div>
        </div>
    </div>

    {{-- Lower row  --}}
    <div class="row mx-3 d-flex ps-5">
        {{-- filters part  --}}
        <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column">
            <div class="card rounded-3 bg-white shadow shadow p-2">
                <div class="card-body py-3">
                    <h5 class="fw-bold">Filters</h5>
                    <hr>

                    <div class="col-md-12">
                        <form class="row g-2 mb-3" wire:submit.prevent>
                            <div class="col-md-4 col-12">
                                <label for="date" class="mb-2 fw-bold">Auto Refresh <i class="fa-solid fa-rotate-right fa-lg" style="color: #00a34e" title="Auto Refresh"></i></label>
                                <div class="input-group mb-3 p-0">
                                    <div class="input-group-text py-0">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="autoRefreshCheckbox" style="cursor: pointer;">
                                        </div>
                                    </div>
                                    <input type="number" id="refreshInterval" min="1" max="60" class="form-control rounded-0 border-1" placeholder="Enter minutes" aria-label="Minutes" aria-describedby="basic-addon2">
                                    <span class="input-group-text" id="basic-addon2">Minutes</span>
                                </div>
                            </div>
                            {{-- <div class="col-md-3">
                                <label for="call id" class="mb-2 fw-bold">Interval <i class="fa-solid fa-clock fa-lg" style="color: #00a34e"></i></label>
                                <select wire:model="selectedTime" class="form-select form-select mb-3">
                                    <option selected value="all">All Time</option>
                                    <option value="1">Last 24 Hours</option>
                                    <option value="7">Last 7 Days</option>
                                    <option value="40">Last 30 Days</option>
                                    <option value="60">Last 60 Days</option>
                                </select>
                            </div> --}}
                            <div class="col-md-4 col-12">
                                <label for="call id" class="mb-2 fw-bold">Interval <i class="fa-solid fa-clock fa-lg" style="color: #00a34e"></i></label>
                                {{-- <label for="call id" class="mb-2 fw-bold">Interval
                                    <img src="{{ asset('assets/images/clock.png') }}" alt="" srcset="" style="width:1.5rem; height:1.5rem">
                                </label> --}}
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $interval }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="z-index: 9999999999999999">
                                        <li><a class="dropdown-item" wire:click="$set('interval', 'Today')">Today</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('interval', 'Last 7 Days')">Last 7 Days</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('interval', 'Last 30 Days')">Last 30 Days</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('interval', 'Last 60 Days')">Last 60 Days</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" data-bs-toggle="modal" data-bs-target="#custom_time_modal">Custom</a></li>
                                        {{-- <li><a class="dropdown-item" href="#" wire:click="$set('interval', '7')">Fixed Time</a></li> --}}
                                    </ul>
                                </div>
                            </div>

                            {{-- <div class="col-md-3">
                                <label for="Agent name" class="mb-2 fw-bold">Organization <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Oraganization"></i></label>
                                <select wire:model="selectedTime" class="form-select form-select mb-3">
                                    <option selected value="all">All Time</option>
                                    <option value="1">Last 24 Hours</option>
                                    <option value="7">Last 7 Days</option>
                                    <option value="40">Last 30 Days</option>
                                    <option value="60">Last 60 Days</option>
                                </select>
                            </div> --}}
                            <div class="col-md-4 col-12">
                                <label for="Agent name" class="mb-2 fw-bold">Organization <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Oraganization"></i></label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        @if ($organizations)
                                            {{ $selected_organization ?? 'All' }}
                                        @else
                                            --
                                        @endif
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; overflow:auto;">
                                        @if ($organizations)
                                            <li><a class="dropdown-item" wire:click="$set('selected_organization', 'All')">All</a></li>
                                            <hr class="m-0">
                                        @endif
                                        @forelse ($organizations as $organization)
                                            <li><a class="dropdown-item" wire:click="selectOrganizations('{{ $organization->id }}')">{{ $organization->name }}</a></li>
                                            @if (!$loop->last)
                                                <hr class="m-0">
                                            @endif
                                        @empty
                                            <li><a class="dropdown-item text-center text-muted">No Organizations Found</a></li>
                                        @endforelse
                                    </ul>
                                </div>
                            </div>


                            {{-- <div class="col-md-3">
                                <label for="Duration" class="mb-2 fw-bold">Skill Group <i class="fa-solid fa-people-roof fa-lg" style="color: #00a34e" title="Skill Group"></i></label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        All
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu" style="height: 8rem; overflow:auto;">
                                        @forelse ($skillGroups as $group)
                                            <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">{{ $group->name }}</a></li>
                                            @if (!$loop->last)
                                                <hr class="m-0">
                                            @endif
                                        @empty
                                            No Skill Groups Found
                                        @endforelse
                                    </ul>
                                </div>
                            </div> --}}
                        </form>
                    </div>

                    <button class="btn btn-md btn-success" style="background-color: #00a34e" id="apply" wire:click="filter">Apply</button>
                    <button class="btn btn-md btn-outline-success ms-1" id="clear" wire:click="clear">Clear</button>
                </div>
            </div>
        </div>

        {{-- analytics part  --}}
        <div class="col-12 col-md-12 col-lg-12 d-flex flex-column mt-5">
            {{-- row1 of analytics --}}
            <div class="row d-flex justify-content-center g-1">

                <div class="col-lg-4 col-12">
                    <div class="card bg-white shadow card-pop-out-effect">
                        <div class="row card-body d-flex justify-content-center">
                            <div class="col-4">
                                {{-- <img src="{{ asset('assets/images/analytics/telephone-call.png') }}" alt="telephone" style="width: 4rem;float:right"> --}}
                                <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-03.svg') }}" alt="telephone" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                            </div>
                            <div class="col-8 flex-column pe-0 ps-0">
                                <div class="col-12 fs-6">Total Interactions</div>
                                <div class="col-12">
                                    <h4>{{ number_format($total_interactions_count) }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- <div class="col-md-2 col-12">
                    <div class="card bg-white shadow card-pop-out-effect">
                        <div class="card-body d-flex justify-content-center pe-0">
                            <div class="col-4">
                                <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-04.svg') }}" alt="transferred" style="width: 4rem">
                            </div>
                            <div class="col-8 flex-column" style="width: fit-content">
                                <div class="col-12">Transferred</div>
                                <div class="col-12">
                                    <h4>00</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> --}}

                {{-- <div class="col-md-2 col-12">
                    <div class="card bg-white shadow card-pop-out-effect">
                        <div class="card-body d-flex justify-content-center pe-0">
                            <div class="col-4">
                                <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-05.svg') }}" alt="transferred" style="width: 4rem">
                            </div>
                            <div class="col-8 flex-column" style="width: fit-content">
                                <div class="col-12">Screen Capture</div>
                                <div class="col-12">
                                    <h4>00</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> --}}

                <div class="col-lg-4 col-12">
                    <div class="card bg-white shadow card-pop-out-effect">
                        <div class="row card-body d-flex justify-content-center">
                            <div class="col-4">
                                {{-- <img src="{{ asset('assets/images/analytics/telephone-call.png') }}" alt="telephone" style="width: 4rem;float:right"> --}}
                                <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-10.svg') }}" alt="transferred" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                            </div>
                            <div class="col-8 flex-column pe-0 ps-0">
                                <div class="col-12 fs-6">Avg. Duration</div>
                                <div class="col-12">
                                    <h4>{{ $average_duration }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col-lg-4 col-12">
                    <div class="card bg-white shadow card-pop-out-effect">
                        {{-- <div class="card-body d-flex justify-content-center">
                            <div class="col-4">
                                <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-06.svg') }}" alt="transferred" style="width: 4rem; float:right">
                            </div>
                            <div class="col-8 flex-column">
                                <div class="col-12">Total Duration</div>
                                <div class="col-12">
                                    <h4>116:23:50</h4>
                                </div>
                            </div>
                        </div> --}}
                        <div class="row card-body d-flex justify-content-center">
                            <div class="col-4">

                                {{-- <img src="{{ asset('assets/images/analytics/telephone-call.png') }}" alt="telephone" style="width: 4rem;float:right"> --}}
                                <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-06.svg') }}" alt="telephone" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                            </div>
                            <div class="col-8 flex-column pe-0 ps-0">
                                <div class="col-12 fs-6">Total Duration</div>
                                <div class="col-12">
                                    <h4>{{ $total_duration }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- first row of analytics part  --}}
            </div>

            {{-- row2 of analytics --}}
            <div class="row d-flex justify-content-center mt-1 g-1">
                <div class="col-lg-3 col-12">
                    <div class="card bg-white shadow py-3 card-pop-out-effect">
                        <div class="row card-body d-flex justify-content-center">
                            <div class="col-4">
                                <img src="{{ asset('assets/images/analytics-svg/Artboard 5.svg') }}" alt="telephone" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                            </div>
                            <div class="col-8 flex-column pe-0 ps-0">
                                <div class="col-12 fs-6">Duration > 8 Minutes</div>
                                <div class="col-12">
                                    <h4>{{ $durationOver8 }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-white shadow mt-2 py-3 card-pop-out-effect">
                        <div class="row card-body d-flex justify-content-center">
                            <div class="col-4">
                                <img src="{{ asset('assets/images/analytics-svg/Artboard 10.svg') }}" alt="telephone" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                            </div>
                            <div class="col-8 flex-column pe-0 ps-0">
                                <div class="col-12 fs-6">Duration < 2 Minutes</div>
                                        <div class="col-12">
                                            <h4>{{ $durationUnder2 }}</h4>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12" wire:ignore>
                        <div class="card bg-white shadow card-pop-out-effect" style="height: 16.7rem !important">
                            <div class="card-body d-flex justify-content-center">
                                <canvas id="barChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-12">
                        <div class="card bg-white shadow py-3 card-pop-out-effect">
                            <div class="row card-body d-flex justify-content-center">
                                <div class="col-4">
                                    <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-09.svg') }}" alt="telephone" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                                </div>
                                <div class="col-8 flex-column pe-0 ps-0">
                                    <div class="col-12 fs-6">Avg. Hold Time</div>
                                    <div class="col-12">
                                        <h4>{{ $averageHoldTime }}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card bg-white shadow mt-2 py-3 card-pop-out-effect">
                            <div class="row card-body d-flex justify-content-center">
                                <div class="col-4">
                                    <img src="{{ asset('assets/images/analytics-svg/769 [Converted]-08.svg') }}" alt="telephone" style="width:3rem;float:right;margin-top:0.5rem;margin-bottom:0.5rem">
                                </div>
                                <div class="col-8 flex-column pe-0 ps-0">
                                    <div class="col-12 fs-6">Hold > 2 Seconds</div>
                                    <div class="col-12">
                                        <h4>{{ $holdOverTwoSeconds }}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- <div class="card bg-white shadow mt-2">
                            <div class="row card-body d-flex justify-content-center">
                                <div class="col-4">
                                    <img src="{{ asset('assets/images/analytics/pause-button.png') }}" alt="telephone" style="width: 4rem;float:right">
                                </div>
                                <div class="col-8 flex-column pe-0 ps-0">
                                    <div class="col-12">Flags</div>
                                    <div class="col-12">
                                        <h4>137</h4>
                                    </div>
                                </div>
                            </div>
                        </div> --}}
                    </div>
                    {{-- second row of analytics part  --}}
                </div>
            </div>

            {{-- row3 of analytics --}}
            <div class="row d-flex justify-content-center mt-1 g-1 px-2 ps-2">
                {{-- Call types chart  --}}
                <div class="col-md-6 col-12" wire:ignore>
                    <div class="card shadow bg-white ms-1 p-3 card-pop-out-effect" style="height: 15.5rem">
                        <h6><b>Interactions Per Group</b></h6>
                        <div class="pieChartContainer" style="height: 11.5rem">
                            <canvas id="pieChart"></canvas>
                        </div>
                    </div>
                </div>

                {{-- Interactions table  --}}
                <div class="col-md-6 col-12">
                    <div class="card shadow bg-white card-pop-out-effect">
                        <div class="card-body">
                            <h6><b>Call Types</b></h6>
                            <div class="table-responsive rounded-0" style="height: 11.8rem; box-shadow:none">
                                <table class="table table-hover table-sm table-hover table-bordered mb-0">
                                    <thead style="height: 3rem !important" class="text-center align-middle">
                                        <tr class="text-center">
                                            <th style="padding: 0.5rem !important; font-size:0.7rem" scope="col">ID</th>
                                            <th style="padding: 0.5rem !important; font-size:0.7rem" scope="col">GROUP</th>
                                            <th style="padding: 0.5rem !important; font-size:0.7rem" scope="col">AVG. INTERACTIONS DURATION</th>
                                            <th style="padding: 0.5rem !important; font-size:0.7rem" scope="col">TOTAL INTERACTIONS</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-center fs-6">
                                        @forelse ($userGroups as $group)
                                            <tr wire:key={{ $group->id }}>
                                                <td style="font-size: 0.8rem">{{ $loop->index + 1 }}</td>
                                                <td style="font-size: 0.8rem">{{ $group->name }}</td>
                                                <td style="font-size: 0.8rem">{{ fake()->numberBetween(1, 50) }}</td>
                                                <td style="font-size: 0.8rem">{{ fake()->numberBetween(100, 1000) }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td style="font-size: 0.8rem" colspan="4">No Records Available</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- end of row 3  --}}
            </div>


            {{-- analytics part  --}}
        </div>
        {{-- bottom content except the page header  --}}
    </div>






    {{-- Auto Refresh Script  --}}
    <script>
        const autoRefreshCheckbox = document.getElementById('autoRefreshCheckbox');
        const refreshIntervalInput = document.getElementById('refreshInterval');
        let intervalId;

        // Retrieve values from local storage
        const localStorageCheckbox = localStorage.getItem('autoRefreshCheckbox');
        const localStorageInterval = localStorage.getItem('refreshInterval');

        // Set checkbox state and interval from local storage
        if (localStorageCheckbox === 'true') {
            autoRefreshCheckbox.checked = true;
        }

        if (localStorageInterval) {
            refreshIntervalInput.value = localStorageInterval;
        }

        function refreshPage() {
            intervalId = setInterval(() => {
                window.location.reload();
            }, refreshIntervalInput.value * 60000);
        }

        // Check if auto-refresh is enabled and set up interval
        if (autoRefreshCheckbox.checked && refreshIntervalInput.value >= 1) {
            refreshPage();
        }

        autoRefreshCheckbox.addEventListener('change', function() {
            if (this.checked && refreshIntervalInput.value >= 1) {
                // Enable auto-refresh and save to local storage
                localStorage.setItem('autoRefreshCheckbox', 'true');
                localStorage.setItem('refreshInterval', refreshIntervalInput.value);
                refreshPage();
            } else {
                // Disable auto-refresh and remove from local storage
                localStorage.removeItem('autoRefreshCheckbox');
                clearInterval(intervalId);
            }
        });

        refreshIntervalInput.addEventListener('input', function() {
            // Save interval to local storage even if checkbox is unchecked
            localStorage.setItem('refreshInterval', this.value);

            if (autoRefreshCheckbox.checked && this.value >= 1) {
                clearInterval(intervalId);
                refreshPage();
            }
        });
    </script>

    {{-- container fluid --}}

</div>
