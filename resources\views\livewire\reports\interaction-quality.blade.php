<div class="container-fluid mt-3 px-4">
    {{-- header row  --}}
    <div class="header mb-4"> <!-- Added margin-bottom to separate filter button from table -->
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button data-bs-toggle="modal" data-bs-target="#filterModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon">
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>
    <div class="parent-sections mx-3 ps-5">
        <div class="section-one">
            <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">
                <table class="table table-hover table-striped" id="table"
                    style="margin-bottom: 0px;border-radius: 0px;">
                    <thead id="thead" class="text-muted thead" style="font-size: 0.7rem">
                        <tr class="text-muted bg-white" style="vertical-align:middle">
                            <th class="text-center" style="width:15rem" title="Unique call identifier">
                                Call&nbsp;ID
                            </th>

                            <th class="text-center" style="width:15rem" title="Overall audio-quality verdict">
                                Final&nbsp;Result
                            </th>

                            <th class="text-center" style="width:15rem"
                                title="Side of the conversation (Agent/Customer)">
                                Side
                            </th>

                            <th class="text-center" style="width:15rem" title="Longest silence in seconds">
                                sec
                            </th>

                            <th class="text-center" style="width:15rem" title="Root-mean-square power">
                                RMS
                            </th>

                            <th class="text-center" style="width:15rem" title="DC offset value">
                                DC&nbsp;Offset
                            </th>

                            <th class="text-center" style="width:15rem" title="Maximum amplitude">
                                Max&nbsp;Amplitude
                            </th>

                            <th class="text-center" style="width:15rem" title="Spectral centroid">
                                Spectral&nbsp;Centroid
                            </th>

                            <th class="text-center" style="width:15rem" title="Codec artefact score">
                                Codec&nbsp;Artifacts
                            </th>

                            <th class="text-center" style="width:15rem" title="Percentage of frames with speech">
                                VAD&nbsp;Speech&nbsp;%
                            </th>
                        </tr>
                    </thead>
                    <tbody style="font-size:0.8rem">
                        @forelse($qualityChecks as $qc)
                            <tr class="align-middle">
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->call_id }}</td>
                                <td class="text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    <span
                                        class="{{ strtolower($qc->final_result) == 'good' ? 'text-success' : (strtolower($qc->final_result) == 'bad' ? 'text-danger' : 'text-muted') }}">
                                        {{ $qc->final_result }}
                                    </span>
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ strtolower($qc->side) === 'right' ? 'Agent' : 'Customer' }}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->longest_silence_sec }}</td>

                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->rms }}</td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->dc_offset }}</td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->max_amplitued }}</td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->spectral_centroid }}</td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->codec_artifacts }}</td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $qc->vad_speech_pct }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="text-center">No records found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="d-flex align-items-center">
                    <label for="perPage" class="me-2">Show:</label>
                    <select wire:change="recordsPerPage($event.target.value)" class="form-select form-select-sm"
                        style="width: auto;">
                        <option value="15" {{ $perPage == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                    </select>
                    <span class="ms-2">entries</span>
                </div>

                {{ $qualityChecks->links() }}
            </div>
        </div>
    </div>
    {{-- Filter Modal --}}
    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white;">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3"
                            style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon"
                                style="width: 30px; height: 30px; color: #01a44f;">
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel"
                            style="font-size: 30px;">Filter</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body" style="border: none;">
                    <form class="row g-2 mb-3" wire:submit.prevent="filterByCallId">
                        <div class="col-12">
                            <label for="call id" class="mb-2 ">Call ID
                                <img src="{{ asset('assets/images/callid.png') }}" alt="" srcset=""
                                    style="width:1.3rem; height:1.3rem">
                            </label>
                            <input style="border: solid 1px #b6b6b6; font-size:0.85rem; width: 100%;" type="text"
                                name="call-id" id="call-id" class="form-control w-100" placeholder="Call #"
                                wire:model.defer="callIdFilter">
                        </div>
                        <div class="col-md-12 mt-3 d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary rounded-3 px-4"
                                style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                                wire:click="clearFilter" data-bs-dismiss="modal">
                                <span style="font-size: 17px;">Clear</span>
                            </button>
                            <button type="submit" class="btn btn-success rounded-3 px-4 ms-2"
                                style="height: 40px; border-color: #01a44f; background: #01a44f;"
                                data-bs-dismiss="modal">
                                <span style="font-size: 17px;">Apply</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
