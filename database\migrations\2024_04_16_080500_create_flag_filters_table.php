<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flag_filters', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('qa_flag_id')->constrained('qa_flags')->onDelete('cascade');
            $table->string('condition');
            $table->string('first_data');
            $table->string('second_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flag_filters');
    }
};
