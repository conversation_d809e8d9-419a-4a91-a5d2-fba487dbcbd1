@extends('layouts.app')

@section('title', 'Recording Playback')

@section('style')
    <style>
        input {
            border: solid 1px #b6b6b6 !important;
            border-radius: 0.6rem !important;
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .play-button:hover {
            color: #00a34e !important;
        }

        #playButton,
        #pauseButton,
        #openSpeedSelectorButton {
            font-size: 16px;
            cursor: pointer;
            width: 3rem;
            height: 3rem;
        }

        #openSpeedSelectorButton {
            font-size: 16px;
            cursor: pointer;
        }

        #playButton {
            background-color: #abc959;
            color: white;
            border: none;
        }

        #pauseButton {
            background-color: #02a34e;
            color: white;
            border: none;
        }

        #openSpeedSelectorButton {
            background-color: #2196F3;
            color: white;
            border: none;
        }

        #speedSelectorModal {
            display: inline;
        }

        .parent-btn {
            margin-top: 0.5rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        #duration {
            float: right;
            background-color: #c6d64e;
            border-radius: 0.5rem;
            padding: 0.1rem 0.3rem;
            font-weight: 400;
            color: black;
        }

        #time {
            background-color: #03a34d;
            border-radius: 0.5rem;
            padding: 0.1rem 0.3rem;
            font-weight: 400;
            color: white;
        }

        #loadingMessage {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: inline-block;
            border-top: 4px solid #FFF;
            border-right: 4px solid transparent;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
        }

        #loadingMessage::after {
            content: '';
            box-sizing: border-box;
            position: absolute;
            /* left: 0; */
            /* top: 0; */
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border-left: 4px solid #c6d64e;
            border-bottom: 4px solid transparent;
            animation: rotateLoader 0.5s linear infinite reverse;
        }

        @keyframes rotateLoader {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .nav-link {
            border-radius: var(--bs-nav-tabs-border-radius) !important;
            color: #40798c !important;

        }

        .nav-tabs .nav-link.active,
        .nav-tabs .nav-item.show .nav-link {
            border-radius: var(--bs-nav-tabs-border-radius) !important;
            color: white !important;
            background: #40798c !important;
            padding-bottom: 5%;
            border-radius: 12px !important;


        }

        .rec-tabs-link {
            font-size: 1.1rem !important;
            border-radius: 12px !important;
            padding-top: 3% !important;
            padding-bottom: 2% !important;
            font-weight: bolder !important;
        }

        .conv-text-container {
            display: flex;
        }

        .conv-text-container-agent {

            justify-content: flex-end;

        }

        .conv-text-container-customer {
            justify-content: flex-start;
        }

        .conv-text {

            /*  width: fit-content; */
            font-weight: bold;
            border-radius: 12px;
            font-size: 1.1rem !important;
            color: #38798b;
        }

        .conv-text-container-customer>.conv-text {

            background: #f3f8ff;
            border: 1px solid #f3f8ff;
        }

        .conv-text-container-agent>.conv-text {

            background: #ffeeee;
            border: 1px solid #fbf3fe;
        }

        .conv-controllers {
            /* background: #aliceblue; */
            width: fit-content;
            border: 1px solid #fbf3fe;
            border-radius: 12px;
            font-size: 0.95rem !important;
            border: none !important;
        }

        .conv-row {
            display: flex;
            align-items: baseline;
        }

        .transcript-header {
            background-color: #ececec;
            border-top-left-radius: 1.2rem;
            border-top-right-radius: 1.2rem;
            color: blue;
            font-weight: bolder;
        }

        .tab-pane .transcription-container {
            max-height: 70vh;
            overflow-x: hidden;
            overflow-y: scroll;
            border-right: 3px solid #eff3f4;
        }

        .events-container {
            max-height: 70vh;
            overflow-x: hidden;
            overflow-y: scroll;
            border-left: 3px solid #eff3f4;
        }

        .events-div {
            display: flex;
            flex-direction: row;
            align-items: center;

            gap: 3%;

        }

        .tab-content {
            box-shadow: #eff3f4 0px 0px 3px 3px;
        }

        #search-wrapper {
            display: flex;
            border: 1px solid rgba(0, 0, 0, 0.276);
            align-items: stretch;
            border-radius: 0.6rem ;
            background-color: #fff;
            overflow: hidden;
            max-width: 50%;
            box-shadow: 2px 1px 5px 1px rgba(0, 0, 0, 0.273);

        }

        #search {
            border: none !important;
            width: 100%;
            font-size: 15px;
        }

        #search:focus {
            outline: none;
        }

        .search-icon {
            margin: 10px;
            color: rgba(0, 0, 0, 0.564);
        }

        #search-button {
            border: none;
            cursor: pointer;
            color: #fff;
            background-color: #1dbf73;
            padding: 0px 10px;
        }
        .events-div > div > h5 {
            text-decoration: underline;
        }
        .events-div > div > h4 {

            font-weight: bold;
        }
        .all-tabs{
            padding-left: 0 !important;
            width: 100% !important;
        }
    </style>
@endsection


@section('content')
    <div class="container-fluid mt-3 px-4">
        {{-- header row  --}}
        <div class="row mx-3 ps-5">
            <div class="col-12 mb-5">
                <div class="card bg-white shadow py-2 px-3">
                    <div class="card-body p-4">
                        <h5>
                            <b>
                                Call ID: <span style="color: "> {{ $callId }} </span>
                            </b>
                        </h5>
                        {{-- <i class="fa-regular fa-file-excel fa-2xl float-end" style="cursor: pointer"></i> --}}
                        <h6 class="text-muted">
                            <span style="color: #bfd23b">
                                {{ $agent_name }} -
                            </span>
                            <span style="color: #bfd23b">
                                {{ $agent_id }} -
                            </span>

                            <span style="color: #bfd23b">
                                {{ Carbon::now()->format('d/m/Y g:i A') }}
                            </span>
                            <i class="fa-solid fa-microphone-lines fa-2xl float-end" style="color: #00a34e"></i>
                        </h6>

                        {{-- <!-- Show loading message -->
                        <div id="loading-message">Loading...</div>

                        <!-- Show quote -->
                        <div id="quote" style="display: none;"></div> --}}
                    </div>
                </div>
            </div>
        </div>

        <div class="row mx-3 d-flex ps-5">
            {{-- filters card  --}}
            {{-- <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column">
                <div class="card rounded-3 bg-white shadow p-2">
                    <div class="card-body py-3">
                        <div id="waveform" class="mb-3"></div>

                        <div id="time" class="d-inline-block">0:00</div>
                        <div id="duration" class="d-inline-block">00:00</div>
                        <span style="margin-left: 48%" id="loadingMessageText" class="mt-5 text-nowrap"></span>
                        <div id="loadingMessage" class="preloader mt-0 mb-4" style="margin-left: 50%"></div>

                        <div class="parent-btn" style="visibility: hidden">
                            <button id="pauseButton" class="rounded-circle align-middle text-center" title="Pause" data-bs-toggle="tooltip">
                                <i class="fa-solid fa-pause fa-lg mt-3"></i>
                            </button>

                            <button id="playButton1" style="display: none">Play Audio</button>
                            <button id="playButton" class="rounded-circle align-middle text-center" title="Play" data-bs-toggle="tooltip">
                                <i class="fa-solid fa-play fa-lg ms-1 mt-3"></i>
                            </button>

                            <button id="openSpeedSelectorButton" class="rounded-circle text-nowrap" title="Speed" data-bs-toggle="tooltip">1x</button>

                            <span class="modal-content3" id="speedSelectorModal" style="display: none; margin-top:0.8rem">
                                <select id="speedSelector" class="selectTaq">
                                    <option value="0.5">.5x</option>
                                    <option value="1" selected>1x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2">2x</option>
                                </select>
                            </span>

                        </div>

                    </div>
                </div> --}}
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item">
                    <a href="#info" role="tab" data-toggle="tab" class="nav-link active rec-tabs-link"> Infos </a>
                </li>
                <li class="nav-item">
                    <a href="#timeline" role="tab" data-toggle="tab" class="nav-link rec-tabs-link"> Timeline </a>
                </li>
                <li class="nav-item">
                    <a href="#qasummary" role="tab" data-toggle="tab" class="nav-link rec-tabs-link"> Quality Summary
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#transcript" role="tab" data-toggle="tab" class="nav-link rec-tabs-link"> Transcript </a>
                </li>
            </ul>
            <div class="tab-content all-tabs">
                <div class="tab-pane active" role="tabpanel" id="info">
                    <h3> Infos </h3>
                    <p> Lorem ipsum sit amet </p>
                </div>
                <div class="tab-pane" role="tabpanel" id="timeline">
                    <h3> Timeline </h3>
                    <p> Lorem ipsum dolorem </p>
                </div>
                <div class="tab-pane" role="tabpanel" id="qasummary">
                    <h3>Quality Summary </h3>
                    <p> Sit amet </p>
                </div>
                <div class="tab-pane" role="tabpanel" id="transcript">
                    {{dd($transcript)}}
                    <x-transcript :transcription="$transcript" />
                </div>
            </div>
        </div>
    </div>

    </div>

    </div>

    <script src="https://unpkg.com/wavesurfer.js@7/dist/wavesurfer.min.js"></script>
    <script src="https://unpkg.com/wavesurfer.js@7/dist/plugin/wavesurfer.timeline.min.js"></script>
    <script src="https://unpkg.com/wavesurfer.js@7/dist/plugin/wavesurfer.minimap.min.js"></script>

    <script type="module">
        let voiceFileName;
        // Listen for the voiceFileName event
        document.addEventListener('voice-File', function(event) {

            voiceFileName = event.detail.voiceFileName;
            // voiceFileName = "testAudio.wav";
        });

        // Function to close modal when clicking outside of it
        window.addEventListener('click', function(event) {
            const speedSelectorModal = document.getElementById('speedSelectorModal');
            const openSpeedSelectorButton = document.getElementById('openSpeedSelectorButton');
            const speedSelector = document.getElementById('speedSelector');
            if (event.target !== speedSelectorModal && event.target !== openSpeedSelectorButton && event.target !==
                speedSelector) {
                speedSelectorModal.style.display = 'none'; // Hide the modal
            }
        });
        // Function to open modal when the button is clicked
        document.getElementById('openSpeedSelectorButton').addEventListener('click', function() {
            const speedSelectorModal = document.getElementById('speedSelectorModal');
            speedSelectorModal.style.display = 'inline'; // Display the modal
        });

        // Close the modal when clicking outside of it
        window.onclick = function(event) {
            const speedSelectorModal = document.getElementById('speedSelectorModal');
            if (event.target == speedSelectorModal) {
                speedSelectorModal.style.display = 'none';
            }
        };
        import WaveSurfer from 'https://unpkg.com/wavesurfer.js@7/dist/wavesurfer.esm.js';

        let wavesurfer; // Declare wavesurfer variable in a global scope
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        // Function to initialize WaveSurfer and start playing audio
        // Initialize WaveSurfer
        const initializeWaveSurfer = () => {
            wavesurfer = WaveSurfer.create({
                container: '#waveform',
                waveColor: '#03a34d',
                progressColor: '#a8c858',
                {{-- url: '{{ asset("audio/$callId.wav") }}', --}}
                backend: 'MediaElement',
                preload: 'auto',
                responsive: true,
                normalize: false,
                barWidth: 5,
                barRadius: 5,
                responsive: true,
                height: 80,
                autoCenter: true,
                pixelRatio: 1
            });

            const formatTime = (seconds) => {
                const minutes = Math.floor(seconds / 60)
                const secondsRemainder = Math.round(seconds) % 60
                const paddedSeconds = `0${secondsRemainder}`.slice(-2)
                return `${minutes}:${paddedSeconds}`
            }
            const timeEl = document.querySelector('#time')
            const durationEl = document.querySelector('#duration')

            wavesurfer.on('ready', function() {
                const duration = wavesurfer.getDuration();
                durationEl.textContent = formatTime(duration)
                const speedSelectorModal = document.getElementById('speedSelectorModal');
                speedSelectorModal.style.display = 'none';

                document.getElementById('loadingMessage').style.display = 'none'; // Hide loading message
                document.getElementById('loadingMessageText').style.display = 'none'; // Hide loading message
                document.getElementById('waveform').style.visibility = 'visible'; // Show waveform
                document.querySelector('.parent-btn').style.visibility = 'visible';
            });

            // Show loading message when loading starts
            wavesurfer.on('loading', function(percentage) {
                document.getElementById('loadingMessage').style.display = 'inline'; // Show loading message
                document.getElementById('waveform').style.visibility =
                'hidden'; // Optionally hide waveform initially
                document.getElementById('loadingMessageText').innerHTML = "Fetching: " + percentage +
                "%"; // Hide loading message
                document.getElementById('loadingMessageText').style.display = 'block'; // Hide loading message
            });

            // Hide loading message and show the waveform when ready
            // wavesurfer.on('ready', function() {
            //     document.getElementById('loadingMessage').style.display = 'none'; // Hide loading message
            //     document.getElementById('waveform').style.visibility = 'visible'; // Show waveform
            //     document.querySelector('.parent-btn').style.visibility = 'visible'; // Show waveform
            //     // Additional initialization such as setting up playback controls
            // });

            // Update time display when playback reaches certain point
            wavesurfer.on('timeupdate', function(time) {
                const formattedTime = formatTime(time); // Format the current time
                document.getElementById('time').textContent =
                formattedTime; // Update the time display in the div
            });
            // Listen for the 'finish' event to detect when the audio playback finishes
            wavesurfer.on('finish', function() {
                document.getElementById('pauseButton').style.opacity = 1;
                document.getElementById('playButton').style.opacity = 1;
            });
            // Listen for changes in playback speed
            document.getElementById('speedSelector').addEventListener('change', function() {
                const selectedSpeed = parseFloat(this.value); // Get the selected playback speed
                wavesurfer.setPlaybackRate(selectedSpeed); // Set the playback speed of the audio
                speedSelectorModal.style.display = 'none';
                document.getElementById('openSpeedSelectorButton').textContent = this.value + "x";
            });
        };

        // Function to fetch decrypted audio using AJAX
        console.log('fetching');
        const fetchDecryptedAudio = (encryptedFilePath, encryptedCallId) => {
            fetch(
                    `/get_call/decrypt-audio/${encodeURIComponent(encryptedFilePath)}/${encodeURIComponent(encryptedCallId)}`)
                .then(response => response.blob())
                .then(blob => {


                    // Assuming decryption is handled on server side and response is a blob
                    wavesurfer.loadBlob(blob);
                })
                .catch(error => console.error('Error fetching audio:', error));
        };

        // // Initialize WaveSurfer when DOM is ready

        document.addEventListener('DOMContentLoaded', function() {
            initializeWaveSurfer();
            const encryptedFilePath = '{{ $callName }}'; // Replace with actual encrypted file path
            const encryptedCallId = '{{ $callId }}'; // Replace with actual encrypted file path
            fetchDecryptedAudio(encryptedFilePath, encryptedCallId);
        });



        // Event listener for the button click to initialize WaveSurfer
        document.getElementById('playButton1').addEventListener('click', () => {
            // Check if the AudioContext is suspended, and resume it if needed
            if (typeof Tone !== 'undefined' && Tone.context.state === 'suspended') {
                Tone.context.resume().then(() => {
                    initializeWaveSurfer();
                });
            } else {
                initializeWaveSurfer();
            }
        });
        // Event listener for the play button
        document.getElementById('playButton').addEventListener('click', () => {
            if (wavesurfer && !wavesurfer.isPlaying()) {
                wavesurfer.play();
                document.getElementById('playButton').style.opacity = .5;
                document.getElementById('pauseButton').style.opacity = 1;
            }

            // save who played the call
            var call_id = '{{ $callId }}';
            var user_id = '{{ Auth::id() }}';

            $.ajax({
                url: '{{ route('userPlayedInteraction') }}',
                type: 'POST',
                data: {
                    interaction_id: call_id,
                    user_id: user_id,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    console.log(response);
                },
                error: function(response) {
                    alert(response.responseJSON.message);
                }
            });
        });

        // Recalculate waveform when the window is resized
        window.addEventListener('resize', function() {
            wavesurfer.drawer.containerWidth = container.clientWidth;
            wavesurfer.drawBuffer();
        });




        // Event listener for the pause button
        document.getElementById('pauseButton').addEventListener('click', () => {
            if (wavesurfer && wavesurfer.isPlaying()) {
                wavesurfer.pause();
                document.getElementById('pauseButton').style.opacity = .5;
                document.getElementById('playButton').style.opacity = 1;
            }
        });
        // Initialize WaveSurfer when the DOM content is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                document.getElementById('playButton1').click();
            }, 500);
        });
    </script>


    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous">
    </script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
        integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous">
    </script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"
        integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous">
    </script>



    </div>
@endsection
