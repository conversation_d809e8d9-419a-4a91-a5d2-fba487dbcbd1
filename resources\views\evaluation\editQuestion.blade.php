@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Edit Question Page')

{{-- Style Section --}}
@section('style')

    <style>
     


        .arrow-icon {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            pointer-events: none;
 
            width: 36px;
            height: 32px;
        }
      




    /*                              ------      CheckBox Style      ------                   */
        .checkbox-parent , .radio-parent{
            display: flex;
        }
        .form-group {

            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 3px;

        }

        .form-group input {
            padding: 0;
            height: initial;
            width: initial;
            margin-bottom: 0;
            display: none;
            cursor: pointer;
        }

        .form-group label {
            position: relative;
            cursor: pointer;
        }

        .form-group label:before {
            content: '';
            -webkit-appearance: none;
            background-color: transparent;
            border: 2px solid #e1e7e4;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
            padding: 8px;
            display: inline-block;
            position: relative;
            vertical-align: middle;
            cursor: pointer;
            margin-right: 15px;
        }

        .form-group input:checked + label:after {
            content: '';
            display: block;
            position: absolute;
            top: 2px;
            left: 7px;
            width: 6px;
            height: 14px;
            border: solid #00a34e;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .form-group-raido{
            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 3px;
        }
        .form-group-raido input {

            cursor: pointer;
            height: 17px;
            width: 18px;
            vertical-align: sub;
            border: 2px solid #cacaca;
        }
    /*                              ------      End CheckBox Style      ------                   */
        .dropdown-style{
            border: 1px solid #e3e3e3;
            padding: 10px;
            border-radius: 10px;
            cursor: pointer;
        }
        .circle {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #00a34e;
            display: flex;
            justify-content: center;
            align-items: center;
        
        }

        .form-control{
            border-radius: 0;
        }
        .content{
            height: 90vh;
            border-radius: 20px;
            background-color: white;
            margin-left: 5rem !important;
            margin-right: 1.5rem !important;
            border-radius: 7px !important;
            padding: 0px !important;
            /*margin-bottom: 5rem;*/
        }
        .header-div{
            /* background-image: linear-gradient(to right, #02a34e , #abc959); */
            background-image: linear-gradient(to right, #40798c , #7a9fab);
            border-top-left-radius: 7px !important;
            border-top-right-radius: 7px !important;
            border-radius: var(--bs-card-border-radius);
        }
        .coustome-style-div{
            padding: 2%;
            height: 100%;
            overflow: auto;
        }
        .footer-btn{
            padding: 20px;
        }
        
    </style>
<style>


    .but{
        padding-left: 20px;
        padding-right: 20px;
        border-radius: 40px;
        border-color: #e0f4ea;
        background-color: #e0f4ea;
        border: 2px;
        padding-top: 5px;
        padding-bottom: 5px;
    }
    .row-tow-table{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: 70vh !important;
    }
    .table2 , .table1{
        /* height: 24rem !important; */
        height: 70vh !important;
        overflow-y: auto ;
    }
    .dropdown-container , .dropdown-container2{
        width: 11% !important;
        /*right: 17%;*/
        --bs-dropdown-zindex: 1000;
        --bs-dropdown-min-width: 10rem;
        --bs-dropdown-padding-x: 0;
        --bs-dropdown-padding-y: .5rem;
        --bs-dropdown-spacer: .125rem;
        --bs-dropdown-font-size: .9rem;
        --bs-dropdown-color: #212529;
        --bs-dropdown-bg: #fff;
        --bs-dropdown-border-color: var(--bs-border-color-translucent);
        --bs-dropdown-border-radius: .375rem;
        --bs-dropdown-border-width: 1px;
        --bs-dropdown-inner-border-radius: calc(.375rem - 1px);
        --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
        --bs-dropdown-divider-margin-y: .5rem;
        --bs-dropdown-box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
        --bs-dropdown-link-color: #212529;
        --bs-dropdown-link-hover-color: #1e2125;
        --bs-dropdown-link-hover-bg: #e9ecef;
        --bs-dropdown-link-active-color: #fff;
        --bs-dropdown-link-active-bg: #0d6efd;
        --bs-dropdown-link-disabled-color: #adb5bd;
        --bs-dropdown-item-padding-x: 1rem;
        --bs-dropdown-item-padding-y: .25rem;
        --bs-dropdown-header-color: #6c757d;
        --bs-dropdown-header-padding-x: 1rem;
        --bs-dropdown-header-padding-y: .5rem;
        position: absolute;
        z-index: var(--bs-dropdown-zindex);
        min-width: var(--bs-dropdown-min-width);
        padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
        margin: 0;
        font-size: var(--bs-dropdown-font-size);
        color: var(--bs-dropdown-color);

        list-style: none;
        background-color: var(--bs-dropdown-bg);
        background-clip: padding-box;
        border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
        border-radius: var(--bs-dropdown-border-radius);
    }

    /*
    new styles
    */
        .switche{
            font-size: 150%;
            cursor: pointer;
        }
        .table{
            text-align: center;
            vertical-align: middle
        }
        .span_icon{
            vertical-align: sub;
            float: inline-end;
            cursor: pointer;
        }
        td{
            vertical-align: middle;
            font-size: 12px;
        }
        thead{
            height: 50px;
            vertical-align: middle;
            
        }
        thead tr th{
            vertical-align: middle;
            background-color: #40798c !important;
            color:white !important;
            font-size: 15px;
        }
        .parent-sections {
            height: 70vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: 1.5%;
            margin-bottom: 3%;
        }

        .section-one{
            width: 100%;
            height: 100%;
        }
        .div-table{
            /* border: 1px solid #d0caca; */
            border-radius: 0px;
            width: 100%;
            height: 100%;
            overflow: auto;
        }
        .form-control , .form-select , .dropdown-toggle-style{
            background-color: #eff3f4 !important;
            border: none !important;
            height: 40px;
        }
        label{
            color : #40798c !important;
            font-size: 17px;
            /* font-weight: 700 !important; */
        }
        .previous{
            margin-bottom: 5px;
        }
    /*
    end new styles
    */
    /*
    pagination styles
    */
            #searchInput {
                height: 2.8rem !important;
                width: 100% !important;
                /* Increase the height for a larger input */
                padding-left: 2.5rem !important;
                /* Increase padding for better spacing */
                border: none !important;
                /* Slightly darker border */
                border-radius: 0.5rem;
                /* Rounded corners */
                box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
                /* Subtle shadow */
                transition: box-shadow 0.3s ease, border-color 0.3s ease;
                /* Smooth transition */
                font-size: 1.2rem;
                /* Slightly larger text size */
                background-position: left 0.5rem center;
                /* Icon positioning */
            }

            /* Focus styles */
            #searchInput:focus {
                outline: none;
                /* Remove default outline */
                box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
                /* Larger shadow on focus */
                border-color: rgba(0, 0, 0, 0.3);
                /* Slightly darker border on focus */
            }

            /* Placeholder styling */
            #searchInput::placeholder {
                font-family: inherit;
                /* Use inherited font style */
                color: #01A44F;
                /* Green placeholder text */
                font-size: 1.2rem;
                /* Match placeholder size with input text */
            }

            .main-buttons-container button {
                height: 2.9rem;
                font-size: 15px;
            }

            .main-buttons-container button:hover {
                background-color: #018F3E !important;
            }

            /* pagination  */
            ul.pagination {
                gap: 0.3rem;
            }

            ul.pagination li button,
            ul.pagination li span {
                padding: 0.9rem;
                padding-top: 0.5rem;
                padding-bottom: 0.5rem;
            }

            ul.pagination li button:hover {
                background-color: rgb(196, 183, 183) !important;
            }

            ul.pagination>li>button,
            ul.pagination>li>span {
                color: black !important;
                font-weight: 600 !important;
                background-color: white;
            }

            .page-item span,
            .page-item button {
                border-radius: 0.7rem !important;
            }

            .page-item.active span,
            .page-item.active button {
                border-radius: 0.5rem !important;
            }

            .page-item.active>span {
                background-color: #00a34e !important;
                color: white !important;
            }

            div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
                font-size: 0.9rem;
            }

            div.tab-pane label {
                font-weight: 600 !important;
            }

            div.tab-pane hr {
                display: none;
            }
    /*
    end pagination styles
    */
</style>

@endsection

{{-- Content Section --}}
@section('content')
    
    <div class="container-fluid">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
        <livewire:evaluation.edit-question :evaluation_id="$evaluation_id" :group_id="$group_id" :question_id="$question_id" />
    </div>
    
@endsection

{{-- Script Section --}}
@section('script')
    <script type="module">

        $(document).ready(function() {
            var searchButton = $('#search_button');
            var search = $('#search');

            searchButton.click(function() {
                if (search.css('display') === 'none') {
                    search.css('display', 'block');
                } else {
                    search.css('display', 'none');
                }
            });
        });
        window.addEventListener('close-modal', event => {
            document.getElementById('closeModal').click()
        });
        window.addEventListener('open-modal', event => {
            $('#open_modal').click()
        });
        window.addEventListener('close-modal-search', event => {
            $('#search_button').click()
        });

        document.addEventListener('DOMContentLoaded', function () {
            var searchButton = document.getElementById('searchButton');

            if (searchButton) {
                searchButton.addEventListener('click', function (event) {
                    event.stopPropagation(); // Stop the event propagation
                });
            }
        });
        window.addEventListener('click-on-fatal-checkbox', event => {
            document.getElementById('Fatal').click()
        });
        window.addEventListener('click-on-na-checkbox', event => {
            document.getElementById('NA').click()
        });








    </script>
@endsection
