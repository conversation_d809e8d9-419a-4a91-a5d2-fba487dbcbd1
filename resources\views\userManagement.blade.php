@extends('layouts.app')

@section('title', 'Users Management')
@section('style')
    <style>
        /* hide the scrollbar for lists  */
        .dropdown-menu::-webkit-scrollbar {
            display: none
        }


        thead th {
            background-color: #40798C !important;
            color: #FFFFFF !important;
            /* font-size: medium; */
            font-size: small;
        }

        tbody td {
            font-size: small !important;
            height: 4.5rem;
            font-weight: 600;
            border-bottom: none;
        }

        .table-responsive {
            box-shadow: none !important;
            border-radius: 1rem !important;
        }

        input {
            border: solid 1px #b6b6b6 !important;
            /* border-radius: 0.6rem !important; */
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .list-group-item:hover {
            color: white;
            background-color: #00a34e;
        }

        th {
            text-wrap: nowrap;
        }

        body {
            background-color: #FFFFFF !important;
        }

        #searchInput {
            height: 2.8rem !important;
            width: 100% !important;
            /* Increase the height for a larger input */
            padding-left: 2.5rem !important;
            /* Increase padding for better spacing */
            border: none !important;
            /* Slightly darker border */
            border-radius: 0.5rem;
            /* Rounded corners */
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow */
            transition: box-shadow 0.3s ease, border-color 0.3s ease;
            /* Smooth transition */
            font-size: 1.2rem;
            /* Slightly larger text size */
            background-position: left 0.5rem center;
            /* Icon positioning */
        }

        /* Focus styles */
        #searchInput:focus {
            outline: none;
            /* Remove default outline */
            box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
            /* Larger shadow on focus */
            border-color: rgba(0, 0, 0, 0.3);
            /* Slightly darker border on focus */
        }

        /* Placeholder styling */
        #searchInput::placeholder {
            font-family: inherit;
            /* Use inherited font style */
            color: #01A44F;
            /* Green placeholder text */
            font-size: 1.2rem;
            /* Match placeholder size with input text */
        }

        .main-buttons-container button {
            height: 2.3rem;
            font-size: 14px;
        }

        .main-buttons-container button:hover {
            background-color: #018F3E !important;
        }

        /* pagination  */
        ul.pagination {
            gap: 0;
        }

        ul.pagination li button,
        ul.pagination li span {
            padding: 0.7rem;
            padding-top: 0.4rem;
            padding-bottom: 0.4rem;
        }

        ul.pagination li button:hover {
            background-color: rgb(196, 183, 183) !important;
        }

        ul.pagination>li>button,
        ul.pagination>li>span {
            color: black !important;
            font-weight: 600 !important;
            background-color: white;
        }

        .page-item span,
        .page-item button {
            border-radius: 0.7rem !important;
        }

        .page-item.active span,
        .page-item.active button {
            border-radius: 0.5rem !important;
        }

        .page-item.active>span {
            background-color: #00a34e !important;
            color: white !important;
        }

        div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
            font-size: 0.9rem;
        }

        div.tab-pane label {
            font-weight: 600 !important;
        }

        div.tab-pane hr {
            display: none;
        }

        .page-link[aria-label="« Previous"],
        .page-link[aria-label="Next »"] {
            padding: 0.8rem !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }


        /* modal  */
        #add-multi .modal-header {
            background-color: transparent !important;
        }

        .modal-title {
            color: black;
            font-weight: 600;
        }

        legend {
            color: #40798C !important;
        }

        .tab-button {
            border: none !important;
            background-color: white !important;
            color: black !important;
            font-weight: 500;
            border: 1px solid rgb(235, 226, 226) !important;
            border-radius: 3rem !important;
        }

        .tab-button.active {
            color: #00a34e !important;
        }

        .modal-content,
        .modal-header {
            background-color: white !important;
        }

        .modal-header {
            border: none !important;
        }

        .modal-apply-close-btns {
            width: 10rem;
        }

        .modal-close {
            height: 40px !important;
            border-color: #eff3f4 !important;
            background: #eff3f4 !important;
            color: #6c97a6 !important;
        }

        .modal-apply {
            height: 40px !important;
            border-color: #01a44f !important;
        }

        .modal-footer {
            border: none !important;
        }

        /* Uploader  */
        .custom-file-input-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            background-color: #eff3f4;
            height: 2.9rem;
            border-radius: 0.5rem;
        }

        .custom-file-input {
            position: absolute;
            opacity: 0;
            z-index: -1;
        }

        .custom-file-label {
            background-color: #01A44F;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            cursor: pointer;
            text-align: center;
            white-space: nowrap;
            height: 2.8rem;
            padding-top: 2.6%;
        }

        .custom-file-label:hover {
            background-color: #028f3d;
        }

        .custom-file-input-wrapper::after {
            content: "No file chosen";
            vertical-align: baseline;
            padding-left: 15px;
            font-size: 14px;
            color: #666;
        }
        .color {
            color: #40798c !important;
        }
    .bg-color {
            background-color: #eff3f4 !important;
        }
        .form-control,
        .form-select,
        .dropdown-toggle-style {
            background-color: #eff3f4 !important;
            color: #40798c !important;
            border: none !important;
            height: 40px;
        }
        tr th {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        tr td {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }
    </style>
@endsection

@section('content')
    <livewire:user-management>

        <script>
            window.addEventListener('closeModal', () => {
                // document.querySelector('#close').click();
                document.querySelector('#closeEdit').click();
                document.querySelector('#closeAdd').click();
                document.querySelector('#closeAddMulti').click();
            });
        </script>
    @endsection
