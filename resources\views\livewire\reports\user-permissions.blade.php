<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto">
                <button wire:target="export" wire:click="export" title="Export" class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3" style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;" wire:loading.attr="disabled">

                    <i wire:loading.remove wire:target="export" class="fas fa-file-excel text-white me-2" style="font-size: 20px;"></i>

                    <span wire:loading.class="spinner-border spinner-border-sm" wire:target="export" style="width: 1rem; height: 1rem;" role="status" aria-hidden="true"></span>

                    <span wire:loading.remove wire:target="export" style="font-size: 17px;">Extract Excel</span>
                </button>
            </div>
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button data-bs-toggle="modal" data-bs-target="#filterModal" class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3" style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon" >
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>


    <div class="parent-sections mx-3 ps-5">

        <div class="section-one">
            <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">

                <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                    <thead id="thead" class="text-muted thead" style="font-size: 0.7rem">
                        <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important;">
                            <th scope="col" class="text-center align-middle px-md-1 px-3 py-0">ID</th>
                            <th scope="col" class="text-center align-middle px-md-1 px-3 py-0">
                                Full Name
                            </th>
                            <th scope="col" style="" class="text-center align-middle px-md-1 px-3 py-0">
                                Active
                            </th>
                            <th scope="col" style="" class="text-center align-middle px-md-1 px-3 py-0">
                                Allow Access Telephony
                            </th>

                            <th scope="col" class="text-center align-middle px-md-1 px-3 py-0">
                                View Evaluation Reports
                            </th>

                            <th scope="col" class="text-center align-middle px-md-1 px-3 py-0">
                                View Other Evaluation Comments
                            </th>

                            <th scope="col" class="text-center align-middle px-md-1 px-3 py-0">
                                Play Recorded Interactions
                            </th>

                            {{-- <th scope="col" style="" class="text-center align-middle px-md-1 px-3 py-0 ">
                                    VIEW INTERACTIONS FLAGGING
                                </th> --}}
                            {{-- <th scope="col" style="" class="text-center align-middle text-nowrap px-md-1 px-3 py-0 ">
                                    SEARCH BY EVALUATION FORM
                                </th> --}}
                            <th scope="col" style="" class="text-center align-middle text-nowrap px-md-1 px-3 py-0 ">
                                Create Interaction Evaluation
                            </th>
                            {{-- <th scope="col" style="" class="text-center align-middle text-nowrap px-md-1 px-3 py-0 ">
                                    DELETE INTERACTION EVALUATION
                                </th> --}}
                            {{-- <th scope="col" style="" class="text-center align-middle text-nowrap px-md-1 px-3 py-0 ">
                                    EDIT OWN EVALUATION
                                </th> --}}
                            {{-- <th scope="col" style="" class="text-center align-middle text-nowrap px-md-1 px-3 py-0 ">
                                    EDIT EVALUATION OF OTHER SUPERVISOR
                                </th> --}}
                            <th scope="col" style="" class="text-center align-middle text-nowrap px-md-1 px-3 py-0 ">
                                View Interaction Evaluation
                            </th>
                        </tr>
                    </thead>
                    <tbody class="" style="font-size:0.8rem" id="tbody">

                        @forelse($users as $user)
                            <tr class="align-middle">
                                <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                <td class="text-muted text-center py-3 align-middle text-nowrap" style="width: 30%"> {{ $user->full_name }} </td>
                                {{-- <td class="text-muted text-center py-3 align-middle"> {{ $user->email }} </td> --}}
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->enabled == 1 && !$user->terminated ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->permissions()->where('permission_id', 1)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->permissions()->where('permission_id', 10)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->permissions()->where('permission_id', 5)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->permissions()->where('permission_id', 2)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        {!! $user->parameters()->where('parameter_id', 23)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                    </td> --}}
                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        {!! $user->permissions()->where('permission_id', 8)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}

                                    </td> --}}
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->permissions()->where('permission_id', 8)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        {!! $user->permissions()->where('permission_id', 8)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                    </td> --}}
                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        {!! $user->permissions()->where('permission_id', 8)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                    </td> --}}

                                {{-- <td class="text-muted text-center py-3 text-nowrap align-middle">

                                        {!! $user->permissions()->where('permission_id', 8)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                    </td> --}}
                                <td class="text-muted text-center py-3 text-nowrap align-middle">
                                    {!! $user->permissions()->where('permission_id', 8)->exists() ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="18" class="text-muted text-center bg-white"> No users found</td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-between mt-2">
                <!-- Dropdown for Number of Items per Page -->
                <div class="mt-0">
                    <select id="itemsPerPage" wire:change="interactionsPerPage($event.target.value)" class="form-select bg-white d-inline fw-bold" style="width: auto; cursor: pointer; height:2rem; background-color:#eff3f4 !important">
                        <option value="10">10</option>
                        <option value="15" selected>15</option>
                        <option value="30">30</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span>Results Per Page</span>
                </div>

                <!-- Pagination Links -->
                <div>
                    {{ $users->links(data: ['scrollTo' => false]) }}
                </div>
            </div>

        </div>
    </div>


    {{-- <div class="mx-3 ps-5">
            <div class="col-6">
                <a href="{{ route('admin.reports') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                    <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
                </a>
            </div>
        </div> --}}

    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white;">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body" style="border: none;">
                    <div class="col-md-12">
                        <form class="row g-2 mb-3">
                            <div class="col-md-6">
                                <label for="call id" class="mb-2 ">Name <i class="fa-solid fa-user fa-lg" style="color: #00a34e"></i></label>
                                <input style="border: solid 1px #b6b6b6;font-size:0.85rem" type="text" class="form-control py-2" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="User Name" wire:model='filter_name'>
                            </div>
                            <div class="col-md-6">
                                <label for="call id" class="mb-2 ">User ID <i class="fa-solid fa-hashtag fa-lg" style="color: #00a34e"></i></label>
                                <input style="border: solid 1px #b6b6b6;font-size:0.85rem" type="text" class="form-control py-2" id="exampleInputEmail1" aria-describedby="emailHelp" wire:model='filter_agentId' placeholder="User ID">
                            </div>

                            <div class="col-md-6">
                                <label for="call id" class="mb-2 ">Role <i class="fa-solid fa-user-group fa-lg" style="color: #00a34e"></i></label>
                                <div class="dropdown">
                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_role_name ?? 'All' }}</span>
                                    </button>
                                    <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                        <li>
                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole('All')">All</span>
                                        </li>
                                        <hr class="m-0">
                                        <li>
                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(4)">Agents</span>
                                        </li>
                                        <hr class="m-0">
                                        <li>
                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(1)">Admins</span>
                                        </li>
                                        <hr class="m-0">
                                        <li>
                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(2)">Supervisors</span>
                                        </li>
                                        <hr class="m-0">
                                        <li>
                                            <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterRole(3)">IT</span>
                                        </li>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="call id" class="mb-2 ">Organization <i class="fa-solid fa-building fa-lg" style="color: #00a34e"></i></label>
                                <div class="dropdown">
                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style  p-2" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_org_name ?? 'All' }}</span>
                                    </button>
                                    <ul id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton" style="height: 10rem; overflow:auto">
                                        @forelse ($orgs as $org)
                                            <li>
                                                <span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filterOrg('{{ $org->id }}')">{{ $org->name }}</span>
                                            </li>
                                            @if (!$loop->last)
                                                <hr class="m-0">
                                            @endif
                                        @empty
                                        @endforelse
                                    </ul>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>

                <div class="modal-footer" style="border: none;">
                    <button type="button" class="btn btn-secondary rounded-3 px-4" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;" wire:click="clear" wire:loading.attr="disabled" wire:target="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Clear</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear" role="status" aria-hidden="true"></span>
                    </button>
                    <button class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f;" wire:click="getData" wire:loading.attr="disabled" wire:target="getData">
                        <span wire:loading.remove wire:target="getData" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="getData" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
