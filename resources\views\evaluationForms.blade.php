@extends('layouts.app')

@section('title', 'Evaluation Forms')
@section('content')
    @livewire('evaluation-forms')

    <style>
        input {
            border: solid 1px #b6b6b6 !important;
            border-radius: 0.6rem !important;
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }
        .thead {
            height: 50px;
            vertical-align: middle;
        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.8rem !important;
            padding: 11px !important;
        }

        table td {
            border-bottom: none !important;
        }

        table {
            position: relative;
        }
        tr th {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

        tr td {
            color: white;
            /* font-size: initial; */
            font-weight: 600;
            font-size: 0.9rem !important;
            padding: 11px !important;
        }

    </style>

    <script>
        window.addEventListener('closeModal', () => {
            document.querySelector('#close').click();
            document.querySelector('#closeEdit').click();
        });
    </script>
@endsection
