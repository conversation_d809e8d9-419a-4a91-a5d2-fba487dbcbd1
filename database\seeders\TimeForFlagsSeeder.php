<?php

namespace Database\Seeders;

use App\Models\TimeForFlags;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TimeForFlagsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        TimeForFlags::create([
            'flag_frequency' => 'daily',
            'time' => now()->startOfDay(),
        ]);
        TimeForFlags::create([
            'flag_frequency' => 'weekly',
            'time' => now()->startOfDay(),
        ]);
        TimeForFlags::create([
            'flag_frequency' => 'daily',
            'time' => now()->startOfDay(),
        ]);
    }
}
