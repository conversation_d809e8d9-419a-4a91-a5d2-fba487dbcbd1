@extends('layouts.app')

{{-- Tilte Section --}}
@section('title', 'Form Builder Page')

{{-- Style Section --}}
@section('style')

    <style>
        .fa-xl {
            line-height: 1.1em !important;
        }
        .d-block {
            width: 60px;
            height: 59px;
        }
        .section-tow{
            width: 45%;
            background-color: white;
            border: 1px solid #c3d1c9;
            border-radius: 10px;
            padding-right: 0 !important;
            padding-left: 0 !important;
        }
        .parent-sections{
            height: 35.5rem;
            margin: 5% 0% 2%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding-left: 3.3rem !important;
            padding-right: 0.6rem !important;
        }
        .header-section-tow{
            background-color: #f9f9f9;
            height: 13%;
            width: 100%;
            
            border: 1px solid #c3d1c9;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 4%;
            text-align: center;
            font-size: larger;
            font-weight: 500;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .content-section-tow{
            padding: 5%;
            height: 82%;
            display: flex;
            flex-direction: column;
            overflow: auto;
            max-height: 25rem;
        }
        .div-content{
            height: 20%;
            border: 1px solid #f6f6f6;
            padding: 2%;
            text-align: center;
            border-radius: 10px;
            box-shadow: 0 0px 6px #00000026!important;
            cursor: pointer;
            display: flex;
            overflow-y: auto;
            margin-bottom: 5%;
        }
        .div-content-text{
            width: 90%;
            font-weight: bold;
            font-size: larger;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .div-content-remove{
            width: 10%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    .div-content-selected{
        background-color: #00a34e;
        color: white;
        height: 20%;
        border: 1px solid #f6f6f6;
        padding: 2%;
        text-align: center;
        border-radius: 10px;
        box-shadow: 0 0px 6px #00000026!important;
        cursor: pointer;
        display: flex;
        overflow-y: auto;
        margin-bottom: 5%;
    }
    .section-one-header{
        height: 5%;
    }
    .section-one-boxes{
        height: 32%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 4% 0%;
        /*margin: 4% 0%;*/
    }
    .section-one-boxes-box{
        width: 33%;
        height: 100%;
        border-radius: 10px;
        box-shadow: 0 0px 10px #00000026!important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background-color: white;
        padding-left: 1%;
        cursor: pointer;
    }
    .div-icon{
        /*width: 30%;*/
        text-align: center;
        /*border-radius: 75%;*/
        background-color: #f4f4f4;
        /*height: 55%;*/
        width: 60px;
        height: 60px;
        border-radius: 50%;
        }
    .div-icon > img {
            margin-top: 24%;
        }
    .div-text{
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    .parent-box-content{
            display: flex;
            flex-direction: row;
            justify-content: space-around;
        }
    .section-one-info{
        height: 63%;
        width: 100%;
        border: 4px dashed #ccc;
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: -webkit-center;
    }
    .footer-back{
        margin: 4% 0% 4%;
    }
    .section-one-circle{
        width: 70px;
        height: 70px;
        border: 4px dashed #646464;
        border-radius: 50px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
        .section-one-text{
            margin-top: 8%;
            color: #646464;
            font-weight: bold;
        }
        .section-one-details{
            height: 65%;
            width: 100%;
            box-shadow: 0 0px 10px #00000026!important;
            border-radius: 10px;
            max-height: 25em;
        }
        .section-one-details-header{
            /* background-image: linear-gradient(to right, #02a34e , #abc959); */
            background-image: linear-gradient(to right, #40798c , #7a9fab);
            color: white;
            height: 20%;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 4%;
        }
        .section-one-details-content{
            height: 80%;
            overflow-x: hidden;
        }

        /*                              ------      CheckBox Style      ------                   */
        .checkbox-parent , .radio-parent{
            display: flex;
        }
        .form-group {

            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 3px;

        }

        .form-group input {
            padding: 0;
            height: initial;
            width: initial;
            margin-bottom: 0;
            display: none;
            cursor: pointer;
        }

        .form-group label {
            position: relative;
            cursor: pointer;
        }

        .form-group label:before {
            content: '';
            -webkit-appearance: none;
            background-color: transparent;
            border: 2px solid #e1e7e4;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
            padding: 8px;
            display: inline-block;
            position: relative;
            vertical-align: middle;
            cursor: pointer;
            margin-right: 15px;
        }

        .form-group input:checked + label:after {
            content: '';
            display: block;
            position: absolute;
            top: 2px;
            left: 7px;
            width: 6px;
            height: 14px;
            border: solid #00a34e;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        /*                              ------      End CheckBox Style      ------                   */
        .mandatory{
            padding: 2% 5%;
        }
        .form-control{
            border-radius: 0px;
        }
        .label{
            padding: 0% 5%;
       
                color : #40798c !important;
                font-size: 17px;
                /* font-weight: 700 !important; */
            }
        .col-form-label{
            padding-left: 0px !important;
            /* font-weight: bold; */
        }
        .arrow-icon {
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            pointer-events: none;
            fill: #00a34e;
            width: 36px;
            height: 32px;
        }
        .submit-style{
            width: 15%;
        }
        .btn-color{
            background-color: #10a64f;
            border-color: #10a64f;
        }
        .btn-color:hover{
            background-color: #198754;
            border-color: #198754;
        }
        h4{
            margin: 0px !important;
        }
        .dragging {
            opacity: 100;
            /*border: 2px dashed #000;*/
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3) !important;
            /* Add other styles to indicate dragging */
        }
        .circle {
            cursor: pointer;
            width: 40px !important;
            height: 40px;
            border-radius: 50%;
            background-color: #00a34e;
            display: flex;
            justify-content: center;
            align-items: center;
        
        }
        .fa-style {
            
            font-size: 18px;
        }
        .text-danger{
            padding: 0px;
        }
        .alert-warning {
            width: 60%;
            border-radius: 5px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
    </style>




@endsection

{{-- Content Section --}}
@section('content')


    <livewire:evaluation.create-form-builder :evaluation_id="$evaluation_id"/>

 
@endsection

{{-- Script Section --}}

    <script type="module">



        window.addEventListener('open-modal', event => {
            document.getElementById('modal').style.display='';
            document.getElementById('dropZone').style.display='none';
        });
        window.addEventListener('close-modal', event => {
            document.getElementById('modal').style.display='none';
            document.getElementById('dropZone').style.display='';
        });
        window.addEventListener('off-checkbox', event => {
            document.getElementById('required').click();
        });



    </script>

