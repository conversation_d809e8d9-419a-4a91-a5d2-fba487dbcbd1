<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interaction_qa_flag', function (Blueprint $table) {
            $table->unsignedBigInteger('interaction_id');
            $table->unsignedBigInteger('qa_flag_id');
            
            // Define foreign keys
            $table->foreign('interaction_id')->references('id')->on('interactions')->onDelete('cascade');
            $table->foreign('qa_flag_id')->references('id')->on('qa_flags')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interaction_qa_flag');
    }
};
