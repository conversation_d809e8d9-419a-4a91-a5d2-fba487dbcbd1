<?php

namespace App\Livewire\Reports;

use App\Exports\DisconnectedExport;
use Livewire\Component;
use App\Models\Evaluation;
use App\Models\SkillGroup;
use App\Exports\HoldExport;
use App\Models\Interaction;
use App\Models\Organization;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class Disconnected extends Component
{
    use LivewireAlert, WithPagination, WithoutUrlPagination;

    public $filterType = 'General';

    public $searchCalls = '';

    public $perPage = 15;

    // filters
    public $filterApplied = false;
    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_agentId;
    public $filter_duration;
    public $filter_callType;
    public $filter_calledId;
    public $filter_callerId;
    public $filter_callEnder;
    public $filter_holdDuration;
    public $filter_holdCount;
    public $filter_isAssigned;
    public $filter_evaluationScore;
    public $filter_isEvaluated;
    public $filter_qaFlaggedInteractions;
    public $filter_flagDate;
    public $filter_agentName;
    public $filter_account;
    public $filter_skillGroup;
    public $filter_callImportance;
    public $filter_pauseCount;
    public $filter_language;
    public $filter_evaluationForm;
    public $filter_duration_sign = '=';
    public $filter_holdDuration_sign = '=';
    public $filter_holdCount_sign = '=';
    public $filter_uniqueId;

    // new filter LANGUAGES 
    public $filter_selected_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
    public $all_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];

    // when the user clicks play 
    public $callId;
    public $evaluationReport = array();
    public $selected_interaction_id;
    public $callID = null;

    public $selectedInteractionComments;
    public $selectedInteractionToAddComment;
    public $selectedCommentToDelete;
    public $added_comment;
    public $recordID;
    public $add_comment_field = false;
    public $viewCommentsModal = false;
    public $selectedInteractionId; //when clicking view comments
    public $viewEvaluationModal = false;
    public $viewEvaluationFormModal = false;
    // filters
    public $filter_name;
    public $filter_agent_id;
    public $filter_user_group;
    public $filter_role;
    public $filter_terminated;
    public $filter_enabled;

    public $filter_user_group_name;
    public $filter_role_name;
    public $filter_terminated_name;
    public $filter_enabled_name;


    public $sortBy = 'full_name';
    public $sortDir = 'ASC';

    public $filtersApplied = false;

    // live search 
    public $searchUser = '';

    // Users modal
    public $activeTab = 'general';

    // public $users;
    public $selectedUserId;
    public $selectedUserName;
    public $selectedUserRole;

    public $email;
    public $name;
    public $password;
    public $role;

    protected $paginationTheme = 'bootstrap';

    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }
    public function mount()
    {
        $this->filter_time_set('1');
        $this->filterApplied = true;
    }

    public function filter_languages($language)
    {
        if ($this->filter_selected_languages && in_array($language, $this->filter_selected_languages)) {
            $this->filter_selected_languages = array_values(array_diff($this->filter_selected_languages, [$language]));
        } else {
            $this->filter_selected_languages[] = $language;
            $this->filter_selected_languages = array_values($this->filter_selected_languages);
        }
    }


    public function filterTypeChange($data)
    {
        $this->filterType = $data;
    }

    public function apply_custom_date()
    {
        $this->filter_time_name = 'Custom';

        $this->validate();

        $this->dispatch('closeCustomDateModal');
    }

    public function getComments($id)
    {
        $this->selectedInteractionId = $id;
        $this->viewCommentsModal = true;

        if (!Auth::user()->permissions()->where('permission_id', 5)->exists()) {

            $this->selectedInteractionComments = Interaction::find($id)->comments()
                ->whereHas('commenter', function ($query) {
                    $query->where('id', '=', Auth::id());
                })->get();
        } else {
            $this->selectedInteractionComments = Interaction::find($id)->comments()->get();
        }
    }

    public function filter_time_set($time)
    {
        $this->filter_time_days = $time;

        $this->filter_time_name = match ((int) $time) {
            1 => 'Last 24 Hours',
            7 => 'Last 7 Days',
            30 => 'Last 30 Days',
            60 => 'Last 60 Days',
            default => 'Unknown time',
        };

        if ($this->filter_time_name == 'Custom') {
            $this->filter_time_days = null;
        }
    }

    public function rules()
    {
        return [
            'custom_date_from' => 'required_if:filter_time_name,Custom|',
            'custom_date_to' => 'required_if:filter_time_name,Custom|after:custom_date_from'
        ];
    }

    public function clear()
    {

        $this->filterApplied = false;
        $this->filter_time_name = 'All Time';
        $this->custom_date_from = null;
        $this->custom_date_to = null;
        $this->filter_time_days = null;
        $this->filter_uniqueId = null;

        $this->filter_callId = null;
        $this->filter_agentId = null;
        $this->filter_duration = null;
        $this->filter_callType = null;

        $this->filter_calledId = null;
        $this->filter_callerId = null;
        $this->filter_callEnder = null;
        $this->filter_holdDuration = null;
        $this->filter_holdCount = null;

        $this->filter_isAssigned = null;
        $this->filter_evaluationScore = null;
        $this->filter_isEvaluated = null;
        $this->filter_qaFlaggedInteractions = null;
        $this->filter_flagDate = null;
        $this->filter_duration_sign = '=';
        $this->filter_holdDuration_sign = '=';
        $this->filter_holdCount_sign = '=';

        $this->filter_agentName = null;
        $this->filter_account = null;
        $this->filter_skillGroup = null;
        $this->filter_callImportance = null;
        $this->filter_pauseCount = null;
        $this->filter_language = null;

        $this->filter_selected_languages = $this->all_languages;
        $this->filter_evaluationForm = null;

        $this->added_comment = '';
        $this->add_comment_field = false;
        $this->viewCommentsModal = false;
        $this->selectedInteractionToAddComment = null;
        $this->selectedInteractionId = null;
        $this->viewEvaluationModal = false;
        $this->viewEvaluationFormModal = false;
        $this->callID = null;
        $this->recordID = null;
        $this->filterType = 'General';

        $this->dispatch('selectAllLangs');
    }

    public function selectAllLanguages()
    {
        // Assuming you have an array of all possible languages
        $allLanguages = ['Arabic', 'English', 'Kurdish', 'Spanish', 'German', 'French', 'Italian', 'Russian', 'Urdu', 'Hebrew', 'Turkish'];

        // Check if all languages are currently selected
        if (count($this->filter_selected_languages) === count($allLanguages)) {
            // If all are selected, clear the selection
            $this->filter_selected_languages = [];
            $shouldCheck = false;
        } else {
            // Otherwise, select all languages
            $this->filter_selected_languages = $allLanguages;
            $shouldCheck = true;
        }

        // Dispatch browser event to check/uncheck checkboxes
        $this->dispatch('checkOrUncheckLangs', ['shouldCheck' => $shouldCheck]);
    }

    public function closeModal()
    {
        $this->resetValidation();
        $this->dispatch('closeModal');
    }

    public function filter()
    {
        $this->filterApplied = true;
        $this->resetPage();
    }

    public function getEvaluation()
    {

        return $data = Evaluation::query()->where('status', '1')->get();
    }
    public function export()
    {
        // Extend maximum execution time
        set_time_limit(0);

        // Extend the memory
        ini_set('memory_limit', '4000M');

        $export = new DisconnectedExport(
            $this->filter_uniqueId,
            $this->all_languages,
            $this->filter_time_name,
            $this->filter_time_days,
            $this->custom_date_from,
            $this->custom_date_to,
            $this->filter_callId,
            $this->filter_agentId,
            $this->filter_duration,
            $this->filter_callType,
            $this->filter_calledId,
            $this->filter_callerId,
            $this->filter_callEnder,
            $this->filter_holdDuration,
            $this->filter_holdCount,
            $this->filter_isAssigned,
            $this->filter_evaluationScore,
            $this->filter_isEvaluated,
            $this->filter_qaFlaggedInteractions,
            $this->filter_flagDate,
            $this->filter_agentName,
            $this->filter_account,
            $this->filter_skillGroup,
            $this->filter_callImportance,
            $this->filter_pauseCount,
            $this->filter_language,
            $this->filter_selected_languages,
            $this->filter_holdDuration_sign,
            $this->filter_holdCount_sign,
            $this->filter_duration_sign,
        );

        // Return the export response
        return Excel::download($export, 'disconnected_calls_report.xlsx');
    }
    public function getData() 
    {
        $this->filter();
        $this->dispatch('close-modal');
    }
    public function render()
    {
        // if agent 
        if (Auth::user()->role == 4) {
            $interactions = Interaction::where('user_id', Auth::id())->orderByDesc('arrival_time');
            // if IT or supervisor 
        } else if (in_array(Auth::user()->role, [2, 3])) {
            $interactions = Interaction::orderByDesc('arrival_time');
            // if quality
        } else if (Auth::user()->role == 5) {
            $interactions = Interaction::orderByDesc('arrival_time');
        }
        // if admin
        else {
            $interactions = Interaction::orderByDesc('arrival_time');
        }

        // filters 
        if ($this->filter_isEvaluated == 'All') $this->filter_isEvaluated = null;
        if ($this->filter_account == 'All') $this->filter_account = null;
        if ($this->filter_callType == 'All') $this->filter_callType = null;
        if ($this->filter_qaFlaggedInteractions == 'All') $this->filter_qaFlaggedInteractions = null;
        if ($this->filter_account == 'All') $this->filter_account = null;
        if ($this->filter_skillGroup == 'All') $this->filter_skillGroup = null;
        if ($this->filter_callEnder == 'All') $this->filter_callEnder = null;

        // reset pagination at real time search 
        if ($this->searchCalls != '') {
            $this->resetPage();
        }


        return view('livewire.reports.disconnected', [
            'evaluations' => $this->getEvaluation(),
            'records' => $this->filterApplied ?
                $interactions
                ->when($this->filter_callerId, function ($q) {
                    $q->where('caller_id', 'like', "%$this->filter_callerId%");
                })
                ->when($this->filter_uniqueId, function ($q) {
                    $q->where('Genesys_CallUUID', 'like', "%$this->filter_uniqueId%");
                })
                ->when($this->filter_callEnder, function ($q) {
                    $q->where('call_ender', 'like', "%$this->filter_callEnder%");
                })
                ->when($this->filter_calledId, function ($q) {
                    $q->where('called_id', 'like', "%$this->filter_calledId%");
                })
                ->when($this->filter_agentId, function ($q) {
                    $q->whereHas('agent', function ($q2) {
                        $q2->where('agent_id', 'like', "%$this->filter_agentId%");
                    });
                })
                ->when($this->filter_duration, function ($q) {
                    $filter_duration = gmdate('H:i:s', $this->filter_duration);
                    $q->where('call_duration', $this->filter_duration_sign, $filter_duration);
                })
                ->when($this->filter_holdDuration, function ($q) {
                    $filter_holdDuration = gmdate('H:i:s', $this->filter_holdDuration);
                    $q->where('call_duration', $this->filter_holdDuration_sign, $filter_holdDuration);
                })
                ->when($this->filter_holdCount, function ($q) {
                    // $filter_holdCount = gmdate('H:i:s', $this->filter_holdCount);
                    $filter_holdCount = $this->filter_holdCount;
                    $q->where('hold_count', $this->filter_holdCount_sign, $filter_holdCount);
                })
                ->when($this->filter_agentName, function ($q) {
                    $q->whereHas('agent', function ($q2) {
                        $q2->where('full_name', 'like', "%$this->filter_agentName%");
                    });
                })
                ->when($this->filter_time_name == 'Custom', function ($q) {
                    $custom_date_from = $this->custom_date_from . ' 00:00:00';
                    $custom_date_to = $this->custom_date_to . ' 23:59:59';
                    $q->whereBetween('arrival_time', [$custom_date_from, $custom_date_to]);
                })
                ->when($this->filter_time_days, function ($q) {
                    $startDate = now()->subDays($this->filter_time_days)->toDateString();
                    $q->whereDate('arrival_time', '>=', $startDate);
                })
                ->when($this->filter_account, function ($q) {
                    return $q->whereHas('agent.organization', function ($q2) {
                        $q2->where('name', 'like', "%{$this->filter_account}%");
                    });
                })
                ->when($this->filter_selected_languages && $this->filter_selected_languages != $this->all_languages, function ($qqq) {
                    $lowercaseLanguages = array_map('lcfirst', $this->filter_selected_languages);
                    $qqq->whereIn('language', $lowercaseLanguages);
                })
                ->paginate($this->perPage)
                :
                $interactions->paginate($this->perPage),
            'organizations' => Organization::orderBy('name')->get(),
            // 'skillGroups' => SkillGroup::all(),
            // 'evaluation_forms' => Evaluation::all(),
        ]);
    }
}
