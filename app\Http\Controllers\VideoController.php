<?php


namespace App\Http\Controllers;

use App\Models\Interaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Models\Encryption;
use App\Models\VideoCapture;

class VideoController extends Controller
{

    public function playVideo($call_id, $agent_name = null, $agent_id = null)
    {

        // Get The encryption status of call type
        $encryptionStatus = Encryption::where('type', 'video')->first();


        $callId = $call_id;
        $agentName = $agent_name;
        $agentId = $agent_id;


        if (!str_ends_with($callId, '@10.202.1.66')) {
            $callId .= '@10.202.1.66';
        }


        $filePath = "video/{$call_id}.mp4";



        // Check if the file exists
        // if (Storage::exists($filePath)) {
        if (file_exists($filePath)) {

            // If the file exists, return it as a response
            return view('video')->with([
                'callId' => $callId,
                'agent_name' => $agentName,
                'agent_id' => $agentId,
                'videoPath' => $filePath,
//                'callName' => $fileName,
            ]);

        }


    }

    public function decrypt($fileName,$call_id)
    {


        $is_encrypted = Interaction::where('is_encrypted', true)->where('call_id',$call_id)->first();

        $encryptionKey = config('app.key'); // Retrieve your encryption key


        if ($is_encrypted){
            $filePath = "video/{$call_id}.mp4";
        }else{
            $filePath = "video/{$call_id}.mp4";
        }


        $encryptedContent = Storage::disk('public')->get("{$filePath}");


        if ($is_encrypted){
            $file = decrypt($encryptedContent, $encryptionKey);
        }else{
            $file = $encryptedContent;
        }

        // Decrypt the content
        // Return the decrypted content as a response with appropriate headers
        return response($file)->header('Content-Type', 'audio/wav');
    }
}
