<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SkillGroup extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function users()
    {
        return $this->hasMany(User::class, 'skill_group_id');
    }

    public function scopeSearch($q, $v)
    {
        $q->where('name', 'like', "%$v%");
    }
}
