<?php

namespace App\Livewire;

use App\Models\Comment;
use Livewire\Component;
use App\Models\Interaction;
use Livewire\WithPagination;
use App\Models\CurrentVoiceFiles;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use App\Models\EvaluationSubmission;
use App\Models\Evaluation;
use App\Models\Organization;
use App\Models\SkillGroup;
use Illuminate\Support\Facades\Redirect;
use Livewire\Features\SupportPagination\WithoutUrlPagination;
use Illuminate\Support\Facades\DB;
class Records extends Component
{

    use LivewireAlert, WithPagination, WithoutUrlPagination;

    protected $paginationTheme = 'bootstrap';
    public $perPage = 15;

    public $filterType = 'General';

    public $searchCalls = '';

    // filters
    public $filterApplied = false;
    public $filter_time_name;
    public $filter_time_days;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_callId;
    public $filter_agentId;
    public $filter_duration;
    public $filter_callType;
    public $filter_calledId;
    public $filter_callerId;
    public $filter_callEnder = 'All';
    public $filter_holdDuration;
    public $filter_holdCount;
    public $filter_isAssigned;
    public $filter_evaluationScore;
    public $filter_isEvaluated;
    public $filter_qaFlaggedInteractions;
    public $filter_flagDate;
    public $filter_agentName;
    public $filter_account;
    public $filter_skillGroup;
    public $filter_callImportance;
    public $filter_pauseCount;
    public $filter_language;
    public $filter_uniqueId;
    public $filter_played;
    public $byFlagType = "All";

    public $filter_duration_sign = "=";
    public $filter_holdCount_sign = "=";
    public $filter_holdDuration_sign = "=";
    public $filter_evaluationScore_sign = "=";



    // new filter LANGUAGES
    public $filter_selected_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
    public $all_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
    public $filter_evaluationForm = null;


    // when the user clicks play
    public $callId;
    public $evaluationReport = array();
    public $selected_interaction_id;
    public $callID = null;

    public $selectedInteractionComments;
    public $selectedInteractionToAddComment;
    public $selectedCommentToDelete;
    public $added_comment;
    public $recordID;
    public $add_comment_field = false;
    public $viewCommentsModal = false;
    public $selectedInteractionId; //when clicking view comments
    public $viewEvaluationModal = false;
    public $viewEvaluationFormModal = false;
    public $viewListenersModal = false;



    public $loading = false;
    public $responseData;

    // filters
    public $evaluationID = 0;
    public $selectedTime;
    public $selectedAccount;
    public $selectedCallId;
    public $selectedAgent;
    public $url;
    public $hideZero = true;
    public $showTranscribed = false;

    public $callListeners = [];

    protected  $listeners  = ['test2w' => 'test2w',
    'confirm_delete_ai_flag',
        'confirm_topics_delete_flag'
    ];

    public function filter_languages($language)
    {
        if ($this->filter_selected_languages && in_array($language, $this->filter_selected_languages)) {
            $this->filter_selected_languages = array_values(array_diff($this->filter_selected_languages, [$language]));
        } else {
            $this->filter_selected_languages[] = $language;
            $this->filter_selected_languages = array_values($this->filter_selected_languages);
        }
    }


    public function filterTypeChange($data)
    {
        $this->filterType = $data;
    }
    public function toggleZero()
    {
        $this->hideZero = ! $this->hideZero;
    }

    public function toggleTranscribed()
    {
        $this->showTranscribed = ! $this->showTranscribed;
    }

    public function byFlag($flag)
    {
        $this->byFlagType = $flag;
        $this->filterApplied = true;
    }


    // public function mount()
    // {
    //     set_time_limit(60 * 20);

    //     // get current records from server 33 (DB)
    //     // proxy api url
    //     // $this->url = 'https://oms.extwebonline.com/Extensya_APIs/recording/getDbData.php';


    //     // Make a GET request to the PHP file
    //     // $response = Http::withoutVerifying()->get($this->url);

    //     // $this->responseData = collect($response->json());

    //     // current downloaded voice files on local server
    //     // $currentFiles = CurrentVoiceFiles::pluck('file')->toArray();

    //     // send a request to the API to download the files from FTP server, and send along the current files array to not download them again
    //     // $url2 = 'https://oms.extwebonline.com/Extensya_APIs/recording/getRecordings.php';
    //     // $response2 = Http::withoutVerifying()->timeout(60 * 10)->post($url2, $currentFiles)->json();

    //     // save the new downloaded files in the local database
    //     // foreach ($response2['newFiles'] as $file) {
    //     // CurrentVoiceFiles::firstOrCreate(['file' => $file]);
    //     // }


    //     $interactions = Interaction::all()->toArray();

    //     // send data to frontend table
    //     // $this->dispatch('renderTable', $this->responseData);
    //     $this->dispatch('renderTable', $interactions);
    // }

    public function apply_custom_date()
    {

        $this->filter_time_name = 'Custom';

        $this->validate();

        $this->dispatch('closeCustomDateModal');
    }

    public function filter_time_set($time)
    {
        $this->filter_time_days = $time;

        $this->filter_time_name = match ((int) $time) {
            1 => 'Last 24 Hours',
            7 => 'Last 7 Days',
            30 => 'Last 30 Days',
            60 => 'Last 60 Days',
            default => 'Unknown time',
        };

        if ($this->filter_time_name == 'Custom') {
            $this->filter_time_days = null;
        }
    }

    public function clear()
    {

        $this->filterApplied = false;
        $this->filter_time_name = 'All Time';
        $this->byFlagType = 'All';
        $this->custom_date_from = null;
        $this->custom_date_to = null;
        $this->filter_time_days = null;

        $this->filter_callId = null;
        $this->filter_uniqueId = null;
        $this->filter_agentId = null;
        $this->filter_duration = null;
        $this->filter_callType = null;

        $this->filter_calledId = null;
        $this->filter_callerId = null;
        $this->filter_callEnder = null;
        $this->filter_holdDuration = null;
        $this->filter_holdCount = null;

        $this->filter_isAssigned = null;
        $this->filter_evaluationScore = null;
        $this->filter_isEvaluated = null;
        $this->filter_qaFlaggedInteractions = null;
        $this->filter_flagDate = null;
        $this->filter_played = null;

        $this->filter_agentName = null;
        $this->filter_account = null;
        $this->filter_skillGroup = null;
        $this->filter_callImportance = null;
        $this->filter_pauseCount = null;
        $this->filter_language = null;
        $this->filter_selected_languages = $this->all_languages;
        $this->filter_evaluationForm = null;

        $this->added_comment = '';
        $this->add_comment_field = false;
        $this->viewCommentsModal = false;
        $this->viewListenersModal = false;
        $this->callListeners = null;

        $this->selectedInteractionToAddComment = null;
        $this->selectedInteractionId = null;
        $this->viewEvaluationModal = false;
        $this->viewEvaluationFormModal = false;
        $this->callID = null;
        $this->recordID = null;

        $this->filter_duration_sign = "=";
        $this->filter_holdCount_sign = "=";
        $this->filter_holdDuration_sign = "=";
        $this->filter_evaluationScore_sign = "=";
        $this->filterType = 'General';
        $this->dispatch('selectAllLangs');

        // send the request again with no filters
        // $this->mount();


        // re render the datatable
        // $this->dispatch('renderTable', $this->responseData);


        // $this->dispatch('renderTable');
    }


    public function clearWhoListenedToCall()
    {
        $this->viewListenersModal = false;
        $this->callListeners = null;
    }


    public function filter()
    {
        $this->filterApplied = true;
    }

    public function downloadAndPlay($call_id)
    {
        $this->callId = $call_id;

        $this->callId = '00517430-04B7-14AD-A1DD-4101CA0AAA77-183429813@10.202.1.66';
        $fileName = "{$this->callId}.wav";
        $filePath = "audio/{$fileName}";

        // Check if the file exists
        if (Storage::exists($filePath)) {
            // If the file exists, return it as a response
            return response()->file(storage_path("app/{$filePath}"), ['Content-Type' => 'audio/wav']);
        }

        // If the file does not exist, download it
        $response = Http::withoutVerifying()->timeout(120)->get('https://oms.extwebonline.com/Extensya_APIs/recording//downloadRecording.php', [
            'call_id' => $this->callId,
        ]);

        // Check if the request was successful
        if ($response->successful()) {
            // Save the file content to storage/app/audio directory
            Storage::put($filePath, $response->body());

            // Return the downloaded file as a response
            return response()->file(storage_path("app/{$filePath}"), [
                'Content-Type' => 'audio/wav',
                'Content-Disposition' => 'inline; filename="' . $fileName . '"',
            ]);
        } else {
            // Handle the case where the request was not successful
            return "Failed to download the file: " . $response->status();
        }
    }

    public function getListeners()
    {
        return [
            'confirm_add_flag',
            'confirm_delete_flag',
            'confirm_delete_comment',
            'confirm_delete_ai_flag',
            'confirm_topics_delete_flag',
        ];
    }

    public function confirm_add_flag()
    {
        $this->selected_interaction_id;
        Interaction::find($this->selected_interaction_id)->qaFlags()->attach(1);
        $this->alert("success", "Flag Added Successfully");
    }

    public function rules()
    {
        return [
            'custom_date_from' => 'required_if:filter_time_name,Custom|',
            'custom_date_to' => 'required_if:filter_time_name,Custom|after_or_equal:custom_date_from'
        ];
    }

    public function showAddFlagAlert($id)
    {
        $this->selected_interaction_id = $id;

        $this->alert('success', 'Are you sure you want to add a flag for this interaction?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirm_add_flag',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function showDeleteCommentAlert($id)
    {
        $this->selectedCommentToDelete = $id;
        $this->alert('warning', 'Are you sure you want to delete this comment?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirm_delete_comment',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function showDeleteFlagAlert($id)
    {
        $this->selected_interaction_id = $id;

        $this->alert('warning', 'Are you sure you want to clear the flag for this interaction?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirm_delete_flag',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function showDeleteAiFlagAlert($id)
    {
        $this->selected_interaction_id = $id;

        $this->alert('warning', 'Are you sure you want to clear the flag for this interaction?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirm_delete_ai_flag',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }
    public function showDeleteTopicsFlagAlert($id)
    {
        $this->selected_interaction_id = $id;

        $this->alert('warning', 'Are you sure you want to clear the flag for this interaction?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirm_topics_delete_flag',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirm_delete_flag()
    {
        $this->selected_interaction_id;
        Interaction::find($this->selected_interaction_id)->qaFlags()->detach();
        $this->alert("success", "Flag Cleared Successfully");
    }
    public function confirm_delete_ai_flag()
    {
        $this->selected_interaction_id;
        Interaction::find($this->selected_interaction_id)->update(['ai_flag'=>0]);
        $this->alert("success", "Flag Cleared Successfully");
    }
    public function confirm_topics_delete_flag()
    {
        $this->selected_interaction_id;
        Interaction::find($this->selected_interaction_id)->update(['topics_flag'=>0]);
        $this->alert("success", "Flag Cleared Successfully");
    }

    public function confirm_delete_comment()
    {
        Comment::find($this->selectedCommentToDelete)->delete();
        $this->alert("success", "Comment Deleted Successfully");
        $this->clear();
        $this->closeModal();
    }

    public function getComments($id)
    {
        $this->selectedInteractionId = $id;
        $this->viewCommentsModal = true;

        if (!Auth::user()->permissions()->where('permission_id', 5)->exists()) {

            $this->selectedInteractionComments = Interaction::find($id)->comments()
                ->whereHas('commenter', function ($query) {
                    $query->where('id', '=', Auth::id());
                })->get();
        } else {
            $this->selectedInteractionComments = Interaction::find($id)->comments()->get();
        }
    }

    public function getCallListeners($interaction_id)
    {
        $this->callListeners = Interaction::find($interaction_id)->listeners;
        $this->viewListenersModal = true;
    }

    public function addComment($id)
    {
        $this->selectedInteractionToAddComment = $id;
    }

    public function saveComment()
    {
        if ($this->viewCommentsModal == true) {
            $this->selectedInteractionToAddComment = $this->selectedInteractionId;
        }

        Comment::create([
            'interaction_id' => $this->selectedInteractionToAddComment,
            'user_id' => Auth::id(),
            'comment' => $this->added_comment,
        ]);

        $this->alert("success", "Comment Added Successfully");
        $this->clear();
        $this->closeModal();
    }



    public function closeModal()
    {
        $this->resetValidation();
        $this->viewCommentsModal = false;
        $this->dispatch('closeModal');
    }

    public function clearComment()
    {
        $this->selectedInteractionComments = null;
        $this->selectedInteractionToAddComment = null;
        $this->selectedCommentToDelete = null;
        $this->added_comment = null;
        $this->recordID = null;
        $this->add_comment_field = false;
        $this->viewCommentsModal = false;
        $this->selectedInteractionId = null; //when clicking view comments
    }

    public function selectAllLanguages()
    {
        // Assuming you have an array of all possible languages
        $allLanguages = ['Arabic', 'English', 'Kurdish', 'Spanish', 'German', 'French', 'Italian', 'Russian', 'Urdu', 'Hebrew', 'Turkish'];

        // Check if all languages are currently selected
        if (count($this->filter_selected_languages) === count($allLanguages)) {
            // If all are selected, clear the selection
            $this->filter_selected_languages = [];
            $shouldCheck = false;
        } else {
            // Otherwise, select all languages
            $this->filter_selected_languages = $allLanguages;
            $shouldCheck = true;
        }

        // Dispatch browser event to check/uncheck checkboxes
        $this->dispatch('checkOrUncheckLangs', ['shouldCheck' => $shouldCheck]);
    }


    // public function downloadAndPlay($call_id)
    // {
    //     $this->callId = $call_id;

    //     $this->callId = '00517430-04B7-14AD-A1DD-4101CA0AAA77-183429813@10.202.1.66';

    //     // Check if the call_id already has the suffix @10.202.1.66....
    //     // if (!str_ends_with($this->callId, '@10.202.1.66')) {
    //     //     $this->callId .= '@10.202.1.66';
    //     // }

    //     // // send a request to the proxy API to download the wanted audio file
    //     $response = Http::withoutVerifying()->timeout(120)->get('https://oms.extwebonline.com/Extensya_APIs/recording/downloadRecording.php', [
    //         'call_id' => $this->callId,
    //     ]);

    //     // Check if the request was successful
    //     if ($response->successful()) {
    //         // Save the file content to storage/app/audio directory
    //         Storage::put("audio/{$this->callId}.wav", $response->body());

    //         // Optionally, you can manipulate the file or perform other actions here

    //         // Success message or redirection
    //         return "File downloaded and saved successfully.";
    //     } else {
    //         // Handle the case where the request was not successful
    //         return "Failed to download the file: " . $response->status();
    //     }
    // }
    public function getEvaluationReport($id)
    {
        return $this->evaluationReport = EvaluationSubmission::query()->where('id', $id)->get();
    }
    public function addEvaluation($id)
    {
        $this->recordID = $id;
        $this->evaluationID = 0;
        $this->dispatch('refresh-model');
        return $this->evaluationReport = EvaluationSubmission::query()->where('id', $id)->get();
    }
    public function getData()
    {
        $this->filter();
        $this->dispatch('close-modall');
    }
    public function getEvaluation()
    {

        // return $data = Evaluation::query()->where('status', '1')->get();
        if (Auth::user()->role == 1 || Auth::user()->role == 2 || Auth::user()->role == 3 || Auth::user()->role == 3 || Auth::user()->role == 7) {
            return Evaluation::query()->where('status', '1')->get();
        } elseif (Auth::user()->role == 5) {
            return Evaluation::query()->whereIn('organization_id', auth()->user()->supervisorOrganizations->pluck('id'))->where('status', '1')->get();
        } else {
            return Evaluation::query()->where('organization_id', auth()->user()->organization->id)->where('status', '1')->get();
        }
    }
    public function redirectToEvaluationPage()
    {

        return Redirect::route('evaluation.submitEvaluationForm', ['evaluation_id' => $this->evaluationID, 'chat_id' => $this->recordID]);
    }

    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }

    public $currentPage = 1;
    public function updatingCurrentPage($value)
    {
        $this->gotoPage($value);
    }


    public function render()
    {
        // Define common eager loading relationships to be used across all queries
        $commonEagerLoads = [
            'organization',
            'agent.organization',
            'userGroup',
            'agent',
            'callEvaluation.evaluation',
            'callEvaluation.evaluator',
            'qaFlags',
            'listeners'
        ];

        // Define common withCount to eliminate duplicate queries in the blade template
        $commonCounts = ['qaFlags', 'comments', 'listeners'];

        if (Auth::user()->role == 4) {
            $interactions = Interaction::with($commonEagerLoads)
                ->withCount($commonCounts)
                ->where('user_id', Auth::id())
                ->when($this->hideZero, function ($query) {
                    $query->where('call_duration', '>', '0:00:00');
                })
                ->when($this->showTranscribed, function ($query) {
                    $transcribedCallIds = DB::table('calls_transcription')
                        ->selectRaw("SUBSTRING_INDEX(call_id, '@', 1) as call_id")
                        ->distinct()
                        ->pluck('call_id')
                        ->toArray();
                    $query->whereIn(DB::raw("SUBSTRING_INDEX(call_id, '@', 1)"), $transcribedCallIds);
                })
                ->orderByDesc('arrival_time');
            // if IT or supervisor
        } else if (in_array(Auth::user()->role, [2, 3, 7])) {
            $interactions = Interaction::with($commonEagerLoads)
                ->withCount($commonCounts)
                ->whereHas('agent.organization', function ($q) {
                    $q->whereIn('id', Auth::user()->supervisorOrganizations->pluck('id'));
                })
                ->when($this->hideZero, function ($query) {
                    $query->where('call_duration', '>', '0:00:00');
                })
                ->when($this->showTranscribed, function ($query) {
                    $transcribedCallIds = DB::table('calls_transcription')
                        ->selectRaw("SUBSTRING_INDEX(call_id, '@', 1) as call_id")
                        ->distinct()
                        ->pluck('call_id')
                        ->toArray();
                    $query->whereIn(DB::raw("SUBSTRING_INDEX(call_id, '@', 1)"), $transcribedCallIds);
                })
                ->orderByDesc('arrival_time');
            // if quality
        } else if (Auth::user()->role == 5) {
            $interactions = Interaction::with($commonEagerLoads)
                ->withCount($commonCounts)
                ->whereHas('agent.organization', function ($q) {
                    $q->whereIn('id', Auth::user()->supervisorOrganizations->pluck('id'));
                })
                ->when($this->hideZero, function ($query) {
                    $query->where('call_duration', '>', '0:00:00');
                })
                ->when($this->showTranscribed, function ($query) {
                    $transcribedCallIds = DB::table('calls_transcription')
                        ->selectRaw("SUBSTRING_INDEX(call_id, '@', 1) as call_id")
                        ->distinct()
                        ->pluck('call_id')
                        ->toArray();
                    $query->whereIn(DB::raw("SUBSTRING_INDEX(call_id, '@', 1)"), $transcribedCallIds);
                })
                ->orderByDesc('arrival_time');
        }
        // if client
        else if (Auth::user()->role == 6) {
            $interactions = Interaction::with($commonEagerLoads)
                ->withCount($commonCounts)
                ->whereHas('agent.organization', function ($q) {
                    $q->where('id', Auth::user()->organization_id);
                })
                ->when($this->hideZero, function ($query) {
                    $query->where('call_duration', '>', '0:00:00');
                })
                ->when($this->showTranscribed, function ($query) {
                    $transcribedCallIds = DB::table('calls_transcription')
                        ->selectRaw("SUBSTRING_INDEX(call_id, '@', 1) as call_id")
                        ->distinct()
                        ->pluck('call_id')
                        ->toArray();
                    $query->whereIn(DB::raw("SUBSTRING_INDEX(call_id, '@', 1)"), $transcribedCallIds);
                })
                ->orderByDesc('arrival_time');
        }
        // if admin
        else {
            $interactions = Interaction::with($commonEagerLoads)
                ->withCount($commonCounts)
                ->when($this->hideZero, function ($query) {
                    $query->where('call_duration', '>', '0:00:00');
                })
                ->when($this->showTranscribed, function ($query) {
                    $transcribedCallIds = DB::table('calls_transcription')
                        ->selectRaw("SUBSTRING_INDEX(call_id, '@', 1) as call_id")
                        ->distinct()
                        ->pluck('call_id')
                        ->toArray();
                    $query->whereIn(DB::raw("SUBSTRING_INDEX(call_id, '@', 1)"), $transcribedCallIds);
                })
                ->orderByDesc('arrival_time');
        }

        // $interactions = $interactions->search($this->searchCalls)->orderByDesc('arrival_time');

        $searchTerm = $this->searchCalls;
        $interactions = $interactions->search($searchTerm)->orderByDesc('arrival_time');
        // dd(Interaction::limit(10)->get());

        // filters
        if ($this->filter_isEvaluated == 'All') $this->filter_isEvaluated = null;
        if ($this->filter_qaFlaggedInteractions == 'All') $this->filter_qaFlaggedInteractions = null;

        if ($this->filter_callType == 'All') $this->filter_callType = null;

        if ($this->filter_callEnder == 'All') $this->filter_callEnder = null;
        if ($this->filter_played == 'All') $this->filter_played = null;




        return view('livewire.records', [
            'evaluations' => $this->getEvaluation(),
            $records = $this->filterApplied ?
                $interactions->when($this->filter_callerId, function ($q) {
                    $q->where('caller_id', 'like', "%" . trim($this->filter_callerId) . "%");
                })
                ->when($this->filter_calledId, function ($q) {
                    $q->where('called_id', 'like', "%" . trim($this->filter_calledId) . "%");
                })

                ->when($this->filter_callId, function ($q) {
                    $q->where('call_id', 'like', "%" . trim($this->filter_callId) . "%");
                })
                ->when(trim($this->filter_agentId), function ($q) {
                    $q->whereHas('agent', function ($q2) {
                        $q2->where('agent_id', 'like', "%" . trim($this->filter_agentId) . "%");
                    });
                })
                ->when($this->filter_duration, function ($q) {
                    $filter_duration = gmdate('H:i:s', $this->filter_duration);
                    $q->where('call_duration', $this->filter_duration_sign, $filter_duration);
                })
                ->when($this->filter_callType, function ($q) {
                    $q->where('call_type', $this->filter_callType);
                })
                ->when($this->filter_callEnder, function ($q) {
                    $q->where('call_ender', 'like', "%$this->filter_callEnder%");
                })
                ->when($this->filter_holdDuration, function ($q) {
                    $filter_holdDuration = gmdate('H:i:s', $this->filter_holdDuration);
                    $q->where('hold_duration', $this->filter_holdDuration_sign, $filter_holdDuration);
                })
                ->when($this->filter_holdCount, function ($q) {
                    // $filter_holdCount = gmdate('H:i:s', $this->filter_holdCount);
                    $q->where('hold_count', $this->filter_holdCount_sign, $this->filter_holdCount);
                })
                ->when($this->filter_agentName, function ($q) {
                    $q->whereHas('agent', function ($q2) {
                        $q2->where('full_name', 'like', "%" . trim($this->filter_agentName) . "%");
                    });
                })
                ->when($this->filter_evaluationScore, function ($q) {
                    $q->whereHas('callEvaluation', function ($q2) {
                        $q2->where('quality_percentage', $this->filter_evaluationScore_sign, (int) $this->filter_evaluationScore);
                    });
                })
                ->when($this->filter_uniqueId, function ($q) {
                    $q->where('Genesys_CallUUID', 'like', "%$this->filter_uniqueId%");
                })
                ->when($this->filter_callType, function ($q) {
                    $q->where('call_type', 'like', "%$this->filter_callType%");
                })
                ->when($this->filter_time_name == 'Custom', function ($q) {
                    $this->custom_date_from = $this->custom_date_from . ' 00:00:00';
                    $this->custom_date_to = $this->custom_date_to . ' 23:59:59';
                    $q->whereBetween('arrival_time', [$this->custom_date_from, $this->custom_date_to]);
                })
                ->when($this->filter_time_days, function ($q) {
                    $startDate = now()->subDays($this->filter_time_days)->toDateString();
                    $q->whereDate('arrival_time', '>=', $startDate);
                })
                ->when($this->filter_isEvaluated, function ($q) {
                    $this->filter_isEvaluated == 'Evaluated' ? $q->whereHas('callEvaluation') : $q->whereDoesntHave('callEvaluation');
                })
                ->when($this->filter_played, function ($q) {
                    $this->filter_played == 'Played' ? $q->whereHas('listeners') : $q->whereDoesntHave('listeners');
                })
                ->when($this->filter_qaFlaggedInteractions, function ($q) {
                    $this->filter_qaFlaggedInteractions == 'Flagged' ? $q->whereHas('qaFlags') : $q->whereDoesntHave('qaFlags');
                })
                // ->when($this->filter_account, function ($q) {
                //     //  $q->whereHas('agent.organization', function ($q2) {
                //     //     $q2->where('name', 'like', "%{$this->filter_account}%");
                //     // });
                //     $account_name = Organization::firstWhere('name', 'like', "%{$this->filter_account}%")->id;
                //     $q->where('organization_id', $account_name);
                // })
                ->when($this->filter_account, function ($q) {
                    // If the filter account is "kease", change it to "ewa" for the search
                    $searchTerm =  $this->filter_account;

                    // Find the organization ID based on the modified search term
                    $account_name = Organization::firstWhere('name', 'like', "%{$searchTerm}%")->id;
                    $q->where('organization_id', $account_name);
                })

                ->when($this->filter_evaluationForm, function ($qqq) {
                    $qqq->whereHas('callEvaluation.evaluation', function ($qq) {
                        $qq->where('evaluation_name', 'like', "%$this->filter_evaluationForm%");
                    });
                })
                ->when($this->filter_selected_languages && $this->filter_selected_languages != $this->all_languages, function ($qqq) {
                    $lowercaseLanguages = array_map('lcfirst', $this->filter_selected_languages);
                    $qqq->whereIn('language', $lowercaseLanguages);
                })
                ->when($this->filter_pauseCount, function ($q) {
                    $q->where('pause_count', $this->filter_pauseCount);
                })
                    ->when($this->byFlagType == 'Classification Flags', function ($q) {
                        $q->where('ai_flag',1);
                    })
                    ->when($this->byFlagType == 'Bad Words Flags', function ($q) {
                        $q->where('topics_flag',1);
                    })
                // ->when($this->filter_selected_languages, function ($q) {
                //     $q->where(function ($query) {
                //         foreach ($this->filter_selected_languages as $language) {
                //             $query->orWhere('language', 'like', "%{$language}%");
                //         }
                //     });
                // })
                ->paginate($this->perPage)->onEachSide(1)

                : $interactions
                    ->when($this->byFlagType == 'Classification Flags', function ($q) {
                        $q->where('ai_flag',1);
                    })
                    ->when($this->byFlagType == 'Bad Words Flags', function ($q) {
                        $q->where('topics_flag',1);
                    })



                    ->paginate($this->perPage)->onEachSide(1),
            'organizations' => Organization::OrderBy('name', 'ASC')

                ->get(),
            'skillGroups' => SkillGroup::all(),
            'evaluation_forms' => Evaluation::all(),
            'records' => $records,
            'lastPage' => $records->lastPage(),
        ]);
    }
}





