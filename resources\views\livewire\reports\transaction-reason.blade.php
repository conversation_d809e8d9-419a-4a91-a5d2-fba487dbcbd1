@php
use Carbon\Carbon;
@endphp

<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto">
                <button
                    wire:target="export"
                    wire:click="export"
                    title="Export"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 180px; height: 45px; border-color: #01a44f; background: #01a44f;"
                    wire:loading.attr="disabled">

                    <i
                        wire:loading.remove
                        wire:target="export"
                        class="fas fa-file-excel text-white me-2"
                        style="font-size: 20px;"></i>

                    <span
                        wire:loading.class="spinner-border spinner-border-sm"
                        wire:target="export"
                        style="width: 1rem; height: 1rem;"
                        role="status"
                        aria-hidden="true"></span>

                    <span
                        wire:loading.remove
                        wire:target="export"
                        style="font-size: 17px;">Extract Excel</span>
                </button>
            </div>
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button
                    data-bs-toggle="modal"
                    data-bs-target="#filterModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    <img class="me-2" src="{{ asset('assets/SVG/assets-v2/Vector.svg') }}" alt="Filter Icon">
                    <span style="font-size: 17px;">Filter</span>
                </button>
            </div>
        </div>
    </div>

    <div class="parent-sections mx-3 ps-5">
        <div class="section-one">
            <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">
                <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">

                    <thead id="thead" class="text-muted thead" style="font-size: 0.7rem">
                        <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                            <th scope="col" class="text-center">

                                <i class="fa-solid fa-hashtag fa-xl" title="Transaction ID" style="color: white"></i>
                            </th>
                            <th scope="col" class="text-center">
                                <i class="fa-solid fa-building fa-xl" title="Account" style="color: white"></i>
                            </th>
                            <th scope="col" class="text-center">
                                <i class="fa-solid fa-phone fa-xl" title="Call Type" style="color: white"></i>
                            </th>
                            <th scope="col" class="text-center">
                                <i class="fa-solid fa-exchange-alt fa-xl" title="Transaction Reason" style="color: white"></i>
                            </th>
                            <th scope="col" class="text-center">
                                <i class="fa-solid fa-list-ul fa-xl" title="Sub Transaction Reason" style="color: white"></i>
                            </th>
                            <th scope="col" class="text-center">
                                <i class="fa-regular fa-calendar-days fa-xl" title="Date Time" style="color: white"></i>
                            </th>
                        </tr>
                    </thead>

                    <tbody class="" style="font-size:0.8rem" id="tbody">
                        @forelse($records as $record)
                            <tr class="align-middle">
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->transaction_id ?? '-' }}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->account ?? '-' }}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->type_call ?? '-' }}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->call_reason ?? '-' }}
                                </td>
                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:20rem">
                                    <span title="{{ $record->sub_call_reason ?? '-' }}">
                                        {{ Str::limit($record->sub_call_reason ?? '-', 50) }}
                                    </span>
                                </td>                                <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem">
                                    {{ $record->arrival_time ? Carbon::parse($record->arrival_time)->format('Y-m-d h:i A') : '-' }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-muted text-center bg-white">No Transaction Reasons found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="d-flex align-items-center">
                    <label for="perPage" class="me-2">Show:</label>
                    <select wire:change="recordsPerPage($event.target.value)" class="form-select form-select-sm" style="width: auto;">
                        <option value="15" {{ $perPage == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                    </select>
                    <span class="ms-2">entries</span>
                </div>

                {{ $records->links() }}
            </div>
        </div>
    </div>

    {{-- Custom Date Modal --}}
    <div wire:ignore.self class="modal fade" id="custom_time_modal" tabindex="-1" role="dialog" aria-labelledby="custom_time_modalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white;">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-regular fa-calendar-days fa-2xl" style="color: #01a44f;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Custom Period</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeCustomDate' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body" style="border: none;">
                    <form wire:submit.prevent="apply_custom_date">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="custom_date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="custom_date_from" wire:model="custom_date_from" required>
                                @error('custom_date_from') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="custom_date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="custom_date_to" wire:model="custom_date_to" required>
                                @error('custom_date_to') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary rounded-3 px-4" data-bs-dismiss="modal" style="height: 40px;">Cancel</button>
                            <button type="submit" class="btn btn-success rounded-3 px-4" style="height: 40px; border-color: #01a44f; background: #01a44f">Apply</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- Filter Modal --}}
    <div wire:ignore.self class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" style="background-color: white;">
                <div class="modal-header" style="border: none;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <img src="{{ asset('assets/SVG/assets-v2/Vector - green.svg') }}" alt="Filter Icon" style="width: 30px; height: 30px; color: #01a44f;">
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Filter</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                    </button>
                </div>
                <div class="modal-body" style="border: none;">
                    <div class="col-md-12">
                        <form class="row g-2 mb-3" wire:submit.prevent>
                            {{-- Time Filter --}}
                            <div class="col-md-6">
                                <label for="date" class="mb-2">Time <i class="fa-regular fa-calendar-days fa-lg" style="color:#00a34e" title="Date"></i></label>
                                <div class="dropdown">
                                    <button id="dropDownList" class="btn btn-sm dropdown-toggle dropdown-toggle-style" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding-top: 0.46rem;padding-bottom: 0.46rem;">
                                        <span style="overflow:hidden; text-overflow:ellipsis; display:inline-block" class="align-middle">{{ $filter_time_name ?? 'All Time' }}</span>
                                    </button>
                                    <div id="dropdownMenu" class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton">
                                        <li><span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(null)">All Time</span></li>
                                        <hr class="m-0">
                                        <li><span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(1)">Last 24 Hours</span></li>
                                        <hr class="m-0">
                                        <li><span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(7)">Last 7 Days</span></li>
                                        <hr class="m-0">
                                        <li><span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(30)">Last 30 Days</span></li>
                                        <hr class="m-0">
                                        <li><span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" wire:click="filter_time_set(60)">Last 60 Days</span></li>
                                        <hr class="m-0">
                                        <li><span style="overflow:hidden; text-overflow:ellipsis;" id="dropdownItem" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#custom_time_modal">Custom</span></li>
                                    </div>
                                </div>
                            </div>

                            {{-- Call ID Filter --}}
                            <div class="col-md-6">
                                <label for="call id" class="mb-2">Call ID
                                    <img src="{{ asset('assets/images/callid.png') }}" alt="" srcset="" style="width:1.3rem; height:1.3rem">
                                </label>
                                <input style="border: solid 1px #b6b6b6; font-size:0.85rem" type="text" name="call-id" id="call-id" class="form-control" placeholder="Call #" wire:model="filter_callId">
                            </div>

                            {{-- Call Type (Fixed) --}}
                            <div class="col-md-6">
                                <label for="call-type" class="mb-2">Call Type <i class="fa-solid fa-phone fa-lg" style="color:#00a34e" title="Call Type"></i></label>
                                <input type="text" class="form-control" value="Call" readonly style="background-color: #f8f9fa; border: solid 1px #b6b6b6; font-size: 0.85rem; height: 2.5rem;">
                            </div>

                            {{-- Account Filter --}}
                            <div class="col-md-6">
                                <label for="account" class="mb-2">Account <i class="fa-solid fa-building fa-lg" style="color: #00a34e" title="Account"></i></label>
                                <select wire:model="filter_account" class="form-select form-select-sm dropdown-toggle-style p-2" style="width: 100%; height: 2.5rem; font-size: 0.85rem; overflow: auto;">
                                    <option value="">All</option>
                                    @foreach ($organizations->where('name', '!=', 'Ewa') as $item)
                                        <option value="{{ $item->name }}">{{ $item->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </form>
                    </div>

                    {{-- Modal Footer --}}
                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary rounded-3 px-4" wire:click="clear" style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;">Clear</button>
                        <button type="button" class="btn btn-success rounded-3 px-4" wire:click="getData" style="height: 40px; border-color: #01a44f; background: #01a44f">Apply</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
