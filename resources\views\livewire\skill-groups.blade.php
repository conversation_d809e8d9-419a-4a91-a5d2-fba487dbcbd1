<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="row mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            Skill Groups
                        </b>
                    </h5>
                    {{-- <i class="fa-regular fa-file-excel fa-2xl float-end" style="cursor: pointer"></i> --}}
                    <h6 class="text-muted">
                        View and manage skill groups
                        <i class="fa-solid fa-people-roof fa-2xl float-end" style="color: #00a34e"></i>
                    </h6>
                </div>
            </div>
        </div>
    </div>


    {{-- bottom row  --}}
    <div class="row mx-3 d-flex ps-5">
        <div class="col-12 col-md-12 col-lg-12" style="letter-spacing: 1px;" wire:key="sss">
            <div class="row px-1">
                <div class="col-12 col-md-12 col-lg-12" style="letter-spacing: 1px;" wire:key="table">
                    <div class="col-12 col-md-12 col-lg-12 border bg-white shadow pt-3 rounded-3 px-3" style="letter-spacing: 1px;" wire:key="table">
                        <div class="d-flex justify-content-between mb-2">

                            <div class="d-flex mb-1">
                                <label for="" class="col-md-1 align-self-center me-1 fs-6" style="width: fit-content">Search: </label>
                                <input id="searchInput" type="text" class="col-md-1 form-control mb-1 d-inline text-muted p-1" placeholder=' &#xF002;' style="font-family:Arial, FontAwesome;width:9rem" wire:model.live.debounce.300ms="searchGroup" onclick="this.placeholder=''" onblur="this.placeholder='&#xF002;'">
                            </div>

                            <div class="col-6 d-flex justify-content-end">
                                <button class="btn btn-md rounded-2" style="background-color: #00a34e; color:white" data-bs-toggle="modal" data-bs-target="#add-group"><i class="fa-solid fa-plus"></i><span> Add Group</span></button>
                            </div>

                        </div>

                        <div class="table-responsive px-0">
                            <table class="table table-hover table-bordered overflow-auto mb-0" id="table" style="width:100%">
                                <thead id="thead" class="text-muted" style="font-size: 0.7rem">
                                    <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                        <th scope="col" class="text-center align-middle">ID</th>
                                        <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('name')">
                                            NAME
                                            @if ($sortBy !== 'name')
                                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @elseif ($sortDir === 'ASC')
                                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @else
                                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @endif
                                        </th>

                                        <th scope="col" class="text-center align-middle">ACDID</th>

                                        <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('type')">
                                            TYPE
                                            @if ($sortBy !== 'acdid')
                                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @elseif ($sortDir === 'ASC')
                                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @else
                                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @endif
                                        </th>
                                        <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('language')">
                                            LANGUAGE
                                            @if ($sortBy !== 'language')
                                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @elseif ($sortDir === 'ASC')
                                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @else
                                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @endif
                                        </th>
                                        <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                                            CREATED AT
                                            @if ($sortBy !== 'created_at')
                                                <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @elseif ($sortDir === 'ASC')
                                                <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @else
                                                <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:#00a34e"></i>
                                            @endif
                                        </th>
                                        <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                                            ACTIONS
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="" style="font-size:0.8rem" id="tbody">

                                    @forelse($skillGroups as $group)
                                        <tr class="align-middle">
                                            <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                            <td class="text-muted text-center py-3 align-middle"> {{ $group->name }} </td>
                                            <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $group->acdid }} </td>
                                            <td class="text-muted text-center py-3 text-nowrap align-middle">{{ Str::title($group->type) }}</td>
                                            <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Str::title($group->language) }} </td>
                                            <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Carbon::parse($group->created_at)->format('Y-m-d h:i A') }}</td>
                                            <td class="text-muted text-center py-3 text-nowrap align-middle">
                                                @if (in_array(Auth::user()->role, [1, 3, 2]))
                                                    <i class="fa-solid fa-pen-to-square fa-xl me-1" aria-hidden="true" style="cursor: pointer;color:#ebb34c" data-bs-toggle="modal" data-bs-target="#edit-group" wire:click="selectGroup('{{ $group->id }}')" title="Edit Group"></i>
                                                    <i class="fa fa-trash fa-xl" aria-hidden="true" style="cursor: pointer; color:tomato" wire:click="showDeleteAlert('{{ $group->id }}')" title="Delete User"></i>
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="18" class="text-muted text-center bg-white"> No groups found</td>
                                        </tr>
                                    @endforelse

                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-end mt-2 pe-0">
                            {{ $skillGroups->links(data: ['scrollTo' => false]) }}
                        </div>



                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="edit-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <h5 class="modal-title" id="staticBackdropLabel">Edit Skill Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Type:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selectedGroupType }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" wire:click="$set('selectedGroupType','Inbound')">Inbound</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" wire:click="$set('selectedGroupType','Outbound')">Outbound</a></li>
                                </ul>
                                @error('selectedGroupType')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Language:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $selectedGroupLanguage }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" wire:click="$set('selectedGroupLanguage','Arabic')">Arabic</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" wire:click="$set('selectedGroupLanguage','English')">English</a></li>
                                </ul>
                                @error('selectedGroupLanguage')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedGroup">
                            @error('selectedGroup')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">ACDID:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedGroupAcdid">
                            @error('selectedGroupAcdid')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Description:</label>
                            <textarea type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedGroupDesc"></textarea>
                            @error('selectedGroupDesc')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-success" style="background-color: #00a34e" wire:click="editGroup">Apply</button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" wire:click="clear" id="closeEdit">Close</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Add Group Modal -->
    <div class="modal fade" id="add-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <h5 class="modal-title" id="staticBackdropLabel">Add Skill Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form wire:submit.prevent>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Type:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $added_type ?? '--' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" wire:click="addType('Inbound')">Inbound</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" wire:click="addType('Outbound')">Outbound</a></li>
                                </ul>
                                @error('added_type')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Language:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $added_lang ?? '--' }}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="addlang('Arabic')">Arabic</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="addlang('English')">English</a></li>
                                </ul>
                                @error('added_lang')
                                    <span class="text-danger fs-6">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="added_name">
                            @error('added_name')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">ACDID:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="added_acdid">
                            @error('added_acdid')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Description:</label>
                            <textarea type="text" class="form-control bg-white" id="recipient-name" wire:model="added_desc"></textarea>
                            @error('added_desc')
                                <span class="text-danger fs-6">{{ $message }}</span>
                            @enderror
                        </div>
                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-success" style="background-color: #00a34e" wire:click="addGroup">Apply</button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" wire:click="clear" id="close">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>
