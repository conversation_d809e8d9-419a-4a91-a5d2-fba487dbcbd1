<?php

namespace App\Livewire\Reports;

use App\Exports\InteractionDurationExport;
use App\Models\Evaluation;
use Livewire\Component;
use App\Models\Interaction;
use App\Models\Organization;
use Livewire\WithPagination;
use App\Exports\InteractionsExport;
use Maatwebsite\Excel\Facades\Excel;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;

class LongInteractions extends Component
{
    use LivewireAlert, WithPagination;

    public $filterType = 'General';
    public $searchCalls = '';
    public $filterApplied = false;
    public $custom_date_from;
    public $custom_date_to;
    public $filter_calledId;
    public $filter_callerId;
    public $filter_callId;
    public $filter_agentName;
    public $filter_account;
    public $filter_duration;
    public $filter_agentId;
    public $filter_callEnder;
    public $filter_uniqueId;
    public $filter_language;
    public $filter_duration_sign = "=";
    public $filter_selected_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
    public $all_languages = ['Arabic', 'English', 'German', 'Hebrew', 'Italian', 'Turkish', 'Kurdish', 'Spanish', 'Russian', 'Urdu', 'French'];
    public $filter_time_name;
    public $filter_time_days;
    public $callID = null;
    public $recordID;
    public $viewCommentsModal = false;
    public $selectedInteractionId; //when clicking view comments
    public $viewEvaluationModal = false;
    public $viewEvaluationFormModal = false;
    public $sortBy = 'full_name';
    public $sortDir = 'ASC';
    public $filtersApplied = false;
    public $activeTab = 'general';
    public $paginationTheme = 'bootstrap';

    public $perPage = 15;
    
    public function mount()
    {
        $this->filter_time_set('1');
        $this->filterApplied = true;
    }

    public function interactionsPerPage($value)
    {
        $this->perPage = (int) $value; // Ensure it's an integer
        $this->resetPage(); // Optional: Reset to the first page
    }


    public function filter_languages($language)
    {
        if (in_array($language, $this->filter_selected_languages)) {
            $this->filter_selected_languages = array_values(array_diff($this->filter_selected_languages, [$language]));
        } else {
            $this->filter_selected_languages[] = $language;
            $this->filter_selected_languages = array_values($this->filter_selected_languages);
        }
    }

    public function filterTypeChange($data)
    {
        $this->filterType = $data;
    }

    public function apply_custom_date()
    {
        $this->filter_time_name = 'Custom';
        $this->validate();
        $this->dispatch('closeCustomDateModal');
    }

    public function rules()
    {
        return [
            'custom_date_from' => 'required_if:filter_time_name,Custom|',
            'custom_date_to' => 'required_if:filter_time_name,Custom|after:custom_date_from'
        ];
    }

    public function clear()
    {
        $this->filterApplied = false;
        $this->filter_time_name = 'All Time';
        $this->custom_date_from = null;
        $this->custom_date_to = null;
        $this->filter_time_days = null;
        $this->filter_calledId = null;
        $this->filter_callerId = null;
        $this->filter_duration = null;
        $this->filter_agentId = null;
        $this->filter_callId = null;
        $this->filter_callEnder = null;
        $this->filter_agentName = null;
        $this->filter_uniqueId = null;
        $this->filter_account = null;
        $this->filter_language = null;
        $this->filter_duration_sign = "=";
        $this->filter_selected_languages = $this->all_languages;
        $this->viewCommentsModal = false;
        $this->selectedInteractionId = null;
        $this->viewEvaluationModal = false;
        $this->viewEvaluationFormModal = false;
        $this->callID = null;
        $this->recordID = null;

        $this->dispatch('selectAllLangs');
    }

    public function selectAllLanguages()
    {
        // Assuming you have an array of all possible languages
        $allLanguages = ['Arabic', 'English', 'Kurdish', 'Spanish', 'German', 'French', 'Italian', 'Russian', 'Urdu', 'Hebrew', 'Turkish'];

        // Check if all languages are currently selected
        if (count($this->filter_selected_languages) === count($allLanguages)) {
            // If all are selected, clear the selection
            $this->filter_selected_languages = [];
            $shouldCheck = false;
        } else {
            // Otherwise, select all languages
            $this->filter_selected_languages = $allLanguages;
            $shouldCheck = true;
        }

        // Dispatch browser event to check/uncheck checkboxes
        $this->dispatch('checkOrUncheckLangs', ['shouldCheck' => $shouldCheck]);
    }

    public function closeModal()
    {
        $this->resetValidation();
        $this->dispatch('closeModal');
    }

    public function filter()
    {
        $this->filterApplied = true;
        $this->resetPage();
    }

    public function getEvaluation()
    {
        return Evaluation::query()->where('status', '1')->get();
    }

    public function export()
    {

        // Extend maximum execution time
        set_time_limit(0);

        // Extend the memory
        ini_set('memory_limit', '5000M');
        ini_set('max_execution_time', 0);

        // Clean the output buffer to avoid issues
        ob_end_clean();

        $export = new InteractionDurationExport(
            $this->filter_time_name,
            $this->filter_time_days,
            $this->custom_date_from,
            $this->custom_date_to,
            $this->filter_callId,
            $this->filter_agentId,
            $this->filter_duration,
            $this->filter_calledId,
            $this->filter_callerId,
            $this->filter_callEnder,
            $this->filter_agentName,
            $this->filter_account,
            $this->filter_language,
            $this->filter_uniqueId,
        );

        return Excel::download($export, 'interactions-duration-report.xlsx');
    }
    public function filter_time_set($time)
    {
        $this->filter_time_days = $time;

        $this->filter_time_name = match ((int) $time) {
            1 => 'Last 24 Hours',
            7 => 'Last 7 Days',
            30 => 'Last 30 Days',
            60 => 'Last 60 Days',
            default => 'Unknown time',
        };

        if ($this->filter_time_name == 'Custom') {
            $this->filter_time_days = null;
        }
    }
    public function getData() 
    {
        $this->filter();
        $this->dispatch('close-modal');
    }
    public function render()
    {
        $interactions = Interaction::orderByDesc('arrival_time');

        if ($this->filter_time_name == 'Custom' && $this->filterApplied) {
            $custom_date_from = $this->custom_date_from . ' 00:00:00';
            $custom_date_to = $this->custom_date_to . ' 23:59:59';
            $interactions = $interactions->whereBetween('arrival_time', [$custom_date_from, $custom_date_to]);
        } else if ($this->filter_time_days && $this->filterApplied) {
            $startDate = now()->subDays($this->filter_time_days)->toDateString();
            $interactions = $interactions->whereDate('arrival_time', '>=', $startDate);
        }

        // if ($this->filter_calledId && $this->filterApplied) $interactions = $interactions->where('called_id', 'like', "%$this->filter_calledId%");
        // if ($this->filter_callerId && $this->filterApplied) $interactions = $interactions->where('caller_id', 'like', "%$this->filter_callerId%");
        // if ($this->filter_callId && $this->filterApplied) $interactions = $interactions->where('call_id', 'like', "%$this->filter_callId%");
        // if ($this->filter_agentName && $this->filterApplied) {
        //     $interactions = $interactions->whereHas('agent', function ($q) {
        //         $q->where('full_name', 'like', "%$this->filter_agentName%");
        //     });
        // }
        // if ($this->filter_account && $this->filterApplied) {
        //     $interactions = $interactions->whereHas('agent.organization', function ($q) {
        //         $q->where('name', 'like', "%$this->filter_account%");
        //     });
        // }

        // if ($this->filter_selected_languages && $this->filterApplied) {
        //     $interactions = $interactions->where(function ($query) {
        //         foreach ($this->filter_selected_languages as $language) {
        //             $query->orWhere('language', 'like', "%{$language}%");
        //         }
        //     });
        // }

        // // Additional duration filter
        // $interactions = $interactions->when($this->filter_duration && $this->filterApplied, function ($q) {
        //     $filter_duration = gmdate('H:i:s', $this->filter_duration);
        //     $q->where('call_duration', $this->filter_duration_sign, $filter_duration);
        // });
        // $interactions = $interactions->when($this->filter_agentId && $this->filterApplied, function ($query) {
        //     $query->whereHas('agent', function ($q2) {
        //         $q2->where('agent_id', 'like', "%$this->filter_agentId%");
        //     });
        // });

        // $interactions = $interactions->when($this->filter_callEnder && $this->filterApplied, function ($query) {
        //     $query->where('call_ender', 'like', "%{$this->filter_callEnder}%");
        // });
        // $interactions = $interactions->when($this->filter_uniqueId && $this->filterApplied, function ($query) {
        //     $query->where('Genesys_CallUUID', 'like', "%{$this->filter_uniqueId}%");
        // });

        return view('livewire.reports.long-interactions', [
            'evaluations' => $this->getEvaluation(),
            'records' => $this->filterApplied ?
                $interactions
                ->when($this->filter_callerId, function ($q) {
                    $q->where('caller_id', 'like', "%$this->filter_callerId%");
                })
                ->when($this->filter_uniqueId, function ($q) {
                    $q->where('Genesys_CallUUID', 'like', "%$this->filter_uniqueId%");
                })
                ->when($this->filter_callEnder, function ($q) {
                    $q->where('call_ender', 'like', "%$this->filter_callEnder%");
                })
                ->when($this->filter_calledId, function ($q) {
                    $q->where('called_id', 'like', "%$this->filter_calledId%");
                })
                ->when($this->filter_agentId, function ($q) {
                    $q->whereHas('agent', function ($q2) {
                        $q2->where('agent_id', 'like', "%$this->filter_agentId%");
                    });
                })
                ->when($this->filter_duration, function ($q) {
                    $filter_duration = gmdate('H:i:s', $this->filter_duration);
                    $q->where('call_duration', $this->filter_duration_sign, $filter_duration);
                })
                ->when($this->filter_agentName, function ($q) {
                    $q->whereHas('agent', function ($q2) {
                        $q2->where('full_name', 'like', "%$this->filter_agentName%");
                    });
                })
                ->when($this->filter_time_name == 'Custom', function ($q) {
                    $custom_date_from = $this->custom_date_from . ' 00:00:00';
                    $custom_date_to = $this->custom_date_to . ' 23:59:59';
                    $q->whereBetween('arrival_time', [$custom_date_from, $custom_date_to]);
                })
                ->when($this->filter_time_days, function ($q) {
                    $startDate = now()->subDays($this->filter_time_days)->toDateString();
                    $q->whereDate('arrival_time', '>=', $startDate);
                })
                ->when($this->filter_account, function ($q) {
                    return $q->whereHas('agent.organization', function ($q2) {
                        $q2->where('name', 'like', "%{$this->filter_account}%");
                    });
                })
                ->when($this->filter_selected_languages && $this->filter_selected_languages != $this->all_languages, function ($qqq) {
                    $lowercaseLanguages = array_map('lcfirst', $this->filter_selected_languages);
                    $qqq->whereIn('language', $lowercaseLanguages);
                })
                ->paginate($this->perPage)
                :
                $interactions->paginate($this->perPage),
            'organizations' => Organization::all(),
        ]);
    }
}
