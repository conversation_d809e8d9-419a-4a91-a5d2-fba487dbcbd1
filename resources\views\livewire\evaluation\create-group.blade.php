<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">

            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button id="open_modal"
                    data-bs-toggle="modal"
                    wire:click='addModal()'
                    data-bs-target="#evaluationModal"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 150px; height: 45px; border-color: #01a44f; background: #01a44f;">
                    <i
                        class="fa fa-plus fa-style text-white ms-3"
                        style="font-size: 20px;    margin-inline-end: auto;"></i>
                    <span style="font-size: 17px;">Add New Group</span>
                </button>
            </div>
        </div>
    </div>

    <div class="row-tow-table">

            <div class="parent-sections col-7 p-0 mx-3 ps-5 table1">

                <div class="section-one">
                        <div class="div-table rounded-2 shadow-sm mb-3">

                        <table class="table table-bordered table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                            <thead style="background-color: #f8fafc !important;font-size: .8rem">
                            <tr>
                                <th scope="col" class="checked text-center" style="width: 10%;">
                                    <div class="form-check">
                                        #
                                    </div>
                                </th>
                                <th scope="col" style="width: 50%;">Group Name</th>
                                <th scope="col" style="width: 25%;">Weight</th>
                                <th scope="col" style="width: 15%;">Status</th>
                            </tr>
                            </thead>
                            <tbody>
                            @forelse($Evaluation_group ?? [] as $group)
                                <tr id="row{{$group->id}}" style="cursor: pointer;" wire:click="showEdit({{$group->id }})" wire:ignore.self>
                                    <td class="text-muted" scope="row text-center">
                                        <div class="form-check">
                                            <button class="but">{{$loop->index +1}}</button>
                                        </div>
                                    </td>
                                    <td class="text-muted" id="td2{{$group->id}}">
                                        {!! $group->group_name !!}
                                        <div class="span_icon" wire:ignore>
                                            <i class="fa-solid fa-pen" id="pencel_icon{{$group->id}}" wire:click="showUpdateModalGroupName({{$group->id }})" style="color: #989898;visibility: hidden;"></i>
                                        </div>
                                    </td>
                                    <td class="text-muted" id="td{{$group->id}}">
                                        {!! $group->group_weight !!}%
                                        <div class="span_icon" wire:ignore>
                                                <i class="fa-solid fa-pen" wire:click="showUpdateModalWeight({{$group->id}})" id="pencel_icon2{{$group->id }}" style="color: #989898; visibility: hidden;"></i>
                                        </div>
                                    </td>
                                    <td class="text-center"> <!-- Centering the content within the table cell -->
                                            <div class="d-flex align-items-center gap-2 justify-content-center align-items-center">
                                                <div class="position-relative">
                                                    <input type="checkbox" class="d-none" id="customSwitch{{ $group->id }}" wire:click="statusUpdateModal({{$group->id }})" />
                                                    <label for="customSwitch{{ $group->id }}" class="custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $group->status ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">
                                                        <div class="switch-handle position-absolute"
                                                            style="
                                                                width: 18px;
                                                                height: 18px;
                                                                background-color: {{ $group->status ? '#ffffff' : '#FF5E60' }};
                                                                border-radius: 50%;
                                                                top: 3px;
                                                                left: {{ $group->status ? '22px' : '3px' }};
                                                                transition: left 0.3s, background-color 0.3s;">
                                                            @if ($group->status)
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                                    <path
                                                                        d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                                        fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                                </svg>
                                                            @else
                                                                <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                                    <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                </svg>
                                                            @endif
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                    </td>

                                </tr>


                            @empty
                                <tr>
                                    <td colspan="11" class="text-muted text-center"> There is no Evaluation form found</td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                    {{-- {!! $CannedResponse_apps->links() !!}--}}
                </div>
            </div>





            <div class="card parent-sections mx-3 col-4 p-0 table2 div-table rounded-2 shadow-sm mb-3" style="border-radius: 0px; height: 100%;">
                <div class="card-body " style="padding: 0px; height: 100%;border:none;width:100%">

                    <div class="table-responsive mb-2 rounded-2 shadow-sm mb-3" style="border-radius: 10px; height: 100%; overflow-y: auto;border:none">
                        <div scope="col" style="margin: 1.7%;text-align: start"><strong style="font-size: large;">{{$groupSelected}} Question</strong></div>
                        <hr style="margin: 0 0;color: #00a34e;">
                        <div style="padding: 4%;height: 80%;overflow: auto;background-color:white">
                    @if($questionShow)
                        <table class="table table-bordered table-striped rounded-2 shadow-sm mb-3" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                            <thead style="background-color: #f9f9f9;">
                            <tr>
                                <th scope="col" class="checked text-center" style="width: 10%;">
                                    <div class="form-check">
                                        #
                                    </div>
                                </th>
                                <th scope="col" style="width: 50%;">Question</th>
                                <th scope="col" style="width: 15%;">Edit</th>
                            </tr>
                            </thead>
                            <tbody>
                            @forelse($Evaluation_question ?? [] as $question)
                                <tr>
                                    <th class="text-muted" scope="row text-center">
                                        <div class="form-check">
                                            <button class="but">{{$loop->index +1}}</button>
                                        </div>
                                    </th>
                                    <td class="text-muted">{{$question->question_name}}</td>
                                    <td class="text-muted">
                                        <a href="{{ route('evaluation.editQuestion', ['evaluation_id' => $form_id,'group_id'=>$groupID,'question_id'=>$question->id]) }}">
                                            <i class="fa-solid fa-pen"  style="font-size: 20px;color: #00a34e;"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="11" class="text-muted text-center"> There is no Question found</td>
                                </tr>
                            @endforelse
                            <tr></tr>
                            </tbody>
                        </table>
                            @else
                                {{-- <img src="{{asset('assets/images/evaluation/NoHeaderSelected.png')}}" width="200" alt="" style="margin: 10% 28%;"> --}}
                                <div class="" style="margin: 10% 28%;color: white;margin: 30% 10%;text-align: center;background: #40798c;border-radius: 7px;"><h4>No Group Selected</h4></div>
                            @endif

                        </div>
                        @if($questionShow)
                        <div class="footer" style="text-align: end;padding: 0px 4%;">
                            <a href="{{ route('evaluation.createQuestion', ['evaluation_id' => $form_id , 'group_id' => $groupID]) }}" style="color: #00a34e;">View all... </a>
                        </div>
                        @endif
                    </div>
                    {{-- {!! $CannedResponse_apps->links() !!}--}}
                </div>
            </div>




    </div>

    <div class="mx-3 ps-5" style="margin-top: 3%;">
        <div class="col-6">
            <a href="{{ route('evaluation.index') }}" class="btn btn-sucess mx-1 shadow" style="padding-top: 8px;border-color: #01a44f; background: #01a44f;">
                <img src="{{ asset('assets/images/evaluation/previous.png') }}" width="18" alt="" style="margin-right: 25px;" class="previous"> <span style="font-size: 17px;color:white">Previous</span>
            </a>
        </div>
    </div>

    <div class="dropdown-container" id="dropdownContainer" style="display: none;width: 11% !important;" wire:ignore.self>
        <div class="dropdown">
            <div style="padding: 15px;">
                <input type="number" class="form-control" wire:model.defer="group_weight" id="group_weight" >
                @error('group_weight')<small class="text-danger"> {{ $message }} </small> @enderror
            </div>
            <div class="dropdown-divider"></div>
            <div style="padding: 8px 15px;">
                <button class="btn btn-success rounded-3 px-4"
                style="height: 40px; border-color: #01a44f; background: #01a44f;width:100%"  wire:click="{{'updateWeight'}}">Save</button>
            </div>
        </div>
    </div>




    <div class="dropdown-container2" id="dropdownContainer2" style="display: none;width: 24% !important;" wire:ignore.self>
        <div class="dropdown">
            <div style="padding: 15px;">
                <textarea class="form-control" wire:model.defer="group_name" id="group_name" style="resize: none;"></textarea>
                @error('group_name')<small class="text-danger"> {{ $message }} </small> @enderror
            </div>
            <div class="dropdown-divider"></div>
            <div style="padding: 8px 15px;">
                <button class="btn btn-success rounded-3 px-4"
                style="height: 40px; border-color: #01a44f; background: #01a44f;float:right"  wire:click="{{'updateGroupName'}}">Save</button>
            </div>
        </div>
    </div>




    <div wire:ignore.self class="modal fade" id="evaluationModal" tabindex="-1" role="dialog" aria-labelledby="evaluationModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: white;">
                    <div class="modal-header" style="border: none;">
                        <div class="d-flex">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                                {{-- <i class="fa-solid fa-filter" ></i> --}}
                                <i class="fas fa-file-alt" style="font-size: 30px; color: #01a44f !important;"></i>
                            </div>
                            <h6 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 20px;">
                                @if($modalIdShow != 'on')
                                    {{$modalId ? 'Update Group' : 'New Group'}}
                                @else
                                    {{ 'View Group'}}
                                @endif
                            </h6>
                        </div>
                        <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times text-success" style="font-size: 24px;"></i>
                        </button>
                    </div>
                    <div class="modal-body" style="border: none;">

                        <div class="col-12">

                            <div class="mb-3">
                                <label for="title" class="col-form-label">Group Name:</label>
                                @if($modalIdShow != 'on')

                                <textarea class="form-control" wire:model.defer="group_name" id="group_name" style="resize: none;"></textarea>
                                    @error('group_name')<small class="text-danger"> {{ $message }} </small> @enderror
                                @else
                                    <textarea class="form-control" wire:model.defer="group_name"  style="resize: none;" disabled></textarea>
                                @endif
                            </div>


                        <div class="mb-3">
                            <label for="title" class="col-form-label">Group Weight (%):</label>
                                @if($modalIdShow != 'on')

                                    <input type="number" class="form-control" wire:model.defer="group_weight" id="group_weight" style="width: 30%;">
                                    @error('group_weight')<small class="text-danger"> {{ $message }} </small> @enderror
                                @else
                                    <input type="number" class="form-control" wire:model.defer="group_weight"  style="width: 30%;" disabled>
                                @endif
                        </div>

                    </div>
                    </div>

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal" aria-label="Close">
                        Close
                    </button>
                    @if(!$modalIdShow)
                        <button
                            class="btn btn-success rounded-3 px-4"
                            style="height: 40px; border-color: #01a44f; background: #01a44f;"
                            wire:click="{{ $modalId ? 'update' : 'store' }}" >
                            {{ $modalId ? 'Update' : 'Submit' }}
                        </button>
                    @endif
                </div>
        </div>
    </div>
</div>
