<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            'Allow Access Telephony',
            'Play Recorded Interactions',
            'Download Audio Files',
            'Show Evaluated Interactions Only',
            'View Other Users Interactions Comments',
            'Allow Search By Evaluation Form',
            'Listen Own Session Recordings',
            'View Interactions Evaluations',
            'Create Interaction Evaluations',
            'View Evaluation Reports',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }
    }
}
