<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CallDetails extends Model
{
    use HasFactory;
    protected $table = 'interactions_details';
    protected $guarded = [];

    /**
     * Get the call that owns the CallDetails
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function call()
    {
        return $this->belongsTo(Interaction::class, 'call_id', 'call_id');
    }
}
