<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\User;
use App\Models\UserGroup;
use App\Models\SkillGroup;
use App\Models\Organization;
use App\Models\TimeForFlags;
use Illuminate\Database\Seeder;
use Database\Seeders\PrefixesSeeder;
use Database\Factories\UserGroupFactory;
use Database\Factories\OrganizationFactory;
use Database\Seeders\OrganizationsAndGroupsSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        /* Abdullah comment */
//        $this->call(PrefixesSeeder::class);
//        $this->call(OrganizationsAndGroupsSeeder::class);
        /* <PERSON> comment */

        // SkillGroup::factory(10)->create();

        /* <PERSON> comment */
//        $this->call(QaFlagSeeder::class);
//        $this->call(FlagFilterSeeder::class);
//        $this->call(TimeForFlagsSeeder::class);
//        $this->call(PermissionSeeder::class);
//        $this->call(ParameterSeeder::class);
//        $this->call(UserSeeder::class);
        /* <PERSON> comment */

        $this->call(EncryptionSeeder::class);
        $this->call(UserSeeder::class);



        // User::factory(20)->create();
        // Organization::factory(10)->create();
        // UserGroup::factory(10)->create();


        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}
