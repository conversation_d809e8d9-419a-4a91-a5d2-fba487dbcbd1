<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationSubmissionAnswer extends Model
{
    use HasFactory;

    public $guarded = [];

    public function submission()
    {
        return $this->belongsTo(EvaluationSubmission::class,'evaluation_submission_id');
    }
    public function question()
    {
        return $this->belongsTo(EvaluationQuestion::class ,'evaluation_question_id');
    }


}
