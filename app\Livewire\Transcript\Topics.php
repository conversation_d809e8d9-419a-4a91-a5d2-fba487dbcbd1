<?php

namespace App\Livewire\Transcript;

use App\Models\TranscriptionTopics;
use Livewire\Component;

class Topics extends Component
{
    public $callId;
    public $decodedTopics = [];  // To store the decoded detected_topics
    public $searchTerm = '';     // To store the search term

    public function mount($callId)
    {
        $this->callId = $callId;

        // Fetch the transcription topics for this call and decode the detected_topics
        $topics = TranscriptionTopics::where('call_id', $this->callId)->get();

        foreach ($topics as $topic) {
            // Decode the JSON detected_topics and store it
            $this->decodedTopics = json_decode($topic->detected_topics, true);
        }

    }

    // Method to filter the topics based on the search term
    public function filteredTopics()
    {
        // If there's no search term, return all decoded topics
        if (empty($this->searchTerm)) {
            // Fetch the transcription topics for this call and decode the detected_topics
            $topics = TranscriptionTopics::where('call_id', $this->callId)->get();

            foreach ($topics as $topic) {
                // Decode the JSON detected_topics and store it
                $this->decodedTopics = json_decode($topic->detected_topics, true);
            }
        }else{
            // Fetch the transcription topics for this call and decode the detected_topics
            $topics = TranscriptionTopics::where('call_id', $this->callId)->get();

            foreach ($topics as $topic) {
                // Decode the JSON detected_topics and store it
                $this->decodedTopics = json_decode($topic->detected_topics, true);
            }
        }


        $this->decodedTopics = collect($this->decodedTopics)->filter(function ($value, $key) {
            // Check if the key contains the search term
            return stripos($key, $this->searchTerm) !== false;
        });
    }



    public function render()
    {
        // Return the filtered topics to the view
        return view('livewire.transcript.topics', ['topics' => $this->decodedTopics]);
    }
}
