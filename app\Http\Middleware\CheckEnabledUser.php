<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckEnabledUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if(!auth()->user()->enabled || Auth::user()->terminated)
        {
            Auth::logout();
            return redirect('/login')->with(["status"=>"Your account is disabled, please contact the Admin or the IT department"]);
        }
        return $next($request);
    }
}
