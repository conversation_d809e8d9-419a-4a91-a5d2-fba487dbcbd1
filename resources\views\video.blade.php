@extends('layouts.app')

@section('title', 'Video Playback')

@section('content')
    <div class="container-fluid mt-3 px-4">
        <div class="row mx-3 ps-5">
            <div class="col-12 mb-5">
                <div class="card bg-white shadow py-2 px-3">
                    <div class="card-body p-4">
                        <h5>
                            <b>
                                Call ID: <span style="color: "> {{ $callId }} </span>
                            </b>
                        </h5>
                        <h6 class="text-muted">
                            <span style="color: #bfd23b">
                                {{ $agent_name }} -
                            </span>
                            <span style="color: #bfd23b">
                                {{ $agent_id }} -
                            </span>
                            <span style="color: #bfd23b">
                                {{ \Carbon\Carbon::now()->format('d/m/Y g:i A') }}
                            </span>
                        </h6>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mx-3 d-flex ps-5">
            <div class="col-12 col-md-12 col-lg-12 mb-lg-0 d-flex flex-column">
                <div class="card rounded-3 bg-white shadow p-2">
                    <div class="card-body py-3">
                        <div>
                            <video id="videoPlayer" class="video-js vjs-default-skin" controls preload="auto" width="100%" height="auto">
                                <source id="videoSource" src="" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to fetch and display the video
        const fetchDecryptedVideo = (encryptedFilePath, encryptedCallId) => {
            fetch(`/get_video/decrypt-video/${encodeURIComponent(encryptedCallId)}/${encodeURIComponent(encryptedCallId)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const videoUrl = URL.createObjectURL(blob);
                    const videoPlayer = document.getElementById('videoPlayer');
                    const videoSource = document.getElementById('videoSource');

                    // Update the video source and play the video
                    videoSource.src = videoUrl;
                    videoPlayer.load();
                    videoPlayer.play();
                })
                .catch(error => console.error('Error fetching video:', error));
        };

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const encryptedFilePath = '{{ $videoPath }}'; // Replace with actual encrypted file path
            const encryptedCallId = '{{ $callId }}'; // Replace with actual encrypted call ID
            fetchDecryptedVideo(encryptedFilePath, encryptedCallId);
        });
    </script>
@endsection
