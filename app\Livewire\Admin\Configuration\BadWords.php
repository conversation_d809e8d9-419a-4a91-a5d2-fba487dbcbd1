<?php

namespace App\Livewire\Admin\Configuration;

use App\Models\BadWord;
use App\Models\Organization;
use App\Models\UserGroup;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class BadWords extends Component
{
    use WithPagination,LivewireAlert;

    public $key;
    public $type;
    public $side_type;
    public $organization_id = null;
    public $getUserGroup = [];
    public $user_group_id;

    public $modalId;
    public $modalIdShow;

    public function getListeners()
    {
        return [
            'confirmed','modelFormReset',
            'getEncryption',
        ];
    }


    public function BadWord()
    {
        try
        {
            return BadWord::query()->get();
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function modelData()
    {
        return [
            'key'          => $this->key,
            'type'          => $this->type,
            'side_type'          => $this->side_type,
            'organization_id'          => $this->organization_id,
            'user_group_id'          => $this->user_group_id,

        ];
    }

    public function rules()
    {
        return [
            'key' =>       ['required'],
            'type' =>       ['required'],
            'side_type' =>       ['required'],
            'organization_id' =>       ['required'],
            'user_group_id' =>       ['required'],


        ];
    }

    public function update(){


        $this->validate();

        try {
            BadWord::find($this->modalId)->update($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }
    public function store(){


        $this->validate();

        try {
            BadWord::create($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }

    public function closeModal()
    {
        $this->modelFormReset();
    }

    public function showModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
        $this->modalIdShow = "on";
    }

    public function showDeleteAlert($id)
    {
        $this->modalId = $id;
        $this->alert('warning', 'Are you sure you want to delete this group?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirmed()
    {
        if (BadWord::find($this->modalId)->delete()) {
            $this->alert("success", "Group Deleted Successfully");
        }
    }

    public function showUpdateModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
    }

    public function modelLoadData()
    {
        $encryption = BadWord::find($this->modalId);

        $this->key = $encryption->key;
        $this->type = $encryption->type;
        $this->side_type = $encryption->side_type;
        $this->organization_id = $encryption->organization_id;
        $this->getUserGroup = UserGroup::where('organization_id',$this->organization_id)->get();
        $this->user_group_id = $encryption->user_group_id;

    }

    public function modelFormReset()
    {
        $this->key = "";
        $this->type = "";
        $this->side_type = "";
        $this->organization_id = "";
        $this->user_group_id = "";

        $this->modalId=null;
        $this->modalIdShow=null;

        $this->resetValidation();
    }

    public function getOrganizations()
    {
        return Organization::all();
    }

    public function storeGet()
    {

        $this->user_group_id = null;
        $this->getUserGroup = UserGroup::where('organization_id',$this->organization_id)->get();

    }


    public function render()
    {
//        dd(UserGroup::where('organization_id',$this->organization_id)->get());

//        $this->getUserGroup= UserGroup::where('organization_id',$this->organization_id)->get();
        return view('livewire.admin.configuration.bad-words', ['transcriptionBadWords' => $this->BadWord(),'organizations'=>$this->getOrganizations()]);
    }
}
