{"__meta": {"id": "X10fff6d2f68ad10365a7dad1347e06e5", "datetime": "2025-07-22 15:33:52", "utime": 1753187632.203987, "method": "GET", "uri": "/recordings", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.727377, "end": 1753187632.204046, "duration": 2.4766690731048584, "duration_str": "2.48s", "measures": [{"label": "Booting", "start": **********.727377, "relative_start": 0, "end": **********.789158, "relative_end": **********.789158, "duration": 1.0617811679840088, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.789176, "relative_start": 1.0617990493774414, "end": 1753187632.204048, "relative_end": 1.9073486328125e-06, "duration": 1.4148719310760498, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31179120, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "recordings", "param_count": null, "params": [], "start": 1753187631.190495, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/recordings.blade.phprecordings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Frecordings.blade.php&line=1", "ajax": false, "filename": "recordings.blade.php", "line": "?"}}, {"name": "livewire.records", "param_count": null, "params": [], "start": 1753187631.874129, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/records.blade.phplivewire.records", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Frecords.blade.php&line=1", "ajax": false, "filename": "records.blade.php", "line": "?"}}, {"name": "livewire::bootstrap", "param_count": null, "params": [], "start": 1753187631.924508, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/bootstrap.blade.phplivewire::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": 1753187632.069923, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "livewire-alert::components.scripts", "param_count": null, "params": [], "start": 1753187632.134352, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\vendor\\jantinnerezo\\livewire-alert\\src/../resources/views/components/scripts.blade.phplivewire-alert::components.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Fjantinnerezo%2Flivewire-alert%2Fresources%2Fviews%2Fcomponents%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}, {"name": "livewire-alert::components.flash", "param_count": null, "params": [], "start": 1753187632.168475, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\i-sentiment\\vendor\\jantinnerezo\\livewire-alert\\src/../resources/views/components/flash.blade.phplivewire-alert::components.flash", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Fjantinnerezo%2Flivewire-alert%2Fresources%2Fviews%2Fcomponents%2Fflash.blade.php&line=1", "ajax": false, "filename": "flash.blade.php", "line": "?"}}]}, "route": {"uri": "GET recordings", "middleware": "web, auth, checkEnabledUser, checkPasswordPolicy, resetNextLogin, allowAccessTelephony, adminAccess", "controller": "App\\Http\\Controllers\\RecordsController@index", "namespace": null, "prefix": "", "where": [], "as": "recordings", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FHttp%2FControllers%2FRecordsController.php&line=9\" onclick=\"\">app/Http/Controllers/RecordsController.php:9-12</a>"}, "queries": {"nb_statements": 21, "nb_failed_statements": 0, "accumulated_duration": 0.21993999999999994, "accumulated_duration_str": "220ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.881634, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "ilog", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 36 limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.940769, "duration": 0.00658, "duration_str": "6.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ilog", "start_percent": 0, "width_percent": 2.992}, {"sql": "select exists(select * from `permissions` inner join `permission_user` on `permissions`.`id` = `permission_user`.`permission_id` where `permission_user`.`user_id` = 36 and `permission_id` = 1) as `exists`", "type": "query", "params": [], "bindings": ["36", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "allowAccessTelephony", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Middleware\\AllowAccessTelephony.php", "line": 20}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "resetNextLogin", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Middleware\\ForcePasswordReset.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "checkPasswordPolicy", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Http\\Middleware\\CheckPasswordPolicy.php", "line": 39}], "start": 1753187631.1608782, "duration": 0.016079999999999997, "duration_str": "16.08ms", "memory": 0, "memory_str": null, "filename": "allowAccessTelephony:20", "source": "middleware::allowAccessTelephony:20", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FHttp%2FMiddleware%2FAllowAccessTelephony.php&line=20", "ajax": false, "filename": "AllowAccessTelephony.php", "line": "20"}, "connection": "ilog", "start_percent": 2.992, "width_percent": 7.311}, {"sql": "select exists(select * from `parameters` inner join `parameter_user` on `parameters`.`id` = `parameter_user`.`parameter_id` where `parameter_user`.`user_id` = 36 and `parameter_id` = 33) as `exists`", "type": "query", "params": [], "bindings": ["36", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "recordings", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/recordings.blade.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753187631.228396, "duration": 0.01602, "duration_str": "16.02ms", "memory": 0, "memory_str": null, "filename": "recordings:155", "source": "view::recordings:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Frecordings.blade.php&line=155", "ajax": false, "filename": "recordings.blade.php", "line": "155"}, "connection": "ilog", "start_percent": 10.303, "width_percent": 7.284}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 698}, {"index": 29, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 697}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": 1753187631.55289, "duration": 0.05683, "duration_str": "56.83ms", "memory": 0, "memory_str": null, "filename": "Records.php:698", "source": "app/Livewire/Records.php:698", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=698", "ajax": false, "filename": "Records.php", "line": "698"}, "connection": "ilog", "start_percent": 17.587, "width_percent": 25.839}, {"sql": "select * from `evaluations` where `status` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 634}, {"index": 16, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 789}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": 1753187631.62588, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Records.php:634", "source": "app/Livewire/Records.php:634", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=634", "ajax": false, "filename": "Records.php", "line": "634"}, "connection": "ilog", "start_percent": 43.425, "width_percent": 0.409}, {"sql": "select count(*) as aggregate from `interactions` where exists (select * from `users` where `interactions`.`user_id` = `users`.`id` and exists (select * from `organizations` where `users`.`organization_id` = `organizations`.`id` and `id` in (4, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50))) and `call_duration` > '0:00:00' and (`call_type` like '%%' or `id` = '' or `Genesys_CallUUID` = '' or `agent_extension` like '%%' or `caller_id` like '%%' or `called_id` like '%%' or `call_ender` like '%%' or `language` like '%%' or exists (select * from `organizations` where `interactions`.`organization_id` = `organizations`.`id` and `name` like '%%') or exists (select * from `users` where `interactions`.`user_id` = `users`.`id` and (`full_name` like '%%' or `agent_id` like '%%' or `username` like '%%')))", "type": "query", "params": [], "bindings": ["4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "0:00:00", "%%", "", "", "%%", "%%", "%%", "%%", "%%", "%%", "%%", "%%", "%%"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.635369, "duration": 0.056100000000000004, "duration_str": "56.1ms", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 43.835, "width_percent": 25.507}, {"sql": "select `interactions`.*, (select count(*) from `qa_flags` inner join `interaction_qa_flag` on `qa_flags`.`id` = `interaction_qa_flag`.`qa_flag_id` where `interactions`.`id` = `interaction_qa_flag`.`interaction_id`) as `qa_flags_count`, (select count(*) from `comments` where `interactions`.`id` = `comments`.`interaction_id`) as `comments_count`, (select count(*) from `users` inner join `interaction_listener` on `users`.`id` = `interaction_listener`.`user_id` where `interactions`.`id` = `interaction_listener`.`interaction_id`) as `listeners_count` from `interactions` where exists (select * from `users` where `interactions`.`user_id` = `users`.`id` and exists (select * from `organizations` where `users`.`organization_id` = `organizations`.`id` and `id` in (4, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50))) and `call_duration` > '0:00:00' and (`call_type` like '%%' or `id` = '' or `Genesys_CallUUID` = '' or `agent_extension` like '%%' or `caller_id` like '%%' or `called_id` like '%%' or `call_ender` like '%%' or `language` like '%%' or exists (select * from `organizations` where `interactions`.`organization_id` = `organizations`.`id` and `name` like '%%') or exists (select * from `users` where `interactions`.`user_id` = `users`.`id` and (`full_name` like '%%' or `agent_id` like '%%' or `username` like '%%'))) order by `arrival_time` desc, `arrival_time` desc limit 15 offset 0", "type": "query", "params": [], "bindings": ["4", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "0:00:00", "%%", "", "", "%%", "%%", "%%", "%%", "%%", "%%", "%%", "%%", "%%"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.696313, "duration": 0.03726, "duration_str": "37.26ms", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 69.342, "width_percent": 16.941}, {"sql": "select * from `organizations` where `organizations`.`id` in (16, 23, 46)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.741582, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 86.283, "width_percent": 0.336}, {"sql": "select * from `users` where `users`.`id` in (166, 356, 368, 515, 521, 742)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.7496421, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 86.619, "width_percent": 0.527}, {"sql": "select * from `organizations` where `organizations`.`id` in (16, 23, 46)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.7582982, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 87.146, "width_percent": 0.364}, {"sql": "select * from `user_groups` where `user_groups`.`id` in (96, 144, 220, 222)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.7651198, "duration": 0.0051600000000000005, "duration_str": "5.16ms", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 87.51, "width_percent": 2.346}, {"sql": "select * from `evaluation_submissions` where `evaluation_submissions`.`referenceID` in (620781, 620839, 620840, 620841, 620842, 620843, 620844, 620845, 620846, 620847, 620873, 620874, 620875, 620876, 620877)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.7989461, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 89.856, "width_percent": 0.941}, {"sql": "select `qa_flags`.*, `interaction_qa_flag`.`interaction_id` as `pivot_interaction_id`, `interaction_qa_flag`.`qa_flag_id` as `pivot_qa_flag_id` from `qa_flags` inner join `interaction_qa_flag` on `qa_flags`.`id` = `interaction_qa_flag`.`qa_flag_id` where `interaction_qa_flag`.`interaction_id` in (620781, 620839, 620840, 620841, 620842, 620843, 620844, 620845, 620846, 620847, 620873, 620874, 620875, 620876, 620877)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.8053222, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 90.797, "width_percent": 0.696}, {"sql": "select `users`.*, `interaction_listener`.`interaction_id` as `pivot_interaction_id`, `interaction_listener`.`user_id` as `pivot_user_id`, `interaction_listener`.`created_at` as `pivot_created_at` from `users` inner join `interaction_listener` on `users`.`id` = `interaction_listener`.`user_id` where `interaction_listener`.`interaction_id` in (620781, 620839, 620840, 620841, 620842, 620843, 620844, 620845, 620846, 620847, 620873, 620874, 620875, 620876, 620877)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 911}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.811012, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Records.php:911", "source": "app/Livewire/Records.php:911", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=911", "ajax": false, "filename": "Records.php", "line": "911"}, "connection": "ilog", "start_percent": 91.493, "width_percent": 0.332}, {"sql": "select * from `organizations` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 914}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.832635, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Records.php:914", "source": "app/Livewire/Records.php:914", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=914", "ajax": false, "filename": "Records.php", "line": "914"}, "connection": "ilog", "start_percent": 91.825, "width_percent": 0.414}, {"sql": "select * from `skill_groups`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 915}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.839859, "duration": 0.00723, "duration_str": "7.23ms", "memory": 0, "memory_str": null, "filename": "Records.php:915", "source": "app/Livewire/Records.php:915", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=915", "ajax": false, "filename": "Records.php", "line": "915"}, "connection": "ilog", "start_percent": 92.239, "width_percent": 3.287}, {"sql": "select * from `evaluations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Records.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\app\\Livewire\\Records.php", "line": 916}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": 1753187631.852914, "duration": 0.005719999999999999, "duration_str": "5.72ms", "memory": 0, "memory_str": null, "filename": "Records.php:916", "source": "app/Livewire/Records.php:916", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FLivewire%2FRecords.php&line=916", "ajax": false, "filename": "Records.php", "line": "916"}, "connection": "ilog", "start_percent": 95.526, "width_percent": 2.601}, {"sql": "select `permission_id` from `permissions` inner join `permission_user` on `permissions`.`id` = `permission_user`.`permission_id` where `permission_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "view", "name": "livewire.records", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/records.blade.php", "line": 5}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1753187631.896098, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "livewire.records:5", "source": "view::livewire.records:5", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Frecords.blade.php&line=5", "ajax": false, "filename": "records.blade.php", "line": "5"}, "connection": "ilog", "start_percent": 98.127, "width_percent": 0.423}, {"sql": "select `parameter_id` from `parameters` inner join `parameter_user` on `parameters`.`id` = `parameter_user`.`parameter_id` where `parameter_user`.`user_id` = 36", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "view", "name": "livewire.records", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/livewire/records.blade.php", "line": 6}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1753187631.902363, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "livewire.records:6", "source": "view::livewire.records:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flivewire%2Frecords.blade.php&line=6", "ajax": false, "filename": "records.blade.php", "line": "6"}, "connection": "ilog", "start_percent": 98.55, "width_percent": 0.305}, {"sql": "select exists(select * from `permissions` inner join `permission_user` on `permissions`.`id` = `permission_user`.`permission_id` where `permission_user`.`user_id` = 36 and `permission_id` = 1) as `exists`", "type": "query", "params": [], "bindings": ["36", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/layouts/app.blade.php", "line": 193}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753187632.0902252, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "layouts.app:193", "source": "view::layouts.app:193", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=193", "ajax": false, "filename": "app.blade.php", "line": "193"}, "connection": "ilog", "start_percent": 98.854, "width_percent": 0.605}, {"sql": "select exists(select * from `permissions` inner join `permission_user` on `permissions`.`id` = `permission_user`.`permission_id` where `permission_user`.`user_id` = 36 and `permission_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["36", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.app", "file": "C:\\xampp\\htdocs\\i-sentiment\\resources\\views/layouts/app.blade.php", "line": 216}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\i-sentiment\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1753187632.0963771, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "layouts.app:216", "source": "view::layouts.app:216", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=216", "ajax": false, "filename": "app.blade.php", "line": "216"}, "connection": "ilog", "start_percent": 99.459, "width_percent": 0.541}]}, "models": {"data": {"App\\Models\\Organization": {"value": 107, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Evaluation": {"value": 94, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FEvaluation.php&line=1", "ajax": false, "filename": "Evaluation.php", "line": "?"}}, "App\\Models\\Interaction": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FInteraction.php&line=1", "ajax": false, "filename": "Interaction.php", "line": "?"}}, "App\\Models\\User": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserGroup": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fi-sentiment%2Fapp%2FModels%2FUserGroup.php&line=1", "ajax": false, "filename": "UserGroup.php", "line": "?"}}}, "count": 227, "is_counter": true}, "livewire": {"data": {"records #MrJSniOeKck9YMdv9g8T": "array:4 [\n  \"data\" => array:66 [\n    \"perPage\" => 15\n    \"filterType\" => \"General\"\n    \"searchCalls\" => \"\"\n    \"filterApplied\" => false\n    \"filter_time_name\" => null\n    \"filter_time_days\" => null\n    \"custom_date_from\" => null\n    \"custom_date_to\" => null\n    \"filter_callId\" => null\n    \"filter_agentId\" => null\n    \"filter_duration\" => null\n    \"filter_callType\" => null\n    \"filter_calledId\" => null\n    \"filter_callerId\" => null\n    \"filter_callEnder\" => null\n    \"filter_holdDuration\" => null\n    \"filter_holdCount\" => null\n    \"filter_isAssigned\" => null\n    \"filter_evaluationScore\" => null\n    \"filter_isEvaluated\" => null\n    \"filter_qaFlaggedInteractions\" => null\n    \"filter_flagDate\" => null\n    \"filter_agentName\" => null\n    \"filter_account\" => null\n    \"filter_skillGroup\" => null\n    \"filter_callImportance\" => null\n    \"filter_pauseCount\" => null\n    \"filter_language\" => null\n    \"filter_uniqueId\" => null\n    \"filter_played\" => null\n    \"byFlagType\" => \"All\"\n    \"filter_duration_sign\" => \"=\"\n    \"filter_holdCount_sign\" => \"=\"\n    \"filter_holdDuration_sign\" => \"=\"\n    \"filter_evaluationScore_sign\" => \"=\"\n    \"filter_selected_languages\" => array:11 [\n      0 => \"Arabic\"\n      1 => \"English\"\n      2 => \"German\"\n      3 => \"Hebrew\"\n      4 => \"Italian\"\n      5 => \"Turkish\"\n      6 => \"Kurdish\"\n      7 => \"Spanish\"\n      8 => \"Russian\"\n      9 => \"Urdu\"\n      10 => \"French\"\n    ]\n    \"all_languages\" => array:11 [\n      0 => \"Arabic\"\n      1 => \"English\"\n      2 => \"German\"\n      3 => \"Hebrew\"\n      4 => \"Italian\"\n      5 => \"Turkish\"\n      6 => \"Kurdish\"\n      7 => \"Spanish\"\n      8 => \"Russian\"\n      9 => \"Urdu\"\n      10 => \"French\"\n    ]\n    \"filter_evaluationForm\" => null\n    \"callId\" => null\n    \"evaluationReport\" => []\n    \"selected_interaction_id\" => null\n    \"callID\" => null\n    \"selectedInteractionComments\" => null\n    \"selectedInteractionToAddComment\" => null\n    \"selectedCommentToDelete\" => null\n    \"added_comment\" => null\n    \"recordID\" => null\n    \"add_comment_field\" => false\n    \"viewCommentsModal\" => false\n    \"selectedInteractionId\" => null\n    \"viewEvaluationModal\" => false\n    \"viewEvaluationFormModal\" => false\n    \"viewListenersModal\" => false\n    \"loading\" => false\n    \"responseData\" => null\n    \"evaluationID\" => 0\n    \"selectedTime\" => null\n    \"selectedAccount\" => null\n    \"selectedCallId\" => null\n    \"selectedAgent\" => null\n    \"url\" => null\n    \"hideZero\" => true\n    \"showTranscribed\" => false\n    \"callListeners\" => []\n    \"currentPage\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"records\"\n  \"component\" => \"App\\Livewire\\Records\"\n  \"id\" => \"MrJSniOeKck9YMdv9g8T\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/recordings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "36", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/9f73949a-b0c2-411e-9542-d6537750f1ba\" target=\"_blank\">View in Telescope</a>", "path_info": "/recordings", "status_code": "<pre class=sf-dump id=sf-dump-57671989 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-57671989\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1964450196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964450196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-157406524 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-157406524\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-832193205 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1253 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndORVZmZm1CQWx1OVVtcUxhcW9Wbnc9PSIsInZhbHVlIjoiNEJpVnZuTkZPUmgxdk1ndVNLeXltN3hiL3lRMkpmckV4Y3ZFczI4Qm0xaXM0eldYak1IU1NYQitGUlhyVUFsODNvTWVWN3NxV2p2bEpkbEc0R1NVWDF2Q0d5dTgrQTgyQWZTNFBMQTFqY25YU1F2UldyNDhxaUNFZ1FiSSthUWpaakExbUJPM0h2cDlyMHI3YU1BaC82d09tYlRzS29EbFpwRTNYVU5GbGY0clN1bTFBTlQzekk0Wi96RUR5aUdwZWtIRFdGaENSYjBDQjlxNnlNOFdKeFg5dXZWQ0twbGNSMkRGcVFGREJ6QT0iLCJtYWMiOiJjYWYxMjVmN2NiZGM3YzJlMDVmYWQ1NjI3YzdjZGQxZmE0MmYyMTVmZDQ5MGY2NTA2NDU3ODc1NThiNTAwYWFmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkszWE1HVDYwMkVOeFVReFZDN0dyZ2c9PSIsInZhbHVlIjoiN01VeGhPQW94dFpDZVN4czVOTHJwQy95emUweU5pTlJqMUliMGVVQitlR3l5dm0rU0tQRzBpRFkzY00yWlhCMGRkNCs3ZHVQWWpWK2M2dHNZcUVDRjAzbjZIZXRrdGh3Y0VHa0RTY1ZQWnE5VEdaY3E5SFV6bjlwVjljeDN6Rk0iLCJtYWMiOiJiZjQ4YWQwZWVlMTcxNjJhZWZkMDFkYmQ0MzY0YmZhZDc0ZWZkM2Y4NGMwNDU0ZDA4MjY3NmMzNDIyNTZhMzk0IiwidGFnIjoiIn0%3D; i_log_session=eyJpdiI6InF1VW80aUhxbEFjekNYMFpCbmRYT1E9PSIsInZhbHVlIjoiQ2RFOEgzc3VvWUV2M3ROL2FiTjg1V1pQOXdPelpMOGxPK1owVXlnbk1STWg5Sm9EMDNPbTNBY0ZpUE03b2U0bDIxNk9ON05wVTdoNm1JU2dlRVNqN1RRZSthY0QwYkJzYXd5a3BTUXZ2L0VuS1pwUmlSU0puRUVsUGRuUHRvWDciLCJtYWMiOiJjZGYyMTdmMzQ2ZjA2MDI1MzQ1MTllYjI2ZDVlNzMzNmNmMmFjMzUxN2MyMDMyMTY2ZDgwODVlNTA5NGVkNThmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832193205\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1533863727 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"124 characters\">36|5ib4AQhtLHzATEGHqeW0FyHXea5XYBhkXcIWMbiaY723twUGMepP4FnyoU3L|$2y$12$sKmCpsW.aT14AEFPyOgHeO2lhV7BRWjK7d1ywUnOncv84uKprV4di</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>i_log_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3Bna7bWbMH5plZLa1koljiLCJBixIU0hg7L8yoS8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533863727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-764798174 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 12:33:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"429 characters\">XSRF-TOKEN=eyJpdiI6IkZMUHNyTmw5NG5CNUFFUTJHaDM3cEE9PSIsInZhbHVlIjoiWUE0SGpBY1g3V2VCT3JsZTY0Mno4UVVYOGdjUGEyYklndU1Sdnp1YllybllkejZIMHZSbjl4Tkt4YTRiZnFINDl4bVpnbTdJVWVZdi9sb2FZVlBMa1JpMU5hYnRzRUNkVDkvYjNZMy9iV1ExSWJsWTQ4QW5KQ0gxa01xL2hPQ0MiLCJtYWMiOiI4MGRlMjU1MzdmZjVjZmNmZTAyOGQ0ZjA3MDNhYTljOGQ0NGJhNGM0YzY3N2I5YTNkODYwNmFiNzgzNGNhN2NiIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:33:52 GMT; Max-Age=21600; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">i_log_session=eyJpdiI6ImxibUNNREJ0ZFhTa1lUb0h5ZExxMnc9PSIsInZhbHVlIjoiUVhqcHRGUWJFTzRyYjhQMmRRZGNPc1FVdUFMTFVseVJtdlFlUE9NcXNaWFl6Q0hnbmNZQ2ZqR2VpazhVNW0yUlZXQnZyanVHRE1SNzczMFJIbzlmOGhlMUpnamZIN2o4d2dqbGppT0JRSGtHUXI3eXVPRUN0N0RRajNmMW9MNW0iLCJtYWMiOiIxMDNmNGUyMDIyMzZmNzkyZTNhYTYwOGI2ZWRkNmZlMDY3NGE0NmViYmIzNGRlMjdmMTM4M2I0ZTllMDlmZWIxIiwidGFnIjoiIn0%3D; expires=Tue, 22 Jul 2025 18:33:52 GMT; Max-Age=21600; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZMUHNyTmw5NG5CNUFFUTJHaDM3cEE9PSIsInZhbHVlIjoiWUE0SGpBY1g3V2VCT3JsZTY0Mno4UVVYOGdjUGEyYklndU1Sdnp1YllybllkejZIMHZSbjl4Tkt4YTRiZnFINDl4bVpnbTdJVWVZdi9sb2FZVlBMa1JpMU5hYnRzRUNkVDkvYjNZMy9iV1ExSWJsWTQ4QW5KQ0gxa01xL2hPQ0MiLCJtYWMiOiI4MGRlMjU1MzdmZjVjZmNmZTAyOGQ0ZjA3MDNhYTljOGQ0NGJhNGM0YzY3N2I5YTNkODYwNmFiNzgzNGNhN2NiIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:33:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">i_log_session=eyJpdiI6ImxibUNNREJ0ZFhTa1lUb0h5ZExxMnc9PSIsInZhbHVlIjoiUVhqcHRGUWJFTzRyYjhQMmRRZGNPc1FVdUFMTFVseVJtdlFlUE9NcXNaWFl6Q0hnbmNZQ2ZqR2VpazhVNW0yUlZXQnZyanVHRE1SNzczMFJIbzlmOGhlMUpnamZIN2o4d2dqbGppT0JRSGtHUXI3eXVPRUN0N0RRajNmMW9MNW0iLCJtYWMiOiIxMDNmNGUyMDIyMzZmNzkyZTNhYTYwOGI2ZWRkNmZlMDY3NGE0NmViYmIzNGRlMjdmMTM4M2I0ZTllMDlmZWIxIiwidGFnIjoiIn0%3D; expires=Tue, 22-Jul-2025 18:33:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764798174\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12925475 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KDojk1GRVc66E0AEkFKeRRrnDMCwPqTkTdoGNSvS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/recordings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>36</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12925475\", {\"maxDepth\":0})</script>\n"}}