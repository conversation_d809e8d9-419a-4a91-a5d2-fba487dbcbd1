<div class="container-fluid mt-3 px-4">

    {{-- header row  --}}
    <div class="row mx-3 ps-5">
        <div class="col-12 mb-5">
            <div class="card bg-white shadow py-2 px-3">
                <div class="card-body p-4">
                    <h5>
                        <b>
                            Evaluation Forms
                        </b>
                    </h5>
                    {{-- <i class="fa-regular fa-file-excel fa-2xl float-end" style="cursor: pointer"></i> --}}
                    <h6 class="text-muted">
                        Create, View, and Manage Evaluation Forms
                        <i class="fa-solid fa-medal fa-2xl float-end" style="color: #00a34e"></i>
                    </h6>
                </div>
            </div>
        </div>
    </div>


    {{-- bottom row  --}}
    <div class="row mx-3 d-flex ps-5">
        {{-- filters card  --}}
        {{-- <div class="col-12 col-md-12 col-lg-3 mb-5 mb-lg-0 d-flex flex-column">
            <div class="card rounded-3 bg-white shadow" style="padding: 0.7rem">
                <div class="card-body py-4">
                    <h5 class="fw-bold">Filters</h5>
                    <hr>
                    <label for="account" class="mb-2 fw-bold">Account</label>
                    <select class="form-select form-select-sm mb-3" wire:model="selectedAccount">
                        <option selected value="all">All</option>
                        <option value="gen01">gen01</option>
                    </select>

                    <label for="date" class="mb-2 fw-bold">Time</label>
                    <select wire:model="selectedTime"class="form-select form-select-sm mb-3">
                        <option selected value="all">All Time</option>
                        <option value="1">Last 24 Hours</option>
                        <option value="7">Last 7 Days</option>
                        <option value="40">Last 30 Days</option>
                        <option value="60">Last 60 Days</option>
                    </select>

                    <label for="call id" class="mb-2 fw-bold">Call ID</label>
                    <div class="input-group input-group-sm mb-3">
                        <input type="text" name="call-id" id="call-id" class="form-control" placeholder="Call #" wire:model="selectedCallId">
                    </div>

                    <label for="Agent name" class="mb-2 fw-bold">Agent</label>
                    <div class="input-group input-group-sm mb-4">
                        <input type="text" name="agent" id="agent" class="form-control" placeholder="Agent Name" wire:model="selectedAgent">
                    </div>

                    <button class="btn btn-md btn-success mb-2" style="background-color: #00a34e" id="apply" wire:click="filter">Apply</button>
                    <button class="btn btn-md btn-outline-success ms-1 mb-2" id="clear" wire:click="clear">Clear</button>
                </div>
            </div>
        </div> --}}

        <div class="col-12 col-md-12 col-lg-12" style="letter-spacing: 1px;" wire:key="sss">
            <div class="row d-flex justify-content-end mb-2">
                <div class="col-3 d-flex justify-content-end">
                    <button class="btn btn-md rounded-2" style="background-color: #00a34e; color:white" data-bs-toggle="modal" data-bs-target="#add-group"><i class="fa-solid fa-plus"></i><span> Add Group</span></button>
                </div>
            </div>

            {{-- table  --}}
            <div class="row px-1">
                <div class="table-responsive px-2">
                    <table class="table shadow-sm border-secondary table-hover table-bordered overflow-auto mb-0 " id="table" style="width:100%">
                        <thead id="thead" class="text-muted" style="font-size: 0.7rem">
                            <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                <th scope="col" class="text-center align-middle">#</th>
                                <th scope="col" class="text-center align-middle">NAME</th>
                                <th scope="col" class="text-center align-middle">ACDID</th>
                                <th scope="col" class="text-center align-middle">TYPE</th>
                                <th colspan="col" scope="col" class=" text-center align-middle">CREATED AT</th>
                                <th colspan="col" scope="col" class=" text-center align-middle">ACTIONS</th>
                            </tr>
                        </thead>
                        <tbody class="" style="font-size:0.8rem" id="tbody">

                            @forelse($users as $user)
                                <tr class="align-middle">
                                    <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                    <td class="text-muted text-center py-3 align-middle" style="min-width: 20rem"> {{ $user->group }} </td>
                                    <td class="text-muted text-center py-3 align-middle" style="width: 20rem"> {{ fake()->numberBetween(1, 1500) }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle" style="width:15rem"> {{ fake()->randomElement(['Inbound', 'Outbound']) }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Carbon::parse($user->created_at)->format('Y-m-d h:i A') }}</td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        @if (in_array(Auth::user()->role, [1, 3]))
                                            <i class="fa-solid fa-pen-to-square fa-xl me-1" aria-hidden="true" style="cursor: pointer;color:rgb(243, 170, 35)" data-bs-toggle="modal" data-bs-target="#edit-group" wire:click="selectUser('{{ $user->id }}', '{{ $user->full_name }}', '{{ $user->role }}')" title="Edit User"></i>
                                            <i class="fa fa-trash fa-xl text-danger" aria-hidden="true" style="cursor: pointer" wire:click="showDeleteAlert('{{ $user->id }}')" title="Delete User"></i>
                                        @else
                                            -
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="18" class="text-muted text-center bg-white"> No users found</td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-end mt-2">
                    {{ $users->links(data: ['scrollTo' => false]) }}
                </div>
            </div>

        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="edit-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="edit-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <h5 class="modal-title" id="staticBackdropLabel">Edit Skill Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Type:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Language:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">ACDID:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Description:</label>
                            <textarea type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName"></textarea>
                        </div>

                        {{-- <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Organization:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>

                        @php
                            $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                        @endphp
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Supervisors:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="searchSupervisors" autocomplete="off">
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Agents:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName">
                            @php
                                $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                            @endphp
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div> --}}




                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-primary" style="background-color: #00a34e" wire:click="editUser">Apply</button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" wire:click="clear" id="close">Close</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Add Group Modal -->
    <div class="modal fade" id="add-group" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: #00a34e">
                    <h5 class="modal-title" id="staticBackdropLabel">Add Skill Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="clear"></button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">
                    <form>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Type:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Language:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>


                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Name:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">ACDID:</label>
                            <input type="text" class="form-control" id="recipient-name" wire:model="selectedUserName">
                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Description:</label>
                            <textarea type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName"></textarea>
                        </div>

                        {{-- <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Organization:</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary bg-transparent dropdown-toggle w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                    @if ($role)
                                        @if ($role == 1)
                                            Admin
                                        @elseif ($role == 2)
                                            Supervisor
                                        @elseif ($role == 3)
                                            IT
                                        @else
                                            Agent
                                        @endif
                                    @else
                                        {{ $selectedUserRole == 1 ? 'Admin' : 'Supervisor' }}
                                    @endif
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Admin</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('2')">Supervisor</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('3')">IT</a></li>
                                    <hr class="m-0">
                                    <li><a class="dropdown-item" href="#" wire:click="selectRole('4')">Agent</a></li>
                                </ul>
                            </div>
                        </div>

                        @php
                            $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                        @endphp
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Supervisors:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="searchSupervisors" autocomplete="off">
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div>

                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Agents:</label>
                            <input type="text" class="form-control bg-white" id="recipient-name" wire:model="selectedUserName">
                            @php
                                $supervisors = ['Mohammad Ali', 'Abdulla Ahmad', 'Hasan', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad', 'Hasan Mohammad'];
                            @endphp
                            <div class="card rounded-0 mt-1">
                                <div class="card-body bg-white p-1 ">
                                    @foreach ($supervisors as $item)
                                        <div class="badge d-inline-block p-2 m-1" style="background-color: #00a34e; width:fit-content">
                                            <span>{!! $item !!}</span>
                                            <i class="fa-regular fa-lg fa-circle-xmark" style="cursor: pointer"></i>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        </div> --}}




                    </form>
                </div>


                <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-primary" style="background-color: #00a34e" wire:click="editUser">Apply</button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" wire:click="clear" id="close">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>
