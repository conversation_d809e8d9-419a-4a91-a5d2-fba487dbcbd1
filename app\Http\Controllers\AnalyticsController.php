<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Interaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class AnalyticsController extends Controller
{
    // public function index()
    // {
    //     // $total_interactions = Interaction::whereBetween('arrival_time', [Carbon::today(), Carbon::today()])->get();
    //     $total_interactions = Interaction::whereNotNull('call_duration')->where('call_duration', '!=', '00:00:00')->whereDate('arrival_time', Carbon::today())->get();

    //     $inboundCount = $total_interactions->where('call_type', 'Inbound')->count();
    //     $outboundCount = $total_interactions->where('call_type', 'Outbound')->count();
    //     // $outboundCount =90;

    //     // bar chart data for all interactions
    //     $barChartData = [];


    //     // Get the current date
    //     $currentDate = Carbon::now()->toDateString();
    //     // $currentDate = Carbon::createFromDate(2024, 2, 4)->toDateString();


    //     // Loop through each hour of the day (from 0 to 23)
    //     for ($hour = 0; $hour < 24; $hour++) {
    //         // Calculate the start and end of the hour range using Carbon
    //         $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $currentDate . ' ' . sprintf("%02d:00:00", $hour));
    //         $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $currentDate . ' ' . sprintf("%02d:59:59", $hour));


    //         // Query the interactions table for the count of interactions within this hour range
    //         $interactionsCount = Interaction::where('arrival_time', '>=', $startTime)
    //             ->where('arrival_time', '<=', $endTime)
    //             ->count();

    //         // Add the count to the bar chart data array
    //         $barChartData[] = $interactionsCount;
    //     }

    //     $barChartData = json_encode($barChartData);

    //     return view('analytics', compact('inboundCount', 'outboundCount', 'barChartData'));
    // }


    public function index()
    {
        // Cache inbound and outbound counts for today
        $totalCounts = Cache::remember('interaction_counts_' . Carbon::today()->toDateString(), 600, function () {
            return Interaction::select(
                DB::raw("COUNT(CASE WHEN call_type = 'Inbound' THEN 1 END) as inbound_count"),
                DB::raw("COUNT(CASE WHEN call_type = 'Outbound' THEN 1 END) as outbound_count")
            )
                ->whereNotNull('call_duration')
                ->where('call_duration', '!=', '00:00:00')
                ->whereDate('arrival_time', Carbon::today())
                ->first();
        });

        $inboundCount = $totalCounts->inbound_count;
        $outboundCount = $totalCounts->outbound_count;

        // Cache hourly interaction data for today
        $barChartData = Cache::remember('hourly_interactions_' . Carbon::today()->toDateString(), 600, function () {
            $hourlyInteractions = Interaction::select(
                DB::raw('HOUR(arrival_time) as hour'),
                DB::raw('COUNT(*) as count')
            )
                ->whereDate('arrival_time', Carbon::today())
                ->whereNotNull('call_duration')
                ->where('call_duration', '!=', '00:00:00')
                ->groupBy(DB::raw('HOUR(arrival_time)'))
                ->pluck('count', 'hour');

            $barChartData = array_fill(0, 24, 0);
            foreach ($hourlyInteractions as $hour => $count) {
                $barChartData[$hour] = $count;
            }

            return $barChartData;
        });

        $barChartData = json_encode($barChartData);

        return view('analytics', compact('inboundCount', 'outboundCount', 'barChartData'));
    }
    public function indexNew()
    {
        return view('analytics-new');
    }
    public function indexQuality()
    {
        return view('analytics-quality');
    }
}
