<?php

namespace App\Livewire\Admin\Configuration;

use App\Models\ScriptVariable;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Variables extends Component
{

    use WithPagination,LivewireAlert;

    public $variable;


    public $modalId;
    public $modalIdShow;

    public function getListeners()
    {
        return [
            'confirmed','modelFormReset',
            'getEncryption',
        ];
    }


    public function BadWord()
    {
        try
        {
            return ScriptVariable::query()->get();
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
        }
    }

    public function modelData()
    {
        return [
            'variable'          => $this->variable,


        ];
    }

    public function rules()
    {
        return [
            'variable' =>       ['required'],



        ];
    }

    public function update(){


        $this->validate();

        try {
            ScriptVariable::find($this->modalId)->update($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }
    public function store(){


        $this->validate();

        try {
            ScriptVariable::create($this->modelData());


            $this->alert('success', 'Successfully Updated!',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);



            $this->modelFormReset();
            $this->dispatch('close-modal');
        }
        catch (\Exception $e){
            $this->alert('error', 'Something was wrong !',[
                'timerProgressBar' => true,
                'timer' => '6000',
            ]);
            $this->modelFormReset();
            $this->dispatch('close-modal');
        }

    }

    public function closeModal()
    {
        $this->modelFormReset();
    }

    public function showModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
        $this->modalIdShow = "on";
    }

    public function showDeleteAlert($id)
    {
        $this->modalId = $id;
        $this->alert('warning', 'Are you sure you want to delete this group?', [
            'position' => 'center',
            'timer' => '10000',
            'toast' => false,
            'customClass' => ['confirmButton' => 'm-2 btn btn-sm btn-warning', 'cancelButton' => 'ml-2 btn btn-sm btn-secondary'],
            'buttonsStyling' => false,
            'allowOutsideClick' => false,
            'showConfirmButton' => true,
            'onConfirmed' => 'confirmed',
            'onDismissed' => 'modelFormReset',
            'onCancelled' => 'modelFormReset',
            'onDenied' => 'modelFormReset',
            'onProgressFinished' => 'modelFormReset',
            'showCancelButton' => true,
            'cancelButtonText' => 'Cancel',
            'confirmButtonText' => 'Confirm',
            'timerProgressBar' => true,
        ]);
    }

    public function confirmed()
    {
        if (ScriptVariable::find($this->modalId)->delete()) {
            $this->alert("success", "Group Deleted Successfully");
        }
    }

    public function showUpdateModal($id)
    {
        $this->modalId =$id;
        $this->modelLoadData($this->modalId);
    }

    public function modelLoadData()
    {
        $encryption = ScriptVariable::find($this->modalId);

        $this->variable = $encryption->variable;


    }

    public function modelFormReset()
    {
        $this->variable = "";


        $this->modalId=null;
        $this->modalIdShow=null;

        $this->resetValidation();
    }


    public function changePage($data)
    {
        $this->dispatch('changePage', $data);

    }

    public function render()
    {
        return view('livewire.admin.configuration.variables', ['transcriptionBadWords' => $this->BadWord()]);
    }
}
