<div class="container-fluid mt-3 px-4">


    {{-- header row  --}}
    <div class="header">
        <div class="row justify-content-end mx-3 ps-lg-5">
            <div class="col-auto mt-3 mt-sm-0 pe-0">
                <button
                    data-bs-toggle="modal"
                    data-bs-target="#add-flag"
                    class="btn btn-success d-flex flex-row-reverse justify-content-between align-items-center w-100 rounded-3"
                    style="min-width: 110px; height: 40px; border-color: #01a44f; background: #01a44f;">
                    <i class="fa-solid fa-plus text-white me-2" style="font-size: 20px;"></i>
                    <span class="me-4" style="font-size: 14px;">Add QA Flag</span>
                </button>
            </div>
        </div>
    </div>
    {{-- bottom row  --}}
    <div class="parent-sections mx-3 ps-5">

        <div class="section-one">
                <div class="div-table rounded-2 shadow-sm mb-3" style="height: fit-content; max-height:90vh">

                    <table class="table table-hover table-striped" id="table" style="margin-bottom: 0px;border-radius: 0px;">
                        <thead id="thead" class="thead text-muted" style="font-size: 0.7rem;height: 1rem !important;">
                            <tr class="text-muted bg-white" style="vertical-align:middle; background-color:white !important">
                                <th scope="col" class="text-center align-middle">#</th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('name')">
                                    Flag Name
                                    @if ($sortBy !== 'name')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('start_date')">
                                    Start Date
                                    @if ($sortBy !== 'start_date')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>

                                <th scope="col" class="text-center align-middle" style="cursor: pointer" wire:click="setSortBy('end_date')">
                                    End Date
                                    @if ($sortBy !== 'end_date')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle" wire:click="setSortBy('time_interval')">
                                    Type
                                    @if ($sortBy !== 'time_interval')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('organization_id')">
                                    Organization
                                    @if ($sortBy !== 'organization_id')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                {{-- <th scope="col" style="cursor:pointer" class="text-center align-middle text-nowrap" wire:click="setSortBy('user_group_id')">
                                    LEVEL OF DISTRIBUTION
                                    @if ($sortBy !== 'user_group_id')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th> --}}
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('interactions_number')">
                                    No. of Interactions
                                    @if ($sortBy !== 'interactions_number')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('active')">
                                    Active
                                    @if ($sortBy !== 'active')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th scope="col" class="text-center align-middle" style="cursor:pointer" wire:click="setSortBy('created_at')">
                                    Created At
                                    @if ($sortBy !== 'created_at')
                                        <i class="fa-solid fa-sort fa-lg" aria-hidden="true" style="color:white"></i>
                                    @elseif ($sortDir === 'ASC')
                                        <i class="fa-solid fa-sort-up fa-lg" aria-hidden="true" style="color:white"></i>
                                    @else
                                        <i class="fa-solid fa-sort-down fa-lg" aria-hidden="true" style="color:white"></i>
                                    @endif
                                </th>
                                <th colspan="col" scope="col" class=" text-center align-middle">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="" style="font-size:0.8rem" id="tbody">

                            @forelse($flags as $flag)
                                <tr class="align-middle">
                                    <td class="text-muted text-center py-3 align-middle"> {{ $loop->index + 1 }}</td>
                                    <td class="text-muted text-center py-3 align-middle"> {{ Str::title($flag->name) }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">{{ $flag->id != 1 ? $flag->start_date : 'Permanent' }}</td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $flag->id != 1 ? $flag->end_date : 'Permanent' }} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        {{ $flag->id != 1 ? ($flag->time_interval == 'Day' ? 'Daily' : $flag->time_interval . 'ly') : '-' }}
                                    </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">{{ $flag->id != 1 ? $flag->organization->name : '-' }}</td>
                                    {{-- <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ $flag->distribution_level }} </td> --}}
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">{{ $flag->id != 1 ? $flag->interactions_number : '-' }} </td>
                                    <td class='text-center py-3 text-nowrap align-middle'> {!! $flag->active == 1 ? '<i class="fa-regular fa-circle-check fa-xl" style="color:#00a34e"></i>' : '<i class="fa-regular fa-circle-xmark fa-xl" style="color:tomato;"></i>' !!} </td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle"> {{ Carbon::parse($flag->created_at)->format('Y-m-d h:i A') }}</td>
                                    <td class="text-muted text-center py-3 text-nowrap align-middle">
                                        @if (in_array(Auth::user()->role, [1, 2]) && $flag->id != 1)
                                            <i class="fa-solid fa-pen-to-square fa-xl me-1" aria-hidden="true" style="cursor: pointer;color:rgb(235, 179, 76)" data-bs-toggle="modal" data-bs-target="#edit-flag" wire:click="selectFlag('{{ $flag->id }}')" title="Edit Flag"></i>
                                            <i class="fa fa-trash fa-xl" aria-hidden="true" style="cursor: pointer; color:tomato" wire:click="showDeleteAlert('{{ $flag->id }}')" title="Delete Flag"></i>
                                        @else
                                            -
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="18" class="text-muted text-center bg-white"> No Flags found</td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
            </div>

            <div class="d-flex justify-content-end mt-2 pe-0">
                {{ $flags->links(data: ['scrollTo' => false]) }}
            </div>



        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="edit-flag" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-color: white;">
                <div class="modal-header" style="border: none;background-color: white;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa-solid fa-filter" style="font-size: 30px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Edit QA Flag</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close" wire:click="clear">
                        <i class="fas fa-times " style="font-size: 24px;"></i>
                    </button>
                </div>
                {{-- <ul class="nav nav-tabs" style="background-color: white;">
                    <li class="nav-item">
                        <a class="nav-link  tab-button @if ($activeTab === 'general') active @endif" wire:click="$set('activeTab', 'general')" style="cursor: pointer">General</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link  tab-button @if ($activeTab === 'interactions') active @endif" wire:click="$set('activeTab', 'interactions')" style="cursor: pointer">Interactions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link  tab-button @if ($activeTab === 'filters') active @endif" wire:click="$set('activeTab', 'filters')" style="cursor: pointer">Filters</a>
                    </li>
                </ul> --}}
                <div class="d-flex gap-3 ms-3 mb-2" style="border: none; padding: 0;background-color: white !important;">
                    <button class="btn btn-outline-success @if ($activeTab === 'general') active @endif tab-button" wire:click="$set('activeTab', 'general')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                        General
                    </button>
                    <button class="btn btn-outline-success @if ($activeTab === 'interactions') active @endif tab-button" wire:click="$set('activeTab', 'interactions')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                        Interactions
                    </button>
                    <button class="btn btn-outline-success @if ($activeTab === 'filters') active @endif tab-button" wire:click="$set('activeTab', 'filters')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                        Filters
                    </button>
                </div>

                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">

                    <div class="tab-content">
                        {{-- GENERAL TAB  --}}
                        <div class="tab-pane @if ($activeTab === 'general') show active @endif">

                            <form wire:submit.prevent>
                                {{-- <div class="form-check form-switch mb-3 mt-2">
                                    <input class="form-check-input custom-switch-input" type="checkbox" id="flexSwitchCheckDefault" wire:model.live="selected_enabled">
                                    <label class="form-check-label" for="flexSwitchCheckDefault">Enabled</label>
                                </div> --}}
                                <div class="d-flex align-items-start gap-2 justify-content-start align-items-start ">
                                    <div class="position-relative" style="display: flex;flex-direction: row;">
                                        <input type="checkbox" class="d-none" id="customSwitch" wire:click="statusUpdateModal()" />
                                        Enabled:
                                        <label for="customSwitch" class="ms-2 custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selected_enabled ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">

                                            <div class="switch-handle position-absolute"
                                                style="
                                                    width: 18px;
                                                    height: 18px;
                                                    background-color: {{ $selected_enabled ? '#ffffff' : '#FF5E60' }};
                                                    border-radius: 50%;
                                                    top: 3px;
                                                    left: {{ $selected_enabled ? '22px' : '3px' }};
                                                    transition: left 0.3s, background-color 0.3s;">
                                                @if ($selected_enabled)
                                                    <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                        <path
                                                            d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                            fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                    </svg>
                                                @else
                                                    <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                        <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                    </svg>
                                                @endif
                                            </div>
                                        </label>

                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="recipient-name" class="col-form-label">Flag Name:</label>
                                    <input type="text" class="form-control" id="recipient-name" wire:model="selected_flagName" placeholder="Enter Flag Name">
                                    @error('selected_flagName')
                                        <span class="text-danger fs-6">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="row gx-2">
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">Start Date:</label>
                                        <input type="date" class="form-control" id="recipient-name" wire:model="selected_startDate" @if (\Carbon\Carbon::parse($selected_startDate) <= \Carbon\Carbon::now()->startOfDay())  @endif @readonly(true)>
                                        @error('selected_startDate')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">End Date:</label>
                                        <input type="date" class="form-control" id="recipient-name" wire:model="selected_endDate" @readonly(true)>
                                        @error('selected_endDate')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="form row gx-2">
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">No. of Interactions:</label>
                                        <input type="number" class="form-control" id="recipient-name" wire:model="selected_interactionsNumber" placeholder="0">
                                        @error('selected_interactionsNumber')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">Per:</label>
                                        {{-- <input type="number" class="form-control" id="recipient-name" wire:model="selected_per" placeholder="0"> --}}
                                        <input type="number" class="form-control" id="recipient-name" value="1" disabled placeholder="0">
                                        @error('selected_per')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-12">
                                        <label for="recipient-name" class="col-form-label">Interval:</label>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                {{ $selected_interval ?? '--' }}
                                            </button>
                                            <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                <li><a class="dropdown-item" wire:click="$set('selected_interval', 'Day')">Day</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="$set('selected_interval', 'Week')">Week</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="$set('selected_interval', 'Month')">Month</a></li>
                                            </ul>
                                            @error('selected_interval')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="recipient-name" class="col-form-label">Level of Distribution:</label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            {{ $selected_distributionLevel }}
                                        </button>
                                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                            {{-- <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Skill Groups</a></li> --}}
                                            {{-- <hr class="m-0"> --}}
                                            <li><a class="dropdown-item" href="#" wire:click="$set('distribution_level', 'Agent')">Agent</a></li>
                                        </ul>
                                        @error('selected_distributionLevel')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                {{-- <div class="mb-3">
                                    <label for="recipient-name" class="col-form-label">Forms Filtration:</label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            {{ $forms }}
                                        </button>
                                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                            <li><a class="dropdown-item" href="#" wire:click="$set('forms', 'All Forms')">All Forms</a></li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" href="#" wire:click="$set('forms', 'Specific Forms(s)')">Specific Form(s)</a></li>
                                        </ul>
                                    </div>
                                    @error('selected_evalForms')
                                        <span class="text-danger fs-6">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="mb-4 @if ($forms == 'Specific Forms(s)') d-block @else d-none @endif">
                                    <label for="recipient-name" class="col-form-label">Forms:</label>
                                    @forelse ($selected_evalForms as $index => $item)
                                        <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                            {{ $item }}
                                            <button class="btn-close btn-close-white" wire:click="removeForm_edit({{ $index }})"></button>
                                        </span>
                                    @empty
                                    @endforelse


                                    <div class="input-group relative">
                                        <span id="searchIcon" class="input-group-text">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                        </span>
                                        <input type="text" class="form-control" wire:model.live.debounce.300ms="searchForms" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                        @if ($searchForms != '')
                                            <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                @forelse ($eval_forms as $form)
                                                    <li class="list-group-item" wire:click="selectForms_edit('{{ $form->id }}', '{{ $form->evaluation_name }}')" style="cursor: pointer">{{ $form->evaluation_name }}</li>
                                                @empty
                                                    <li class="list-group-item text-muted" style="cursor: pointer">No Forms Found</li>
                                                @endforelse
                                            </ul>
                                        @endif
                                    </div>

                                </div> --}}

                                {{-- <div class="form-check form-switch mb-3">
                                    <input class="form-check-input custom-switch-input" type="checkbox" id="flexSwitchCheckDefault">
                                    <label class="form-check-label" for="flexSwitchCheckDefault">Enable Auto Assignment</label>
                                </div> --}}
                            </form>
                        </div>


                        {{-- Interactions tab  --}}
                        <div class="tab-pane @if ($activeTab === 'interactions') show active @endif">
                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Interaction Days:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $interaction_days_field_name ?? '--' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" onclick="event.stopPropagation()" wire:ignore.self aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li wire:click><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(1)" @if (in_array(1, $selected_interactionDays)) checked @endif> Saturday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(2)" @if (in_array(2, $selected_interactionDays)) checked @endif> Sunday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(3)" @if (in_array(3, $selected_interactionDays)) checked @endif> Monday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(4)" @if (in_array(4, $selected_interactionDays)) checked @endif> Tuesday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(5)" @if (in_array(5, $selected_interactionDays)) checked @endif> Wednesday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(6)" @if (in_array(6, $selected_interactionDays)) checked @endif> Thursday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days_edit(7)" @if (in_array(7, $selected_interactionDays)) checked @endif> Friday</label></li>
                                    </ul>
                                    @error('selected_interactionDays')
                                        <span class="text-danger fs-6">{{ $message }}</span>
                                    @enderror

                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Interactions Time:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $interaction_time ?? '--' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" wire:click="$set('interaction_time','All Time')">All Time</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('interaction_time', 'Specific Time')">Specific Time</a></li>
                                    </ul>
                                </div>

                                @if ($interaction_time == 'Specific Time')
                                    <div class="row gx-2 mt-2">
                                        <div class="mb-3 col-md-6">
                                            <label for="recipient-name" class="col-form-label">From:</label>
                                            <input type="time" class="form-control" id="recipient-name" wire:model="selected_interactionTimeFrom">
                                            @error('selected_interactionDurationFrom')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                        <div class="mb-3 col-md-6">
                                            <label for="recipient-name" class="col-form-label">To:</label>
                                            <input type="time" class="form-control" id="recipient-name" wire:model="selected_interactionTimeTo">
                                            @error('selected_interactionDurationTo')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="form row gx-2">
                                <div class="mb-3 col-md-6">
                                    <label for="recipient-name" class="col-form-label">Duration (MM:SS)</label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            {{ $interactions_duration_condition }}
                                        </button>
                                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition', '=')">=</a></li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition', '>')">></a></li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition', '<')">
                                                    < </a>
                                            </li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition','between')">Between</a></li>
                                        </ul>
                                    </div>
                                </div>
                                @if ($interactions_duration_condition == 'between')
                                    <div class="mb-3 col-md-3">
                                        <label for="recipient-name" class="col-form-label">Starting From:</label>
                                        <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="selected_interactionDurationFrom">
                                        @error('selected_interactionDurationFrom')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-3">
                                        <label for="recipient-name" class="col-form-label">Up to:</label>
                                        <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="selected_interactionDurationTo">
                                        @error('selected_interactionDurationTo')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @else
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">Time:</label>
                                        <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="selected_interactionDurationFrom">
                                        @error('selected_interactionDurationFrom')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @endif
                            </div>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Call Type:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $call_type_add ?? 'All' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" wire:click="$set('call_type_add', 'All')">All</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('call_type_add', 'Incoming')">Incoming</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('call_type_add', 'Outgoing')">Outgoing</a></li>
                                    </ul>
                                </div>
                            </div>
                            {{-- <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Screen Capture:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $screen_capture_add ?? 'All' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" wire:click="$set('screen_capture_add', 'All')">All</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('screen_capture_add', 'Includes')">Includes</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('screen_capture_add', 'Does Not Include')">Does Not Include</a></li>
                                    </ul>
                                </div>
                            </div> --}}

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Organization:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $selected_org_edit ?? '--' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        @forelse ($organizations as $item)
                                            <li><a class="dropdown-item" wire:click="selectOrg_edit('{{ $item->id }}', '{{ $item->name }}')">{{ $item->name }}</a></li>
                                            <hr class="m-0">
                                        @empty
                                        @endforelse
                                    </ul>
                                </div>
                            </div>

                        </div>

                        {{-- filters tab  --}}
                        <div class="tab-pane @if ($activeTab === 'filters') show active @endif">
                            <form wire:submit.prevent>
                                <fieldset class="row">
                                    <legend class="col-form-label ">General</legend>
                                    <div class="@if (!$filter_agentId_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Agent ID</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_agentId_trigger">
                                    </div>

                                    @if ($filter_agentId_trigger)
                                        <div class="w-75 mt-2 mb-2">
                                            <input type="number" class="form-control w-50" id="recipient-name" wire:model="filter_agentId" placeholder="Enter ID">
                                            @error('filter_agentId')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif

                                    {{-- <div class="@if (!$filter_silentDuration_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1">Silent Duration</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_silentDuration_trigger">
                                    </div>
                                    @if ($filter_silentDuration_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-4">
                                                <label for="recipient-name" class="col-form-label">Condition</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_silentDuration_condition }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', '=')">=</a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', '>')">></a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', '<')">
                                                                < </a>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', 'between')">Between</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            @if ($filter_silentDuration_condition == 'between')
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Starting From:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_silentDuration_from">
                                                    @error('filter_silentDuration_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Up To:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_silentDuration_to">
                                                    @error('filter_silentDuration_to')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @else
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Time:</label>
                                                    <input type="text" class="form-control durationInput h-50" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_silentDuration_from">
                                                    @error('filter_silentDuration_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @endif
                                        </div>
                                    @endif --}}
                                </fieldset>

                                <hr class="m-0">

                                <fieldset class="row">
                                    <legend class="col-form-label ">Telephony</legend>
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Called ID</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    <div class="@if (!$filter_interactionEnder_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Interaction Ender</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_interactionEnder_trigger">
                                    </div>

                                    @if ($filter_interactionEnder_trigger)
                                        <div class="w-75 mt-2 mb-2">
                                            <input type="text" class="form-control w-50" id="recipient-name" wire:model="filter_interactionEnder" placeholder="Agent or Customer">
                                            @error('filter_interactionEnder')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Caller ID</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    <div class="@if (!$filter_holdDuration_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Hold Duration</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_holdDuration_trigger">
                                    </div>

                                    @if ($filter_holdDuration_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-4">
                                                <label for="recipient-name" class="col-form-label" style="color: black !important;">Condition</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_holdDuration_condition }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', '=')">=</a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', '>')">></a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', '<')">
                                                                < </a>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', 'between')">Between</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            @if ($filter_holdDuration_condition == 'between')
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label" style="color: black !important;">Starting From:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_holdDuration_from">
                                                    @error('filter_holdDuration_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label" style="color: black !important;">Up To:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_holdDuration_to">
                                                    @error('filter_holdDuration_to')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @else
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label" style="color: black !important;">Time:</label>
                                                    <input type="text" class="form-control durationInput h-50" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_holdDuration_from">
                                                    @error('filter_holdDuration_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Digits</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Extension</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Digits Count</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    <div class="@if (!$filter_holdCount_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Hold Count</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_holdCount_trigger">
                                    </div>

                                    @if ($filter_holdCount_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-4">
                                                <label for="recipient-name" class="col-form-label" style="color: black !important;">Condition</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_holdCount_condition }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', '=')">=</a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', '>')">></a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', '<')">
                                                                < </a>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', 'between')">Between</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            @if ($filter_holdCount_condition == 'between')
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label" style="color: black !important;">Min Count:</label>
                                                    <input type="number" class="form-control durationInput" maxlength="5" placeholder="0" wire:model="filter_holdCount_from">
                                                    @error('filter_holdCount_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label" style="color: black !important;">Max Count:</label>
                                                    <input type="number" class="form-control durationInput" maxlength="5" placeholder="0" wire:model="filter_holdCount_to">
                                                    @error('filter_holdCount_to')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @else
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label" style="color: black !important;">Count:</label>
                                                    <input type="number" class="form-control durationInput h-50" maxlength="5" placeholder="0" wire:model="filter_holdCount_from">
                                                    @error('filter_holdCount_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Pause Duration</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Ring</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Server Name</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Transferred From</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Transferred To</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Trunk</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                </fieldset>

                                <hr class="m-0">

                                <fieldset class="row">
                                    <legend class="col-form-label ">Users & Actions</legend>
                                    <div class="@if (!$filter_agentName_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Agent Name</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_agentName_trigger">
                                    </div>

                                    @if ($filter_agentName_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-12">
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ \App\Models\User::find($filter_agentName)->full_name ?? '--' }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        @forelse ($possible_agents_edit as $item)
                                                            <li><a class="dropdown-item" wire:click="$set('filter_agentName', '{{ $item->id }}')">{{ $item->full_name }}</a></li>
                                                            @if (!$loop->last)
                                                                <hr class="m-0">
                                                            @endif
                                                        @empty
                                                            <li><a class="dropdown-item text-muted">No Agents Found</a></li>
                                                        @endforelse
                                                    </ul>
                                                    @error('filter_agentName')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Interaction Importance</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Comments</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Custom Flag</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1">Groups</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Played Interactions</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Skill Groups</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                </fieldset>

                                <hr class="m-0">

                                <fieldset class="row">
                                    <legend class="col-form-label ">Telephony Custom Attributes</legend>
                                    {{-- <div class="@if (!$filter_language_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1">Language</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_language_trigger">
                                    </div>

                                    @if ($filter_language_trigger)
                                        <div class="w-75 mt-2 mb-2">
                                            <input type="text" class="form-control w-50" id="recipient-name" wire:model="filter_language" placeholder="Enter Language">
                                            @error('filter_language')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif --}}

                                    <div class="@if (!$filter_language_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Language</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_language_trigger">
                                    </div>


                                    @if ($filter_language_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-12">
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_language ?? '--' }}
                                                    </button>
                                                    @error('filter_language')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_language', 'Arabic')">Arabic</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('filter_language', 'English')">English</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('filter_language', 'Spanish')">Spanish</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('filter_language', 'French')">French</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('filter_language', 'German')">German</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('filter_language', 'Italian')">Italian</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                </fieldset>

                            </form>

                        </div>
                    </div>
                </div>

                {{-- <div class="modal-footer bg-white">
                    <button type="button" class="btn btn-primary" style="background-color: #00a34e" wire:click="editFlag">Apply</button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" wire:click="clear" id="closeEdit">Close</button>
                </div> --}}

                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal" aria-label="Close" wire:click="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;" id="closeEdit">Close</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="editFlag"
                        wire:loading.attr="disabled"
                        wire:target="editFlag">
                        <span wire:loading.remove wire:target="editFlag" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="editFlag" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </div>
        </div>

        {{-- <script>
            function formatDuration(input) {
                let value = input.value.replace(/\D/g, ''); // Remove non-numeric characters

                if (value.length > 4) {
                    value = value.slice(0, 4); // Limit the length to 4 characters (MMSS)
                }

                if (value.length > 2) {
                    value = value.replace(/(\d{2})(\d{2})/, '$1:$2'); // Add a colon between MM and SS
                }

                const minutes = parseInt(value.slice(0, 2), 10) || 0;
                const seconds = parseInt(value.slice(3, 5), 10) || 0;

                // Enforce maximum values
                if (minutes > 59) {
                    value = '59:00';
                }

                if (seconds > 59) {
                    value = '00:59';
                }

                input.value = value;
            }

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('focusout', handleOutsideClick);

            function handleOutsideClick(event) {
                const inputField = document.querySelector('.durationInput');

                if (!event.target.matches('.durationInput')) {
                    console.log('Clicked or tabbed outside the input field!');

                    // Check if the input value is in the format MM:SS
                    const inputValue = inputField.value.trim();
                    const isValidFormat = /^\d{2}:\d{2}$/.test(inputValue);

                    if (isValidFormat) {
                        console.log('Input format is valid (MM:SS):', inputValue);
                    } else {
                        console.log('Input format is not valid:', inputValue);
                    }
                }
            }
        </script> --}}

        <script>
            function formatDuration(input) {
                let value = input.value.replace(/\D/g, ''); // Remove non-numeric characters

                if (value.length > 4) {
                    value = value.slice(0, 4); // Limit the length to 4 characters (MMSS)
                }

                if (value.length > 2) {
                    value = value.replace(/(\d{2})(\d{2})/, '$1:$2'); // Add a colon between MM and SS
                }

                const minutes = parseInt(value.slice(0, 2), 10) || 0;
                const seconds = parseInt(value.slice(3, 5), 10) || 0;

                // Enforce maximum values
                if (minutes > 59) {
                    value = '59:00';
                }

                if (seconds > 59) {
                    value = '00:59';
                }

                input.value = value;
            }

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('focusout', handleOutsideClick);

            function handleOutsideClick(event) {
                const inputField = document.querySelector('.durationInput');

                if (!event.target.matches('.durationInput')) {
                    console.log('Clicked or tabbed outside the input field!');

                    // Check if the input value is in the format MM:SS
                    const inputValue = inputField.value.trim();
                    const isValidFormat = /^\d{2}:\d{2}$/.test(inputValue);

                    if (isValidFormat) {
                        console.log('Input format is valid (MM:SS):', inputValue);
                    } else {
                        // Pad zeros for missing digits
                        // const paddedValue = inputValue.padEnd(5, '0');
                        const paddedValue = inputValue.padStart(4, '0').replace(/(\d{2})(\d{2})/, '$1:$2');
                        console.log('Input format is not valid. Padded value:', paddedValue);
                        inputField.value = paddedValue;
                    }
                }
            }
        </script>
    </div>


    <!-- Add QA Flag Modal -->
    <div class="modal fade" id="add-flag" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" wire:ignore.self wire:key="add-2651484846">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-color: white !important;">

                <div class="modal-header" style="border: none;background-color: white;">
                    <div class="d-flex">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center me-3" style="width: 60px; height: 60px; background-color:#eff3f4 !important">
                            <i class="fa fa-flag" style="font-size: 28px; color: #01a44f !important;"></i>
                        </div>
                        <h4 class="modal-title d-flex justify-content-center align-items-center" id="filterModalLabel" style="font-size: 30px;">Add QA Flag</h4>
                    </div>
                    <button type="button" class="border-0 bg-transparent" id='closeModal' data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times " style="font-size: 24px;"></i>
                    </button>
                </div>
                {{-- <ul class="nav nav-tabs" style="background-color: white;">
                    <li class="nav-item">
                        <a class="nav-link  @if ($activeTab === 'general') active @endif" wire:click="$set('activeTab', 'general')" style="cursor: pointer">General</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link  @if ($activeTab === 'interactions') active @endif" wire:click="$set('activeTab', 'interactions')" style="cursor: pointer">Interactions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link  @if ($activeTab === 'filters') active @endif" wire:click="$set('activeTab', 'filters')" style="cursor: pointer">Filters</a>
                    </li>
                </ul> --}}
                <div class="d-flex gap-3 ms-3 mb-2" style="border: none; padding: 0;background-color: white !important;">
                    <button class="btn btn-outline-success @if ($activeTab === 'general') active @endif tab-button" wire:click="$set('activeTab', 'general')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                        General
                    </button>
                    <button class="btn btn-outline-success @if ($activeTab === 'interactions') active @endif tab-button" wire:click="$set('activeTab', 'interactions')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                        Interactions
                    </button>
                    <button class="btn btn-outline-success @if ($activeTab === 'filters') active @endif tab-button" wire:click="$set('activeTab', 'filters')" style="box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);">
                        Filters
                    </button>
                </div>
                <div class="modal-body bg-white" style="height: 70vh; overflow-y: auto;">

                    <div class="tab-content">
                        {{-- GENERAL TAB  --}}
                        <div class="tab-pane @if ($activeTab === 'general') show active @endif">

                            <form wire:submit.prevent>
                                {{-- <div class="form-check form-switch mb-3 mt-2">
                                    <input class="form-check-input custom-switch-input" type="checkbox" id="flexSwitchCheckDefault" wire:model.live="enabled">
                                    <label class="form-check-label" for="flexSwitchCheckDefault">Enabled</label>
                                </div> --}}
                                <div class="d-flex align-items-start gap-2 justify-content-start align-items-start ">
                                    <div class="position-relative" style="display: flex;flex-direction: row;">
                                        <input type="checkbox" class="d-none" id="customSwitch" wire:click="statusUpdateModal()" />
                                        Enabled:
                                        <label for="customSwitch" class="ms-2 custom-switch-label position-relative d-inline-block" style="width: 44px; height: 24px; background-color: {{ $selected_enabled ? '#01A44F' : '#e5e7eb' }}; border-radius: 12px; cursor: pointer; transition: background-color 0.3s;">

                                            <div class="switch-handle position-absolute"
                                                style="
                                                    width: 18px;
                                                    height: 18px;
                                                    background-color: {{ $selected_enabled ? '#ffffff' : '#FF5E60' }};
                                                    border-radius: 50%;
                                                    top: 3px;
                                                    left: {{ $selected_enabled ? '22px' : '3px' }};
                                                    transition: left 0.3s, background-color 0.3s;">
                                                @if ($selected_enabled)
                                                    <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 6px; left: 4px;">
                                                        <path
                                                            d="M10.0915 0.951972L10.0867 0.946075L10.0813 0.940568C9.90076 0.753564 9.61034 0.753146 9.42927 0.939309L4.16201 6.22962L1.58507 3.63469C1.40401 3.44841 1.11351 3.44879 0.932892 3.63584C0.755703 3.81933 0.755703 4.10875 0.932892 4.29224L0.932878 4.29225L0.934851 4.29424L3.58046 6.95832C3.73676 7.11955 3.94983 7.2 4.1473 7.2C4.36196 7.2 4.55963 7.11773 4.71406 6.9584L10.0468 1.60234C10.2436 1.4199 10.2421 1.1339 10.0915 0.951972ZM4.2327 6.30081L4.2317 6.2998C4.23206 6.30015 4.23237 6.30049 4.23269 6.30082L4.2327 6.30081Z"
                                                            fill="#01A44F" stroke="#01A44F" stroke-width="0.4" />
                                                    </svg>
                                                @else
                                                    <svg width="11" height="8" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="position-absolute" style="top: 5px; left: 4px;">
                                                        <path d="M1 4H10" stroke="white" stroke-width="2" stroke-linecap="round" />
                                                    </svg>
                                                @endif
                                            </div>
                                        </label>

                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="recipient-name" class="col-form-label">Flag Name:</label>
                                    <input type="text" class="form-control" id="recipient-name" wire:model="name_add" placeholder="Enter Flag Name">
                                    @error('name_add')
                                        <span class="text-danger fs-6">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="row gx-2">
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">Start Date:</label>
                                        <input type="date" class="form-control" id="recipient-name" wire:model="start_date">
                                        @error('start_date')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">End Date:</label>
                                        <input type="date" class="form-control" id="recipient-name" wire:model="end_date">
                                        @error('end_date')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="form row gx-2">
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">No. of Interactions:</label>
                                        <input type="number" class="form-control" id="recipient-name" wire:model="interactions_number" placeholder="0">
                                        @error('interactions_number')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>


                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">Per:</label>
                                        {{-- <input type="number" class="form-control" id="recipient-name" wire:model="per" placeholder="0"> --}}
                                        <input type="number" class="form-control" id="recipient-name" value="1" placeholder="0" disabled>
                                        @error('per')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-12">
                                        <label for="recipient-name" class="col-form-label">Interval:</label>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                {{ $criteria ?? '--' }}
                                            </button>
                                            @error('criteria')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                            <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                <li><a class="dropdown-item" wire:click="$set('criteria', 'Day')">Day</a></li>
                                                <hr class="m-0">
                                                <li><a class="dropdown-item" wire:click="$set('criteria', 'Week')">Week</a></li>
                                                {{-- <hr class="m-0"> --}}
                                                {{-- <li><a class="dropdown-item" wire:click="$set('criteria', 'Month')">Month</a></li> --}}
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="recipient-name" class="col-form-label">Level of Distribution:</label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            {{ $distribution_level ?? '--' }}
                                        </button>
                                        @error('distribution_level')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                            {{-- <li><a class="dropdown-item" href="#" wire:click="selectRole('1')">Skill Groups</a></li> --}}
                                            {{-- <hr class="m-0"> --}}
                                            <li><a class="dropdown-item" href="#" wire:click="$set('distribution_level', 'Agent')">Agent</a></li>
                                        </ul>
                                    </div>
                                </div>

                                {{-- <div class="mb-3">
                                    <label for="recipient-name" class="col-form-label">Forms Filtration:</label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            {{ $forms }}
                                        </button>
                                        @error('selectedForms_add')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                            <li><a class="dropdown-item" href="#" wire:click="$set('forms', 'All Forms')">All Forms</a></li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" href="#" wire:click="$set('forms', 'Specific Forms(s)')">Specific Form(s)</a></li>
                                        </ul>
                                    </div>
                                </div> --}}
                                {{--
                                <div class="mb-4 @if ($forms == 'Specific Forms(s)') d-block @else d-none @endif">
                                    <label for="recipient-name" class="col-form-label">Forms:</label>
                                    @forelse ($selectedForms_add as $index => $item)
                                        <span class="badge me-2 mb-1" style="background-color: #00a34e">
                                            {{ $item }}
                                            <button class="btn-close btn-close-white" wire:click="removeForm_addModal({{ $index }})"></button>
                                        </span>
                                    @empty
                                    @endforelse

                                    <div class="input-group relative">
                                        <span id="searchIcon" class="input-group-text">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                        </span>
                                        <input type="text" class="form-control" wire:model.live.debounce.300ms="searchForms" placeholder="Search" style="border-radius: 0 0.5rem 0.5rem 0" aria-describedby="searchIcon">

                                        @if ($searchForms != '')
                                            <ul class="list-group mt-2 w-100 position-absolute top-100" style="z-index: 99999">
                                                @forelse ($eval_forms as $form)
                                                    <li class="list-group-item" wire:click="selectForms('{{ $form->id }}', '{{ $form->evaluation_name }}')" style="cursor: pointer">{{ $form->evaluation_name }}</li>
                                                @empty
                                                    <li class="list-group-item text-muted" style="cursor: pointer">No Forms Found</li>
                                                @endforelse
                                            </ul>
                                        @endif
                                    </div>

                                </div> --}}

                                {{-- <div class="form-check form-switch mb-3">
                                    <input class="form-check-input custom-switch-input" type="checkbox" id="flexSwitchCheckDefault">
                                    <label class="form-check-label" for="flexSwitchCheckDefault">Enable Auto Assignment</label>
                                </div> --}}
                            </form>
                        </div>


                        {{-- Interactions tab  --}}
                        <div class="tab-pane @if ($activeTab === 'interactions') show active @endif">
                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Interaction Days:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $interaction_days_field_name ?? '--' }}
                                    </button>
                                    @error('interactionDays_add')
                                        <span class="text-danger fs-6">{{ $message }}</span>
                                    @enderror
                                    <ul class="dropdown-menu w-100" onclick="event.stopPropagation()" wire:ignore.self aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li wire:click><label class="dropdown-item"><input type="checkbox" wire:click="add_days(1)" @if (in_array(1, $interaction_days_add)) checked @endif> Saturday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days(2)" @if (in_array(2, $interaction_days_add)) checked @endif> Sunday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days(3)" @if (in_array(3, $interaction_days_add)) checked @endif> Monday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days(4)" @if (in_array(4, $interaction_days_add)) checked @endif> Tuesday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days(5)" @if (in_array(5, $interaction_days_add)) checked @endif> Wednesday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days(6)" @if (in_array(6, $interaction_days_add)) checked @endif> Thursday</label></li>
                                        <hr class="m-0">
                                        <li><label class="dropdown-item"><input type="checkbox" wire:click="add_days(7)" @if (in_array(7, $interaction_days_add)) checked @endif> Friday</label></li>
                                    </ul>

                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Interactions Time:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $interaction_time ?? '--' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" wire:click="$set('interaction_time','All Time')">All Time</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('interaction_time', 'Specific Time')">Specific Time</a></li>
                                    </ul>
                                </div>

                                @if ($interaction_time == 'Specific Time')
                                    <div class="row gx-2 mt-2">
                                        <div class="mb-3 col-md-6">
                                            <label for="recipient-name" class="col-form-label">From:</label>
                                            <input type="time" class="form-control" id="recipient-name" wire:model="interaction_time_from">
                                            @error('interaction_time_from')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                        <div class="mb-3 col-md-6">
                                            <label for="recipient-name" class="col-form-label">To:</label>
                                            <input type="time" class="form-control" id="recipient-name" wire:model="interaction_time_to">
                                            @error('interaction_time_to')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="form row gx-2">
                                <div class="mb-3 col-md-6">
                                    <label for="recipient-name" class="col-form-label">Duration (MM:SS)</label>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            {{ $interactions_duration_condition }}
                                        </button>
                                        <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition', '=')">=</a></li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition', '>')">></a></li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition', '<')">
                                                    < </a>
                                            </li>
                                            <hr class="m-0">
                                            <li><a class="dropdown-item" wire:click="$set('interactions_duration_condition','between')">Between</a></li>
                                        </ul>
                                    </div>
                                </div>
                                @if ($interactions_duration_condition == 'between')
                                    <div class="mb-3 col-md-3">
                                        <label for="recipient-name" class="col-form-label">Starting From:</label>
                                        <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="interactions_duration_from">
                                        @error('interactions_duration_from')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="mb-3 col-md-3">
                                        <label for="recipient-name" class="col-form-label">Up to:</label>
                                        <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="interactions_duration_to">
                                        @error('interactions_duration_to')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @else
                                    <div class="mb-3 col-md-6">
                                        <label for="recipient-name" class="col-form-label">Time:</label>
                                        <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="interactions_duration_from">
                                        @error('interactions_duration_from')
                                            <span class="text-danger fs-6">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @endif
                            </div>

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Call Type:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $call_type_add ?? 'All' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" wire:click="$set('call_type_add', 'All')">All</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('call_type_add', 'Incoming')">Incoming</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('call_type_add', 'Outgoing')">Outgoing</a></li>
                                    </ul>
                                </div>
                            </div>
                            {{-- <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Screen Capture:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $screen_capture_add ?? 'All' }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        <li><a class="dropdown-item" wire:click="$set('screen_capture_add', 'All')">All</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('screen_capture_add', 'Includes')">Includes</a></li>
                                        <hr class="m-0">
                                        <li><a class="dropdown-item" wire:click="$set('screen_capture_add', 'Does Not Include')">Does Not Include</a></li>
                                    </ul>
                                </div>
                            </div> --}}

                            <div class="mb-3">
                                <label for="recipient-name" class="col-form-label">Organization:</label>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ $selectedOrg_add ?? '--' }}
                                    </button>
                                    @error('selectedOrg_add')
                                        <span class="text-danger fs-6">{{ $message }}</span>
                                    @enderror
                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                        @forelse ($organizations as $item)
                                            <li><a class="dropdown-item" wire:click="selectOrg_add('{{ $item->id }}', '{{ $item->name }}')">{{ $item->name }}</a></li>
                                            <hr class="m-0">
                                        @empty
                                        @endforelse
                                    </ul>
                                </div>
                            </div>

                        </div>

                        {{-- filters tab  --}}
                        <div class="tab-pane @if ($activeTab === 'filters') show active @endif">
                            <form wire:submit.prevent>
                                <fieldset class="row">
                                    <legend class="col-form-label ">General</legend>
                                    <div class="@if (!$filter_agentId_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Agent ID</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_agentId_trigger">
                                    </div>

                                    @if ($filter_agentId_trigger)
                                        <div class="w-75 mt-2 mb-2">
                                            <input type="number" class="form-control w-50" id="recipient-name" wire:model="filter_agentId" placeholder="Enter ID">
                                            @error('filter_agentId')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif

                                    {{-- <div class="@if (!$filter_silentDuration_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1">Silent Duration</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_silentDuration_trigger">
                                    </div>
                                    @if ($filter_silentDuration_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-4">
                                                <label for="recipient-name" class="col-form-label">Condition</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_silentDuration_condition }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', '=')">=</a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', '>')">></a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', '<')">
                                                                < </a>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_silentDuration_condition', 'between')">Between</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            @if ($filter_silentDuration_condition == 'between')
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Starting From:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_silentDuration_from">
                                                    @error('filter_silentDuration_from')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                                </div>
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Up To:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_silentDuration_to">
                                                    @error('filter_silentDuration_to')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                                </div>
                                            @else
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Time:</label>
                                                    <input type="text" class="form-control durationInput h-50" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_silentDuration_from">
                                                    @error('filter_silentDuration_from')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                                </div>
                                            @endif
                                        </div>
                                    @endif --}}
                                </fieldset>

                                <hr class="m-0">

                                <fieldset class="row">
                                    <legend class="col-form-label ">Telephony</legend>
                                    <div class="@if (!$filter_interactionEnder_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Interaction Ender</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_interactionEnder_trigger">
                                    </div>

                                    @if ($filter_interactionEnder_trigger)
                                        <div class="w-75 mt-2 mb-2">
                                            <input type="text" class="form-control w-50" id="recipient-name" wire:model="filter_interactionEnder" placeholder="Agent or Customer">
                                            @error('filter_interactionEnder')
                                                <span class="text-danger fs-6">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif

                                    <div class="@if (!$filter_holdDuration_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Hold Duration</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_holdDuration_trigger">
                                    </div>

                                    @if ($filter_holdDuration_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-4">
                                                <label for="recipient-name" class="col-form-label" >Condition</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d; height: 2.3rem;" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_holdDuration_condition }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', '=')">=</a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', '>')">></a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', '<')">
                                                                < </a>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdDuration_condition', 'between')">Between</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            @if ($filter_holdDuration_condition == 'between')
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Starting From:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_holdDuration_from">
                                                    @error('filter_holdDuration_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Up To:</label>
                                                    <input type="text" class="form-control durationInput" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_holdDuration_to">
                                                    @error('filter_holdDuration_to')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @else
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Time:</label>
                                                    <input type="text" class="form-control durationInput h-50" maxlength="5" oninput="formatDuration(this)" placeholder="00:00" wire:model="filter_holdDuration_from">
                                                    @error('filter_holdDuration_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Digits</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Extension</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Digits Count</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    <div class="@if (!$filter_holdCount_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Hold Count</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_holdCount_trigger">
                                    </div>

                                    @if ($filter_holdCount_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-4">
                                                <label for="recipient-name" class="col-form-label">Condition</label>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $filter_holdCount_condition }}
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', '=')">=</a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', '>')">></a></li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', '<')">
                                                                < </a>
                                                        </li>
                                                        <hr class="m-0">
                                                        <li><a class="dropdown-item" wire:click="$set('filter_holdCount_condition', 'between')">Between</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            @if ($filter_holdCount_condition == 'between')
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Min Count:</label>
                                                    <input type="number" class="form-control durationInput" maxlength="5" placeholder="0" wire:model="filter_holdCount_from">
                                                    @error('filter_holdCount_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Max Count:</label>
                                                    <input type="number" class="form-control durationInput" maxlength="5" placeholder="0" wire:model="filter_holdCount_to">
                                                    @error('filter_holdCount_to')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @else
                                                <div class="mb-3 col-md-4">
                                                    <label for="recipient-name" class="col-form-label">Count:</label>
                                                    <input type="number" class="form-control durationInput h-50" maxlength="5" placeholder="0" wire:model="filter_holdCount_from">
                                                    @error('filter_holdCount_from')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Pause Duration</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Ring</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Server Name</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Transferred From</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Transferred To</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Trunk</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                </fieldset>

                                <hr class="m-0">

                                <fieldset class="row">
                                    <legend class="col-form-label ">Users & Actions</legend>
                                    <div class="@if (!$filter_agentName_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Agent Name</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_agentName_trigger">
                                    </div>

                                    @if ($filter_agentName_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-12">
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ \App\Models\User::find($selected_agent_add)->full_name ?? '--' }}
                                                    </button>
                                                    @error('filter_agentName')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        @forelse ($possible_agents_add as $item)
                                                            <li><a class="dropdown-item" wire:click="$set('selected_agent_add', '{{ $item->id }}')">{{ $item->full_name }}</a></li>
                                                            @if (!$loop->last)
                                                                <hr class="m-0">
                                                            @endif
                                                        @empty
                                                            <li><a class="dropdown-item text-muted">No Agents Found</a></li>
                                                        @endforelse
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Interaction Importance</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Comments</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Custom Flag</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1">Groups</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Played Interactions</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                    {{-- <div class="mb-3 ms-3 form-check">
                                        <label class="form-check-label" for="exampleCheck1">Skill Groups</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model="yourCheckboxModel">
                                    </div> --}}
                                </fieldset>

                                <hr class="m-0">

                                <fieldset class="row">
                                    <legend class="col-form-label ">Telephony Custom Attributes</legend>

                                    <div class="@if (!$filter_language_trigger) mb-3 @endif ms-3 form-check w-50">
                                        <label class="form-check-label" for="exampleCheck1" style="color: black !important;">Language</label>
                                        <input type="checkbox" class="form-check-input" id="exampleCheck1" wire:model.live="filter_language_trigger">
                                    </div>

                                    @if ($filter_language_trigger)
                                        <div class="form row gx-2 ms-1">
                                            <div class="mb-3 col-md-12">
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary  dropdown-toggle dropdown-toggle-style  w-100 text-start" style="color:#6c757d" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                        {{ $selected_lang_add ?? '--' }}
                                                    </button>
                                                    @error('filter_language')
                                                        <span class="text-danger fs-6">{{ $message }}</span>
                                                    @enderror
                                                    <ul class="dropdown-menu w-100" aria-labelledby="dropdownMenuButton1" id="dropdownMenu">
                                                        <li><a class="dropdown-item" wire:click="$set('selected_lang_add', 'Arabic')">Arabic</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('selected_lang_add', 'English')">English</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('selected_lang_add', 'Spanish')">Spanish</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('selected_lang_add', 'French')">French</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('selected_lang_add', 'German')">German</a></li>
                                                        <li><a class="dropdown-item" wire:click="$set('selected_lang_add', 'Italian')">Italian</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                </fieldset>

                            </form>

                        </div>
                    </div>
                </div>


                <div class="modal-footer" style="border: none;">
                    <button
                        type="button"
                        class="btn btn-secondary rounded-3 px-4"
                        style="height: 40px; border-color: #eff3f4; background: #eff3f4; color: #6c97a6;"
                        data-bs-dismiss="modal" aria-label="Close" wire:click="clear">
                        <span wire:loading.remove wire:target="clear" style="font-size: 17px;">Close</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="clear"  role="status" aria-hidden="true"></span>
                    </button>
                    <button
                        class="btn btn-success rounded-3 px-4"
                        style="height: 40px; border-color: #01a44f; background: #01a44f;"
                        wire:click="applyFlag"
                        wire:loading.attr="disabled"
                        wire:target="applyFlag">
                        <span wire:loading.remove wire:target="applyFlag" style="font-size: 17px;">Apply</span>
                        <span wire:loading.class="spinner-border spinner-border-sm" wire:target="applyFlag" role="status" aria-hidden="true"></span>
                    </button>
                </div>

            </div>
        </div>

        {{-- <script>
            function formatDuration(input) {
                let value = input.value.replace(/\D/g, ''); // Remove non-numeric characters

                if (value.length > 4) {
                    value = value.slice(0, 4); // Limit the length to 4 characters (MMSS)
                }

                if (value.length > 2) {
                    value = value.replace(/(\d{2})(\d{2})/, '$1:$2'); // Add a colon between MM and SS
                }

                const minutes = parseInt(value.slice(0, 2), 10) || 0;
                const seconds = parseInt(value.slice(3, 5), 10) || 0;

                // Enforce maximum values
                if (minutes > 59) {
                    value = '59:00';
                }

                if (seconds > 59) {
                    value = '00:59';
                }

                input.value = value;
            }

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('focusout', handleOutsideClick);

            function handleOutsideClick(event) {
                const inputField = document.querySelector('.durationInput');

                if (!event.target.matches('.durationInput')) {
                    console.log('Clicked or tabbed outside the input field!');

                    // Check if the input value is in the format MM:SS
                    const inputValue = inputField.value.trim();
                    const isValidFormat = /^\d{2}:\d{2}$/.test(inputValue);

                    if (isValidFormat) {
                        console.log('Input format is valid (MM:SS):', inputValue);
                    } else {
                        console.log('Input format is not valid:', inputValue);
                    }
                }
            }
        </script> --}}

        <script>
            function formatDuration(input) {
                let value = input.value.replace(/\D/g, ''); // Remove non-numeric characters

                if (value.length > 4) {
                    value = value.slice(0, 4); // Limit the length to 4 characters (MMSS)
                }

                if (value.length > 2) {
                    value = value.replace(/(\d{2})(\d{2})/, '$1:$2'); // Add a colon between MM and SS
                }

                const minutes = parseInt(value.slice(0, 2), 10) || 0;
                const seconds = parseInt(value.slice(3, 5), 10) || 0;

                // Enforce maximum values
                if (minutes > 59) {
                    value = '59:00';
                }

                if (seconds > 59) {
                    value = '00:59';
                }

                input.value = value;
            }

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('focusout', handleOutsideClick);

            function handleOutsideClick(event) {
                const inputField = document.querySelector('.durationInput');

                if (!event.target.matches('.durationInput')) {
                    console.log('Clicked or tabbed outside the input field!');

                    // Check if the input value is in the format MM:SS
                    const inputValue = inputField.value.trim();
                    const isValidFormat = /^\d{2}:\d{2}$/.test(inputValue);

                    if (isValidFormat) {
                        console.log('Input format is valid (MM:SS):', inputValue);
                    } else {
                        // Pad zeros for missing digits
                        // const paddedValue = inputValue.padEnd(5, '0');
                        const paddedValue = inputValue.padStart(4, '0').replace(/(\d{2})(\d{2})/, '$1:$2');
                        console.log('Input format is not valid. Padded value:', paddedValue);
                        inputField.value = paddedValue;
                    }
                }
            }
        </script>
    </div>
