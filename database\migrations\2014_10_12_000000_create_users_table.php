<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('full_name');
            $table->string('username');
            // $table->string('email')->unique();
            $table->string('email');
            $table->string('agent_id')->unique();
            $table->unsignedBigInteger('supervisor_id')->nullable(); 
            // $table->foreign('supervisor_id')->references('id')->on('users')->onDelete('set null');
            $table->string('password');
            $table->unsignedBigInteger('organization_id')->nullable();
            $table->unsignedBigInteger('user_group_id')->nullable();
            $table->integer('role')->comment('Admin = 1, Supervisor = 2, IT = 3, agent = 4');
            $table->boolean('terminated')->default(0);
            $table->boolean('enabled')->default(1);
            $table->boolean('web_access')->default(1);
            $table->string('password_policy')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
