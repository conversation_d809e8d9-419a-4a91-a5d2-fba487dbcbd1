@extends('layouts.app')

@section('title', 'Interaction Quality')

@section('style')
        <style>
        /* Table styles */
        .table-responsive {
            box-shadow: none !important;
            border-radius: 0 !important;
        }

        input {
            border: solid 1px #b6b6b6 !important;
            background-color: white !important;
            padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
        }

        .list-group-item:hover {
            color: white;
            background-color: #00a34e;
        }

        th {
            text-wrap: nowrap;
        }

        .dropdown-menu::-webkit-scrollbar {
            display: none
        }

        #dropDownList {
            border-radius: 7px !important;
        }

        .dropdown-toggle::after {
            vertical-align: top !important;
        }

        input::placeholder {
            font-size: 0.85rem;
        }

        .header-button {
            transition: box-shadow 0.3s !important;
        }

        .header-button:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        }

        .dropdown-menu.w-100.show {
            transform: translate3d(0px, 39.2px, 0px) !important;
        }

        /* Table header styles */
        .thead {
            height: 50px;
            vertical-align: middle;
        }

        .thead tr th {
            background-color: #40798c !important;
            color: white;
            font-size: initial;
        }

        .parent-sections {
            height: 70vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: 1.5%;
            margin-bottom: 3%;
        }

        .section-one {
            width: 100%;
            height: 100%;
        }

        .div-table {
            border-radius: 0px;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        .form-control,
        .form-select,
        .dropdown-toggle-style {
            background-color: #eff3f4 !important;
            border: none !important;
            height: 40px;
        }

        label {
            color: #40798c;
            font-size: 17px;
        }

        .previous {
            margin-bottom: 5px;
        }

        /* Pagination styles */
        ul.pagination {
            gap: 0;
        }

        ul.pagination li button,
        ul.pagination li span {
            padding: 0.7rem;
            padding-top: 0.4rem;
            padding-bottom: 0.4rem;
        }

        ul.pagination li button:hover {
            background-color: rgb(196, 183, 183) !important;
        }

        ul.pagination>li>button,
        ul.pagination>li>span {
            color: black !important;
            font-weight: 600 !important;
            background-color: white;
        }

        .page-item span,
        .page-item button {
            border-radius: 0.7rem !important;
        }

        .page-item.active span,
        .page-item.active button {
            border-radius: 0.5rem !important;
        }

        .page-item.active>span {
            background-color: #00a34e !important;
            color: white !important;
        }

        div.d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between>div>p {
            font-size: 0.9rem;
        }

        .page-link[aria-label="« Previous"],
        .page-link[aria-label="Next »"] {
            padding: 0.8rem !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        .table tr th {
            font-size: small !important;
            font-weight: 600;
        }

        table td {
            font-size: small !important;
            border-bottom: none;
        }

        /* Main buttons container styles */
        .main-buttons-container button {
            height: 2.9rem;
            font-size: 15px;
        }

        .main-buttons-container button:hover {
            background-color: #018F3E !important;
        }
    </style>
@endsection

@section('content')
<div class="container-fluid mt-3 px-4">
    @livewire('reports.interaction-quality')
</div>
@endsection
