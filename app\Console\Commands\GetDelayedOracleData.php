<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\Interaction;
use Illuminate\Support\Str;
use App\Models\Organization;
use App\Models\AccountPrefix;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use PhpOffice\PhpSpreadsheet\Calculation\Financial\CashFlow\Constant\Periodic\Interest;

class GetDelayedOracleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delayedOracleData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Extend maximum execution time
        set_time_limit(0);

        // Extend the memory
        ini_set('memory_limit', '1000M');


        // UUIDs for recent calls 
        $genesysCallUUIDs = Interaction::whereBetween('created_at', [now()->subHour(), now()])->pluck('Genesys_CallUUID')->toArray();
        // $genesysCallUUIDs = Interaction::whereBetween('created_at', [now()->startOfDay()->subDays(2), now()->startOfDay()->subDays(1)])->pluck('Genesys_CallUUID')->toArray();


        $this->info('UUIDS:');
        print_r($genesysCallUUIDs);


        // recent interactions to update 
        $recentInteractions = Interaction::whereBetween('created_at', [now()->subHour(), now()])->get();
        // $recentInteractions = Interaction::whereBetween('created_at', [now()->startOfDay()->subDays(2), now()->startOfDay()->subDays(1)])->get();

        try {
            $oracleApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/fetchDataFromOracleDb.php';
            $oracleResponse = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])
                ->withoutVerifying()
                ->timeout(60 * 6000)
                ->post($oracleApiUrl, [
                    'Genesys_CallUUIDs' => $genesysCallUUIDs,
                ]);

            $oracleData = $oracleResponse->json();


            foreach ($recentInteractions as $interaction) {

                $genesysCallUUID = $interaction->Genesys_CallUUID;

                // Log::info('Oracle response is: ');
                // Log::info($oracleData);

                if (isset($oracleData[$genesysCallUUID]) && !empty($oracleData[$genesysCallUUID])) {


                    Log::info('Oracle Data is set and entered the if statement');


                    $oracleRecord = collect($oracleData[$genesysCallUUID])->sortByDesc('ENGAGE_DURATION')->first();

                    // Save Oracle data into the interaction
                    if ($interaction->caller_id == null) $interaction->caller_id = $oracleRecord['ORIGINATOR'];
                    if ($interaction->called_id == null) $interaction->called_id = $oracleRecord['TARGET'];
                    if ($interaction->call_duration == null) $interaction->call_duration =  gmdate('H:i:s', $oracleRecord['ENGAGE_DURATION']);
                    if ($interaction->call_type == null) $interaction->call_type = Str::title($oracleRecord['INTERACTION_TYPE']);
                    if ($interaction->hold_duration == null) $interaction->hold_duration =  gmdate('H:i:s', $oracleRecord['HOLD_DURATION']);
                    if ($interaction->hold_count == null) $interaction->hold_count =  $oracleRecord['HOLD_COUNT'];

                    // ring
                    if ($interaction->ring == null) $interaction->ring = $oracleRecord['RING_DURATION'];

                    // call ender
                    if ($interaction->call_ender == null) {

                        if (isset($oracleRecord['PARTY_DISCONNECTED'])) {
                            // Convert the value to an integer
                            $partyDisconnected = (int) $oracleRecord['PARTY_DISCONNECTED'];

                            // Check the value of partyDisconnected
                            if ($partyDisconnected === 0) {
                                $interaction->call_ender = 'Agent';
                            } else {
                                $interaction->call_ender = 'Customer';
                            }
                        }
                    }

                    // if the interaction has no user_id, get the agent data from Oracle and create it.
                    if ($interaction->user_id == null) {
                        Log::info('entered the of user id is null and we should get it from Oracle');

                        $agent_id = $oracleRecord['EMPLOYEE_ID'];

                        // if theres an ID, get data from headcount
                        if ($agent_id) {
                            // Check the prefix
                            $prefixes = DB::table('account_prefixes')->pluck('prefix');
                            $prefixMatched = false;
                            $agent_prefix = null;

                            foreach ($prefixes as $prefix) {
                                $this->info('Oracle loop - checking prefix' . $prefix);
                                if (substr($agent_id, 0, strlen($prefix)) === $prefix) {
                                    $prefixMatched = true;
                                    $agent_prefix = $prefix;
                                    break;
                                }
                            }

                            // if ($prefixMatched) {
                            //     // Delete the first 3 characters from $agent_id if prefix matched
                            //     $agent_id = substr($agent_id, 3);
                            // } else {
                            //     // Delete the first two characters from $agent_id
                            //     $agent_id = substr($agent_id, 2);

                            //     // get the prefix for later use (in case of a new user that have no org from sdk table)
                            //     $agent_prefix = substr($agent_id, 0, 2);
                            // }

                            if ($prefixMatched) {
                                // Delete the matched prefix length from $agent_id
                                $agent_id = substr($agent_id, strlen($agent_prefix));
                            } else {
                                // Delete the first two characters from $agent_id
                                $agent_id = substr($agent_id, 2);

                                // Get the prefix for later use (in case of a new user with no org from the SDK table)
                                $agent_prefix = substr($agent_id, 0, 2);
                            }


                            // Find the corresponding agent (if exists), otherwise create it
                            $user = User::where('agent_id', $agent_id)->where('role', 4)->first();

                            $emailThatExists = $user?->email;


                            // ***NEW FEATURE*** save the org for the interaction regardless the existing user org, because he maybe changed account
                            $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                            $user_group_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->user_group;

                            $org_id_for_interaction = null;
                            $user_group_id_for_interaction = null;

                            if ($account_name_in_prefix_table) {
                                $org_id_for_interaction = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;

                                // user group
                                $user_group_id_for_interaction = UserGroup::where('name', 'like', "%$account_name_in_prefix_table - $user_group_in_prefix_table%")?->first()?->id;
                            }

                            $interaction->organization_id = $org_id_for_interaction;
                            $interaction->user_group_id = $user_group_id_for_interaction;
                            ///////////////////////////////////////////////////////////////////////////////////////////////////////////////


                            if ($user) {
                                $this->info('Oracle loop - entered user existing statement');
                                Log::info('Oracle loop - entered user existing statement');

                                // check for the organization if he doesn't have one already
                                // already existing org of the user (if any)
                                $org_id = $user->organization_id;

                                $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                if ($account_name_in_prefix_table) {
                                    $org_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                }


                                // if (!$user->organization_id) {
                                //     $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;
                                //     if ($account_name_in_prefix_table) {
                                //         $org_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                //     } else {
                                //         $org_id = null;
                                //     }
                                // }

                                // If the user exists, get the user's id
                                $userId = $user->id;
                                $newUser = $user; // Assign the existing user to $newUser for later use

                                if (!$user->organization_id) {
                                    Log::info('the already existing user doesnt have org');
                                    $this->info('the already existing user doesnt have org');

                                    Log::info('The new org id for this already existing user that has no org: ' . $org_id . ' and user ID ' . $user->id);
                                    $this->info('The new org id for this already existing user that has no org: ' . $org_id . ' and user ID ' . $user->id);

                                    // $newUser->organization_id = $org_id;
                                    $newUser->organization_id = $org_id;
                                }
                            } else {
                                $this->info('Oracle loop - entered headcount statement');
                                // If the user does not exist, get its data from headcount
                                $headcountApiUrl = 'https://oms.extwebonline.com/Extensya_APIs/recording/agentsFromHeadcount.php';

                                // Send a GET request to the API and get the result
                                $userData = Http::withoutVerifying()->timeout(60 * 6000)->get($headcountApiUrl, [
                                    'ops_id' => $agent_id,
                                ])->json();

                                // if ($userData['error'] == "No user found with the provided ops_id") {
                                //     $this->info('no user in headcount');
                                //     Log::info('no user in headcount');

                                //     continue;
                                // }

                                // Construct full name
                                $full_name = $userData['first_name'] . ' ' . $userData['second_name'] . ' ' . $userData['third_name'] . ' ' . $userData['last_name'];


                                // Construct username
                                $username = $userData['first_name'] . ' ' . $userData['last_name'];

                                // Get email
                                $email = $userData['email'];

                                if ($email == $emailThatExists) {
                                    Log::info('Email Exists but role is not agent, the email is: ' . $email);
                                    $this->info('Email Exists but role is not agent, the email is: ' . $email);

                                    $newUser = User::where('email', $email)->first();
                                    $newUser->update([
                                        'role' => 4,
                                        'enabled' => 0
                                    ]);
                                } else {
                                    // Create the new user
                                    $newUser = User::create([
                                        'full_name' => $full_name,
                                        'username' => $username,
                                        'email' => $email,
                                        'agent_id' => $agent_id,
                                        'password' => Hash::make('extensya@12345678'),
                                        'role' => 4,
                                        'terminated' => 0,
                                        'enabled' => 0,
                                        'web_access' => 0,
                                    ]);
                                }


                                // Get the new user's id
                                $userId = $newUser->id;

                                Log::info('user id ' . $userId);

                                Log::info('agent prefix: ' . $agent_prefix);
                                $this->info('agent prefix: ' . $agent_prefix);

                                $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                $this->info('account name: ' . $account_name_in_prefix_table);

                                $org_id = null;
                                if ($account_name_in_prefix_table) {
                                    $org_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                }

                                $this->info('new org id: ' . $org_id);
                                Log::info('new org id: ' . $org_id);

                                $newUser->organization_id = $org_id;
                            }


                            // Add default params and permissions to the new agent
                            $newUser->parameters()->sync([2, 4, 6, 7, 19, 22, 25, 27]);
                            $newUser->permissions()->sync([1, 2, 4, 5, 8, 10]);

                            $newUser->save();

                            // Save the user id for the call
                            $interaction->user_id = $userId;
                        }
                    }



                    // *********************************** if the user is not null but the user has no org for some reason, add it ..
                    else if ($interaction->user_id) {
                        $agent_id = $oracleRecord['EMPLOYEE_ID'];

                        // if theres an ID, get data from headcount
                        if ($agent_id) {
                            // Check the prefix
                            $prefixes = DB::table('account_prefixes')->pluck('prefix');
                            $prefixMatched = false;
                            $agent_prefix = null;

                            foreach ($prefixes as $prefix) {
                                $this->info('Oracle loop - checking prefix' . $prefix);
                                if (substr($agent_id, 0, strlen($prefix)) === $prefix) {
                                    $prefixMatched = true;
                                    $agent_prefix = $prefix;
                                    break;
                                }
                            }

                            // if ($prefixMatched) {
                            //     // Delete the first 3 characters from $agent_id if prefix matched
                            //     $agent_id = substr($agent_id, 3);
                            // } else {
                            //     // Delete the first two characters from $agent_id
                            //     $agent_id = substr($agent_id, 2);

                            //     // get the prefix for later use (in case of a new user that have no org from sdk table)
                            //     $agent_prefix = substr($agent_id, 0, 2);
                            // }

                            if ($prefixMatched) {
                                // Delete the matched prefix length from $agent_id
                                $agent_id = substr($agent_id, strlen($agent_prefix));
                            } else {
                                // Delete the first two characters from $agent_id
                                $agent_id = substr($agent_id, 2);

                                // Get the prefix for later use (in case of a new user with no org from the SDK table)
                                $agent_prefix = substr($agent_id, 0, 2);
                            }

                            // ***NEW FEATURE*** save the org for the interaction regardless the existing user org, because he maybe changed account
                            $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                            $user_group_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->user_group;

                            $org_id_for_interaction = null;
                            $user_group_id_for_interaction = null;

                            if ($account_name_in_prefix_table) {
                                $org_id_for_interaction = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;

                                // user group
                                $user_group_id_for_interaction = UserGroup::where('name', 'like', "%$account_name_in_prefix_table - $user_group_in_prefix_table%")?->first()?->id;
                            }

                            // org id 
                            $interaction->organization_id = $org_id_for_interaction;

                            // user group
                            $interaction->user_group_id = $user_group_id_for_interaction;
                            ///////////////////////////////////////////////////////////////////////////////////////////////////////////////

                        }

                        // Find the corresponding agent (if exists), otherwise create it
                        $user = User::where('agent_id', $agent_id)->where('role', 4)->first();

                        if ($user) {
                            $this->info('the user exists with ID ' . $user->id);
                            Log::info('the user exists with ID ' . $user->id);

                            if (!$user->organization_id) {
                                $account_name_in_prefix_table = AccountPrefix::where('prefix', $agent_prefix)->first()->account;

                                $this->info('org name: ' . $account_name_in_prefix_table);

                                if ($account_name_in_prefix_table) {
                                    $user->organization_id = Organization::where('name', 'like', "%$account_name_in_prefix_table%")->first()->id;
                                    $user->save();
                                    $this->info('user saved with org: ' . $user->organization_id);
                                }
                            }
                        }
                    }


                    //*********************************************************** */






                    // if theres no arrival time already, get it from oracle
                    if ($interaction->arrival_time == null && $oracleRecord['START_TS']) {
                        $interaction->arrival_time = Carbon::createFromTimestamp($oracleRecord['START_TS'])->toDateTimeString();
                    }

                    $interaction->save();
                }
            }

            $this->info('Interactions updated from Oracle DB successfully! ' . now());
            Log::info('Interactions updated from Oracle DB successfully! ' . now());
        } catch (\Exception $e) {
            Log::error('Error fetching data from Oracle API: ' . $e->getMessage());
            $this->info('Error fetching data from Oracle API: ' . $e->getMessage());
        }
    }
}
