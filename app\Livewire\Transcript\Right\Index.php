<?php

namespace App\Livewire\Transcript\Right;

use App\Models\TranscriptionBadWord;
use App\Models\TranscriptionClassifications;
use App\Models\TranscriptionTopics;
use Livewire\Component;

class Index extends Component
{
    public $page= "Classification";

    public $callId;

    public function mount($callId)
    {
        $this->callId = $callId;
    }

    public function setPage($type)
    {
        $this->page= $type;
    }

    public function classificationResult()
    {
        $defaultClassifications = [
            'positive' => 0,
            'negative' => 0,
            'neutral' => 0,
        ];

        // Fetch classifications from the database
        $classifications = TranscriptionClassifications::selectRaw('
        LOWER(classification) as classification,
        ROUND((COUNT(*) * 100.0) / (SELECT COUNT(*) FROM transcription_classifications WHERE call_id = ?), 2) as average_percentage
    ', [$this->callId])
            ->where('call_id', $this->callId)
            ->groupByRaw('LOWER(classification)')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->classification => $item->average_percentage];
            })
            ->toArray();

        return array_merge($defaultClassifications, $classifications);
    }

    public function classification()
    {
         $classification = TranscriptionClassifications::where('call_id', $this->callId)->get();

        return $classification;
    }
    public function topics()
    {
        $topics = TranscriptionTopics::where('call_id', $this->callId)->get();

        return $topics;
    }
    public function bads()
    {
        $bads = TranscriptionBadWord::where('call_id', $this->callId)->get();

        return $bads;
    }

    public function render()
    {

        return view('livewire.transcript.right.index',
            ['result'=>$this->classificationResult(),'classifications'=>$this->classification(),'topics'=>$this->topics(),'bads'=>$this->bads()]);
    }
}
