<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Models\Parameter;
use \App\Models\UserGroup;
use App\Models\Evaluation;
use App\Models\Permission;
use App\Models\Organization;
use App\Models\Interaction;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $connection = 'mysql';
    protected $table = 'users';

    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'pass_reset_date' => 'datetime'
    ];


    // for agents 
    public function directSupervisor()
    {
        return $this->belongsTo(User::class, 'supervisor_id')->where('role', 2);
    }
    public function supervisorEvaluation()
    {
        return $this->hasMany(EvaluationSubmission::class, 'created_by')->where('role', 2);
    }

    // for supervisor 
    public function agents()
    {
        return $this->hasMany(User::class)->where('role', 4); //agent has role 2
    }

    public function agentEvaluation()
    {
        return $this->hasMany(EvaluationSubmission::class, 'user_id'); //agent has role 2
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    // for non agents (they have many orgs)
    public function supervisorOrganizations()
    {
        return $this->belongsToMany(Organization::class);
    }

    public function adminOrganizations()
    {
        return $this->belongsToMany(Organization::class)->where('role', 1);
    }

    // user groups for agents 
    public function userGroup()
    {
        return $this->belongsTo(UserGroup::class);
    }

    // the user groups of each supervisor 
    public function supervisorGroups()
    {
        return $this->belongsToMany(UserGroup::class, 'supervisor_group');
    }


    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'permission_user')->withPivot('has_permission');
    }

    public function parameters()
    {
        return $this->belongsToMany(Parameter::class);
    }

    // the evaluations for a specific supervisor 
    public function evaluations()
    {
        return $this->belongsToMany(Evaluation::class, 'evaluation_supervisor', 'user_id', 'evaluation_id');
    }

    public function interactions()
    {
        return $this->hasMany(Interaction::class, 'user_id');
    }

    public function listenedInteractions()
    {
        return $this->belongsToMany(Interaction::class, 'interaction_listener', 'user_id', 'interaction_id');
    }


    // public function scopeSearch($q, $value)
    // {
    //     $q->where('username', 'like', "%$value%")
    //         ->orWhere('email', 'like', "%$value%")
    //         ->orWhere('full_name', 'like', "%$value%")
    //         ->orWhere('supervisor_id', 'like', "%$value%")
    //         ->orWhere('agent_id', 'like', "%$value%")
    //         ->orWhere('created_at', 'like', "%$value%")
    //         ->orWhereHas('userGroup', function ($query) use ($value) {
    //             $query->where('name', 'like', "%$value%");
    //         })
    //         ->orWhereHas('organization', function ($query2) use ($value) {
    //             $query2->where('name', 'like', "%$value%");
    //         });
    // }

    public function scopeSearch($q, $value)
    {
        // Map role names to role IDs
        $roleMap = [
            'agent' => 4,
            'quality' => 5,
            'supervisor' => 2,
            'admin' => 1,
            'client' => 6
        ];

        // Check if the user has typed in a role name and map it to its corresponding role ID
        $roleId = null;
        foreach ($roleMap as $roleName => $id) {
            if (stripos($value, $roleName) !== false) {
                $roleId = $id;
                break;
            }
        }

        // Apply the search logic
        $q->where('username', 'like', "%$value%")
            ->orWhere('email', 'like', "%$value%")
            ->orWhere('full_name', 'like', "%$value%")
            ->orWhere('supervisor_id', 'like', "%$value%")
            ->orWhere('agent_id', 'like', "%$value%")
            ->orWhere('created_at', 'like', "%$value%");

        // If a role name was typed, search by role ID
        if ($roleId !== null) {
            $q->orWhere('role', $roleId);
        }

        // Continue the search through related userGroup and organization models
        $q->orWhereHas('userGroup', function ($query) use ($value) {
            $query->where('name', 'like', "%$value%");
        })
            ->orWhereHas('organization', function ($query2) use ($value) {
                $query2->where('name', 'like', "%$value%");
            });
    }
}
