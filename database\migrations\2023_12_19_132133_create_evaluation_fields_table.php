<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('evaluation_fields', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignId('evaluation_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->string('type');
            $table->string('label');
            $table->longText('value')->nullable();
            $table->unsignedTinyInteger('required');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('evaluation_fields');
    }
};
