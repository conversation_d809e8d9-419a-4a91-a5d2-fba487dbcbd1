<?php

use App\Http\Controllers\ScriptController;
use App\Models\CallsTranscription;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Config;
use App\Http\Controllers\FtpController;
use App\Http\Controllers\AudioController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\QaFlagsController;
use App\Http\Controllers\RecordsController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\RecordingController;
use App\Http\Controllers\AudioCheckController;
use App\Http\Controllers\UserGroupsController;
use App\Http\Controllers\InteractionController;
use App\Http\Controllers\SkillGroupsController;
use App\Http\Controllers\VideoUploadController;
use App\Http\Controllers\AdminReportsController;
use App\Http\Controllers\Reports\HoldController;
use App\Http\Controllers\GetVoiceFilesController;
use App\Http\Controllers\OrganizationsController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\UserPermissionController;
use App\Http\Controllers\EvaluationFormsController;
use App\Http\Controllers\SkillGroupsReportController;
use App\Http\Controllers\Reports\UsersReportController;
use App\Http\Controllers\AgentsEvaluationListController;

use App\Http\Controllers\Evaluation\EvaluationController;
use App\Http\Controllers\Reports\LongInteractionsController;


use App\Http\Controllers\Admin\Configuration\IndexController;
use App\Http\Controllers\ChannelsController;
use App\Http\Controllers\Reports\DisconnectedCallsController;
use App\Http\Controllers\Reports\ShortInteractionsController;
use App\Http\Controllers\Reports\InteractionsReportController;
use App\Http\Controllers\Reports\TransactionReasonController;
use App\Http\Controllers\Reports\SkillGroupsReportController as ReportsSkillGroupsReportController;
use App\Jobs\Scripts\DurationJob;
use App\Mail\ZaraFilesMail;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect('/analytics-new');
})->middleware('auth');

Route::get('/home', function () {
    return redirect('/analytics-new');
})->middleware('auth');

Route::get('/websocket-test', function () {
    return view('websocket-test');
});

Route::get('/analytics-quality', [AnalyticsController::class, 'indexQuality'])->name('analytics-quality');

Route::get('/ftp-connect', [FtpController::class, 'ftpConnect']);
// Route::get('/interactions/count', [InteractionController::class, 'countTodayInteractions']);
Route::post('/python/video/upload', [VideoUploadController::class, 'upload'])->name('file.upload');

Auth::routes();

// Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::middleware(['auth', 'checkEnabledUser', 'checkPasswordPolicy', 'resetNextLogin'])->group(function () {
    Route::get('/analytics-new', [AnalyticsController::class, 'indexNew'])->name('analytics-new');

    Route::middleware(['agent'])->group(function () {
        Route::get('/user-management', [UserManagementController::class, 'index'])->middleware(['supervisorAccess', 'clientAccess'])->name('users');
        Route::get('/analytics', [AnalyticsController::class, 'index'])->middleware('clientAccess')->name('analytics');
        // Route::get('/analytics-new', [AnalyticsController::class, 'indexNew'])->middleware('agent','clientAccess')->name('analytics-new');
        Route::get('/evaluation', [AnalyticsController::class, 'index'])->name('evaluation');
        Route::get('/user-groups', [UserGroupsController::class, 'index'])->middleware(['supervisorAccess', 'clientAccess'])->name('userGroups');
        Route::get('/skill-groups', [SkillGroupsController::class, 'index'])->middleware(['supervisorAccess', 'clientAccess'])->name('skillGroups');
        Route::get('/organizations', [OrganizationsController::class, 'index'])->middleware(['supervisorAccess', 'clientAccess'])->name('organizations');
        Route::get('/evaluation-forms', [EvaluationFormsController::class, 'index'])->name('evaluation.forms');
        Route::get('/qa-flags', [QaFlagsController::class, 'index'])->middleware(['adminAccess', 'agent', 'qualityAccess', 'clientAccess'])->name('qa.flags');
        // exports
        // Route::prefix('export')->group(function () {
        //     Route::get('user-report', [UsersReportController::class, 'export'])->name('export.users');
        // });
    });
    Route::get('/agents-evaluation-list', [AgentsEvaluationListController::class, 'index'])->middleware('adminAccess')->name('agents.evaluation.list');

    // this route is accessible also to agents
    Route::get('/recordings', [RecordsController::class, 'index'])->middleware(['allowAccessTelephony', 'adminAccess'])->name('recordings');

    // Route::get('/call/{call_id}/{agent_name?}/{agent_id?}', [AudioController::class, 'playAudio'])->middleware(['allowAccessTelephony', 'playInteractions', 'adminAccess'])->name('play');
    Route::get('/call/{call_id}/{agent_name?}/{agent_id?}', [AudioController::class, 'playAudio'])->middleware(['allowAccessTelephony', 'playInteractions', 'adminAccess'])->name('play.new');

    Route::get('/videos/view/{call_id}', [VideoController::class, 'playVideo'])->middleware(['allowAccessTelephony', 'playInteractions', 'adminAccess'])->name('playvideo');

    Route::get('/get_call/decrypt-audio/{encryptedFilePath}/{call_id}', [AudioController::class, 'decrypt'])->name('decrypt.audio');
    Route::get('/get_video/decrypt-video/{encryptedFilePath}/{call_id}', [VideoController::class, 'decrypt'])->name('decrypt.video');
});

Route::get('/reports', [AdminReportsController::class, 'index'])->middleware(['auth', 'checkEnabledUser', 'checkPasswordPolicy', 'resetNextLogin'])->name('admin.reports');

// admin reports
Route::middleware(['auth', 'checkEnabledUser', 'checkPasswordPolicy', 'resetNextLogin'])->prefix('reports')->group(function () {
    Route::get('/skill-groups', [ReportsSkillGroupsReportController::class, 'index'])->name('skill.group.report');
    Route::get('/users', [UsersReportController::class, 'index'])->name('user.report');
    Route::get('/interactions', [InteractionsReportController::class, 'index'])->name('interactions.report');
    Route::get('/shortInteractions', [ShortInteractionsController::class, 'index'])->name('shortInteractions.report');
    Route::get('/longInteractions', [LongInteractionsController::class, 'index'])->name('longInteractions.report');
    Route::get('/hold', [HoldController::class, 'index'])->name('hold.report');
    Route::get('/disconnectedCalls', [DisconnectedCallsController::class, 'index'])->name('disconnectedCalls.report');
    Route::get('/transaction-reason', [TransactionReasonController::class, 'index'])->name('transactionReason.report');
    Route::get('/user-permissions', [UserPermissionController::class, 'index'])->name('user.permissions');
    Route::get('/interaction-quality', [\App\Http\Controllers\Reports\InteractionQualityReportController::class, 'index'])->name('interaction.quality.report');
});

/************** Evaluation Routes ************/
Route::group(['prefix' => 'evaluation', 'middleware' => ['auth', 'checkEnabledUser', 'checkPasswordPolicy', 'resetNextLogin', 'viewEvalReports'], 'as' => 'evaluation.'], function () {

    Route::get('', [EvaluationController::class, 'index'])->name('index');
    Route::get('createGroup/{id}', [EvaluationController::class, 'createGroup'])->name('createGroup');
    Route::get('createQuestion/{evaluation_id}/{group_id}', [EvaluationController::class, 'createQuestion'])->name('createQuestion');
    Route::get('EditQuestion/{evaluation_id}/{group_id}/{question_id}', [EvaluationController::class, 'editQuestion'])->name('editQuestion');
    Route::get('createFormBuilder/{evaluation_id}', [EvaluationController::class, 'createFormBuilder'])->name('createFormBuilder');
    Route::get('submitEvaluationForm/{evaluation_id}', [EvaluationController::class, 'submitEvaluationForm'])->name('submitEvaluationForm');
    Route::get('report/{reportType}', [EvaluationController::class, 'report'])->name('report');
    Route::get('evaluation-reports', [EvaluationController::class, 'reports'])->name('reports');
    Route::get('reportAnswers/{submit_id}', [EvaluationController::class, 'reportAnswers'])->name('reportAnswers');
});
/************** Evaluation Routes ************/



/************** Admin Configuration ************/
Route::group(['prefix' => 'config', 'middleware' => ['auth'], 'as' => 'admin.config.'], function () {

    Route::get('', [IndexController::class, 'index'])->name('index');
    Route::get('/encryption', [IndexController::class, 'call_encryption'])->name('encryption');
    Route::get('/bad-words', [IndexController::class, 'bad_words'])->name('bad_words');
    Route::get('/scripts', [IndexController::class, 'scripts'])->name('scripts');
});
/************** Evaluation Routes ************/




// get Audio file via cURL
// Route::post('/download-audio', [AudioController::class, 'downloadAudio']);
// Route::get('/download-audio', [AudioController::class, 'downloadAudio']);

Route::get('/test-api', function () {
    $callIds = [
        "00FSVG7U1SAVT10U840SK2LAES023OA0",
        "00FSVG7U1SAVT10U840SK2LAES023OUG",
        "00FSVG7U1SAVT10U840SK2LAES023LKR",
        "00FSVG7U1SAVT10U840SK2LAES023KNF",
        "00FSVG7U1SAVT10U840SK2LAES023L42",
        "00FSVG7U1SAVT10U840SK2LAES023M10",
        "00FSVG7U1SAVT10U840SK2LAES023MGT",
        "00FSVG7U1SAVT10U840SK2LAES023M01",
        "00FSVG7U1SAVT10U840SK2LAES023NGR",
        "00FSVG7U1SAVT10U840SK2LAES023KNF",
        "00FSVG7U1SAVT10U840SK2LAES023LKR",
        "00FSVG7U1SAVT10U840SK2LAES023MO9",
        "00FSVG7U1SAVT10U840SK2LAES023MR0"
    ];

    $batchSize = 5;
    $responses = [];

    // Chunk the callIds array into batches of 5
    $batches = array_chunk($callIds, $batchSize);

    // Loop through each batch and send the POST request
    foreach ($batches as $batch) {
        $response = Http::withoutVerifying()->post('https://oms.extwebonline.com/Extensya_APIs/recording/fetchInteractionsDataBatches.php', [
            'callIds' => $batch
        ]);

        // Add the response to the responses array
        $responses[] = $response->json();
    }

    // Return all the responses as a JSON array
    return response()->json($responses);
});


Route::get('info', function () {
    phpinfo();
});

Route::post('/user-played-interaction', [AudioController::class, 'userPlayedInteraction'])->name('userPlayedInteraction');

// abdelgani route
Route::get('/zara/interaction', [InteractionController::class, 'countTodayInteractions']);


Route::get('test', function () {
    try {
        // Reconfigure SQL Server connection
        Config::set('database.connections.sqlsrv.options.Encrypt', false);
        Config::set('database.connections.sqlsrv.options.TrustServerCertificate', true);
        Config::set('database.connections.sqlsrv.options.ConnectionPooling', false);

        DB::purge('sqlsrv'); // Clear previous connection
        DB::reconnect('sqlsrv'); // Reconnect with new settings

        // Test simple query
        $results = DB::connection('sqlsrv')->select('SELECT TOP 1 * FROM headcount_users_info');
        dd($results);
    } catch (\Exception $e) {
        dd('Error: ' . $e->getMessage());
    }
});


Route::get('zaraTest', function () {
    Mail::to(['<EMAIL>', '<EMAIL>', '<EMAIL>'])->send(new ZaraFilesMail('emails.zara_files_failed'));
});


Route::get('calls/run/tools/{callId}',             [ScriptController::class, 'index']);

Route::get('/download-call/{call_id}', [AudioController::class, 'downloadCall']);

Route::get('/overlap/{call_id}', function ($call_id) {
    // Fetch all transcription data for the given call_id, ordered by duration_from
    $callData = CallsTranscription::orderBy('duration_from', 'asc')
        ->where('call_id', $call_id)
        ->get();

    $overlapDuration = 0; // Initialize the overlap duration
    $lastAgentEndTime = null; // Track the end time of the last agent segment
    $ddd =[];
    // Loop through all segments and compare them
    foreach ($callData as $segment) {
        // If it's an agent segment (left)
        if ($segment->source == 'left') {
            // Echo details for the left (Agent) segment
//            echo "left " . $segment->duration_from . " to " . $segment->duration_to . "\n";

            // If this is the first agent segment, update lastAgentEndTime
            $lastAgentEndTime = $segment->duration_to;
        }
        // If it's a customer segment (right), check if there's an overlap with the previous agent (left)
        elseif ($segment->source == 'right' && $lastAgentEndTime !== null) {
            // Echo details for the right (Customer) segment
//            echo "right " . $segment->duration_from . " to " . $segment->duration_to . "\n";

            // Calculate the overlap between the agent's segment and the customer segment
            if ($lastAgentEndTime > $segment->duration_from) {
                // Calculate the start and end of the overlap
                $overlapStart = $segment->duration_from;
                $overlapEnd = min($segment->duration_to, $lastAgentEndTime);

                // If the overlapStart is less than overlapEnd, there is an actual overlap
                if ($overlapStart < $overlapEnd) {
                    $overlapDuration += ($overlapEnd - $overlapStart); // Add the overlap duration
                    $ddd[]= "Overlap is " . ($overlapEnd - $overlapStart) . " from " . $overlapStart . " to " . $overlapEnd . "\n";
                }
            }
            // Update the last agent's end time to the current customer's end time
            $lastAgentEndTime = max($lastAgentEndTime, $segment->duration_to);
        }
    }

    // Return the total overlap duration as JSON
    return round($overlapDuration, 2);
});


Route::get('/download-channels', [ChannelsController::class, 'index']);
Route::get('/download-channels/{call_id}', [ChannelsController::class, 'download']);

Route::get('/audio/{filename}', [AudioController::class, 'streamAudio'])->middleware('auth')->name('audio')->middleware('signed');
