<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;

use App\Models\PasswordHistory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    protected function resetPassword($user, $password)
    {
        $user->password = bcrypt($password);
        $user->reset_pass_next_login = 0; // Update the column
        $user->setRememberToken(Str::random(60));

        $user->save();

        $this->guard()->login($user);
    }

    // protected function rules()
    // {
    //     return [
    //         'password' => [
    //             'required',
    //             'string',
    //             'min:8',                       // Minimum length of 8 characters
    //             'regex:/[a-zA-Z]/',             // At least one letter
    //             'regex:/[0-9]/',                // At least one number
    //             'regex:/[@$!%*?&#]/',           // At least one special character
    //             'confirmed',                    // Matches the confirmation field
    //         ],
    //         'password_letter' => 'required|regex:/[a-zA-Z]/', // Custom rule for letters
    //         'password_number' => 'required|regex:/[0-9]/',    // Custom rule for numbers
    //         'password_special' => 'required|regex:/[@$!%*?&#]/', // Custom rule for special characters
    //     ];
    // }

    // protected function messages()
    // {
    //     return [
    //         'password.required' => 'Please enter a password.',
    //         'password.string' => 'The password must be a valid string.',
    //         'password.min' => 'The password must be at least 8 characters long.',
    //         'password.confirmed' => 'The password confirmation does not match.',
    //         'password_letter.regex' => 'The password must contain at least one letter.',
    //         'password_number.regex' => 'The password must contain at least one number.',
    //         'password_special.regex' => 'The password must contain at least one special character (e.g., @, $, !, %, *, ?, & or #).',
    //     ];
    // }

    // protected function validationErrorMessages()
    // {
    //     return [
    //         'password.required' => 'Please enter a password.',
    //         'password.string' => 'The password must be a valid string.',
    //         'password.min' => 'The password must be at least 8 characters long.',
    //         'password.regex' => 'The password must contain at least one letter, one number, and one special character (e.g., @, $, !, %, *, ?, & or #).',
    //         'password.confirmed' => 'The password confirmation does not match.',
    //     ];
    // }


    // public function reset(Request $request)
    // {
    //     $request->validate($this->rules(), $this->validationErrorMessages());

    //     $user = User::where('email', $request->email)->first();

    //     // make a copy of the current password into the password histories table
    //     PasswordHistory::create([
    //         'user_id' => $user->id,
    //         'password' => $user->password,
    //     ]);

    //     // Check if the new password matches any of the last 5 passwords in password history
    //     $recentPasswords = PasswordHistory::where('user_id', $user->id)
    //         ->latest()
    //         ->take(5)
    //         ->pluck('password');

    //     // Check if the new password matches the current password
    //     if (Hash::check($request->password, $user->password)) {

    //         return redirect()->back()->withErrors(['password' => 'Your new password cannot be the same as your current password.']);
    //     }


    //     foreach ($recentPasswords as $oldPassword) {
    //         if (Hash::check($request->password, $oldPassword)) {
    //             return redirect()->back()->withErrors(['password' => 'You cannot reuse any of your last 5 passwords.']);
    //         }
    //     }

    //     // Update the user password, password last reset time, and remember token (reset every 3 months part)
    //     $user->password = Hash::make($request->password);
    //     $user->pass_reset_date = now();
    //     $user->setRememberToken(Str::random(60));
    //     $user->save();


    //     // Delete oldest records if there are more than 5
    //     if (PasswordHistory::where('user_id', $user->id)->count() > 5) {
    //         PasswordHistory::where('user_id', $user->id)
    //             ->oldest()
    //             ->take(1)
    //             ->delete();
    //     }


    //     // Log the user in after successful password reset
    //     $this->guard()->login($user);

    //     // Here we will attempt to reset the user's password. If it is successful we
    //     // will update the password on an actual user model and persist it to the
    //     // database. Otherwise we will parse the error and return the response.
    //     $response = $this->broker()->reset(
    //         $this->credentials($request),
    //         function ($user, $password) {
    //             $this->resetPassword($user, $password);
    //         }
    //     );

    //     // If the password was successfully reset, we will redirect the user back to
    //     // the application's home authenticated view. If there is an error we can
    //     // redirect them back to where they came from with their error message.
    //     return $response == Password::PASSWORD_RESET
    //         ? $this->sendResetResponse($request, $response)
    //         : $this->sendResetFailedResponse($request, $response);
    // }

    public function reset(Request $request)
    {
        // First, validate with standard rules
        $request->validate($this->rules(), $this->validationErrorMessages());

        $user = User::where('email', $request->email)->first();

        // Custom checks for password history and current password
        $recentPasswords = PasswordHistory::where('user_id', $user->id)
            ->latest()
            ->take(5)
            ->pluck('password');

        // Check if the new password matches the current password
        if (Hash::check($request->password, $user->password)) {
            return redirect()->back()->withErrors(['password' => 'Your new password cannot be the same as your current password.']);
        }

        // Check if the new password matches any of the last 5 passwords
        foreach ($recentPasswords as $oldPassword) {
            if (Hash::check($request->password, $oldPassword)) {
                return redirect()->back()->withErrors(['password' => 'You cannot reuse any of your last 5 passwords.']);
            }
        }

        // If validation and custom checks pass, proceed to update the password
        $user->password = Hash::make($request->password);
        $user->pass_reset_date = now();
        $user->setRememberToken(Str::random(60));
        $user->save();

        // Make a copy of the new password into the password histories table
        PasswordHistory::create([
            'user_id' => $user->id,
            'password' => $user->password,
        ]);

        // Delete oldest records if there are more than 5
        if (PasswordHistory::where('user_id', $user->id)->count() > 5) {
            PasswordHistory::where('user_id', $user->id)
                ->oldest()
                ->take(1)
                ->delete();
        }

        // Log the user in after successful password reset
        $this->guard()->login($user);

        // Redirect to the home page after successful reset
        return $this->sendResetResponse($request, Password::PASSWORD_RESET);
    }

    protected function rules()
    {
        return [
            'password' => [
                'required',
                'string',
                'min:8',                 // Minimum length of 8 characters
                'regex:/[a-zA-Z]/',       // At least one letter
                'regex:/[0-9]/',          // At least one number
                'regex:/[@$!%*?&#]/',     // At least one special character
                'confirmed',              // Matches the confirmation field
            ],
        ];
    }

    protected function validationErrorMessages()
    {
        return [
            'password.required' => 'Please enter a password.',
            'password.string' => 'The password must be a valid string.',
            'password.min' => 'The password must be at least 8 characters long.',
            'password.regex' => 'The password must contain at least one letter, one number, and one special character (e.g., @, $, !, %, *, ?, & or #).',
            'password.confirmed' => 'The password confirmation does not match.',
        ];
    }
}
