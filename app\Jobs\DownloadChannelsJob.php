<?php

namespace App\Jobs;

use App\Jobs\Scripts\DurationJob;
use App\Models\Interaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class DownloadChannelsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    protected $fileNames;
    public function __construct($fileNames)
    {
        //
        $this->fileNames = $fileNames;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //
        Log::channel('duration')->info('Job started.');
        Log::channel('duration')->info($this->fileNames);

        foreach ($this->fileNames as $fileName) {

            try {
                // Fetch the file content
                if (is_null($fileName)) {
                    continue;
                }
                $response1 = Http::withoutVerifying()->timeout(300 * 3)->get($fileName['file1']);
                $response2 = Http::withoutVerifying()->timeout(300 * 3)->get($fileName['file2']);

                if ($response1->successful() && $response2->successful()) {
                    // Get the file content
                    $content1 = $response1->body();
                    $content2 = $response2->body();

                    // Generate the file path in the public directory
                    // $path = 'calls/' . basename($fileName);
                    $filenameBase1 = basename($fileName['file1']);
                    $filenameBase2 = basename($fileName['file2']);


                    $path1 = 'channels/' . $filenameBase1;
                    $path2 = 'channels/' . $filenameBase2;
                    // Store the file in the "public" disk
                    // Storage::disk('calls_volume')->put($path, $content);
                    Storage::disk('local')->put($path1, $content1);
                    Storage::disk('local')->put($path2, $content2);

                    $call_id = str_replace('_channel1', '', pathinfo(parse_url($fileName['file1'], PHP_URL_PATH), PATHINFO_FILENAME));

                   /*  Log::info($fileName); */
                    $duration = Interaction::where('call_id', $call_id)->value('call_duration');
                    list($hours, $minutes, $seconds) = explode(':', $duration);

                    // Convert to total seconds
                    $duration = ($hours * 3600) + ($minutes * 60) + $seconds;
                    DurationJob::dispatch($call_id, "/var/www/i-log/storage/app/channels/$filenameBase1", "/var/www/i-log/storage/app/channels/$filenameBase2", $duration);

                } else {
                    Log::error("Failed to download file: $fileName (HTTP {$response1->status()}) (HTTP {$response2->status()})");
                }
            } catch (\Exception $e) {
                // Log error and continue with the next file
                Log::error("Failed to process file: $fileName", [
                    'error' => $e->getMessage(),
                ]);
                continue;
            }
        }
    }
}
