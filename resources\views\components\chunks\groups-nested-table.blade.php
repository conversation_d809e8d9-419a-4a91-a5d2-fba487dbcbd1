@props(['org', 'openMenu'])
@if ($openMenu == $org->id)

   <tr class="sub-table-tr-head text-center">
       <th style="background-color: black !important;">Group</th>
       <th style="background-color: black !important;">NO. Of Users</th>
       <th style="background-color: black !important;">Skill</th>
       <th style="background-color: black !important;">ACDID</th>
       <th style="background-color: black !important;">Created At</th>

   </tr>
   
   @foreach ($org->userGroups as $group)
       <tr class="sub-table-tr-body text-center">
           <td style="background-color: white !important;">{{ $group->name }}</td>
           <td style="background-color: white !important;">{{ $group->users->count() + $group->supervisors->count() }}</td>
           <td style="background-color: white !important;">{{ $group->skill ?? '-' }}</td>
           <td style="background-color: white !important;">{{ $group->acdid ?? '-' }}</td>
           <td style="background-color: white !important;">{{ $group->created_at->format('Y-m-d') }}</td>

       </tr>
  
   @endforeach

@endif