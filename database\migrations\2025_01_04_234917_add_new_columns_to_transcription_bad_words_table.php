<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transcription_bad_words', function (Blueprint $table) {
            //$table->foreignId('organization_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            //$table->foreignId('user_group_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transcription_bad_words', function (Blueprint $table) {
            //$table->dropColumn('organization_id');
            //$table->dropColumn('user_group_id');
        });
    }
};
