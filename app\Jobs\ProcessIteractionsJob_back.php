<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessIteractionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
	    //start
	           //

$call_ids = ['007B4468-3BF0-17A0-847D-4101CA0AAA77-27328707@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27360685@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27367156@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27390676@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27399995@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27403674@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27407752@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27414649@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27431308@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27467512@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27553875@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27749357@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27757415@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27758886@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27793317@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27844465@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27847931@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27848425@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27849794@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27850650@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27851131@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27851156@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27851891@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27852185@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27852982@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27854673@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27856539@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27857161@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27860514@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27860303@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27862395@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27865441@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27871144@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27873689@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27875510@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27877032@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27881752@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27882563@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27885952@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27888591@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27890289@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27892801@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27895682@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27897056@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27897777@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27898371@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27901488@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27904648@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27907960@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27910598@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27913950@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27925356@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27928764@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27932499@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27933648@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27934828@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27934447@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27935048@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27941091@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27943844@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27928321@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27943204@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27947216@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27950127@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27966279@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27969550@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27975855@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27976018@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27982234@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27986367@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27989671@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27990509@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27991770@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27995120@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27995434@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-27996344@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28009699@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28014719@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28018321@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28049734@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28068557@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28072811@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28084195@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28085094@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28085349@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28142270@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28146500@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28150977@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28151136@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28166626@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28170820@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28172181@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28194615@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28211433@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28228390@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28230824@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28232706@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28273158@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28302749@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28313515@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28331560@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28362972@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28382578@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28384543@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28391789@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28416687@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28973760@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28978165@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28978328@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28980992@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28982959@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28984365@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28987009@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28987579@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28987833@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28990394@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28991294@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28991593@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28993001@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-28998400@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29043278@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29046895@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29050430@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29056645@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29064426@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29069895@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29072971@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29074823@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29076656@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29077052@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29079689@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29084183@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29104212@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29105261@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29111980@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29113098@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29125136@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29135604@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29143128@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29169763@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29180767@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29186864@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29190391@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29191227@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29197068@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29200713@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29205193@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29221808@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29242571@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29252531@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29255643@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29256051@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29256536@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29261410@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29262704@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29267386@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29269948@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29282657@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29289697@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29310704@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29325424@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29329286@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29338579@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29372721@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29387664@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29391843@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29473349@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29486336@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29489336@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29491661@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29495639@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29498837@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29500961@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29501991@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29503098@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29504034@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29505486@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29505733@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29508920@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29510412@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29510551@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29512608@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29517963@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29564523@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29570974@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29585834@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29639487@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-29663101@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30096601@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30103843@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30106159@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30126162@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30163901@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30164699@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30166065@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30186685@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30186922@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30187935@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30190994@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30193247@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30205948@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30229777@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30305954@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30306562@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30307602@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30308435@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30311803@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30312127@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30312629@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30375871@***********',
'007B4468-3BF0-17A0-847D-4101CA0AAA77-30468526@***********',];
$interactions = Interaction::whereIn('call_id', $callIds)->get();

        foreach ($interactions as $interaction) {
            // Fetch left and right channel durations from calls_transcription table
            $leftDuration = DB::table('calls_transcription')
                ->where('call_id', $interaction->call_id)
                ->where('source', 'left') // Assuming "source" column indicates left/right
                ->sum(DB::raw('duration_to - duration_from'));

            $rightDuration = DB::table('calls_transcription')
                ->where('call_id', $interaction->call_id)
                ->where('source', 'right')
                ->sum(DB::raw('duration_to - duration_from'));

            // Calculate total duration (from interactions table)
            $totalDuration = $interaction->duration; // Assuming this is already stored in seconds

            // Calculate overtalk duration (simultaneous speaking)
            $overtalkDuration = max(0, min($leftDuration, $rightDuration));

            // Calculate silence duration
            $silenceDuration = max(0, $totalDuration - ($leftDuration + $rightDuration - $overtalkDuration));
            $otherDuration = $totalDuration - ($rightDuration + $leftDuration + $silenceDuration);
            CallDetails::create([
                'call_id' => $this->call_id,
                'left_channel_duration' => $leftDuration,
                'right_channel_duration' => $rightDuration,
                'silence_duration' => $silenceDuration,
//                'overtalk_duration' => $data["overlap_duration"],
                'overtalk_duration' => $overtalkDuration,
                'engage_duration' => $totalDuration,
                'other_duration' => $otherDuration,
            ]);
        }
    }
}
