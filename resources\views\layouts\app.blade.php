<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/x-icon" href="{{ asset('assets/images/logo.png') }}">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>



    @yield('style')


    <!-- Fonts -->
    {{-- <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet"> --}}


    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Poppins" rel="stylesheet">


    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"> --}}
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

    <style>
        td {
            font-weight: 600;
        }

        .zoom-out {
            /* -webkit-transform: scale(0.9);
            -moz-transform: scale(0.9);
            -ms-transform: scale(0.9);
            -o-transform: scale(0.9);
            transform: scale(0.9);
            transform-origin: top left; */
            zoom: 0.76;
        }

        /* HTML: <div class="loader"></div> */
        .loader {
            width: 60px;
            aspect-ratio: 4;
            background: radial-gradient(circle closest-side, #000 90%, #0000) 0/calc(100%/3) 100% space;
            clip-path: inset(0 100% 0 0);
            animation: l1 1s steps(4) infinite;
        }

        .pre-loader {
            opacity: .2;
        }

        @keyframes l1 {
            to {
                clip-path: inset(0 -34% 0 0)
            }
        }

        .dropdown-toggle::after {
            margin-top: 0.8rem !important;
        }

        th {
            text-wrap: nowrap !important;
        }

        body {
            background-color: White !important;
        }

        .sidebar-icons {
            width: 1.5rem;
            aspect-ratio: 1/1;
            object-fit: contain;
        }

        .sidenav {
            background: #f8f8f8 !important;

        }

        .arrow-icon {
            bottom: 20px;
            position: absolute;
            right: 22px;
            transform: translateY(-50%);
            pointer-events: none;
            width: 30px;
            height: 33px;
        }

        .colored-nav {
            background: #f8f8f8;
            color: white;
            box-shadow: #f4f4f4 0 2px 2px 2px;
        }

        thead {
            height: 1rem !important;
        }

        tbody {
            height: 1rem !important;
        }

        .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Spinner style */
.spinner {
    border: 8px solid #f3f3f3;
    border-top: 8px solid #3498db;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 1s linear infinite;
    left: 50%;
    position: absolute;
    right: 50%;
    top: 50%;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
    </style>

    <!-- Scripts -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])
    @livewireStyles
</head>

<body>
    <div class="loader" id="loader" style="
    position: fixed;
    top: 50%;
    z-index: 9999;
    left: 50%;">
    </div>

    <div id="app" class="pre-loader zoom-out">


        {{-- do not show the sidebar if the user is not logged in, e.g the reset and forgot password pages  --}}
        @if (Auth::check() && Route::currentRouteName() != 'password.reset')
            <div class="sidenav shadow text-center" style="width: 3.5rem">
                {{-- extensya logo  --}}
                <img src="{{ asset('assets/images/logo.png') }}" alt="logo" class="logo mb-5">

                {{-- sidebar items  --}}
                {{-- all can see analytics except agent and client  --}}
                @if (!in_array(Auth::user()->role, [6]))
                    <a href="{{ route('analytics-new') }}" class="d-block" title="Analytics">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-chart-simple fa-xl @if (Route::currentRouteName() != 'analytics') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'analytics-new')
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-12 - black.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-12.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif

                {{-- only admin can see users  --}}
                @if (Auth::user()->role == 1)
                    <a href="{{ route('users') }}" class="d-block mt-5" title="User Management">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-user-gear fa-xl @if (Route::currentRouteName() != 'users') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'users')
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-2 - black.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-2.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif
                @if (Auth::user()->permissions()->where('permission_id', 1)->exists() && Auth::user()->role != 1)
                    <a href="{{ route('recordings') }}" class="d-block mt-5" title="Recordings">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-microphone-lines fa-xl @if (Route::currentRouteName() != 'recordings') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'recordings')
                            <img src="{{ asset('assets/SVG/assets-v2/mic bold.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/mic bold - green.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif
                @if (Auth::user()->role != 4)

                    {{-- quality and agents can't see the flags  --}}
                    @if (!in_array(Auth::user()->role, [1, 4, 5, 6]))
                        <a href="{{ route('qa.flags') }}" class="d-block mt-5" title="QA Flags">
                            @if (Route::currentRouteName() != 'qa.flags')
                                <img src="{{ asset('assets/SVG/assets-v2/flag.svg') }}" alt="" class="sidebar-icons">
                            @else
                                <img src="{{ asset('assets/SVG/assets-v2/flag - green.svg') }}" alt="" class="sidebar-icons">
                            @endif
                        </a>
                    @endif

                    @if (Auth::user()->permissions()->where('permission_id', 10)->exists() && Auth::user()->role == 2)
                    <a href="{{ route('evaluation.index') }}" class="d-block mt-5" title="Evaluation Forms">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-medal fa-xl @if (Route::currentRouteName() != 'evaluation.index') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'evaluation.index')
                            <img src="{{ asset('assets/SVG/assets-v2/108 - black.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/108 - green.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif

                    {{-- quality and agents cant see the orgs  --}}
                    @if (!in_array(Auth::user()->role, [2, 5, 6]))
                        <a href="{{ route('organizations') }}" class="d-block mt-5" title="Organizations">
                            {{-- <i style="color: #00a34e" class="fa-solid fa-building fa-xl @if (Route::currentRouteName() != 'organizations') text-muted @endif mb-5"></i> --}}
                            @if (Route::currentRouteName() != 'organizations')
                                <img src="{{ asset('assets/SVG/assets-v2/Vector-5 - black.svg') }}" alt="" class="sidebar-icons">
                            @else
                                <img src="{{ asset('assets/SVG/assets-v2/Vector-5.svg') }}" alt="" class="sidebar-icons">
                            @endif
                        </a>
                    @endif
                @endif
                @if (Auth::user()->role != 1)
                    <a href="{{ route('agents.evaluation.list') }}" class="d-block mt-5" title="Evaluation List">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-list-check fa-xl @if (Route::currentRouteName() != 'agents.evaluation.list') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'agents.evaluation.list')
                            <img src="{{ asset('assets/SVG/assets-v2/list.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/list - green.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif
                <a href="{{ route('admin.reports') }}" class="d-block mt-5" title="Reports">
                    {{-- <i style="color: #00a34e" class="fa-solid fa-file-lines fa-xl @if (Route::currentRouteName() != 'admin.reports') text-muted @endif mb-5"></i> --}}
                    @if (Route::currentRouteName() != 'admin.reports')
                        <img src="{{ asset('assets/SVG/assets-v2/Vector-6.svg') }}" alt="" class="sidebar-icons">
                    @else
                        <img src="{{ asset('assets/SVG/assets-v2/Vector-6 - green.svg') }}" alt="" class="sidebar-icons">
                    @endif
                </a>

                {{-- only admin can see users  --}}
                @if (Auth::user()->role == 1)
                    <a href="{{ route('admin.config.index') }}" class="d-block mt-5" title="System Configuration">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-cog fa-xl @if (Route::currentRouteName() != 'admin.config.index') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'admin.config.index')
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-8.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-8 - green.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif

                @if (!in_array(Auth::user()->role, [1, 4, 5, 6]))
                    <a href="{{ route('admin.config.index') }}" class="d-block mt-5" title="Configuration">
                        {{-- <i style="color: #00a34e" class="fa-solid fa-flag fa-xl @if (Route::currentRouteName() != 'qa.flags') text-muted @endif mb-5"></i> --}}
                        @if (Route::currentRouteName() != 'admin.config.index')
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-8.svg') }}" alt="" class="sidebar-icons">
                        @else
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-8 - green.svg') }}" alt="" class="sidebar-icons">
                        @endif
                    </a>
                @endif
                <a href="{{ route('logout') }}" onclick="event.preventDefault();    document.getElementById('logout-form').submit();" style="position: absolute;bottom: 50px;left: 19px;" class="d-block text-black text-xxl-center" title="Logout">
                    <i class="fas fa-sign-out text-danger "></i>
                </a>
            </div>
        @endif


        {{-- <nav class="navbar navbar-expand-md navbar-light shadow-sm py-2"> --}}
        <nav class="navbar navbar-expand-md navbar-light py-2 border-bottom ms-4 colored-nav">
            <div class="container-fluid me-0 ms-5">


                <a class="navbar-brand fs-3" href="" style="font-weight: 600">
                    {{-- {{ config('app.name', 'Laravel') }} --}}
                    {{-- {{ Str::title(str_replace('-', ' ', request()->segment(count(request()->segments())))) }} --}}
                    @yield('title')
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">

                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                                </li>
                            @endif

                            @if (Route::has('register'))
                                <li class="nav-item">
                                    {{-- <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a> --}}
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <span>
                                        @if (Auth::user()->role == 4)
                                            <img src="{{ asset('assets/SVG/assets-v2/82.svg') }}" alt="User Icon" style="width: 2rem; height: 2rem;">
                                        @elseif (Auth::user()->role == 1 || Auth::user()->role == 7)
                                            <img src="{{ asset('assets/SVG/assets-v2/83.svg') }}" alt="User Icon" style="width: 2rem; height: 2rem;">
                                        @elseif (Auth::user()->role == 2)
                                            <img src="{{ asset('assets/SVG/assets-v2/81.svg') }}" alt="User Icon" style="width: 2rem; height: 2rem;">
                                        @elseif (Auth::user()->role == 5)
                                            <img src="{{ asset('assets/SVG/assets-v2/80.svg') }}" alt="User Icon" style="width: 2rem; height: 2rem;">
                                        @elseif (Auth::user()->role == 6)
                                            <img src="{{ asset('assets/images/avatar.png') }}" alt="User Icon" style="width: 2rem; height: 2rem;">
                                        @endif
                                        {{-- <img src="{{ asset('assets/images/avatar.png') }}" style="width:2rem; height:2rem;" alt=""> --}}
                                    </span>
                                    <span style="font-size: 1rem" style="vertical-align: middle !important;">
                                        {{ Auth::user()->username }}
                                    </span>
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item fs-6" href="{{ route('logout') }}" onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        <i class="fa fa-sign-in fa-xl" style="font-size: 12px;margin-right: 4px"></i>
                                        {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <main class="py-4">
            @yield('content')
        </main>

    </div>

    @livewireScripts
    <script>
        @yield('script')
    </script>
    @yield('scripts')
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <x-livewire-alert::scripts />

    <script>
        // window.onload = function() {
        //     // Get the element by its ID
        //     var targetElement = document.getElementById('app');


        //     // Remove the class from the element
        //     targetElement.classList.remove('pre-loader');
        //     document.getElementById('loader').style.display = "none";
        // };
        window.onload = function() {
    // Delay the execution by at least 1000 milliseconds
    setTimeout(function() {
        // Get the element by its ID
        var targetElement = document.getElementById('app');

        // Remove the class from the element
        targetElement.classList.remove('pre-loader');
        document.getElementById('loader').style.display = "none";
    }, 1000); // Delay for 1000ms (1 second)
};

    </script>
</body>



</html>
