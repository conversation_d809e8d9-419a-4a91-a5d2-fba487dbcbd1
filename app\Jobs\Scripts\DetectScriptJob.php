<?php
namespace App\Jobs\Scripts;

use App\Models\Interaction;
use App\Models\ScriptsWord;
use App\Models\ScriptVariable;
use App\Models\TranscriptionScripts;
use App\Models\UserGroup;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class DetectScriptJob implements ShouldQueue
{
    use Batchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public $callConversation;
    public $call_id;
    public $lookingFor;
    public $timeout = 7200;

    public function __construct($callConversation, $call_id, $lookingFor)
    {
        $this->callConversation = $callConversation;
        $this->call_id          = $call_id;
        $this->lookingFor       = $lookingFor;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $firstItem = $this->callConversation->first();

            if (! $firstItem) {
                Log::channel('corrector')->warning('callConversation is empty in DetectScriptJob', ['call_id' => $this->call_id]);
                return; // or throw an exception
            }

            $Interaction = Interaction::where('call_id', 'like', '%' . $firstItem->call_id . '%')->first();


            $newCallId   = $Interaction->call_id;

            $checkGroup = false;

            $userGroup = UserGroup::whereId($Interaction->user_group_id)->first();
            if ($userGroup) {
                if (str_contains($userGroup->name, 'Blending')) {
                    $checkGroup = true;
                } else {
                    $checkGroup = false;
                }
            }

            // Store the original ScriptsWord models for later use
            $originalScriptModels = null;

            // $scripts is a COLLECTION of ScriptsWord models here
            if ($checkGroup) {
                $scripts = ScriptsWord::where('organization_id', $Interaction->organization_id)->where('script_type', $this->lookingFor)
                    ->get();
                $originalScriptModels = $scripts; // Save the original collection
            } else {
                $scripts = ScriptsWord::where('organization_id', $Interaction->organization_id)->where('user_group_id', $Interaction->user_group_id)->where('script_type', $this->lookingFor)
                    ->get();
                $originalScriptModels = $scripts; // Save the original collection
            }

            Log::channel('corrector')->info('scripts:' . $scripts); // Your original log

            // Check if scripts exist and are not empty
            if ($scripts && $scripts->isNotEmpty()) {
                Log::channel('corrector')->info('scripts:' . $scripts);

                // Store a copy of script keys before conversion to string
                $scriptsForLookup = $scripts;

                $scripts   = $scripts->pluck('key')->implode('||');
                $variables = ScriptVariable::get();

                $Interaction->call_type == "Outbound" ? $sideLog = "right" : $sideLog = "left";

                foreach ($this->callConversation as $textData) {
                    $exePath = storage_path('app/scripts/run_detect_scripts.sh');
                    $text    = $textData->content;

                    // Loop through each value and check if it's contained in the text
                    foreach ($variables as $data) {
                        foreach ($data->values as $value) {
                            if (strpos($text, $value->value) !== false) {
                                $scripts = str_replace($data->variable, $value->value, $scripts);
                            }
                        }
                    }

                    Log::channel('corrector')->info($textData->content);

                    $text = mb_convert_encoding($text, 'UTF-8', 'auto');

                    $process = new Process([$exePath, $text, $scripts]);

                    $process->setEnv([
                        'LANG'             => 'en_US.UTF-8', // Ensure UTF-8 locale
                        'PYTHONIOENCODING' => 'utf-8',       // Force Python to use UTF-8
                    ]);
                    $process->setTimeout(360);
                    $process->run();

                    // Check for errors
                    if (! $process->isSuccessful()) {
                        Log::channel('corrector')->info($process->getErrorOutput());
                    }

                    $response = json_decode($process->getOutput(), true);

                    // Initialize variables outside the conditional blocks to ensure they're always defined
                    $languageForInsert    = null;
                    $scriptsTypeForInsert = null;
                    $_matchedModel        = null;

                    // Add debug logs to track script detection
                    if (isset($response['best_script'])) {
                        Log::channel('corrector')->info('Best script found: ' . $response['best_script']);
                    }

                    // Look up script metadata from the original models collection
                    if (isset($response['best_script']) && ! empty($response['best_script'])) {
                        $_bestScriptKey = $response['best_script'];

                        // Look for matching script in our original collection
                        if ($originalScriptModels && $originalScriptModels->isNotEmpty()) {
                            // Try exact match first
                            $_matchedModel = $originalScriptModels->first(function ($model) use ($_bestScriptKey) {
                                return $model->key === $_bestScriptKey;
                            });

                            // If no exact match, try case-insensitive match
                            if (! $_matchedModel) {
                                $_matchedModel = $originalScriptModels->first(function ($model) use ($_bestScriptKey) {
                                    return strtolower($model->key) === strtolower($_bestScriptKey);
                                });
                            }

                            if ($_matchedModel) {
                                $languageForInsert    = $_matchedModel->language;
                                $scriptsTypeForInsert = $_matchedModel->script_type;

                                Log::channel('corrector')->info('Found matching script model with: Language=' .
                                    $languageForInsert . ', Type=' . $scriptsTypeForInsert);
                            } else {
                                // If still no match, try a direct database query as a last resort
                                $directMatch = ScriptsWord::where('key', 'like', '%' . $_bestScriptKey . '%')
                                    ->where('organization_id', $Interaction->organization_id)
                                    ->first();

                                if ($directMatch) {
                                    $languageForInsert    = $directMatch->language;
                                    $scriptsTypeForInsert = $directMatch->script_type;

                                    Log::channel('corrector')->info('Found direct database match with: Language=' .
                                        $languageForInsert . ', Type=' . $scriptsTypeForInsert);
                                } else {
                                    Log::channel('corrector')->warning('No match found for key: ' . $_bestScriptKey);
                                }
                            }
                        } else {
                            Log::channel('corrector')->warning('Original script models not available for lookup');
                        }
                    }

                    if (isset($response['input_text'])) {
                        // Only log $_matchedModel if it exists
                        if (isset($_matchedModel)) {
                            Log::channel('corrector')->info('response ' . json_encode($_matchedModel));
                        }

                        // Create the transcription record with properly initialized variables
                        $transcription = TranscriptionScripts::create([
                            'input_text'             => $response['input_text'],
                            'best_script'            => $response['best_script'] ?? null,
                            'best_ratio'             => $response['best_ratio'] ?? null,
                            'scripts'                => json_encode($response['scripts'] ?? []),
                            'call_id'                => $newCallId,
                            'calls_transcription_id' => $textData->id,
                            'language'               => $languageForInsert,
                            'scripts_type'           => $scriptsTypeForInsert,
                        ]);

                        // Log successful insertion with actual values
                        Log::channel('corrector')->info('Created TranscriptionScripts with ID: ' . $transcription->id .
                            ', Language: ' . ($languageForInsert ?? 'null') .
                            ', Type: ' . ($scriptsTypeForInsert ?? 'null'));
                    }

                    Log::channel('corrector')->info($process->getOutput());
                }
            }
        } catch (\Exception $e) {
            Log::channel('corrector')->error('Error in DetectJob: ' . $e->getMessage(), [
                'callConversation' => $this->callConversation,
                'exception'        => $e,
                'file'             => $e->getFile(),
                'line'             => $e->getLine(),
                'trace'            => $e->getTraceAsString(),
            ]);
        }
    }
}
