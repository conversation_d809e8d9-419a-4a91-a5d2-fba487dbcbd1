<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\File;

class ChannelsController extends Controller
{
    public function index()
    {
        $directory = storage_path('app/channels'); // Example path inside storage/app/files

        // Get all file names in the directory
        $files = File::files($directory);

        // Collect file names
        $fileNames = [];
        foreach ($files as $file) {
            $fileNames[] = $file->getFilename();
        }



        // Return file names as JSON response
        return response()->json(['fiels' => $fileNames]);

/*                 // Delete all files in the directory
                foreach ($files as $file) {
                    File::delete($file);
                } */
    }

    public function download($id)
    {


        $filePath = storage_path('app/channels/' . $id);

        // Check if the file exists
        if (!File::exists($filePath)) {
            return response()->json(['error' => 'File not found.'], 404);
        }

        // Return the file as a download
        $response = response()->download($filePath, $id, ['Content-Type' => 'audio/wav']);

        // Delete the file after sending
        File::delete($filePath);

        return $response;


    }
}
