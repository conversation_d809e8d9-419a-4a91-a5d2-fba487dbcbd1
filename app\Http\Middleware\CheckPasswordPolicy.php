<?php

namespace App\Http\Middleware;

use Closure;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Symfony\Component\HttpFoundation\Response;

class CheckPasswordPolicy
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        if ($user && $user->password_policy) {
            $updated_at = Carbon::parse($user->pass_reset_date);
            $password_reset_due_date = $updated_at->addDays($user->password_policy)->startOfDay();

            if ($password_reset_due_date->isPast()) {
                // Logout the user
                Auth::logout();

                // Generate password reset token
                $token = Password::broker()->createToken($user);

                // Redirect the user to the password reset page with the token
                return redirect()->route('password.reset', ['token' => $token])->with('warning', 'Your password has expired. Please reset your password.');
            }
        }

        return $next($request);
    }
}
