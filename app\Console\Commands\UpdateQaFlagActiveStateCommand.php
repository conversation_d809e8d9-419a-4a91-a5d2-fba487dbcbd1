<?php

namespace App\Console\Commands;

use App\Models\QaFlag;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateQaFlagActiveStateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qa_flags:updateActiveState';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the active status of qa_flags.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // QaFlag::where('end_date', '<=', now()->startOfDay())->where('start_date', '>', now()->startOfDay()->addDay())->update(['active' => 0]);
        QaFlag::where('end_date', '<=', now()->startOfDay())->update(['active' => 0]);

        // at 23:58, make the flags that starts tomorrow (00:00) active
        QaFlag::where('start_date', now()->startOfDay()->addDay())->where('end_date', '>=', now()->startOfDay()->addDay())->update(['active' => 1]);

        $this->info('qa_flags active state updated successfully.');
        Log::info('qa_flags active state updated successfully.');
    }
}
