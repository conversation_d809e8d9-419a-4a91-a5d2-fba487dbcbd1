<?php
namespace App\Console\Commands;

use App\Models\CallDetails;
use App\Models\Interaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ProcessInteractionsCommand extends Command
{
    // Command signature (how you will run it)
    protected $signature = 'interactions:process';

    // Description of the command
    protected $description = 'Process interactions with specific call_ids';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Define call IDs
        $callIds = [
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-48012811@***********',
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-48006624@***********',
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-47994632@***********',
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-47992140@***********',
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-47990003@***********',
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-47959540@***********',
        '00310E70-4AC2-17CE-8A95-4101CA0AAA77-47942256@***********'
];

        // Fetch interactions
        $interactions = Interaction::whereIn('call_id', $callIds)->get();

        foreach ($interactions as $interaction) {
            $cleanCallId = explode('@', $interaction->call_id)[0];
            // Fetch all left and right channel durations
            $leftSegments = DB::table('calls_transcription')
                ->where('call_id', $cleanCallId)
                ->where('source', 'left')
                ->select('duration_from', 'duration_to')
                ->orderBy('duration_from')
                ->get();

            $rightSegments = DB::table('calls_transcription')
                ->where('call_id', $cleanCallId)
                ->where('source', 'right')
                ->select('duration_from', 'duration_to')
                ->orderBy('duration_from')
                ->get();

            // Convert stored call_duration to seconds
            list($hours, $minutes, $seconds) = sscanf($interaction->call_duration, "%d:%d:%d");
            $totalDuration                   = ($hours * 3600) + ($minutes * 60) + $seconds;

            // Calculate overtalk duration
            $overtalkDuration = $this->calculateOvertalk($leftSegments, $rightSegments);

            // Sum total left and right durations
            $leftDuration = $leftSegments->sum(function ($segment) {
                return $segment->duration_to - $segment->duration_from;
            });

            $rightDuration = $rightSegments->sum(function ($segment) {
                return $segment->duration_to - $segment->duration_from;
            });

            // Adjust right and left durations (actual speaking time minus overtalk)
            $adjustedRight = max(0, $rightDuration - $overtalkDuration);
            $adjustedLeft  = max(0, $leftDuration - $overtalkDuration);

            // Calculate silence duration
            $silenceDuration = max(0, $totalDuration - ($adjustedLeft + $adjustedRight + $overtalkDuration));

            // Calculate other duration
            $otherDuration = $totalDuration - ($adjustedRight + $adjustedLeft + $silenceDuration + $overtalkDuration);

            // Store results in call_details table
            CallDetails::create([
                'call_id'                => $interaction->call_id,
                'left_channel_duration'  => $adjustedLeft,
                'right_channel_duration' => $adjustedRight,
                'silence_duration'       => $silenceDuration,
                'overtalk_duration'      => $overtalkDuration,
                'engage_duration'        => $totalDuration,
                'other_duration'         => $otherDuration,
            ]);
        }

        $this->info('Interaction processing completed.');
    }

    private function calculateOvertalk($leftSegments, $rightSegments)
    {
        $overtalk = 0;
        $i        = 0;
        $j        = 0;

        while ($i < count($leftSegments) && $j < count($rightSegments)) {
            $left  = $leftSegments[$i];
            $right = $rightSegments[$j];

            // Find overlap range
            $start = max($left->duration_from, $right->duration_from);
            $end   = min($left->duration_to, $right->duration_to);

            if ($start < $end) {
                // Valid overlap found, add to total overtalk
                $overtalk += ($end - $start);
            }

            // Move to next segment
            if ($left->duration_to < $right->duration_to) {
                $i++;
            } else {
                $j++;
            }
        }

        return $overtalk;
    }
}




