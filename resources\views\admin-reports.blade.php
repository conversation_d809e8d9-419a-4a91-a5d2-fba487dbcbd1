@extends('layouts.app')

@section('title', 'Reports')

@section('style')
    <style>
        /* input {
                border: solid 1px #b6b6b6 !important;
                border-radius: 0.6rem !important;
                background-color: white !important;
                padding: 0.4rem 0.3rem 0.4rem 0.5rem !important;
            }

            .lower-card {
                cursor: pointer;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                transition: box-shadow 0.3s ease;
            }

            .lower-card:hover {
                box-shadow: 0 0 20px rgba(0,163,78,0.5);
            }

            .lower-card-body {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 20vh;
            }

            a {
                text-decoration: none !important;
            } */
        /* Card styles */

        .card-with-motion {
            opacity: 0;
            /* Start hidden */
            transform: translateY(20px);
            /* Start slightly shifted */
            animation: fadeIn 0.5s ease-out forwards;
            max-width: 19rem;
        }

        .card-with-motion:hover {
            background: #f8f8f8 !important;
            transform: scale(1.05) !important;
            /* Make the card slightly larger */
            box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3);
            /* Add a larger shadow */
        }

        .card-with-motion:nth-child(1) {
            animation-delay: 0.2s;
            /* Delayed by 0.2s */
        }

        .card-with-motion:nth-child(2) {
            animation-delay: 0.4s;
        }

        .card-with-motion:nth-child(3) {
            animation-delay: 0.6s;
        }

        .card-with-motion:nth-child(4) {
            animation-delay: 0.8s;
        }

        .card-with-motion:nth-child(5) {
            animation-delay: 1s;
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: translateY(20px);
                /* Offset to animate into position */
            }

            100% {
                opacity: 1;
                transform: translateY(0);
                /* End in the original position */
            }
        }
    </style>
@endsection


@section('content')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
    <div class="container-fluid mt-3 px-4">
        {{-- <div class="row mx-3 ps-5">
            <div class="col-12 mb-5">
                <div class="card bg-white shadow shadow py-2 px-3">
                    <div class="card-body p-4">
                        <h5>
                            <b>
                                Reports
                            </b>
                        </h5>
                        <i class="fa-regular fa-file-excel fa-2xl float-end" style="cursor: pointer"></i>
                        <h6 class="text-muted">
                            View and export reports
                            <i class="fa-solid fa-file-lines fa-2xl float-end" style="color: #00a34e"></i>
                        </h6>
                    </div>
                </div>
            </div>
        </div> --}}

        {{-- lower cards  --}}
        {{-- reports for supervisors and admins --}}
        <div class="row mx-3 ps-5 d-flex flex-wrap justify-content-start" style="gap:3rem; padding-left: 5% !important;">
            @if (!in_array(Auth::user()->role, [4, 5, 6]))
            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('interactions.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #ffd6da !important;">
                        <i class="fas fa-phone-volume" style="font-size: 30px; color: #fe7883 !important;"></i>
                    </div>
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Interactions Report</h6>
                        <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                            This report shows interaction data, including call times, durations, and agent performance.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('hold.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #ebd6fd !important;">
                        <i class="fas fa-circle-pause" style="font-size: 30px; color: #c07bf9 !important;"></i>
                    </div>
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Hold Report</h6>
                        <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                            Review hold times, including reasons for hold and how long calls are kept on hold.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('longInteractions.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #d7e5ff !important;">
                        <i class="fas fa-clock" style="font-size: 30px; color: #8cb4fc !important;"></i>
                    </div>
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Interactions Duration Report</h6>
                        <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                            This report provides details on interaction durations, including hold times and agent activity.
                        </p>
                    </div>
                </a>
            </div>


            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('disconnectedCalls.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #fed7d8 !important;">
                        <i class="fa-solid fa-phone-slash" style="font-size: 30px; color: #ff7686 !important;"></i>
                    </div>
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Disconnected Calls Report</h6>
                        <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                            This report provides insights into disconnected calls, including reasons and impact analysis.
                        </p>
                    </div>
                </a>
            </div>

                <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                    <a href="{{ route('user.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #5cfda9 !important;">
                            {{-- <i class="fas fa-user-large" style="font-size: 30px; color: #ffc560 !important;"></i> --}}
                            <img src="{{ asset('assets/SVG/assets-v2/Vector-6 - green.svg') }}" alt="" class="sidebar-icons">
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-start; gap: 0.5rem;" class="mt-5">
                            <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Users Report</h6>
                            <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                                This report allows you to view all user details such as their activity, roles, and access permissions.
                            </p>
                        </div>
                    </a>
                </div>

                <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                    <a href="{{ route('skill.group.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #f1d7fd !important;">
                            <i class="fas fa-people-roof" style="font-size: 30px; color: #c07bf9 !important;"></i>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-start; gap: 0.5rem;" class="mt-5">
                            <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Skill Groups Report</h6>
                            <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                                This report shows the skill groups within your system, their composition, and performance.
                            </p>
                        </div>
                    </a>
                </div>

                <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                    <a href="{{ route('user.permissions') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                        <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #fff0d7 !important;">
                            <i class="fas fa-user-gear" style="font-size: 30px; color: #ffc65b !important;"></i>
                        </div>
                        <div class="mt-5">
                            <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Permissions Report</h6>
                            <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                                You can manage and review user permissions and their access to resources here.
                            </p>
                        </div>
                    </a>
                </div>




                {{-- <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion"
                        style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                        <a href="{{ route('shortInteractions.report') }}"
                            class="text-decoration-none h-100 d-flex flex-column justify-content-between align-items-start p-2 pt-3">
                            <div class="rounded-circle bg-light d-flex justify-content-center align-items-center"
                                style="width: 60px; height: 60px; background-color: #d7ffe7 !important;">
                                <i class="fas fa-stopwatch" style="font-size: 30px; color: #28a745 !important;"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Short Interactions Report</h6>
                                <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                                    Analyze short interactions to identify quick resolutions and improve efficiency.
                                </p>
                            </div>
                        </a>
                    </div> --}}
            @endif

            {{-- Transaction Reason Report - Available to all roles --}}
            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">
                <a href="{{ route('transactionReason.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #e8f5e8 !important; flex-shrink:0; overflow:hidden;">
                        <i class="fas fa-exchange-alt" style="font-size: 30px; color: #28a745 !important;"></i>
                    </div>
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Transaction Reason Report</h6>
                        <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                            This report shows transaction reasons and sub-reasons from call summaries for analysis.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem;border-color: #f2f2f2;" @if (in_array(Auth::user()->role, [4, 5, 6])) style="margin-left: 6%" @endif>
                <a href="{{ route('evaluation.reports') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <!-- Icon Section -->
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px;background-color:#fff1d6 !important">
                        <i class="fas fa-clipboard-list" style="font-size: 30px; color: #fbc068 !important;"></i>
                    </div>

                    <!-- Title Section -->
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px;color:#40798c !important">Evaluation Reports</h6>
                        <p class="text-muted mb-0" style="font-size: 14px;color: #8f9b9e;">
                            These reports provides an evaluation of agent performance, including scores, feedback, and areas of improvement.
                        </p>
                    </div>
                </a>
            </div>

            <div class="card col-md-2 col-12 lower-card bg-white shadow-sm card-with-motion" style="border-radius: 12px; height: 17rem; border-color: #f2f2f2;">                <a href="{{ route('interaction.quality.report') }}" class="text-decoration-none h-100 d-flex flex-column justify-content-start align-items-start p-2 pt-3">
                    <div class="rounded-circle bg-light d-flex justify-content-center align-items-center" style="width: 60px; height: 60px; background-color: #d6f5fd !important;  flex-shrink:0; overflow:hidden;">
                        <i class="fas fa-star-half-alt" style="font-size: 30px; color: #00bcd4 !important; "></i>
                    </div>
                    <div class="mt-5">
                        <h6 class="fw-bold mb-2" style="font-size: 16px; color: #40798c !important;">Interaction Quality Report</h6>
                        <p class="text-muted" style="font-size: 14px; color: #8f9b9e;">
                            Analyze call audio quality metrics, silence, artifacts, and more for each interaction.
                        </p>
                    </div>
                </a>
            </div>
        </div>

        {{-- <div class="card col-md-2 col-12 lower-card bg-white card-pop-out-effect" @if (in_array(Auth::user()->role, [4, 5, 6])) style="margin-left: 6%" @endif>
            <a href="{{ route('evaluation.report') }}" style="color: #00a34e">
                <div class="card-body lower-card-body d-flex flex-column justify-content-evenly pt-1">
                    <img src="{{ asset('assets/images/evaluation/survey_active.png') }}" alt="Icon" width="37" height="37" class="pt-0">
                    <h6><b>Evaluations Report</b></h6>
                </div>
            </a>
        </div> --}}










    </div>
    </div>
@endsection
