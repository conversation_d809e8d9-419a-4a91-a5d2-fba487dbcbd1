<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\Storage;

class FtpTest extends TestCase
{
    /**
     * Test FTP connection and basic operations.
     *
     * @return void
     */
    public function testFtpConnection()
    {
        // Get the FTP disk
        $disk = Storage::disk('ftp');

        // Check if we can connect to FTP server
        $this->assertTrue($disk->exists('/'), 'Failed to connect to FTP server.');

        // Print message if connected
        echo "Connected to FTP server.\n";

        // Directory to list files from (adjust as needed)
        $directory = '/'; // Adjust the directory path as needed
        echo "Listing files from directory: {$directory}\n";

        // List files in the directory on FTP server
        $files = $disk->files($directory);
        $this->assertNotEmpty($files, "Failed to list files on FTP server in directory: {$directory}");

        // Example: Get contents of a file (adjust as needed)
        $file = '/2024-07-14 09-19-39.mkv'; // Adjust the file path as needed
        $contents = $disk->get($file);
        $this->assertNotEmpty($contents, "Failed to get contents of file: {$file}");

        // Example: Put a file on the server (adjust as needed)
        $localFilePath = storage_path('app/local-file.txt');
        $remoteFilePath = '/new-file.txt'; // Adjust the file path as needed
        file_put_contents($localFilePath, 'Content to upload');
        $this->assertTrue($disk->put($remoteFilePath, file_get_contents($localFilePath)), 'Failed to upload file.');

        // Example: Delete a file (adjust as needed)
        $this->assertTrue($disk->delete($remoteFilePath), 'Failed to delete file.');

        // Cleanup (adjust as needed)
        unlink($localFilePath);
    }
}
